/**
 * 高级情感设置组件
 * 用于配置情感分析和表情生成的高级选项
 */
import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Select, Switch, Slider, Button, Collapse, Divider, Space, Card, Typography, Tooltip, Row, Col} from 'antd';
import { SettingOutlined, InfoCircleOutlined, PlusOutlined, MinusOutlined, EditOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import ColorPicker from '../common/ColorPicker';

const { Panel } = Collapse;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { TextArea } = Input;

/**
 * 情感配置
 */
export interface EmotionConfig {
  /** 名称 */
  name: string;
  /** 颜色 */
  color: string;
  /** 强度 */
  intensity: number;
  /** 是否启用 */
  enabled: boolean;
  /** 子情感 */
  subEmotions?: EmotionConfig[];
  /** 混合模式 */
  blendMode?: 'add' | 'multiply' | 'override';
  /** 过渡时间 */
  transitionTime?: number;
  /** 持续时间 */
  duration?: number;
  /** 延迟时间 */
  delay?: number;
  /** 自定义参数 */
  customParams?: Record<string, any>;
}

/**
 * 微表情配置
 */
export interface MicroExpressionConfig {
  /** 是否启用 */
  enabled: boolean;
  /** 频率 */
  frequency: number;
  /** 强度 */
  intensity: number;
  /** 持续时间 */
  duration: number;
  /** 类型 */
  types: string[];
  /** 随机性 */
  randomness: number;
}

/**
 * 高级情感设置属性
 */
interface AdvancedEmotionSettingsProps {
  /** 初始情感配置 */
  initialEmotions?: EmotionConfig[];
  /** 初始微表情配置 */
  initialMicroExpressions?: MicroExpressionConfig;
  /** 变更回调 */
  onChange?: (emotions: EmotionConfig[], microExpressions: MicroExpressionConfig) => void;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 默认情感配置
 */
const DEFAULT_EMOTIONS: EmotionConfig[] = [
  { name: 'happy', color: '#52c41a', intensity: 1.0, enabled: true },
  { name: 'sad', color: '#1890ff', intensity: 1.0, enabled: true },
  { name: 'angry', color: '#f5222d', intensity: 1.0, enabled: true },
  { name: 'surprised', color: '#faad14', intensity: 1.0, enabled: true },
  { name: 'fear', color: '#722ed1', intensity: 1.0, enabled: true },
  { name: 'disgust', color: '#eb2f96', intensity: 1.0, enabled: true },
  { name: 'neutral', color: '#bfbfbf', intensity: 1.0, enabled: true }
];

/**
 * 默认微表情配置
 */
const DEFAULT_MICRO_EXPRESSIONS: MicroExpressionConfig = {
  enabled: true,
  frequency: 0.3,
  intensity: 0.5,
  duration: 0.2,
  types: ['blink', 'eyebrow_raise', 'mouth_twitch'],
  randomness: 0.5
};

/**
 * 可用微表情类型
 */
const MICRO_EXPRESSION_TYPES = [
  { label: '眨眼', value: 'blink' },
  { label: '眉毛抬起', value: 'eyebrow_raise' },
  { label: '嘴角抽动', value: 'mouth_twitch' },
  { label: '鼻子皱起', value: 'nose_wrinkle' },
  { label: '眼睛眯起', value: 'eye_squint' },
  { label: '头部轻微摇晃', value: 'head_shake' },
  { label: '头部轻微点头', value: 'head_nod' },
  { label: '嘴唇抿起', value: 'lip_press' },
  { label: '眉头皱起', value: 'brow_furrow' },
  { label: '眼睛睁大', value: 'eye_widen' }
];

/**
 * 高级情感设置组件
 */
const AdvancedEmotionSettings: React.FC<AdvancedEmotionSettingsProps> = ({
  initialEmotions,
  initialMicroExpressions,
  onChange,
  disabled = false
}) => {
  const { t } = useTranslation();
  
  // 状态
  const [emotions, setEmotions] = useState<EmotionConfig[]>(initialEmotions || [...DEFAULT_EMOTIONS]);
  const [microExpressions, setMicroExpressions] = useState<MicroExpressionConfig>(
    initialMicroExpressions || { ...DEFAULT_MICRO_EXPRESSIONS }
  );
  const [editingEmotion, setEditingEmotion] = useState<number | null>(null);
  const [showEmotionDetails, setShowEmotionDetails] = useState<boolean>(false);
  const [showMicroExpressions, setShowMicroExpressions] = useState<boolean>(false);
  
  // 当情感或微表情配置变更时，调用回调
  useEffect(() => {
    onChange?.(emotions, microExpressions);
  }, [emotions, microExpressions, onChange]);
  
  // 处理情感变更
  const handleEmotionChange = (index: number, field: keyof EmotionConfig, value: any) => {
    const newEmotions = [...emotions];
    newEmotions[index] = { ...newEmotions[index], [field]: value };
    setEmotions(newEmotions);
  };
  
  // 处理微表情变更
  const handleMicroExpressionChange = (field: keyof MicroExpressionConfig, value: any) => {
    setMicroExpressions({ ...microExpressions, [field]: value });
  };
  
  // 添加情感
  const handleAddEmotion = () => {
    const newEmotion: EmotionConfig = {
      name: `emotion_${emotions.length + 1}`,
      color: `#${Math.floor(Math.random() * 16777215).toString(16)}`,
      intensity: 1.0,
      enabled: true
    };
    
    setEmotions([...emotions, newEmotion]);
    setEditingEmotion(emotions.length);
  };
  
  // 删除情感
  const handleRemoveEmotion = (index: number) => {
    const newEmotions = [...emotions];
    newEmotions.splice(index, 1);
    setEmotions(newEmotions);
    
    if (editingEmotion === index) {
      setEditingEmotion(null);
    } else if (editingEmotion !== null && editingEmotion > index) {
      setEditingEmotion(editingEmotion - 1);
    }
  };
  
  // 重置为默认配置
  const handleReset = () => {
    setEmotions([...DEFAULT_EMOTIONS]);
    setMicroExpressions({ ...DEFAULT_MICRO_EXPRESSIONS });
    setEditingEmotion(null);
  };
  
  // 渲染情感列表
  const renderEmotionList = () => (
    <div className="emotion-list">
      {emotions.map((emotion, index) => (
        <div key={index} className="emotion-item">
          <Row align="middle" gutter={8}>
            <Col span={1}>
              <div
                className="emotion-color"
                style={{
                  backgroundColor: emotion.color,
                  width: 16,
                  height: 16,
                  borderRadius: 4
                }}
              />
            </Col>
            <Col span={6}>
              <Text strong>{emotion.name}</Text>
            </Col>
            <Col span={8}>
              <Slider
                value={emotion.intensity}
                min={0}
                max={1}
                step={0.1}
                onChange={(value) => handleEmotionChange(index, 'intensity', value)}
                disabled={disabled || !emotion.enabled}
              />
            </Col>
            <Col span={3}>
              <Switch
                checked={emotion.enabled}
                onChange={(checked) => handleEmotionChange(index, 'enabled', checked)}
                disabled={disabled}
              />
            </Col>
            <Col span={6}>
              <Space>
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => setEditingEmotion(editingEmotion === index ? null : index)}
                  disabled={disabled}
                />
                <Button
                  type="text"
                  danger
                  icon={<MinusOutlined />}
                  onClick={() => handleRemoveEmotion(index)}
                  disabled={disabled || emotions.length <= 1}
                />
              </Space>
            </Col>
          </Row>
          
          {editingEmotion === index && (
            <Card size="small" className="emotion-details" style={{ marginTop: 8, marginBottom: 16 }}>
              <Form layout="vertical">
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label={t('editor.emotion.name')}>
                      <Input
                        value={emotion.name}
                        onChange={(e) => handleEmotionChange(index, 'name', e.target.value)}
                        disabled={disabled}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('editor.emotion.color')}>
                      <ColorPicker
                        value={emotion.color}
                        onChange={(color) => handleEmotionChange(index, 'color', color)}
                        disabled={disabled}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label={t('editor.emotion.blendMode')}>
                      <Select
                        value={emotion.blendMode || 'add'}
                        onChange={(value) => handleEmotionChange(index, 'blendMode', value)}
                        disabled={disabled}
                      >
                        <Option value="add">{t('editor.emotion.blendModes.add')}</Option>
                        <Option value="multiply">{t('editor.emotion.blendModes.multiply')}</Option>
                        <Option value="override">{t('editor.emotion.blendModes.override')}</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('editor.emotion.transitionTime')}>
                      <InputNumber
                        value={emotion.transitionTime || 0.3}
                        onChange={(value) => handleEmotionChange(index, 'transitionTime', value)}
                        disabled={disabled}
                        min={0}
                        max={5}
                        step={0.1}
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label={t('editor.emotion.duration')}>
                      <InputNumber
                        value={emotion.duration || 1.0}
                        onChange={(value) => handleEmotionChange(index, 'duration', value)}
                        disabled={disabled}
                        min={0}
                        max={10}
                        step={0.1}
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('editor.emotion.delay')}>
                      <InputNumber
                        value={emotion.delay || 0.0}
                        onChange={(value) => handleEmotionChange(index, 'delay', value)}
                        disabled={disabled}
                        min={0}
                        max={5}
                        step={0.1}
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </Card>
          )}
        </div>
      ))}
      
      <div className="emotion-actions" style={{ marginTop: 16 }}>
        <Button
          type="dashed"
          icon={<PlusOutlined />}
          onClick={handleAddEmotion}
          disabled={disabled}
          block
        >
          {t('editor.emotion.addEmotion')}
        </Button>
      </div>
    </div>
  );
  
  // 渲染微表情设置
  const renderMicroExpressionSettings = () => (
    <div className="micro-expression-settings">
      <Form layout="vertical">
        <Form.Item
          label={
            <Space>
              {t('editor.emotion.enableMicroExpressions')}
              <Tooltip title={t('editor.emotion.microExpressionsTooltip')}>
                <InfoCircleOutlined />
              </Tooltip>
            </Space>
          }
        >
          <Switch
            checked={microExpressions.enabled}
            onChange={(checked) => handleMicroExpressionChange('enabled', checked)}
            disabled={disabled}
          />
        </Form.Item>
        
        {microExpressions.enabled && (
          <>
            <Form.Item label={t('editor.emotion.microExpressionTypes')}>
              <Select
                mode="multiple"
                value={microExpressions.types}
                onChange={(value) => handleMicroExpressionChange('types', value)}
                disabled={disabled}
                style={{ width: '100%' }}
              >
                {MICRO_EXPRESSION_TYPES.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            <Form.Item label={t('editor.emotion.frequency')}>
              <Slider
                value={microExpressions.frequency}
                min={0}
                max={1}
                step={0.1}
                onChange={(value) => handleMicroExpressionChange('frequency', value)}
                disabled={disabled}
                marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
              />
            </Form.Item>
            
            <Form.Item label={t('editor.emotion.intensity')}>
              <Slider
                value={microExpressions.intensity}
                min={0}
                max={1}
                step={0.1}
                onChange={(value) => handleMicroExpressionChange('intensity', value)}
                disabled={disabled}
                marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
              />
            </Form.Item>
            
            <Form.Item label={t('editor.emotion.duration')}>
              <InputNumber
                value={microExpressions.duration}
                onChange={(value) => handleMicroExpressionChange('duration', value)}
                disabled={disabled}
                min={0.1}
                max={1}
                step={0.1}
                style={{ width: '100%' }}
              />
            </Form.Item>
            
            <Form.Item label={t('editor.emotion.randomness')}>
              <Slider
                value={microExpressions.randomness}
                min={0}
                max={1}
                step={0.1}
                onChange={(value) => handleMicroExpressionChange('randomness', value)}
                disabled={disabled}
                marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
              />
            </Form.Item>
          </>
        )}
      </Form>
    </div>
  );
  
  return (
    <div className="advanced-emotion-settings">
      <Collapse
        bordered={false}
        activeKey={showEmotionDetails ? ['emotions'] : []}
        onChange={(key) => setShowEmotionDetails(key.includes('emotions'))}
      >
        <Panel
          header={
            <span>
              <SettingOutlined /> {t('editor.emotion.emotionDetails')}
            </span>
          }
          key="emotions"
        >
          {renderEmotionList()}
        </Panel>
      </Collapse>
      
      <Divider />
      
      <Collapse
        bordered={false}
        activeKey={showMicroExpressions ? ['microExpressions'] : []}
        onChange={(key) => setShowMicroExpressions(key.includes('microExpressions'))}
      >
        <Panel
          header={
            <span>
              <SettingOutlined /> {t('editor.emotion.microExpressions')}
            </span>
          }
          key="microExpressions"
        >
          {renderMicroExpressionSettings()}
        </Panel>
      </Collapse>
      
      <div className="settings-actions" style={{ marginTop: 16 }}>
        <Button onClick={handleReset} disabled={disabled}>
          {t('editor.emotion.resetToDefaults')}
        </Button>
      </div>
    </div>
  );
};

export default AdvancedEmotionSettings;
