/**
 * WebSocket连接管理器
 * 负责管理与服务器的WebSocket连接，提供高级连接管理功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { store } from '../store';
import { setConnectionStatus } from '../store/collaboration/connectionSlice';

// 连接状态枚举
export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// 连接统计信息接口
export interface ConnectionStats {
  // 连接尝试次数
  connectAttempts: number;
  // 重连尝试次数
  reconnectAttempts: number;
  // 成功连接次数
  successfulConnections: number;
  // 连接失败次数
  failedConnections: number;
  // 发送的消息数量
  messagesSent: number;
  // 接收的消息数量
  messagesReceived: number;
  // 发送的字节数
  bytesSent: number;
  // 接收的字节数
  bytesReceived: number;
  // 平均延迟（毫秒）
  averageLatency: number;
  // 最后一次延迟（毫秒）
  lastLatency: number;
  // 最大延迟（毫秒）
  maxLatency: number;
  // 最小延迟（毫秒）
  minLatency: number;
  // 连接持续时间（毫秒）
  connectionDuration: number;
  // 连接开始时间
  connectionStartTime: number;
  // 最后一次活动时间
  lastActivityTime: number;
}

// 连接配置接口
export interface ConnectionConfig {
  // 初始重连延迟（毫秒）
  initialReconnectDelay: number;
  // 最大重连延迟（毫秒）
  maxReconnectDelay: number;
  // 重连延迟因子（指数退避）
  reconnectBackoffFactor: number;
  // 重连延迟抖动（随机因子，0-1）
  reconnectJitter: number;
  // 最大重连尝试次数
  maxReconnectAttempts: number;
  // 心跳间隔（毫秒）
  heartbeatInterval: number;
  // 心跳超时（毫秒）
  heartbeatTimeout: number;
  // 是否启用消息压缩
  enableCompression: boolean;
  // 压缩阈值（字节）
  compressionThreshold: number;
  // 是否启用消息队列
  enableMessageQueue: boolean;
  // 最大队列大小
  maxQueueSize: number;
  // 是否启用连接状态监控
  enableStatusMonitoring: boolean;
  // 状态监控间隔（毫秒）
  statusMonitoringInterval: number;
  // 是否自动重连
  autoReconnect: boolean;
  // 是否启用调试日志
  debug: boolean;
}

/**
 * WebSocket连接管理器类
 */
export class WebSocketConnectionManager extends EventEmitter {
  // WebSocket实例
  private socket: WebSocket | null = null;
  // 服务器URL
  private serverUrl: string = '';
  // 连接状态
  private status: ConnectionStatus = ConnectionStatus.DISCONNECTED;
  // 重连尝试次数
  private reconnectAttempts: number = 0;
  // 当前重连延迟
  private currentReconnectDelay: number = 0;
  // 重连定时器ID
  private reconnectTimerId: number | null = null;
  // 心跳定时器ID
  private heartbeatTimerId: number | null = null;
  // 心跳超时定时器ID
  private heartbeatTimeoutId: number | null = null;
  // 状态监控定时器ID
  private statusMonitorTimerId: number | null = null;
  // 消息队列
  private messageQueue: any[] = [];
  // 连接配置
  private config: ConnectionConfig;
  // 连接统计信息
  private stats: ConnectionStats;
  // 连接参数
  private connectionParams: Record<string, string> = {};
  // 是否正在进行重连
  private isReconnecting: boolean = false;
  // 是否手动断开连接
  private manualDisconnect: boolean = false;

  /**
   * 创建WebSocket连接管理器
   * @param config 连接配置
   */
  constructor(config?: Partial<ConnectionConfig>) {
    super();

    // 默认配置
    this.config = {
      initialReconnectDelay: 1000,
      maxReconnectDelay: 30000,
      reconnectBackoffFactor: 1.5,
      reconnectJitter: 0.2,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      heartbeatTimeout: 10000,
      enableCompression: true,
      compressionThreshold: 1024,
      enableMessageQueue: true,
      maxQueueSize: 100,
      enableStatusMonitoring: true,
      statusMonitoringInterval: 5000,
      autoReconnect: true,
      debug: false,
      ...config
    };

    // 初始化统计信息
    this.stats = {
      connectAttempts: 0,
      reconnectAttempts: 0,
      successfulConnections: 0,
      failedConnections: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesSent: 0,
      bytesReceived: 0,
      averageLatency: 0,
      lastLatency: 0,
      maxLatency: 0,
      minLatency: Infinity,
      connectionDuration: 0,
      connectionStartTime: 0,
      lastActivityTime: 0
    };

    // 初始化当前重连延迟
    this.currentReconnectDelay = this.config.initialReconnectDelay;
  }

  /**
   * 初始化连接管理器
   * @param serverUrl 服务器URL
   * @param params 连接参数
   */
  public initialize(serverUrl: string, params: Record<string, string> = {}): void {
    this.serverUrl = serverUrl;
    this.connectionParams = params;
    this.manualDisconnect = false;
  }

  /**
   * 连接到服务器
   */
  public connect(): void {
    if (this.status === ConnectionStatus.CONNECTED || this.status === ConnectionStatus.CONNECTING) {
      console.log('已经连接或正在连接中');
      return;
    }

    this.manualDisconnect = false;
    this.stats.connectAttempts++;
    this.setStatus(ConnectionStatus.CONNECTING);

    try {
      // 构建WebSocket URL，包含连接参数
      let url = this.serverUrl;
      const params = new URLSearchParams();

      // 添加连接参数
      Object.entries(this.connectionParams).forEach(([key, value]) => {
        params.append(key, value);
      });

      // 添加时间戳防止缓存
      params.append('t', Date.now().toString());

      // 拼接URL
      if (url.includes('?')) {
        url += '&' + params.toString();
      } else {
        url += '?' + params.toString();
      }

      // 创建WebSocket连接
      this.socket = new WebSocket(url);

      // 设置二进制类型
      this.socket.binaryType = 'arraybuffer';

      // 设置事件处理器
      this.socket.onopen = this.handleOpen.bind(this);
      this.socket.onmessage = this.handleMessage.bind(this);
      this.socket.onclose = this.handleClose.bind(this);
      this.socket.onerror = this.handleError.bind(this);

      // 记录连接开始时间
      this.stats.lastActivityTime = Date.now();

      if (this.config.debug) {
        console.log('正在连接到服务器:', url);
      }
    } catch (error) {
      console.error('连接到服务器时出错:', error);
      this.setStatus(ConnectionStatus.ERROR);
      this.stats.failedConnections++;

      if (this.config.autoReconnect && !this.manualDisconnect) {
        this.scheduleReconnect();
      }
    }
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    this.manualDisconnect = true;

    // 停止所有定时器
    this.stopHeartbeat();
    this.stopReconnect();
    this.stopStatusMonitor();

    if (this.socket) {
      try {
        this.socket.close();
      } catch (error) {
        console.error('关闭WebSocket连接时出错:', error);
      }

      this.socket = null;
    }

    this.setStatus(ConnectionStatus.DISCONNECTED);
    this.emit('disconnected');
  }

  /**
   * 发送消息
   * @param type 消息类型
   * @param data 消息数据
   * @returns 是否成功发送
   */
  public send(type: string, data: any): boolean {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      if (this.config.enableMessageQueue) {
        this.queueMessage(type, data);
        return false;
      }
      return false;
    }

    try {
      const message = {
        type,
        data,
        timestamp: Date.now()
      };

      const messageStr = JSON.stringify(message);
      const messageSize = new Blob([messageStr]).size;

      // 更新统计信息
      this.stats.messagesSent++;
      this.stats.bytesSent += messageSize;
      this.stats.lastActivityTime = Date.now();

      // 发送消息
      this.socket.send(messageStr);

      if (this.config.debug) {
        console.log(`已发送消息 [${type}]:`, data);
      }

      return true;
    } catch (error) {
      console.error('发送消息时出错:', error);

      if (this.config.enableMessageQueue) {
        this.queueMessage(type, data);
      }

      return false;
    }
  }

  /**
   * 获取连接状态
   * @returns 连接状态
   */
  public getStatus(): ConnectionStatus {
    return this.status;
  }

  /**
   * 获取连接统计信息
   * @returns 连接统计信息
   */
  public getStats(): ConnectionStats {
    // 如果连接中，更新连接持续时间
    if (this.status === ConnectionStatus.CONNECTED && this.stats.connectionStartTime > 0) {
      this.stats.connectionDuration = Date.now() - this.stats.connectionStartTime;
    }

    return { ...this.stats };
  }

  /**
   * 更新配置
   * @param config 新配置
   */
  public updateConfig(config: Partial<ConnectionConfig>): void {
    this.config = {
      ...this.config,
      ...config
    };

    // 如果心跳间隔改变，重新启动心跳
    if (config.heartbeatInterval !== undefined && this.status === ConnectionStatus.CONNECTED) {
      this.stopHeartbeat();
      this.startHeartbeat();
    }

    // 如果状态监控间隔改变，重新启动状态监控
    if (config.statusMonitoringInterval !== undefined && this.config.enableStatusMonitoring) {
      this.stopStatusMonitor();
      this.startStatusMonitor();
    }
  }

  /**
   * 清空消息队列
   */
  public clearMessageQueue(): void {
    this.messageQueue = [];
  }

  /**
   * 处理WebSocket打开事件
   */
  private handleOpen(): void {
    this.setStatus(ConnectionStatus.CONNECTED);
    this.reconnectAttempts = 0;
    this.currentReconnectDelay = this.config.initialReconnectDelay;
    this.stats.successfulConnections++;
    this.stats.connectionStartTime = Date.now();
    this.stats.lastActivityTime = Date.now();

    // 启动心跳
    this.startHeartbeat();

    // 启动状态监控
    if (this.config.enableStatusMonitoring) {
      this.startStatusMonitor();
    }

    // 处理消息队列
    this.processMessageQueue();

    this.emit('connected');

    if (this.config.debug) {
      console.log('已连接到服务器');
    }
  }

  /**
   * 处理WebSocket消息事件
   * @param event 消息事件
   */
  private handleMessage(event: MessageEvent): void {
    this.stats.lastActivityTime = Date.now();
    this.stats.messagesReceived++;
    this.stats.bytesReceived += event.data.length || 0;

    // 重置心跳超时
    if (this.heartbeatTimeoutId) {
      clearTimeout(this.heartbeatTimeoutId);
      this.heartbeatTimeoutId = null;
    }

    try {
      const message = JSON.parse(event.data);

      // 如果是心跳响应消息
      if (message.type === 'heartbeat') {
        this.handleHeartbeatResponse(message);
        return;
      }

      // 发出消息事件
      this.emit('message', message);

      if (this.config.debug) {
        console.log(`已接收消息 [${message.type}]:`, message.data);
      }
    } catch (error) {
      console.error('处理消息时出错:', error);
    }
  }

  /**
   * 处理WebSocket关闭事件
   * @param event 关闭事件
   */
  private handleClose(event: CloseEvent): void {
    this.socket = null;

    // 停止心跳和状态监控
    this.stopHeartbeat();
    this.stopStatusMonitor();

    // 如果不是手动断开连接，尝试重连
    if (!this.manualDisconnect && this.config.autoReconnect) {
      this.setStatus(ConnectionStatus.RECONNECTING);
      this.scheduleReconnect();
    } else {
      this.setStatus(ConnectionStatus.DISCONNECTED);
    }

    this.emit('disconnected', event);

    if (this.config.debug) {
      console.log('与服务器的连接已关闭:', event.code, event.reason);
    }
  }

  /**
   * 处理WebSocket错误事件
   * @param event 错误事件
   */
  private handleError(event: Event): void {
    this.setStatus(ConnectionStatus.ERROR);
    this.stats.failedConnections++;

    this.emit('error', event);

    if (this.config.debug) {
      console.error('WebSocket错误:', event);
    }
  }

  /**
   * 设置连接状态
   * @param status 新状态
   */
  private setStatus(status: ConnectionStatus): void {
    if (this.status !== status) {
      this.status = status;
      store.dispatch(setConnectionStatus(status));
      this.emit('statusChange', status);
    }
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    if (this.heartbeatTimerId) {
      clearInterval(this.heartbeatTimerId);
    }

    this.heartbeatTimerId = window.setInterval(() => {
      this.sendHeartbeat();
    }, this.config.heartbeatInterval);
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimerId) {
      clearInterval(this.heartbeatTimerId);
      this.heartbeatTimerId = null;
    }

    if (this.heartbeatTimeoutId) {
      clearTimeout(this.heartbeatTimeoutId);
      this.heartbeatTimeoutId = null;
    }
  }

  /**
   * 发送心跳消息
   */
  private sendHeartbeat(): void {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      return;
    }

    const heartbeatMessage = {
      type: 'heartbeat',
      data: {
        timestamp: Date.now(),
        clientTime: Date.now()
      },
      timestamp: Date.now()
    };

    try {
      this.socket.send(JSON.stringify(heartbeatMessage));

      // 设置心跳超时
      this.heartbeatTimeoutId = window.setTimeout(() => {
        console.warn('心跳超时，尝试重新连接');
        this.reconnect();
      }, this.config.heartbeatTimeout);

      if (this.config.debug) {
        console.log('已发送心跳消息');
      }
    } catch (error) {
      console.error('发送心跳消息时出错:', error);
    }
  }

  /**
   * 处理心跳响应
   * @param message 心跳响应消息
   */
  private handleHeartbeatResponse(message: any): void {
    const now = Date.now();
    const sentTime = message.data?.clientTime || 0;

    if (sentTime > 0) {
      const latency = now - sentTime;

      // 更新延迟统计
      this.stats.lastLatency = latency;
      this.stats.maxLatency = Math.max(this.stats.maxLatency, latency);
      this.stats.minLatency = Math.min(this.stats.minLatency, latency);

      // 更新平均延迟（加权平均）
      if (this.stats.averageLatency === 0) {
        this.stats.averageLatency = latency;
      } else {
        this.stats.averageLatency = this.stats.averageLatency * 0.7 + latency * 0.3;
      }

      if (this.config.debug) {
        console.log(`心跳响应: 延迟 ${latency}ms`);
      }
    }
  }

  /**
   * 启动状态监控
   */
  private startStatusMonitor(): void {
    if (this.statusMonitorTimerId) {
      clearInterval(this.statusMonitorTimerId);
    }

    this.statusMonitorTimerId = window.setInterval(() => {
      this.checkConnectionStatus();
    }, this.config.statusMonitoringInterval);
  }

  /**
   * 停止状态监控
   */
  private stopStatusMonitor(): void {
    if (this.statusMonitorTimerId) {
      clearInterval(this.statusMonitorTimerId);
      this.statusMonitorTimerId = null;
    }
  }

  /**
   * 检查连接状态
   */
  private checkConnectionStatus(): void {
    if (this.status !== ConnectionStatus.CONNECTED || !this.socket) {
      return;
    }

    // 检查最后活动时间
    const inactiveTime = Date.now() - this.stats.lastActivityTime;

    // 如果超过两个心跳间隔没有活动，尝试发送心跳
    if (inactiveTime > this.config.heartbeatInterval * 2) {
      console.warn(`连接不活跃 ${Math.round(inactiveTime / 1000)}秒，发送心跳检测`);
      this.sendHeartbeat();
    }

    // 发出状态监控事件
    this.emit('statusMonitor', {
      status: this.status,
      stats: this.getStats()
    });
  }

  /**
   * 重新连接
   */
  private reconnect(): void {
    if (this.isReconnecting || this.manualDisconnect) {
      return;
    }

    this.isReconnecting = true;

    // 关闭现有连接
    if (this.socket) {
      try {
        this.socket.close();
      } catch (error) {
        console.error('关闭现有连接时出错:', error);
      }

      this.socket = null;
    }

    // 停止心跳和状态监控
    this.stopHeartbeat();
    this.stopStatusMonitor();

    // 设置状态为重连中
    this.setStatus(ConnectionStatus.RECONNECTING);

    // 安排重连
    this.scheduleReconnect();
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimerId) {
      clearTimeout(this.reconnectTimerId);
      this.reconnectTimerId = null;
    }

    // 检查是否超过最大重连尝试次数
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error(`已达到最大重连尝试次数 (${this.config.maxReconnectAttempts})`);
      this.setStatus(ConnectionStatus.ERROR);
      this.isReconnecting = false;
      this.emit('reconnectFailed');
      return;
    }

    // 增加重连尝试次数
    this.reconnectAttempts++;
    this.stats.reconnectAttempts++;

    // 计算下一次重连延迟（指数退避 + 随机抖动）
    const jitter = 1 - this.config.reconnectJitter / 2 + Math.random() * this.config.reconnectJitter;
    this.currentReconnectDelay = Math.min(
      this.currentReconnectDelay * this.config.reconnectBackoffFactor * jitter,
      this.config.maxReconnectDelay
    );

    console.log(`安排重连 #${this.reconnectAttempts} 在 ${Math.round(this.currentReconnectDelay)}ms 后`);

    // 设置重连定时器
    this.reconnectTimerId = window.setTimeout(() => {
      this.reconnectTimerId = null;
      this.isReconnecting = false;
      this.connect();
    }, this.currentReconnectDelay);

    // 发出重连尝试事件
    this.emit('reconnectScheduled', {
      attempt: this.reconnectAttempts,
      delay: this.currentReconnectDelay,
      maxAttempts: this.config.maxReconnectAttempts
    });
  }

  /**
   * 停止重连
   */
  private stopReconnect(): void {
    if (this.reconnectTimerId) {
      clearTimeout(this.reconnectTimerId);
      this.reconnectTimerId = null;
    }

    this.isReconnecting = false;
  }

  /**
   * 将消息添加到队列
   * @param type 消息类型
   * @param data 消息数据
   */
  private queueMessage(type: string, data: any): void {
    // 如果队列已满，移除最旧的消息
    if (this.messageQueue.length >= this.config.maxQueueSize) {
      this.messageQueue.shift();
    }

    // 添加消息到队列
    this.messageQueue.push({ type, data, timestamp: Date.now() });

    if (this.config.debug) {
      console.log(`消息已加入队列 [${type}]，队列长度: ${this.messageQueue.length}`);
    }
  }

  /**
   * 处理消息队列
   */
  private processMessageQueue(): void {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN || this.messageQueue.length === 0) {
      return;
    }

    console.log(`处理消息队列，队列长度: ${this.messageQueue.length}`);

    // 复制队列并清空原队列
    const queue = [...this.messageQueue];
    this.messageQueue = [];

    // 发送队列中的消息
    for (const message of queue) {
      this.send(message.type, message.data);
    }
  }
}
