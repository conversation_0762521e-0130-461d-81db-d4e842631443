/**
 * 混合曲线编辑器组件
 * 用于编辑动画混合曲线
 */
import React, { useState, useEffect, useRef } from 'react';
import { Form, Select, Button, SliderNumber, Row, Col, Tabs} from 'antd';
import { useTranslation } from 'react-i18next';
// 移除引擎直接导入
import { LineChartOutlined, EditOutlined, SaveOutlined, UndoOutlined, RedoOutlined } from '@ant-design/icons';
import './AnimationEditor.less';

// 定义本地的混合曲线类型枚举
enum BlendCurveType {
  LINEAR = 'linear',
  SMOOTH = 'smooth',
  EASE_IN = 'ease_in',
  EASE_OUT = 'ease_out',
  EASE_IN_OUT = 'ease_in_out',
  CUSTOM = 'custom'
}

const { TabPane } = Tabs;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';

/**
 * 混合曲线编辑器属性
 */
interface BlendCurveEditorProps {
  /** 初始曲线类型 */
  initialCurveType?: BlendCurveType;
  /** 曲线类型变更回调 */
  onCurveTypeChange?: (curveType: BlendCurveType) => void;
  /** 自定义曲线变更回调 */
  onCustomCurveChange?: (curve: (t: number) => number) => void;
  /** 预设曲线变更回调 */
  onPresetCurveChange?: (presetName: string) => void;
  /** 贝塞尔曲线变更回调 */
  onBezierCurveChange?: (x1: number, y1: number, x2: number, y2: number) => void;
}

/**
 * 混合曲线编辑器组件
 */
const BlendCurveEditor: React.FC<BlendCurveEditorProps> = ({
  initialCurveType = BlendCurveType.LINEAR,
  onCurveTypeChange,
  onCustomCurveChange,
  onPresetCurveChange,
  onBezierCurveChange
}) => {
  const { t } = useTranslation();

  // 引用
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const bezierCanvasRef = useRef<HTMLCanvasElement>(null);

  // 本地状态
  const [curveType, setCurveType] = useState<BlendCurveType>(initialCurveType);
  const [activeTab, setActiveTab] = useState<string>('standard');
  const [presetCurve, setPresetCurve] = useState<string>('easeInOutQuad');
  const [bezierPoints, setBezierPoints] = useState({ x1: 0.42, y1: 0, x2: 0.58, y2: 1 });
  const [testValue, setTestValue] = useState<number>(0.5);
  const [form] = Form.useForm();
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragPoint, setDragPoint] = useState<string | null>(null);

  // 曲线类型选项
  const curveTypeOptions = [
    { label: t('editor.animation.curve.linear'), value: BlendCurveType.LINEAR },
    { label: t('editor.animation.curve.easeIn'), value: BlendCurveType.EASE_IN },
    { label: t('editor.animation.curve.easeOut'), value: BlendCurveType.EASE_OUT },
    { label: t('editor.animation.curve.easeInOut'), value: BlendCurveType.EASE_IN_OUT },
    { label: t('editor.animation.curve.elastic'), value: BlendCurveType.ELASTIC },
    { label: t('editor.animation.curve.bounce'), value: BlendCurveType.BOUNCE },
    { label: t('editor.animation.curve.sine'), value: BlendCurveType.SINE },
    { label: t('editor.animation.curve.exponential'), value: BlendCurveType.EXPONENTIAL },
    { label: t('editor.animation.curve.circular'), value: BlendCurveType.CIRCULAR },
    { label: t('editor.animation.curve.quadratic'), value: BlendCurveType.QUADRATIC },
    { label: t('editor.animation.curve.cubic'), value: BlendCurveType.CUBIC },
    { label: t('editor.animation.curve.quartic'), value: BlendCurveType.QUARTIC },
    { label: t('editor.animation.curve.quintic'), value: BlendCurveType.QUINTIC },
    { label: t('editor.animation.curve.custom'), value: BlendCurveType.CUSTOM }
  ];

  // 预设曲线选项
  const presetCurveOptions = [
    { label: t('editor.animation.curve.easeInQuad'), value: 'easeInQuad' },
    { label: t('editor.animation.curve.easeOutQuad'), value: 'easeOutQuad' },
    { label: t('editor.animation.curve.easeInOutQuad'), value: 'easeInOutQuad' },
    { label: t('editor.animation.curve.easeInCubic'), value: 'easeInCubic' },
    { label: t('editor.animation.curve.easeOutCubic'), value: 'easeOutCubic' },
    { label: t('editor.animation.curve.easeInOutCubic'), value: 'easeInOutCubic' },
    { label: t('editor.animation.curve.easeInQuart'), value: 'easeInQuart' },
    { label: t('editor.animation.curve.easeOutQuart'), value: 'easeOutQuart' },
    { label: t('editor.animation.curve.easeInOutQuart'), value: 'easeInOutQuart' },
    { label: t('editor.animation.curve.easeInQuint'), value: 'easeInQuint' },
    { label: t('editor.animation.curve.easeOutQuint'), value: 'easeOutQuint' },
    { label: t('editor.animation.curve.easeInOutQuint'), value: 'easeInOutQuint' },
    { label: t('editor.animation.curve.easeInSine'), value: 'easeInSine' },
    { label: t('editor.animation.curve.easeOutSine'), value: 'easeOutSine' },
    { label: t('editor.animation.curve.easeInOutSine'), value: 'easeInOutSine' },
    { label: t('editor.animation.curve.easeInExpo'), value: 'easeInExpo' },
    { label: t('editor.animation.curve.easeOutExpo'), value: 'easeOutExpo' },
    { label: t('editor.animation.curve.easeInOutExpo'), value: 'easeInOutExpo' },
    { label: t('editor.animation.curve.easeInCirc'), value: 'easeInCirc' },
    { label: t('editor.animation.curve.easeOutCirc'), value: 'easeOutCirc' },
    { label: t('editor.animation.curve.easeInOutCirc'), value: 'easeInOutCirc' },
    { label: t('editor.animation.curve.easeInElastic'), value: 'easeInElastic' },
    { label: t('editor.animation.curve.easeOutElastic'), value: 'easeOutElastic' },
    { label: t('editor.animation.curve.easeInOutElastic'), value: 'easeInOutElastic' },
    { label: t('editor.animation.curve.easeInBack'), value: 'easeInBack' },
    { label: t('editor.animation.curve.easeOutBack'), value: 'easeOutBack' },
    { label: t('editor.animation.curve.easeInOutBack'), value: 'easeInOutBack' },
    { label: t('editor.animation.curve.easeInBounce'), value: 'easeInBounce' },
    { label: t('editor.animation.curve.easeOutBounce'), value: 'easeOutBounce' },
    { label: t('editor.animation.curve.easeInOutBounce'), value: 'easeInOutBounce' }
  ];

  // 初始化表单
  useEffect(() => {
    form.setFieldsValue({
      curveType,
      presetCurve,
      x1: bezierPoints.x1,
      y1: bezierPoints.y1,
      x2: bezierPoints.x2,
      y2: bezierPoints.y2
    });
  }, [form, curveType, presetCurve, bezierPoints]);

  // 绘制曲线
  useEffect(() => {
    drawCurve();
    drawBezierCurve();
  }, [curveType, presetCurve, bezierPoints, testValue, activeTab]);

  // 处理曲线类型变更
  const handleCurveTypeChange = (value: BlendCurveType) => {
    setCurveType(value);

    if (onCurveTypeChange) {
      onCurveTypeChange(value);
    }

    // 如果选择了自定义曲线，切换到预设曲线标签页
    if (value === BlendCurveType.CUSTOM) {
      setActiveTab('preset');
    }
  };

  // 处理预设曲线变更
  const handlePresetCurveChange = (value: string) => {
    setPresetCurve(value);

    if (onPresetCurveChange) {
      onPresetCurveChange(value);
    }
  };

  // 处理贝塞尔曲线变更
  const handleBezierPointsChange = (values: any) => {
    const newPoints = {
      x1: values.x1,
      y1: values.y1,
      x2: values.x2,
      y2: values.y2
    };

    setBezierPoints(newPoints);

    if (onBezierCurveChange) {
      onBezierCurveChange(newPoints.x1, newPoints.y1, newPoints.x2, newPoints.y2);
    }
  };

  // 处理贝塞尔预设
  const handleBezierPreset = (x1: number, y1: number, x2: number, y2: number) => {
    const newPoints = { x1, y1, x2, y2 };
    setBezierPoints(newPoints);
    form.setFieldsValue(newPoints);

    if (onBezierCurveChange) {
      onBezierCurveChange(x1, y1, x2, y2);
    }
  };

  // 绘制贝塞尔曲线
  const drawBezierCurve = () => {
    const canvas = bezierCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;
    const padding = 20;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 绘制网格
    ctx.strokeStyle = '#e8e8e8';
    ctx.lineWidth = 0.5;

    // 绘制水平网格线
    for (let i = 0; i <= 10; i++) {
      const y = padding + (height - 2 * padding) * (1 - i / 10);
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }

    // 绘制垂直网格线
    for (let i = 0; i <= 10; i++) {
      const x = padding + (width - 2 * padding) * (i / 10);
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, height - padding);
      ctx.stroke();
    }

    // 绘制坐标轴
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 1;

    // X轴
    ctx.beginPath();
    ctx.moveTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();

    // Y轴
    ctx.beginPath();
    ctx.moveTo(padding, height - padding);
    ctx.lineTo(padding, padding);
    ctx.stroke();

    // 绘制贝塞尔曲线
    ctx.strokeStyle = '#1890ff';
    ctx.lineWidth = 2;
    ctx.beginPath();

    // 起点
    const startX = padding;
    const startY = height - padding;
    ctx.moveTo(startX, startY);

    // 终点
    const endX = width - padding;
    const endY = padding;

    // 控制点
    const cp1X = padding + (width - 2 * padding) * bezierPoints.x1;
    const cp1Y = height - padding - (height - 2 * padding) * bezierPoints.y1;
    const cp2X = padding + (width - 2 * padding) * bezierPoints.x2;
    const cp2Y = height - padding - (height - 2 * padding) * bezierPoints.y2;

    // 绘制贝塞尔曲线
    ctx.bezierCurveTo(cp1X, cp1Y, cp2X, cp2Y, endX, endY);
    ctx.stroke();

    // 绘制控制点线
    ctx.strokeStyle = '#999';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);

    // 起点到控制点1
    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(cp1X, cp1Y);
    ctx.stroke();

    // 终点到控制点2
    ctx.beginPath();
    ctx.moveTo(endX, endY);
    ctx.lineTo(cp2X, cp2Y);
    ctx.stroke();

    ctx.setLineDash([]);

    // 绘制控制点
    const drawControlPoint = (x: number, y: number, label: string, isActive: boolean) => {
      ctx.beginPath();
      ctx.arc(x, y, 6, 0, Math.PI * 2);
      ctx.fillStyle = isActive ? '#f5222d' : '#1890ff';
      ctx.fill();
      ctx.strokeStyle = '#fff';
      ctx.lineWidth = 2;
      ctx.stroke();

      // 绘制标签
      ctx.fillStyle = '#000';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';
      ctx.fillText(label, x, y - 10);
    };

    // 绘制控制点
    drawControlPoint(cp1X, cp1Y, 'P1', dragPoint === 'p1');
    drawControlPoint(cp2X, cp2Y, 'P2', dragPoint === 'p2');
  };

  // 处理贝塞尔画布点击
  const handleBezierCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    // 如果已经在拖动，则不处理点击
    if (isDragging) return;

    const canvas = bezierCanvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 检查是否点击了控制点
    const width = canvas.width;
    const height = canvas.height;
    const padding = 20;

    // 控制点坐标
    const cp1X = padding + (width - 2 * padding) * bezierPoints.x1;
    const cp1Y = height - padding - (height - 2 * padding) * bezierPoints.y1;
    const cp2X = padding + (width - 2 * padding) * bezierPoints.x2;
    const cp2Y = height - padding - (height - 2 * padding) * bezierPoints.y2;

    // 检查是否点击了控制点1
    const distance1 = Math.sqrt(Math.pow(x - cp1X, 2) + Math.pow(y - cp1Y, 2));
    if (distance1 <= 10) {
      setDragPoint('p1');
      return;
    }

    // 检查是否点击了控制点2
    const distance2 = Math.sqrt(Math.pow(x - cp2X, 2) + Math.pow(y - cp2Y, 2));
    if (distance2 <= 10) {
      setDragPoint('p2');
      return;
    }
  };

  // 处理贝塞尔画布鼠标移动
  const handleBezierCanvasMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!dragPoint) return;

    const canvas = bezierCanvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const width = canvas.width;
    const height = canvas.height;
    const padding = 20;

    // 计算新的控制点位置
    let newX = Math.max(0, Math.min(1, (x - padding) / (width - 2 * padding)));
    let newY = Math.max(0, Math.min(1, 1 - (y - padding) / (height - 2 * padding)));

    // 更新控制点
    const newPoints = { ...bezierPoints };
    if (dragPoint === 'p1') {
      newPoints.x1 = newX;
      newPoints.y1 = newY;
    } else if (dragPoint === 'p2') {
      newPoints.x2 = newX;
      newPoints.y2 = newY;
    }

    // 更新状态
    setBezierPoints(newPoints);
    form.setFieldsValue(newPoints);

    // 调用回调
    if (onBezierCurveChange) {
      onBezierCurveChange(newPoints.x1, newPoints.y1, newPoints.x2, newPoints.y2);
    }

    setIsDragging(true);
  };

  // 处理贝塞尔画布鼠标按下
  const handleBezierCanvasMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    handleBezierCanvasClick(e);
  };

  // 处理贝塞尔画布鼠标抬起
  const handleBezierCanvasMouseUp = () => {
    setDragPoint(null);
    setIsDragging(false);
  };

  // 处理贝塞尔画布鼠标离开
  const handleBezierCanvasMouseLeave = () => {
    if (isDragging) {
      setDragPoint(null);
      setIsDragging(false);
    }
  };

  // 绘制曲线
  const drawCurve = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;
    const padding = 20;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 绘制网格
    ctx.strokeStyle = '#e8e8e8';
    ctx.lineWidth = 0.5;

    // 绘制水平网格线
    for (let i = 0; i <= 10; i++) {
      const y = padding + (height - 2 * padding) * (1 - i / 10);
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }

    // 绘制垂直网格线
    for (let i = 0; i <= 10; i++) {
      const x = padding + (width - 2 * padding) * (i / 10);
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, height - padding);
      ctx.stroke();
    }

    // 绘制坐标轴
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 1;

    // X轴
    ctx.beginPath();
    ctx.moveTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();

    // Y轴
    ctx.beginPath();
    ctx.moveTo(padding, height - padding);
    ctx.lineTo(padding, padding);
    ctx.stroke();

    // 绘制曲线
    ctx.strokeStyle = '#1890ff';
    ctx.lineWidth = 2;
    ctx.beginPath();

    // 根据曲线类型绘制不同的曲线
    for (let i = 0; i <= 100; i++) {
      const t = i / 100;
      let y = t;

      // 根据曲线类型计算y值
      switch (curveType) {
        case BlendCurveType.LINEAR:
          y = t;
          break;
        case BlendCurveType.EASE_IN:
          y = t * t;
          break;
        case BlendCurveType.EASE_OUT:
          y = t * (2 - t);
          break;
        case BlendCurveType.EASE_IN_OUT:
          y = t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
          break;
        case BlendCurveType.ELASTIC:
          const p = 0.3;
          y = Math.pow(2, -10 * t) * Math.sin((t - p / 4) * (2 * Math.PI) / p) + 1;
          break;
        case BlendCurveType.BOUNCE:
          if (t < 1 / 2.75) {
            y = 7.5625 * t * t;
          } else if (t < 2 / 2.75) {
            const t2 = t - 1.5 / 2.75;
            y = 7.5625 * t2 * t2 + 0.75;
          } else if (t < 2.5 / 2.75) {
            const t2 = t - 2.25 / 2.75;
            y = 7.5625 * t2 * t2 + 0.9375;
          } else {
            const t2 = t - 2.625 / 2.75;
            y = 7.5625 * t2 * t2 + 0.984375;
          }
          break;
        case BlendCurveType.SINE:
          y = Math.sin(t * Math.PI / 2);
          break;
        case BlendCurveType.EXPONENTIAL:
          y = t === 0 ? 0 : Math.pow(2, 10 * (t - 1));
          break;
        case BlendCurveType.CIRCULAR:
          y = 1 - Math.sqrt(1 - t * t);
          break;
        case BlendCurveType.QUADRATIC:
          y = t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
          break;
        case BlendCurveType.CUBIC:
          y = t * t * t;
          break;
        case BlendCurveType.QUARTIC:
          y = t * t * t * t;
          break;
        case BlendCurveType.QUINTIC:
          y = t * t * t * t * t;
          break;
        case BlendCurveType.CUSTOM:
          // 在这里可以添加自定义曲线的计算
          break;
      }

      // 将y值映射到画布坐标
      const x = padding + (width - 2 * padding) * t;
      const yPos = padding + (height - 2 * padding) * (1 - y);

      if (i === 0) {
        ctx.moveTo(x, yPos);
      } else {
        ctx.lineTo(x, yPos);
      }
    }

    ctx.stroke();

    // 绘制测试点
    const testX = padding + (width - 2 * padding) * testValue;
    let testY = testValue;

    // 根据曲线类型计算测试点的y值
    switch (curveType) {
      case BlendCurveType.LINEAR:
        testY = testValue;
        break;
      case BlendCurveType.EASE_IN:
        testY = testValue * testValue;
        break;
      case BlendCurveType.EASE_OUT:
        testY = testValue * (2 - testValue);
        break;
      case BlendCurveType.EASE_IN_OUT:
        testY = testValue < 0.5 ? 2 * testValue * testValue : -1 + (4 - 2 * testValue) * testValue;
        break;
      case BlendCurveType.ELASTIC:
        const p = 0.3;
        testY = Math.pow(2, -10 * testValue) * Math.sin((testValue - p / 4) * (2 * Math.PI) / p) + 1;
        break;
      case BlendCurveType.BOUNCE:
        if (testValue < 1 / 2.75) {
          testY = 7.5625 * testValue * testValue;
        } else if (testValue < 2 / 2.75) {
          const t2 = testValue - 1.5 / 2.75;
          testY = 7.5625 * t2 * t2 + 0.75;
        } else if (testValue < 2.5 / 2.75) {
          const t2 = testValue - 2.25 / 2.75;
          testY = 7.5625 * t2 * t2 + 0.9375;
        } else {
          const t2 = testValue - 2.625 / 2.75;
          testY = 7.5625 * t2 * t2 + 0.984375;
        }
        break;
      case BlendCurveType.SINE:
        testY = Math.sin(testValue * Math.PI / 2);
        break;
      case BlendCurveType.EXPONENTIAL:
        testY = testValue === 0 ? 0 : Math.pow(2, 10 * (testValue - 1));
        break;
      case BlendCurveType.CIRCULAR:
        testY = 1 - Math.sqrt(1 - testValue * testValue);
        break;
      case BlendCurveType.QUADRATIC:
        testY = testValue < 0.5 ? 2 * testValue * testValue : -1 + (4 - 2 * testValue) * testValue;
        break;
      case BlendCurveType.CUBIC:
        testY = testValue * testValue * testValue;
        break;
      case BlendCurveType.QUARTIC:
        testY = testValue * testValue * testValue * testValue;
        break;
      case BlendCurveType.QUINTIC:
        testY = testValue * testValue * testValue * testValue * testValue;
        break;
      case BlendCurveType.CUSTOM:
        // 使用自定义曲线
        if (activeTab === 'bezier' && bezierPoints) {
          // 使用贝塞尔曲线
          const { x1, y1, x2, y2 } = bezierPoints;
          // 简化的贝塞尔曲线计算
          const t = testValue;
          const cx = 3 * x1;
          const bx = 3 * (x2 - x1) - cx;
          const ax = 1 - cx - bx;
          const cy = 3 * y1;
          const by = 3 * (y2 - y1) - cy;
          const ay = 1 - cy - by;
          testY = ay * t * t * t + by * t * t + cy * t;
        }
        break;
    }

    // 将y值映射到画布坐标
    const testYPos = padding + (height - 2 * padding) * (1 - testY);

    // 绘制测试点
    ctx.fillStyle = '#f5222d';
    ctx.beginPath();
    ctx.arc(testX, testYPos, 5, 0, 2 * Math.PI);
    ctx.fill();

    // 绘制测试点的坐标值
    ctx.fillStyle = '#000000';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'bottom';
    ctx.fillText(`(${testValue.toFixed(2)}, ${testY.toFixed(2)})`, testX + 10, testYPos - 5);
  };

  return (
    <div className="blend-curve-editor">
      <Form form={form} layout="vertical">
        <Form.Item name="curveType" label={t('editor.animation.curve.type')}>
          <Select
            options={curveTypeOptions}
            onChange={handleCurveTypeChange}
          />
        </Form.Item>

        <div className="curve-preview">
          <canvas ref={canvasRef} width={400} height={300} />

          <div className="test-controls">
            <span>{t('editor.animation.curve.testValue')}:</span>
            <Slider
              value={testValue}
              onChange={setTestValue}
              min={0}
              max={1}
              step={0.01}
              style={{ width: 200 }}
            />
            <InputNumber
              value={testValue}
              onChange={setTestValue}
              min={0}
              max={1}
              step={0.01}
              style={{ width: 80 }}
            />
          </div>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t('editor.animation.curve.standard')} key="standard">
            <p>{t('editor.animation.curve.standardDescription')}</p>
          </TabPane>

          <TabPane tab={t('editor.animation.curve.preset')} key="preset">
            <Form.Item name="presetCurve" label={t('editor.animation.curve.preset')}>
              <Select
                options={presetCurveOptions}
                onChange={handlePresetCurveChange}
              />
            </Form.Item>
          </TabPane>

          <TabPane tab={t('editor.animation.curve.bezier')} key="bezier">
            <div className="bezier-editor">
              <div className="bezier-canvas-container">
                <canvas
                  ref={bezierCanvasRef}
                  width={400}
                  height={300}
                  onClick={handleBezierCanvasClick}
                  onMouseMove={handleBezierCanvasMouseMove}
                  onMouseDown={handleBezierCanvasMouseDown}
                  onMouseUp={handleBezierCanvasMouseUp}
                  onMouseLeave={handleBezierCanvasMouseLeave}
                />
                <div className="bezier-instructions">
                  {t('editor.animation.curve.bezierInstructions')}
                </div>
              </div>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="x1" label="X1">
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.01}
                      style={{ width: '100%' }}
                      onChange={() => form.validateFields().then(handleBezierPointsChange)}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="y1" label="Y1">
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.01}
                      style={{ width: '100%' }}
                      onChange={() => form.validateFields().then(handleBezierPointsChange)}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="x2" label="X2">
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.01}
                      style={{ width: '100%' }}
                      onChange={() => form.validateFields().then(handleBezierPointsChange)}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="y2" label="Y2">
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.01}
                      style={{ width: '100%' }}
                      onChange={() => form.validateFields().then(handleBezierPointsChange)}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <div className="bezier-presets">
                <Button size="small" onClick={() => handleBezierPreset(0.42, 0, 0.58, 1)}>
                  {t('editor.animation.curve.easeInOut')}
                </Button>
                <Button size="small" onClick={() => handleBezierPreset(0.25, 0.1, 0.25, 1)}>
                  {t('editor.animation.curve.easeOutQuad')}
                </Button>
                <Button size="small" onClick={() => handleBezierPreset(0.55, 0.055, 0.675, 0.19)}>
                  {t('editor.animation.curve.easeInCubic')}
                </Button>
                <Button size="small" onClick={() => handleBezierPreset(0.215, 0.61, 0.355, 1)}>
                  {t('editor.animation.curve.easeOutCubic')}
                </Button>
                <Button size="small" onClick={() => handleBezierPreset(0.645, 0.045, 0.355, 1)}>
                  {t('editor.animation.curve.easeInOutCubic')}
                </Button>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </Form>
    </div>
  );
};

export default BlendCurveEditor;
