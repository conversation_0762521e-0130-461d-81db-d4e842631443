/**
 * 用户测试服务
 * 提供用户测试会话管理、反馈收集和分析功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { message } from 'antd';
import { store } from '../store';
import {
  startTestSession,
  endTestSession,
  addFeedback,
  setCurrentTask,
  completeTask,
  setTestingEnabled
} from '../store/testing/userTestingSlice';
import { collaborationService } from './CollaborationService';

/**
 * 测试任务接口
 */
export interface TestTask {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  startTime?: number;
  endTime?: number;
  timeSpent?: number;
  progress?: number; // 任务进度百分比 (0-100)
  dependencies?: string[]; // 依赖的任务ID列表
  subtasks?: TestTask[]; // 子任务列表
  weight?: number; // 任务权重，用于计算整体进度
  tags?: string[]; // 任务标签
}

/**
 * 用户反馈接口
 */
export interface UserFeedback {
  id: string;
  userId: string;
  userName: string;
  sessionId: string;
  taskId?: string;
  type: 'bug' | 'suggestion' | 'rating' | 'comment';
  content: string;
  rating?: number;
  timestamp: number;
  metadata?: {
    browser?: string;
    os?: string;
    screenSize?: string;
    location?: string;
    [key: string]: any;
  };
  screenshots?: string[]; // Base64编码的截图
  relatedActions?: any[]; // 相关的用户操作
}

/**
 * 测试会话接口
 */
export interface TestSession {
  id: string;
  userId: string;
  userName: string;
  startTime: number;
  endTime?: number;
  tasks: TestTask[];
  feedback: UserFeedback[];
  metadata?: {
    projectId?: string;
    sceneId?: string;
    browser?: string;
    os?: string;
    screenSize?: string;
    [key: string]: any;
  };
  recordings?: any[]; // 用户操作记录
  reports?: any[]; // 生成的报告
}

/**
 * 用户测试服务配置
 */
export interface UserTestingServiceConfig {
  autoSaveInterval?: number; // 自动保存间隔（毫秒）
  recordingInterval?: number; // 录制间隔（毫秒）
  maxSessionsHistory?: number; // 最大会话历史记录数
  enableAutoReporting?: boolean; // 是否启用自动报告生成
  enableBehaviorAnalysis?: boolean; // 是否启用行为分析
}

/**
 * 用户测试服务类
 */
class UserTestingService extends EventEmitter {
  private currentSession: TestSession | null = null;
  private sessionsHistory: TestSession[] = [];
  private enabled: boolean = false;
  private recordingEnabled: boolean = false;
  private recordedActions: any[] = [];
  private recordingInterval: number | null = null;
  private autoSaveInterval: number | null = null;
  private config: Required<UserTestingServiceConfig>;

  constructor(config: UserTestingServiceConfig = {}) {
    super();

    // 默认配置
    this.config = {
      autoSaveInterval: config.autoSaveInterval ?? 60000, // 默认1分钟
      recordingInterval: config.recordingInterval ?? 1000, // 默认1秒
      maxSessionsHistory: config.maxSessionsHistory ?? 10,
      enableAutoReporting: config.enableAutoReporting ?? true,
      enableBehaviorAnalysis: config.enableBehaviorAnalysis ?? true
    };

    // 加载历史会话
    this.loadSessionsHistory();

    // 设置自动保存
    this.setupAutoSave();
  }

  /**
   * 初始化服务
   * @param _userId 用户ID（暂未使用）
   * @param _userName 用户名（暂未使用）
   */
  public initialize(_userId: string, _userName: string): void {
    // 设置基本信息
    this.enabled = false;
    this.recordingEnabled = false;

    // 更新Redux状态
    store.dispatch(setTestingEnabled(false));

    // 加载历史会话
    this.loadSessionsHistory();
  }

  /**
   * 启用/禁用用户测试
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    store.dispatch(setTestingEnabled(enabled));

    if (enabled) {
      message.info('用户测试模式已启用');
    } else {
      if (this.currentSession) {
        this.endSession();
      }
      message.info('用户测试模式已禁用');
    }
  }

  /**
   * 启动测试会话
   * @param tasks 测试任务列表
   * @param metadata 元数据
   * @returns 会话ID
   */
  public startSession(tasks: Omit<TestTask, 'completed' | 'startTime' | 'endTime' | 'timeSpent' | 'progress'>[], metadata?: any): string {
    if (!this.enabled) {
      throw new Error('用户测试未启用');
    }

    // 如果已有会话，先结束
    if (this.currentSession) {
      this.endSession();
    }

    // 创建会话ID
    const sessionId = `session_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // 格式化任务
    const formattedTasks: TestTask[] = tasks.map(task => ({
      ...task,
      completed: false,
      progress: 0,
      weight: task.weight || 1,
      subtasks: task.subtasks ? task.subtasks.map(subtask => ({
        ...subtask,
        completed: false,
        progress: 0,
        weight: subtask.weight || 1
      })) : undefined
    }));

    // 获取设备信息
    const deviceInfo = {
      browser: this.getBrowserInfo(),
      os: this.getOSInfo(),
      screenSize: this.getScreenSize()
    };

    // 创建会话
    this.currentSession = {
      id: sessionId,
      userId: collaborationService.getUserId(),
      userName: collaborationService.getUserName(),
      startTime: Date.now(),
      tasks: formattedTasks,
      feedback: [],
      metadata: {
        ...deviceInfo,
        ...metadata
      },
      recordings: []
    };

    // 更新Redux状态
    store.dispatch(startTestSession({
      sessionId,
      tasks: formattedTasks
    }));

    // 添加到历史记录
    this.addSessionToHistory(this.currentSession);

    // 如果启用了录制，开始录制
    if (this.recordingEnabled) {
      this.startRecording();
    }

    // 发出事件
    this.emit('sessionStarted', this.currentSession);

    return sessionId;
  }

  /**
   * 结束测试会话
   * @param generateReport 是否生成报告
   * @returns 会话数据
   */
  public endSession(generateReport: boolean = true): TestSession | null {
    if (!this.currentSession) {
      return null;
    }

    // 更新会话结束时间
    this.currentSession.endTime = Date.now();

    // 如果有录制，停止录制
    if (this.recordingEnabled && this.recordingInterval !== null) {
      this.stopRecording();
    }

    // 保存会话数据
    const sessionData = { ...this.currentSession };

    // 更新历史记录中的会话
    this.updateSessionInHistory(sessionData);

    // 如果启用了自动报告生成，生成报告
    if (generateReport && this.config.enableAutoReporting) {
      this.generateSessionReport(sessionData);
    }

    // 更新Redux状态
    store.dispatch(endTestSession());

    // 发出事件
    this.emit('sessionEnded', sessionData);

    // 清除当前会话
    this.currentSession = null;

    return sessionData;
  }

  /**
   * 设置当前任务
   * @param taskId 任务ID
   */
  public setCurrentTask(taskId: string): void {
    if (!this.currentSession) {
      throw new Error('没有活动的测试会话');
    }

    // 查找任务
    const task = this.currentSession.tasks.find(t => t.id === taskId);

    if (!task) {
      throw new Error(`找不到任务: ${taskId}`);
    }

    // 设置任务开始时间
    if (!task.startTime) {
      task.startTime = Date.now();
    }

    // 更新Redux状态
    store.dispatch(setCurrentTask(taskId));

    // 发出事件
    this.emit('taskStarted', task);
  }

  /**
   * 完成当前任务
   * @param taskId 任务ID
   */
  public completeTask(taskId: string): void {
    if (!this.currentSession) {
      throw new Error('没有活动的测试会话');
    }

    // 查找任务
    const task = this.currentSession.tasks.find(t => t.id === taskId);

    if (!task) {
      throw new Error(`找不到任务: ${taskId}`);
    }

    // 设置任务完成
    task.completed = true;
    task.endTime = Date.now();
    task.progress = 100;

    // 计算花费时间
    if (task.startTime) {
      task.timeSpent = task.endTime - task.startTime;
    }

    // 更新Redux状态
    store.dispatch(completeTask(taskId));

    // 更新历史记录中的会话
    if (this.currentSession) {
      this.updateSessionInHistory(this.currentSession);
    }

    // 发出事件
    this.emit('taskCompleted', task);

    // 检查是否所有任务都已完成
    const allTasksCompleted = this.currentSession.tasks.every(t => t.completed);
    if (allTasksCompleted) {
      this.emit('allTasksCompleted', this.currentSession);
    }
  }

  /**
   * 更新任务进度
   * @param taskId 任务ID
   * @param progress 进度百分比 (0-100)
   */
  public updateTaskProgress(taskId: string, progress: number): void {
    if (!this.currentSession) {
      throw new Error('没有活动的测试会话');
    }

    // 查找任务
    const task = this.currentSession.tasks.find(t => t.id === taskId);

    if (!task) {
      throw new Error(`找不到任务: ${taskId}`);
    }

    // 限制进度范围
    progress = Math.max(0, Math.min(100, progress));

    // 设置任务进度
    task.progress = progress;

    // 如果进度为100%，自动完成任务
    if (progress === 100 && !task.completed) {
      this.completeTask(taskId);
      return;
    }

    // 注意：这里可以添加Redux状态更新，如果需要的话
    // store.dispatch(updateTaskProgress({ taskId, progress }));

    // 更新历史记录中的会话
    this.updateSessionInHistory(this.currentSession);

    // 发出事件
    this.emit('taskProgressUpdated', { task, progress });
  }

  /**
   * 添加用户反馈
   * @param feedback 反馈数据
   * @param screenshots 截图列表（Base64编码）
   * @returns 反馈ID
   */
  public addFeedback(
    feedback: Omit<UserFeedback, 'id' | 'userId' | 'userName' | 'sessionId' | 'timestamp' | 'screenshots' | 'relatedActions'>,
    screenshots: string[] = []
  ): string {
    if (!this.currentSession) {
      throw new Error('没有活动的测试会话');
    }

    // 创建反馈ID
    const feedbackId = `feedback_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // 获取最近的用户操作（如果有录制）
    const recentActions = this.recordedActions.slice(-10); // 最近10个操作

    // 创建完整反馈对象
    const fullFeedback: UserFeedback = {
      id: feedbackId,
      userId: this.currentSession.userId,
      userName: this.currentSession.userName,
      sessionId: this.currentSession.id,
      timestamp: Date.now(),
      screenshots,
      relatedActions: recentActions,
      ...feedback
    };

    // 添加到会话
    this.currentSession.feedback.push(fullFeedback);

    // 更新Redux状态
    store.dispatch(addFeedback(fullFeedback));

    // 更新历史记录中的会话
    this.updateSessionInHistory(this.currentSession);

    // 发出事件
    this.emit('feedbackAdded', fullFeedback);

    return feedbackId;
  }

  /**
   * 捕获屏幕截图
   * @returns Promise<string> Base64编码的截图
   */
  public async captureScreenshot(): Promise<string> {
    try {
      // 使用html2canvas库捕获屏幕截图
      // 注意：这需要安装html2canvas库
      // npm install html2canvas

      // @ts-ignore - 假设已经导入了html2canvas
      if (typeof html2canvas === 'undefined') {
        throw new Error('html2canvas库未加载');
      }

      // @ts-ignore
      const canvas = await html2canvas(document.body);
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.error('捕获屏幕截图失败:', error);
      return '';
    }
  }

  /**
   * 启用/禁用用户操作录制
   * @param enabled 是否启用
   */
  public setRecordingEnabled(enabled: boolean): void {
    this.recordingEnabled = enabled;

    if (enabled && this.currentSession) {
      this.startRecording();
    } else if (!enabled && this.recordingInterval !== null) {
      this.stopRecording();
    }
  }

  /**
   * 获取当前会话
   * @returns 当前会话
   */
  public getCurrentSession(): TestSession | null {
    return this.currentSession;
  }

  /**
   * 获取录制的操作
   * @returns 录制的操作
   */
  public getRecordedActions(): any[] {
    return [...this.recordedActions];
  }

  /**
   * 开始录制
   * @private
   */
  private startRecording(): void {
    // 清空录制数据
    this.recordedActions = [];

    // 设置录制间隔
    this.recordingInterval = window.setInterval(() => {
      this.recordUserAction();
    }, 1000); // 每秒记录一次
  }

  /**
   * 停止录制
   * @private
   */
  private stopRecording(): void {
    if (this.recordingInterval !== null) {
      clearInterval(this.recordingInterval);
      this.recordingInterval = null;
    }
  }

  /**
   * 记录用户操作
   * @private
   */
  private recordUserAction(): void {
    // 获取鼠标位置（使用全局鼠标位置跟踪）
    const mouseX = 0; // 这里可以通过事件监听器获取实际鼠标位置
    const mouseY = 0; // 这里可以通过事件监听器获取实际鼠标位置

    // 创建操作记录
    const action = {
      type: 'mouse_position',
      x: mouseX,
      y: mouseY,
      timestamp: Date.now(),
      target: document.activeElement ? {
        tagName: document.activeElement.tagName,
        id: (document.activeElement as HTMLElement).id || '',
        className: (document.activeElement as HTMLElement).className || ''
      } : null
    };

    // 添加到录制数据
    this.recordedActions.push(action);

    // 如果当前会话存在，添加到会话的录制数据中
    if (this.currentSession && this.currentSession.recordings) {
      this.currentSession.recordings.push(action);
    }
  }

  /**
   * 生成会话报告
   * @param session 测试会话
   * @returns 报告ID
   */
  public generateSessionReport(session: TestSession): string {
    try {
      // 导入测试报告生成器
      // 注意：这里假设已经导入了TestReportGenerator
      // @ts-ignore
      const reportGenerator = new TestReportGenerator();

      // 生成报告
      // @ts-ignore
      const report = reportGenerator.generateReport(session, session.recordings || []);

      // 添加报告到会话
      if (!session.reports) {
        session.reports = [];
      }
      session.reports.push(report);

      // 注意：这里可以添加Redux状态更新，如果需要的话
      // store.dispatch(addTestReport({
      //   reportId: report.id,
      //   report
      // }));

      // 更新历史记录中的会话
      this.updateSessionInHistory(session);

      // 发出事件
      this.emit('reportGenerated', report);

      return report.id;
    } catch (error) {
      console.error('生成报告失败:', error);
      return '';
    }
  }

  /**
   * 加载历史会话
   * @private
   */
  private loadSessionsHistory(): void {
    try {
      // 从localStorage加载历史会话
      const historyJson = localStorage.getItem('userTestingSessions');
      if (historyJson) {
        this.sessionsHistory = JSON.parse(historyJson);
      }
    } catch (error) {
      console.error('加载历史会话失败:', error);
      this.sessionsHistory = [];
    }
  }

  /**
   * 保存历史会话
   * @private
   */
  private saveSessionsHistory(): void {
    try {
      // 保存历史会话到localStorage
      localStorage.setItem('userTestingSessions', JSON.stringify(this.sessionsHistory));
    } catch (error) {
      console.error('保存历史会话失败:', error);
    }
  }

  /**
   * 添加会话到历史记录
   * @param session 测试会话
   * @private
   */
  private addSessionToHistory(session: TestSession): void {
    // 检查是否已存在
    const existingIndex = this.sessionsHistory.findIndex(s => s.id === session.id);

    if (existingIndex !== -1) {
      // 更新现有会话
      this.sessionsHistory[existingIndex] = session;
    } else {
      // 添加新会话
      this.sessionsHistory.unshift(session);

      // 限制历史记录大小
      if (this.sessionsHistory.length > this.config.maxSessionsHistory) {
        this.sessionsHistory.pop();
      }
    }

    // 保存历史记录
    this.saveSessionsHistory();

    // 注意：这里可以添加Redux状态更新，如果需要的话
    // store.dispatch(addTestSession(session.id));
  }

  /**
   * 更新历史记录中的会话
   * @param session 测试会话
   * @private
   */
  private updateSessionInHistory(session: TestSession): void {
    // 查找会话
    const existingIndex = this.sessionsHistory.findIndex(s => s.id === session.id);

    if (existingIndex !== -1) {
      // 更新会话
      this.sessionsHistory[existingIndex] = session;

      // 保存历史记录
      this.saveSessionsHistory();
    } else {
      // 如果不存在，添加到历史记录
      this.addSessionToHistory(session);
    }
  }

  /**
   * 设置自动保存
   * @private
   */
  private setupAutoSave(): void {
    // 清除现有的自动保存定时器
    if (this.autoSaveInterval !== null) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }

    // 设置新的自动保存定时器
    this.autoSaveInterval = window.setInterval(() => {
      if (this.currentSession) {
        this.updateSessionInHistory(this.currentSession);
      }
    }, this.config.autoSaveInterval);
  }

  /**
   * 获取浏览器信息
   * @returns 浏览器名称和版本
   * @private
   */
  private getBrowserInfo(): string {
    const userAgent = navigator.userAgent;
    let browserName = '';
    let browserVersion = '';

    // 检测常见浏览器
    if (userAgent.indexOf('Firefox') > -1) {
      browserName = 'Firefox';
      browserVersion = userAgent.match(/Firefox\/([\d.]+)/)![1];
    } else if (userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Edge') === -1 && userAgent.indexOf('Edg') === -1) {
      browserName = 'Chrome';
      browserVersion = userAgent.match(/Chrome\/([\d.]+)/)![1];
    } else if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1) {
      browserName = 'Safari';
      browserVersion = userAgent.match(/Version\/([\d.]+)/)![1];
    } else if (userAgent.indexOf('Edge') > -1) {
      browserName = 'Edge (Legacy)';
      browserVersion = userAgent.match(/Edge\/([\d.]+)/)![1];
    } else if (userAgent.indexOf('Edg') > -1) {
      browserName = 'Edge (Chromium)';
      browserVersion = userAgent.match(/Edg\/([\d.]+)/)![1];
    } else if (userAgent.indexOf('MSIE') > -1 || userAgent.indexOf('Trident') > -1) {
      browserName = 'Internet Explorer';
      browserVersion = userAgent.match(/(?:MSIE |rv:)([\d.]+)/)![1];
    } else {
      browserName = '未知浏览器';
      browserVersion = '未知版本';
    }

    return `${browserName} ${browserVersion}`;
  }

  /**
   * 获取操作系统信息
   * @returns 操作系统名称和版本
   * @private
   */
  private getOSInfo(): string {
    const userAgent = navigator.userAgent;
    let osName = '';
    let osVersion = '';

    // 检测常见操作系统
    if (userAgent.indexOf('Windows NT') > -1) {
      osName = 'Windows';
      const ntVersion = userAgent.match(/Windows NT ([\d.]+)/)![1];

      // 转换NT版本到Windows版本
      switch (ntVersion) {
        case '10.0':
          osVersion = '10/11';
          break;
        case '6.3':
          osVersion = '8.1';
          break;
        case '6.2':
          osVersion = '8';
          break;
        case '6.1':
          osVersion = '7';
          break;
        case '6.0':
          osVersion = 'Vista';
          break;
        case '5.2':
        case '5.1':
          osVersion = 'XP';
          break;
        default:
          osVersion = ntVersion;
      }
    } else if (userAgent.indexOf('Mac OS X') > -1) {
      osName = 'macOS';
      osVersion = userAgent.match(/Mac OS X ([0-9_\.]+)/)![1].replace(/_/g, '.');
    } else if (userAgent.indexOf('Android') > -1) {
      osName = 'Android';
      osVersion = userAgent.match(/Android ([\d.]+)/)![1];
    } else if (userAgent.indexOf('iOS') > -1 || userAgent.indexOf('iPhone') > -1 || userAgent.indexOf('iPad') > -1) {
      osName = 'iOS';
      osVersion = userAgent.match(/OS ([\d_]+)/)![1].replace(/_/g, '.');
    } else if (userAgent.indexOf('Linux') > -1) {
      osName = 'Linux';
      osVersion = '';
    } else {
      osName = '未知操作系统';
      osVersion = '';
    }

    return osVersion ? `${osName} ${osVersion}` : osName;
  }

  /**
   * 获取屏幕尺寸信息
   * @returns 屏幕尺寸信息
   * @private
   */
  private getScreenSize(): string {
    const width = window.screen.width;
    const height = window.screen.height;
    const pixelRatio = window.devicePixelRatio || 1;

    return `${width}x${height} (${pixelRatio}x)`;
  }
}

// 创建单例实例
export const userTestingService = new UserTestingService();
