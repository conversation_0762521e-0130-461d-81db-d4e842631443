/**
 * 场景优化面板组件
 * 用于分析场景并提供优化建议
 */
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, List, Typography, Space, Button, Tag, Collapse, Progress, Alert, Spin, Tooltip, Checkbox } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  ThunderboltOutlined,
  BulbOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  RightOutlined,
  ToolOutlined,
  QuestionCircleOutlined,
  LineChartOutlined,
  InfoCircleOutlined} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { SceneOptimizer, OptimizationSuggestion, OptimizationSeverity, OptimizationType, SceneOptimizerEventType } from 'dl-engine-core/debug/SceneOptimizer';
import { Scene } from 'dl-engine-core/scene/Scene';
import './OptimizerPanel.less';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface OptimizerPanelProps {
  className?: string;
}

/**
 * 场景优化面板组件
 */
const OptimizerPanel: React.FC<OptimizerPanelProps> = ({ className }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取当前场景
  const activeScene = useSelector((state: RootState) => state.scene.activeScene);

  // 状态
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [isOptimizing, setIsOptimizing] = useState<boolean>(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [optimizationProgress, setOptimizationProgress] = useState<number>(0);
  const [selectedSuggestions, setSelectedSuggestions] = useState<string[]>([]);
  const [expandedPanels, setExpandedPanels] = useState<string[]>([]);

  // 初始化
  useEffect(() => {
    // 如果有活跃场景，自动分析
    if (activeScene) {
      analyzeScene();
    }

    // 添加事件监听器
    const optimizer = SceneOptimizer.getInstance();

    const handleOptimizationProgress = (data: any) => {
      setOptimizationProgress(data.progress * 100);
    };

    const handleOptimizationComplete = () => {
      setIsOptimizing(false);
      setOptimizationProgress(100);

      // 重新分析场景
      setTimeout(() => {
        analyzeScene();
      }, 500);
    };

    optimizer.on(SceneOptimizerEventType.OPTIMIZATION_PROGRESS, handleOptimizationProgress);
    optimizer.on(SceneOptimizerEventType.OPTIMIZATION_COMPLETE, handleOptimizationComplete);

    return () => {
      // 移除事件监听器
      optimizer.off(SceneOptimizerEventType.OPTIMIZATION_PROGRESS, handleOptimizationProgress);
      optimizer.off(SceneOptimizerEventType.OPTIMIZATION_COMPLETE, handleOptimizationComplete);
    };
  }, [activeScene]);

  // 分析场景
  const analyzeScene = async () => {
    if (!activeScene) {
      return;
    }

    setIsAnalyzing(true);

    try {
      // 获取场景优化器实例
      const optimizer = SceneOptimizer.getInstance();

      // 分析场景
      const result = await optimizer.analyzeScene(activeScene as Scene);

      // 更新状态
      setAnalysisResult(result);

      // 默认选择所有建议
      setSelectedSuggestions(result.suggestions.map((s: OptimizationSuggestion) => s.type));
    } catch (error) {
      console.error('场景分析失败:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 优化场景
  const optimizeScene = async () => {
    if (!activeScene || !analysisResult) {
      return;
    }

    setIsOptimizing(true);
    setOptimizationProgress(0);

    try {
      // 获取场景优化器实例
      const optimizer = SceneOptimizer.getInstance();

      // 优化场景
      await optimizer.optimizeScene(activeScene as Scene);
    } catch (error) {
      console.error('场景优化失败:', error);
      setIsOptimizing(false);
    }
  };

  // 优化选定项
  const optimizeSelected = async () => {
    if (!activeScene || !analysisResult || selectedSuggestions.length === 0) {
      return;
    }

    setIsOptimizing(true);
    setOptimizationProgress(0);

    try {
      // 获取选定的建议
      const suggestions = analysisResult.suggestions.filter(
        (s: OptimizationSuggestion) => selectedSuggestions.includes(s.type)
      );

      // 执行优化
      let progress = 0;
      const totalSuggestions = suggestions.length;

      for (const suggestion of suggestions) {
        if (suggestion.autoOptimize) {
          await suggestion.autoOptimize();
          progress++;
          setOptimizationProgress((progress / totalSuggestions) * 100);
        }
      }

      // 重新分析场景
      setTimeout(() => {
        analyzeScene();
      }, 500);
    } catch (error) {
      console.error('场景优化失败:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: 'good' | 'warning' | 'error') => {
    switch (status) {
      case 'good':
        return <CheckCircleOutlined className="optimization-good" />;
      case 'warning':
        return <WarningOutlined className="optimization-warning" />;
      case 'error':
        return <CloseCircleOutlined className="optimization-error" />;
      default:
        return null;
    }
  };

  // 获取严重程度标签
  const getSeverityTag = (severity: OptimizationSeverity) => {
    switch (severity) {
      case OptimizationSeverity.HIGH:
        return <Tag color="error">{t('debug.optimization.severityHigh')}</Tag>;
      case OptimizationSeverity.MEDIUM:
        return <Tag color="warning">{t('debug.optimization.severityMedium')}</Tag>;
      case OptimizationSeverity.LOW:
        return <Tag color="success">{t('debug.optimization.severityLow')}</Tag>;
      default:
        return null;
    }
  };

  // 获取分数状态
  const getScoreStatus = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'normal';
    return 'exception';
  };

  // 处理建议选择变化
  const handleSuggestionSelect = (type: string) => {
    if (selectedSuggestions.includes(type)) {
      setSelectedSuggestions(selectedSuggestions.filter(t => t !== type));
    } else {
      setSelectedSuggestions([...selectedSuggestions, type]);
    }
  };

  // 处理面板展开变化
  const handlePanelChange = (keys: string | string[]) => {
    setExpandedPanels(typeof keys === 'string' ? [keys] : keys);
  };

  // 渲染优化建议
  const renderSuggestions = () => {
    if (!analysisResult || !analysisResult.suggestions || analysisResult.suggestions.length === 0) {
      return (
        <Alert
          message={t('debug.optimization.noSuggestions')}
          description={t('debug.optimization.noSuggestionsDesc')}
          type="success"
          showIcon
        />
      );
    }

    return (
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => <RightOutlined rotate={isActive ? 90 : 0} />}
        activeKey={expandedPanels}
        onChange={handlePanelChange}
      >
        {analysisResult.suggestions.map((item: OptimizationSuggestion) => (
          <Panel
            key={item.type}
            header={
              <div className="optimization-item-header">
                <Checkbox
                  checked={selectedSuggestions.includes(item.type)}
                  onChange={() => handleSuggestionSelect(item.type)}
                  onClick={(e) => e.stopPropagation()}
                />
                {getStatusIcon(
                  item.severity === OptimizationSeverity.HIGH
                    ? 'error'
                    : item.severity === OptimizationSeverity.MEDIUM
                      ? 'warning'
                      : 'good'
                )}
                <span className="optimization-item-title">{item.title}</span>
                {getSeverityTag(item.severity)}
                <Progress
                  percent={item.score}
                  status={getScoreStatus(item.score)}
                  size="small"
                  style={{ width: 100, marginLeft: 'auto' }}
                />
              </div>
            }
          >
            <div className="optimization-item-content">
              <Paragraph>{item.description}</Paragraph>
              <Title level={5}>{t('debug.optimization.tips')}</Title>
              <ul className="optimization-tips">
                {item.tips.map((tip, index) => (
                  <li key={index}>
                    <BulbOutlined className="optimization-tip-icon" />
                    <Text>{tip}</Text>
                  </li>
                ))}
              </ul>
              {item.autoOptimize && (
                <Button
                  type="primary"
                  icon={<ToolOutlined />}
                  size="small"
                  onClick={() => {
                    setSelectedSuggestions([item.type]);
                    optimizeSelected();
                  }}
                  disabled={isOptimizing}
                >
                  {t('debug.optimization.applyThis')}
                </Button>
              )}
            </div>
          </Panel>
        ))}
      </Collapse>
    );
  };

  // 渲染统计信息
  const renderStats = () => {
    if (!analysisResult) {
      return null;
    }

    const stats = [
      {
        label: t('debug.optimization.entityCount'),
        value: analysisResult.entityCount
      },
      {
        label: t('debug.optimization.renderableCount'),
        value: analysisResult.renderableCount
      },
      {
        label: t('debug.optimization.triangleCount'),
        value: Math.round(analysisResult.triangleCount).toLocaleString()
      },
      {
        label: t('debug.optimization.vertexCount'),
        value: Math.round(analysisResult.vertexCount).toLocaleString()
      },
      {
        label: t('debug.optimization.materialCount'),
        value: analysisResult.materialCount
      },
      {
        label: t('debug.optimization.textureCount'),
        value: analysisResult.textureCount
      },
      {
        label: t('debug.optimization.textureMemory'),
        value: `${Math.round(analysisResult.textureMemory)} MB`
      },
      {
        label: t('debug.optimization.lightCount'),
        value: analysisResult.lightCount
      },
      {
        label: t('debug.optimization.drawCalls'),
        value: analysisResult.drawCalls
      },
      {
        label: t('debug.optimization.memoryUsage'),
        value: `${Math.round(analysisResult.memoryUsage)} MB`
      }
    ];

    return (
      <List
        size="small"
        bordered
        dataSource={stats}
        renderItem={item => (
          <List.Item>
            <Text strong>{item.label}:</Text> {item.value}
          </List.Item>
        )}
      />
    );
  };

  return (
    <div className={`optimizer-panel ${className || ''}`}>
      <div className="optimizer-toolbar">
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={analyzeScene}
            loading={isAnalyzing}
            disabled={!activeScene || isOptimizing}
          >
            {isAnalyzing ? t('debug.optimization.analyzing') : t('debug.optimization.analyze')}
          </Button>

          <Button
            type="primary"
            icon={<ToolOutlined />}
            onClick={optimizeSelected}
            disabled={!analysisResult || selectedSuggestions.length === 0 || isOptimizing || isAnalyzing}
            loading={isOptimizing}
          >
            {t('debug.optimization.optimizeSelected')}
          </Button>

          <Button
            type="primary"
            danger
            icon={<ThunderboltOutlined />}
            onClick={optimizeScene}
            disabled={!analysisResult || isOptimizing || isAnalyzing}
            loading={isOptimizing}
          >
            {t('debug.optimization.optimizeAll')}
          </Button>

          <Tooltip title={t('debug.optimization.helpTooltip')}>
            <Button icon={<QuestionCircleOutlined />} />
          </Tooltip>
        </Space>
      </div>

      {isOptimizing && (
        <div className="optimization-progress">
          <Progress percent={Math.round(optimizationProgress)} status="active" />
        </div>
      )}

      <div className="optimizer-content">
        {!activeScene ? (
          <Alert
            message={t('debug.optimization.noScene')}
            description={t('debug.optimization.noSceneDesc')}
            type="info"
            showIcon
          />
        ) : isAnalyzing ? (
          <div className="analyzing-spinner">
            <Spin size="large" />
            <div className="analyzing-text">{t('debug.optimization.analyzingScene')}</div>
          </div>
        ) : !analysisResult ? (
          <Alert
            message={t('debug.optimization.noAnalysis')}
            description={t('debug.optimization.noAnalysisDesc')}
            type="info"
            showIcon
          />
        ) : (
          <>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Card>
                  <div className="optimization-score">
                    <Title level={4}>{t('debug.optimization.overallScore')}</Title>
                    <Progress
                      type="circle"
                      percent={Math.round(analysisResult.overallScore)}
                      status={getScoreStatus(analysisResult.overallScore)}
                      format={(percent) => `${percent}`}
                      width={120}
                    />
                    <div className="optimization-score-text">
                      {analysisResult.overallScore >= 80 && (
                        <Alert
                          message={t('debug.optimization.scoreGood')}
                          type="success"
                          showIcon
                        />
                      )}
                      {analysisResult.overallScore >= 60 && analysisResult.overallScore < 80 && (
                        <Alert
                          message={t('debug.optimization.scoreWarning')}
                          type="warning"
                          showIcon
                        />
                      )}
                      {analysisResult.overallScore < 60 && (
                        <Alert
                          message={t('debug.optimization.scoreError')}
                          type="error"
                          showIcon
                        />
                      )}
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>

            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              <Col span={24} lg={16}>
                <Card
                  title={
                    <Space>
                      <BulbOutlined />
                      {t('debug.optimization.recommendations')}
                      <Tag color="blue">{analysisResult.suggestions.length}</Tag>
                    </Space>
                  }
                >
                  {renderSuggestions()}
                </Card>
              </Col>

              <Col span={24} lg={8}>
                <Card
                  title={
                    <Space>
                      <LineChartOutlined />
                      {t('debug.optimization.statistics')}
                    </Space>
                  }
                >
                  {renderStats()}
                </Card>
              </Col>
            </Row>
          </>
        )}
      </div>
    </div>
  );
};

export default OptimizerPanel;
