# DL（Digital Learning）引擎用户手册

## 1. 系统概述

DL（Digital Learning）引擎是一个强大的3D引擎和编辑器平台，采用微服务架构，支持大规模并发用户访问。系统由三大部分组成：底层引擎、编辑器和服务器端。

### 1.1 底层引擎

底层引擎基于TypeScript和Three.js开发，提供了强大的3D渲染能力、物理模拟、粒子系统、动画系统等功能。引擎采用实体组件系统（ECS）架构，具有高性能和可扩展性。

### 1.2 编辑器

编辑器基于React、Redux和Ant Design开发，提供了直观的可视化界面，让用户可以轻松创建和编辑3D场景、模型、材质、动画等内容。编辑器支持实时预览、协作编辑、版本控制等功能。

### 1.3 服务器端

服务器端基于Nest.js和MySQL开发，采用微服务架构，包括用户服务、项目服务、资产服务、渲染服务等多个微服务。服务器端提供了用户认证、权限管理、资产管理、项目管理、渲染任务等功能。

## 2. 快速入门

### 2.1 注册和登录

1. 访问DL（Digital Learning）引擎官网（https://dl-engine.example.com）
2. 点击右上角的"注册"按钮
3. 填写用户名、邮箱和密码，完成注册
4. 使用注册的用户名和密码登录系统

### 2.2 创建新项目

1. 登录后，进入项目列表页面
2. 点击"新建项目"按钮
3. 填写项目名称、描述等信息
4. 选择项目模板（空白项目、基础场景、游戏模板等）
5. 点击"创建"按钮完成项目创建

### 2.3 编辑场景

1. 在项目列表中点击项目名称，进入项目详情页
2. 点击"编辑"按钮，打开编辑器
3. 使用左侧的场景树面板查看和管理场景中的对象
4. 使用右侧的属性面板编辑选中对象的属性
5. 使用顶部的工具栏添加新对象、调整视图等
6. 编辑完成后，点击"保存"按钮保存场景

### 2.4 预览和发布

1. 在编辑器中点击"预览"按钮，在新窗口中预览场景
2. 调整场景后，点击"发布"按钮
3. 选择发布选项（Web、移动端、桌面端等）
4. 等待发布完成，获取发布链接或下载发布包

## 3. 编辑器功能

### 3.1 界面布局

编辑器界面分为以下几个主要区域：

- **菜单栏**：位于顶部，包含文件、编辑、视图等菜单
- **工具栏**：位于菜单栏下方，包含常用工具按钮
- **场景视图**：中央区域，显示3D场景
- **场景树面板**：左侧面板，显示场景中的对象层次结构
- **资产面板**：左下角面板，显示项目资产（模型、材质、纹理等）
- **属性面板**：右侧面板，显示选中对象的属性
- **时间轴面板**：底部面板，用于编辑动画和关键帧

### 3.2 对象操作

- **添加对象**：点击工具栏中的"添加"按钮，选择要添加的对象类型
- **选择对象**：在场景视图中点击对象，或在场景树中选择对象
- **移动对象**：选中对象后，使用移动工具（快捷键：W）拖动对象
- **旋转对象**：选中对象后，使用旋转工具（快捷键：E）旋转对象
- **缩放对象**：选中对象后，使用缩放工具（快捷键：R）缩放对象
- **删除对象**：选中对象后，按Delete键或右键菜单选择"删除"

### 3.3 材质编辑

1. 在场景树中选择一个网格对象
2. 在属性面板中找到"材质"部分
3. 点击"编辑材质"按钮，打开材质编辑器
4. 调整材质参数（颜色、纹理、反射率等）
5. 点击"应用"按钮应用材质更改

### 3.4 动画编辑

1. 在场景树中选择要添加动画的对象
2. 点击底部时间轴面板中的"添加动画"按钮
3. 设置动画名称和持续时间
4. 在时间轴上添加关键帧（位置、旋转、缩放等）
5. 调整关键帧参数和时间
6. 点击播放按钮预览动画

## 4. 高级功能

### 4.1 物理模拟

1. 选择一个对象
2. 在属性面板中找到"物理"部分
3. 勾选"启用物理"选项
4. 设置物理属性（质量、摩擦力、弹性等）
5. 选择碰撞形状（盒体、球体、凸包等）
6. 点击"应用"按钮启用物理模拟

### 4.2 粒子系统

1. 点击工具栏中的"添加"按钮，选择"粒子系统"
2. 在属性面板中设置粒子系统参数（发射率、生命周期、速度等）
3. 设置粒子外观（颜色、大小、纹理等）
4. 设置粒子行为（重力、旋转、碰撞等）
5. 点击"应用"按钮创建粒子系统

### 4.3 光照和阴影

1. 点击工具栏中的"添加"按钮，选择光源类型（点光源、方向光、聚光灯等）
2. 在属性面板中设置光源参数（颜色、强度、衰减等）
3. 勾选"启用阴影"选项
4. 设置阴影参数（分辨率、偏移、模糊等）
5. 调整光源位置和方向，预览阴影效果

## 5. 常见问题

### 5.1 性能优化

**问题**：场景运行时帧率较低，如何优化性能？

**解决方案**：
- 减少场景中的多边形数量，使用低多边形模型
- 合并静态网格，减少绘制调用
- 优化纹理大小和数量，使用纹理图集
- 使用LOD（细节层次）技术，根据距离显示不同细节级别的模型
- 使用遮挡剔除，不渲染被遮挡的对象
- 减少光源数量，使用烘焙光照贴图

### 5.2 导入模型问题

**问题**：导入的3D模型显示异常或无法导入。

**解决方案**：
- 确保模型格式受支持（GLB、GLTF、FBX、OBJ等）
- 检查模型比例，可能需要调整缩放
- 检查模型坐标系，可能需要旋转模型
- 检查模型材质和纹理路径是否正确
- 尝试在3D建模软件中重新导出模型，选择兼容的格式和选项

### 5.3 账户和权限

**问题**：无法访问某些功能或项目。

**解决方案**：
- 检查账户类型和权限级别
- 联系项目管理员获取访问权限
- 检查订阅状态，可能需要升级账户
- 确认是否有足够的配额（存储空间、渲染时间等）

## 6. 技术支持

如需技术支持，请通过以下方式联系我们：

- **官方网站**：https://dl-engine.example.com/support
- **电子邮件**：<EMAIL>
- **在线社区**：https://community.dl-engine.example.com
- **文档中心**：https://docs.dl-engine.example.com
