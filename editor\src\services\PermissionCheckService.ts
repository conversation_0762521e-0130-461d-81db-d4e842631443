/**
 * 权限检查服务
 * 负责在关键操作处进行权限验证
 */
import { message } from 'antd';
import { Permission, permissionService } from './PermissionService';
import { collaborationService } from './CollaborationService';
import i18n from '../i18n';
import { permissionLogService } from './PermissionLogService';

/**
 * 权限检查服务类
 */
class PermissionCheckService {
  private enabled: boolean = true;
  private showErrorMessages: boolean = true;
  private logFailedChecks: boolean = true;

  /**
   * 设置是否启用权限检查
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * 设置是否显示错误消息
   * @param show 是否显示
   */
  public setShowErrorMessages(show: boolean): void {
    this.showErrorMessages = show;
  }

  /**
   * 设置是否记录失败的权限检查
   * @param log 是否记录
   */
  public setLogFailedChecks(log: boolean): void {
    this.logFailedChecks = log;
  }

  /**
   * 检查当前用户是否有指定权限
   * @param permission 权限
   * @param showMessage 是否显示错误消息
   * @returns 是否有权限
   */
  public checkPermission(permission: Permission, showMessage: boolean = true, context?: any): boolean {
    // 如果权限检查被禁用，直接返回true
    if (!this.enabled) {
      return true;
    }

    // 获取当前用户ID
    const userId = collaborationService.getUserId();
    if (!userId) {
      if (showMessage && this.showErrorMessages) {
        message.error(i18n.t('permission.errors.notLoggedIn'));
      }
      return false;
    }

    // 检查权限
    const hasPermission = permissionService.hasPermission(userId, permission);

    // 如果没有权限
    if (!hasPermission) {
      // 记录失败的权限检查
      if (this.logFailedChecks) {
        permissionLogService.logPermissionCheckFailed(userId, permission, context);
      }

      // 显示错误消息
      if (showMessage && this.showErrorMessages) {
        message.error(i18n.t('permission.errors.noPermission', { permission: this.getPermissionName(permission) }));
      }
    }

    return hasPermission;
  }

  /**
   * 检查指定用户是否有指定权限
   * @param userId 用户ID
   * @param permission 权限
   * @returns 是否有权限
   */
  public checkUserPermission(userId: string, permission: Permission, context?: any): boolean {
    // 如果权限检查被禁用，直接返回true
    if (!this.enabled) {
      return true;
    }

    // 检查权限
    const hasPermission = permissionService.hasPermission(userId, permission);

    // 如果没有权限且需要记录失败的权限检查
    if (!hasPermission && this.logFailedChecks) {
      permissionLogService.logPermissionCheckFailed(userId, permission, context);
    }

    return hasPermission;
  }

  /**
   * 获取权限名称
   * @param permission 权限
   * @returns 权限名称
   */
  private getPermissionName(permission: Permission): string {
    // 根据权限枚举返回对应的i18n键
    return i18n.t(`permission.types.${permission}`);
  }

  /**
   * 检查当前用户是否可以编辑场景
   * @returns 是否可以编辑
   */
  public canEditScene(): boolean {
    return this.checkPermission(Permission.EDIT_SCENE);
  }

  /**
   * 检查当前用户是否可以创建实体
   * @returns 是否可以创建实体
   */
  public canCreateEntity(): boolean {
    return this.checkPermission(Permission.CREATE_ENTITY);
  }

  /**
   * 检查当前用户是否可以更新实体
   * @returns 是否可以更新实体
   */
  public canUpdateEntity(): boolean {
    return this.checkPermission(Permission.UPDATE_ENTITY);
  }

  /**
   * 检查当前用户是否可以删除实体
   * @returns 是否可以删除实体
   */
  public canDeleteEntity(): boolean {
    return this.checkPermission(Permission.DELETE_ENTITY);
  }

  /**
   * 检查当前用户是否可以添加组件
   * @returns 是否可以添加组件
   */
  public canAddComponent(): boolean {
    return this.checkPermission(Permission.ADD_COMPONENT);
  }

  /**
   * 检查当前用户是否可以更新组件
   * @returns 是否可以更新组件
   */
  public canUpdateComponent(): boolean {
    return this.checkPermission(Permission.UPDATE_COMPONENT);
  }

  /**
   * 检查当前用户是否可以移除组件
   * @returns 是否可以移除组件
   */
  public canRemoveComponent(): boolean {
    return this.checkPermission(Permission.REMOVE_COMPONENT);
  }

  /**
   * 检查当前用户是否可以上传资源
   * @returns 是否可以上传资源
   */
  public canUploadAsset(): boolean {
    return this.checkPermission(Permission.UPLOAD_ASSET);
  }

  /**
   * 检查当前用户是否可以删除资源
   * @returns 是否可以删除资源
   */
  public canDeleteAsset(): boolean {
    return this.checkPermission(Permission.DELETE_ASSET);
  }

  /**
   * 检查当前用户是否可以保存场景
   * @returns 是否可以保存场景
   */
  public canSaveScene(): boolean {
    return this.checkPermission(Permission.SAVE_SCENE);
  }

  /**
   * 检查当前用户是否可以导出场景
   * @returns 是否可以导出场景
   */
  public canExportScene(): boolean {
    return this.checkPermission(Permission.EXPORT_SCENE);
  }

  /**
   * 检查当前用户是否可以导入场景
   * @returns 是否可以导入场景
   */
  public canImportScene(): boolean {
    return this.checkPermission(Permission.IMPORT_SCENE);
  }

  /**
   * 检查当前用户是否可以管理用户
   * @returns 是否可以管理用户
   */
  public canManageUsers(): boolean {
    return this.checkPermission(Permission.MANAGE_USERS);
  }

  /**
   * 检查当前用户是否可以分配角色
   * @returns 是否可以分配角色
   */
  public canAssignRoles(): boolean {
    return this.checkPermission(Permission.ASSIGN_ROLES);
  }

  /**
   * 检查当前用户是否可以管理权限
   * @returns 是否可以管理权限
   */
  public canManagePermissions(): boolean {
    return this.checkPermission(Permission.MANAGE_PERMISSIONS);
  }

  /**
   * 检查当前用户是否可以管理项目
   * @returns 是否可以管理项目
   */
  public canManageProject(): boolean {
    return this.checkPermission(Permission.MANAGE_PROJECT);
  }

  /**
   * 检查当前用户是否可以删除项目
   * @returns 是否可以删除项目
   */
  public canDeleteProject(): boolean {
    return this.checkPermission(Permission.DELETE_PROJECT);
  }
}

// 创建权限检查服务实例
export const permissionCheckService = new PermissionCheckService();
