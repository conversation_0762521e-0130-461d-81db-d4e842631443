/**
 * 音频源编辑器
 */
import React from 'react';
import GenericEditor from '../common/GenericEditor';

/**
 * 音频源编辑器属性
 */
interface AudioSourceEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * 音频源编辑器组件
 */
const AudioSourceEditor: React.FC<AudioSourceEditorProps> = ({ data, onChange }) => {
  return <GenericEditor title="音频源" data={data} onChange={onChange} />;
};

export default AudioSourceEditor;
