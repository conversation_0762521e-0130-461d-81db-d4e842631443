/**
 * 八叉树实现
 * 用于空间分割和快速查询
 */
import * as THREE from 'three';

/**
 * 八叉树节点接口
 */
export interface OctreeNode {
  /** 节点边界框 */
  bounds: THREE.Box3;
  /** 节点中心点 */
  center: THREE.Vector3;
  /** 节点大小 */
  size: number;
  /** 节点深度 */
  depth: number;
  /** 子节点 */
  children: OctreeNode[] | null;
  /** 节点中的对象 */
  objects: string[];
  /** 对象位置映射 */
  objectPositions: Map<string, THREE.Vector3>;
  /** 对象半径映射 */
  objectRadii: Map<string, number>;
}

/**
 * 八叉树配置选项
 */
export interface OctreeOptions {
  /** 八叉树大小 */
  size: number;
  /** 最大深度 */
  maxDepth: number;
  /** 每个节点最大对象数 */
  maxObjects: number;
  /** 八叉树中心点 */
  center?: THREE.Vector3;
}

/**
 * 八叉树类
 */
export class Octree {
  /** 根节点 */
  private root: OctreeNode;
  
  /** 最大深度 */
  private maxDepth: number;
  
  /** 每个节点最大对象数 */
  private maxObjects: number;
  
  /** 对象到节点的映射 */
  private objectToNode: Map<string, OctreeNode>;

  /**
   * 创建八叉树
   * @param options 配置选项
   */
  constructor(options: OctreeOptions) {
    this.maxDepth = options.maxDepth;
    this.maxObjects = options.maxObjects;
    this.objectToNode = new Map();

    const center = options.center || new THREE.Vector3(0, 0, 0);
    const halfSize = options.size / 2;

    // 创建根节点
    this.root = {
      bounds: new THREE.Box3(
        new THREE.Vector3(center.x - halfSize, center.y - halfSize, center.z - halfSize),
        new THREE.Vector3(center.x + halfSize, center.y + halfSize, center.z + halfSize)
      ),
      center: center.clone(),
      size: options.size,
      depth: 0,
      children: null,
      objects: [],
      objectPositions: new Map(),
      objectRadii: new Map()
    };
  }

  /**
   * 插入对象
   * @param objectId 对象ID
   * @param position 对象位置
   * @param radius 对象半径
   */
  public insert(objectId: string, position: THREE.Vector3, radius: number = 1): void {
    // 如果对象已存在，先移除
    if (this.objectToNode.has(objectId)) {
      this.remove(objectId);
    }

    this.insertIntoNode(this.root, objectId, position, radius);
  }

  /**
   * 移除对象
   * @param objectId 对象ID
   */
  public remove(objectId: string): void {
    const node = this.objectToNode.get(objectId);
    if (!node) {
      return;
    }

    // 从节点中移除对象
    const index = node.objects.indexOf(objectId);
    if (index !== -1) {
      node.objects.splice(index, 1);
      node.objectPositions.delete(objectId);
      node.objectRadii.delete(objectId);
    }

    // 从映射中移除
    this.objectToNode.delete(objectId);

    // 尝试合并节点
    this.tryMergeNode(node);
  }

  /**
   * 更新对象位置
   * @param objectId 对象ID
   * @param newPosition 新位置
   * @param radius 对象半径
   */
  public update(objectId: string, newPosition: THREE.Vector3, radius: number = 1): void {
    // 简单的实现：移除后重新插入
    this.remove(objectId);
    this.insert(objectId, newPosition, radius);
  }

  /**
   * 获取与视锥体相交的节点
   * @param frustum 视锥体
   * @returns 相交的节点数组
   */
  public getFrustumIntersectedNodes(frustum: THREE.Frustum): OctreeNode[] {
    const result: OctreeNode[] = [];
    this.getFrustumIntersectedNodesRecursive(this.root, frustum, result);
    return result;
  }

  /**
   * 获取与包围盒相交的对象
   * @param box 包围盒
   * @returns 相交的对象ID数组
   */
  public getObjectsInBox(box: THREE.Box3): string[] {
    const result: string[] = [];
    this.getObjectsInBoxRecursive(this.root, box, result);
    return result;
  }

  /**
   * 获取与球体相交的对象
   * @param sphere 球体
   * @returns 相交的对象ID数组
   */
  public getObjectsInSphere(sphere: THREE.Sphere): string[] {
    const result: string[] = [];
    this.getObjectsInSphereRecursive(this.root, sphere, result);
    return result;
  }

  /**
   * 清空八叉树
   */
  public clear(): void {
    this.clearNode(this.root);
    this.objectToNode.clear();
  }

  /**
   * 获取统计信息
   */
  public getStats(): {
    totalNodes: number;
    totalObjects: number;
    maxDepth: number;
    averageObjectsPerNode: number;
  } {
    const stats = {
      totalNodes: 0,
      totalObjects: 0,
      maxDepth: 0,
      averageObjectsPerNode: 0
    };

    this.getStatsRecursive(this.root, stats);
    
    if (stats.totalNodes > 0) {
      stats.averageObjectsPerNode = stats.totalObjects / stats.totalNodes;
    }

    return stats;
  }

  /**
   * 将对象插入到节点中
   * @param node 节点
   * @param objectId 对象ID
   * @param position 对象位置
   * @param radius 对象半径
   */
  private insertIntoNode(node: OctreeNode, objectId: string, position: THREE.Vector3, radius: number): void {
    // 检查对象是否在节点边界内
    if (!this.isObjectInNode(node, position, radius)) {
      return;
    }

    // 如果节点没有子节点且对象数量未超过限制，直接添加
    if (!node.children && node.objects.length < this.maxObjects) {
      node.objects.push(objectId);
      node.objectPositions.set(objectId, position.clone());
      node.objectRadii.set(objectId, radius);
      this.objectToNode.set(objectId, node);
      return;
    }

    // 如果节点没有子节点但需要分割
    if (!node.children && node.depth < this.maxDepth) {
      this.subdivideNode(node);
    }

    // 如果有子节点，尝试插入到子节点
    if (node.children) {
      let inserted = false;
      for (const child of node.children) {
        if (this.isObjectInNode(child, position, radius)) {
          this.insertIntoNode(child, objectId, position, radius);
          inserted = true;
          break;
        }
      }

      // 如果无法插入到任何子节点，插入到当前节点
      if (!inserted) {
        node.objects.push(objectId);
        node.objectPositions.set(objectId, position.clone());
        node.objectRadii.set(objectId, radius);
        this.objectToNode.set(objectId, node);
      }
    } else {
      // 如果达到最大深度，直接添加到当前节点
      node.objects.push(objectId);
      node.objectPositions.set(objectId, position.clone());
      node.objectRadii.set(objectId, radius);
      this.objectToNode.set(objectId, node);
    }
  }

  /**
   * 检查对象是否在节点内
   * @param node 节点
   * @param position 对象位置
   * @param radius 对象半径
   * @returns 是否在节点内
   */
  private isObjectInNode(node: OctreeNode, position: THREE.Vector3, radius: number): boolean {
    // 创建对象的包围盒
    const objectBox = new THREE.Box3(
      new THREE.Vector3(position.x - radius, position.y - radius, position.z - radius),
      new THREE.Vector3(position.x + radius, position.y + radius, position.z + radius)
    );

    // 检查是否与节点边界相交
    return node.bounds.intersectsBox(objectBox);
  }

  /**
   * 分割节点
   * @param node 要分割的节点
   */
  private subdivideNode(node: OctreeNode): void {
    const halfSize = node.size / 4;
    const childSize = node.size / 2;
    const childDepth = node.depth + 1;

    node.children = [];

    // 创建8个子节点
    for (let x = 0; x < 2; x++) {
      for (let y = 0; y < 2; y++) {
        for (let z = 0; z < 2; z++) {
          const childCenter = new THREE.Vector3(
            node.center.x + (x === 0 ? -halfSize : halfSize),
            node.center.y + (y === 0 ? -halfSize : halfSize),
            node.center.z + (z === 0 ? -halfSize : halfSize)
          );

          const childBounds = new THREE.Box3(
            new THREE.Vector3(
              childCenter.x - halfSize,
              childCenter.y - halfSize,
              childCenter.z - halfSize
            ),
            new THREE.Vector3(
              childCenter.x + halfSize,
              childCenter.y + halfSize,
              childCenter.z + halfSize
            )
          );

          const child: OctreeNode = {
            bounds: childBounds,
            center: childCenter,
            size: childSize,
            depth: childDepth,
            children: null,
            objects: [],
            objectPositions: new Map(),
            objectRadii: new Map()
          };

          node.children.push(child);
        }
      }
    }

    // 重新分配现有对象到子节点
    const objectsToReassign = [...node.objects];
    const positionsToReassign = new Map(node.objectPositions);
    const radiiToReassign = new Map(node.objectRadii);

    node.objects = [];
    node.objectPositions.clear();
    node.objectRadii.clear();

    for (const objectId of objectsToReassign) {
      this.objectToNode.delete(objectId);
      const objPos = positionsToReassign.get(objectId);
      const objRadius = radiiToReassign.get(objectId);
      if (objPos && objRadius !== undefined) {
        this.insertIntoNode(node, objectId, objPos, objRadius);
      }
    }
  }

  /**
   * 尝试合并节点
   * @param node 节点
   */
  private tryMergeNode(node: OctreeNode): void {
    // 如果节点没有子节点，无需合并
    if (!node.children) {
      return;
    }

    // 计算所有子节点的总对象数
    let totalObjects = node.objects.length;
    for (const child of node.children) {
      totalObjects += child.objects.length;
      // 如果任何子节点有子节点，不能合并
      if (child.children) {
        return;
      }
    }

    // 如果总对象数小于等于最大对象数，可以合并
    if (totalObjects <= this.maxObjects) {
      // 将所有子节点的对象移动到当前节点
      for (const child of node.children) {
        for (const objectId of child.objects) {
          node.objects.push(objectId);
          const pos = child.objectPositions.get(objectId);
          const radius = child.objectRadii.get(objectId);
          if (pos && radius !== undefined) {
            node.objectPositions.set(objectId, pos);
            node.objectRadii.set(objectId, radius);
          }
          this.objectToNode.set(objectId, node);
        }
      }

      // 移除子节点
      node.children = null;
    }
  }

  /**
   * 递归获取与视锥体相交的节点
   * @param node 当前节点
   * @param frustum 视锥体
   * @param result 结果数组
   */
  private getFrustumIntersectedNodesRecursive(node: OctreeNode, frustum: THREE.Frustum, result: OctreeNode[]): void {
    // 检查节点边界是否与视锥体相交
    if (!frustum.intersectsBox(node.bounds)) {
      return;
    }

    // 如果节点有对象，添加到结果中
    if (node.objects.length > 0) {
      result.push(node);
    }

    // 递归检查子节点
    if (node.children) {
      for (const child of node.children) {
        this.getFrustumIntersectedNodesRecursive(child, frustum, result);
      }
    }
  }

  /**
   * 递归获取与包围盒相交的对象
   * @param node 当前节点
   * @param box 包围盒
   * @param result 结果数组
   */
  private getObjectsInBoxRecursive(node: OctreeNode, box: THREE.Box3, result: string[]): void {
    // 检查节点边界是否与包围盒相交
    if (!node.bounds.intersectsBox(box)) {
      return;
    }

    // 检查节点中的对象
    for (const objectId of node.objects) {
      const position = node.objectPositions.get(objectId);
      const radius = node.objectRadii.get(objectId);
      if (position && radius !== undefined) {
        const objectBox = new THREE.Box3(
          new THREE.Vector3(position.x - radius, position.y - radius, position.z - radius),
          new THREE.Vector3(position.x + radius, position.y + radius, position.z + radius)
        );
        if (box.intersectsBox(objectBox)) {
          result.push(objectId);
        }
      }
    }

    // 递归检查子节点
    if (node.children) {
      for (const child of node.children) {
        this.getObjectsInBoxRecursive(child, box, result);
      }
    }
  }

  /**
   * 递归获取与球体相交的对象
   * @param node 当前节点
   * @param sphere 球体
   * @param result 结果数组
   */
  private getObjectsInSphereRecursive(node: OctreeNode, sphere: THREE.Sphere, result: string[]): void {
    // 检查节点边界是否与球体相交
    if (!sphere.intersectsBox(node.bounds)) {
      return;
    }

    // 检查节点中的对象
    for (const objectId of node.objects) {
      const position = node.objectPositions.get(objectId);
      const radius = node.objectRadii.get(objectId);
      if (position && radius !== undefined) {
        const distance = sphere.center.distanceTo(position);
        if (distance <= sphere.radius + radius) {
          result.push(objectId);
        }
      }
    }

    // 递归检查子节点
    if (node.children) {
      for (const child of node.children) {
        this.getObjectsInSphereRecursive(child, sphere, result);
      }
    }
  }

  /**
   * 清空节点
   * @param node 节点
   */
  private clearNode(node: OctreeNode): void {
    node.objects = [];
    node.objectPositions.clear();
    node.objectRadii.clear();

    if (node.children) {
      for (const child of node.children) {
        this.clearNode(child);
      }
      node.children = null;
    }
  }

  /**
   * 递归获取统计信息
   * @param node 当前节点
   * @param stats 统计信息对象
   */
  private getStatsRecursive(node: OctreeNode, stats: any): void {
    stats.totalNodes++;
    stats.totalObjects += node.objects.length;
    stats.maxDepth = Math.max(stats.maxDepth, node.depth);

    if (node.children) {
      for (const child of node.children) {
        this.getStatsRecursive(child, stats);
      }
    }
  }
}
