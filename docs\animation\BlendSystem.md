# 动画混合系统

动画混合系统是dl-engine引擎中的核心功能之一，用于实现复杂的角色动画混合效果。本文档介绍了动画混合系统的基本概念、使用方法和高级功能。

## 目录

1. [基本概念](#基本概念)
2. [基本用法](#基本用法)
3. [混合层](#混合层)
4. [混合曲线](#混合曲线)
5. [遮罩系统](#遮罩系统)
6. [子片段](#子片段)
7. [性能优化](#性能优化)
8. [示例](#示例)

## 基本概念

动画混合系统主要由以下几个部分组成：

- **AnimationBlender**：动画混合器，负责管理和混合多个动画片段
- **BlendLayer**：混合层，表示一个参与混合的动画片段及其属性
- **BlendCurveType**：混合曲线类型，控制混合过程中的插值方式
- **BlendMode**：混合模式，控制动画片段之间的混合方式
- **AnimationMask**：动画遮罩，用于控制动画对骨骼的影响范围
- **SubClip**：子片段，从现有动画片段中提取的部分动画

## 基本用法

### 创建混合器

```typescript
// 创建动画控制器
const animator = new Animator(entity);

// 创建混合器
const blender = new AnimationBlender(animator);
```

### 添加动画片段

```typescript
// 添加动画片段
animator.addClip('idle', idleClip);
animator.addClip('walk', walkClip);
animator.addClip('run', runClip);
```

### 创建混合层

```typescript
// 添加混合层
const idleLayerIndex = blender.addLayer('idle', 1.0, BlendMode.OVERRIDE);
const walkLayerIndex = blender.addLayer('walk', 0.0, BlendMode.OVERRIDE);
```

### 控制混合

```typescript
// 从空闲到走路的混合
blender.setLayerWeight(idleLayerIndex, 0.0, 1.0); // 1秒内将空闲动画权重降为0
blender.setLayerWeight(walkLayerIndex, 1.0, 1.0); // 1秒内将走路动画权重升为1
```

## 混合层

混合层是动画混合系统的基本单位，每个混合层包含一个动画片段及其属性。

### 混合层属性

- **clipName**：动画片段名称
- **weight**：权重，控制动画片段的影响程度（0-1）
- **timeScale**：时间缩放，控制动画播放速度
- **blendMode**：混合模式，控制与其他层的混合方式
- **mask**：遮罩，控制动画对骨骼的影响范围

### 混合模式

- **OVERRIDE**：覆盖模式，完全替换之前的动画
- **ADDITIVE**：叠加模式，在之前的动画基础上叠加
- **MULTIPLY**：乘法模式，将当前动画与之前的动画相乘
- **DIFFERENCE**：差值模式，计算当前动画与之前动画的差值
- **AVERAGE**：平均模式，计算当前动画与之前动画的平均值
- **MAX**：最大值模式，取当前动画与之前动画中的最大值
- **MIN**：最小值模式，取当前动画与之前动画中的最小值

### 示例

```typescript
// 添加走路动画层（权重为0.7，时间缩放为1.2，使用叠加模式）
const walkLayerIndex = blender.addLayer('walk', 0.7, BlendMode.ADDITIVE, 1.2);

// 修改混合层属性
blender.setLayerWeight(walkLayerIndex, 0.5); // 设置权重为0.5
blender.setLayerTimeScale(walkLayerIndex, 1.5); // 设置时间缩放为1.5
blender.setLayerBlendMode(walkLayerIndex, BlendMode.MULTIPLY); // 设置混合模式为乘法
```

## 混合曲线

混合曲线控制混合过程中的插值方式，可以实现各种平滑过渡效果。

### 曲线类型

- **LINEAR**：线性插值
- **EASE_IN**：缓入插值
- **EASE_OUT**：缓出插值
- **EASE_IN_OUT**：缓入缓出插值
- **ELASTIC**：弹性插值
- **BOUNCE**：弹跳插值
- **SINE**：正弦插值
- **EXPONENTIAL**：指数插值
- **CIRCULAR**：圆形插值
- **QUADRATIC**：二次方插值
- **CUBIC**：三次方插值
- **QUARTIC**：四次方插值
- **QUINTIC**：五次方插值
- **CUSTOM**：自定义插值

### 示例

```typescript
// 设置混合曲线类型
blender.setBlendCurveType(BlendCurveType.EASE_IN_OUT);

// 使用预设曲线
blender.usePresetCurve('easeInOutQuad');

// 创建贝塞尔曲线
const bezierCurve = blender.createBezierCurve(0.42, 0, 0.58, 1);
blender.setCustomBlendCurve(bezierCurve);
```

## 遮罩系统

遮罩系统用于控制动画对骨骼的影响范围，可以实现上半身和下半身分别播放不同动画等效果。

### 遮罩类型

- **INCLUDE**：包含模式，只对指定骨骼产生影响
- **EXCLUDE**：排除模式，对除指定骨骼外的所有骨骼产生影响

### 权重类型

- **BINARY**：二进制权重，骨骼要么完全受影响（1），要么完全不受影响（0）
- **SMOOTH**：平滑权重，骨骼的影响程度可以平滑过渡

### 预设遮罩

- **上半身遮罩**：影响上半身骨骼（脊柱、颈部、头部、手臂等）
- **下半身遮罩**：影响下半身骨骼（腿部、脚部等）
- **左手遮罩**：影响左手骨骼
- **右手遮罩**：影响右手骨骼

### 示例

```typescript
// 创建上半身遮罩
const upperBodyMask = blender.createUpperBodyMask();

// 创建自定义遮罩
const customMask = blender.createMask(
  'customMask',
  MaskType.INCLUDE,
  ['spine', 'neck', 'head'],
  MaskWeightType.SMOOTH
);

// 应用遮罩到动画片段
blender.applyMaskToClip('walk', 'upperBodyMask');

// 添加带遮罩的混合层
const walkLayerIndex = blender.addLayer('walk', 1.0, BlendMode.OVERRIDE, 1.0, ['lowerBody']);
const waveLayerIndex = blender.addLayer('wave', 1.0, BlendMode.OVERRIDE, 1.0, ['upperBody']);
```

## 子片段

子片段用于从现有动画片段中提取部分动画，可以实现循环播放、调整播放速度等功能。

### 基本子片段

```typescript
// 创建子片段
const fastWalk = blender.createSubClip(
  'fastWalk',  // 子片段名称
  'walk',      // 源片段名称
  0,           // 开始时间（秒）
  1,           // 结束时间（秒）
  true         // 是否循环
);

// 设置子片段属性
fastWalk.setTimeScale(1.5); // 设置时间缩放为1.5
```

### 高级子片段

```typescript
// 创建高级子片段
const walkLoop = new AnimationSubClip({
  name: 'walkLoop',
  sourceClipName: 'walk',
  startTime: 0.2,
  endTime: 0.8,
  loop: true,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE
});

// 添加高级子片段
blender.addAdvancedSubClip(walkLoop);
```

## 性能优化

动画混合系统提供了多种性能优化选项，可以根据需要进行配置。

### 缓存机制

```typescript
// 设置缓存配置
blender.setCacheConfig(true, 1000); // 启用缓存，精度为1000
```

### 对象池

```typescript
// 设置对象池配置
blender.setObjectPoolConfig(true); // 启用对象池
```

### 批处理

```typescript
// 设置批处理配置
blender.setBatchProcessingConfig(true); // 启用批处理
```

### 性能统计

```typescript
// 获取性能统计信息
const stats = blender.getPerformanceStats();
console.log(stats);
```

## 示例

完整的动画混合系统示例可以在 `examples/animation/BlendSystemExample.ts` 文件中找到。该示例展示了如何使用动画混合系统的各种功能，包括基本混合、高级混合、遮罩混合、曲线混合和子片段混合。

```typescript
// 创建示例
const example = new BlendSystemExample();

// 启动示例
example.start();
```

更多示例和详细文档，请参考API文档和示例代码。
