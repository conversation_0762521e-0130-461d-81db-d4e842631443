#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表（基于错误报告）
const filesToFix = [
  'src/components/animation/AnimationEditor.tsx',
  'src/components/animation/AnimationEditorPanel.tsx',
  'src/components/animation/AnimationEventEditor.tsx',
  'src/components/animation/AnimationPreview.tsx',
  'src/components/animation/AnimationTimeline.tsx',
  'src/components/animation/BlendModeEditor.tsx',
  'src/components/animation/FacialAnimationEditor.tsx',
  'src/components/animation/MaskEditor.tsx',
  'src/components/animation/RetargetingEditor.tsx',
  'src/components/animation/RetargetingEditorPanel.tsx',
  'src/components/animation/SubClipEditor.tsx',
  'src/components/AnimationEditor/BlendCurveEditor.tsx',
  'src/components/AnimationEditor/BlendSpace1DEditor.tsx',
  'src/components/AnimationEditor/BlendSpace2DEditor.tsx',
  'src/components/AnimationEditor/StateMachineDebugger.tsx',
  'src/components/AnimationEditor/StateMachineEditor.tsx',
  'src/components/AnimationEditor/StateMachineEditorPanel.tsx',
  'src/components/AnimationEditor/StateMachinePanel.tsx'
];

// 常见的重复import模式
const duplicatePatterns = [
  // 重复的 antd imports
  {
    pattern: /import { Select, Switch, InputNumber, Form } from 'antd';/g,
    description: '重复的 antd 组件导入'
  },
  // 重复的 const 解构
  {
    pattern: /const { Option } = Select;[\s\S]*?const { Option } = Select;/g,
    replacement: 'const { Option } = Select;',
    description: '重复的 Select Option 解构'
  },
  // 自引用导入
  {
    pattern: /import.*from ['"]\.\/[^'"]*['"];/g,
    description: '可能的自引用导入'
  }
];

// 修复单个文件
function fixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠ 文件不存在: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const originalContent = content;

    // 1. 修复 .mjs 导入
    if (content.includes('.mjs')) {
      content = content.replace(/from ['"](.+?)\.mjs['"]/g, "from '$1'");
      modified = true;
    }

    // 2. 移除重复的import行
    const lines = content.split('\n');
    const newLines = [];
    const seenLines = new Set();

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // 跳过空行
      if (!trimmedLine) {
        newLines.push(line);
        continue;
      }

      // 检查重复的import或const解构
      if (trimmedLine.startsWith('import ') || 
          (trimmedLine.startsWith('const {') && trimmedLine.includes('} = '))) {
        
        if (!seenLines.has(trimmedLine)) {
          seenLines.add(trimmedLine);
          newLines.push(line);
        } else {
          modified = true;
          console.log(`  - 移除重复行: ${trimmedLine}`);
        }
      } else {
        newLines.push(line);
      }
    }

    // 3. 检查自引用导入
    const fileName = path.basename(filePath, path.extname(filePath));
    const selfImportRegex = new RegExp(`import.*from ['"]\\.\/${fileName}['"]`, 'g');
    if (selfImportRegex.test(content)) {
      content = content.replace(selfImportRegex, '');
      modified = true;
      console.log(`  - 移除自引用导入: ${fileName}`);
    }

    if (modified) {
      const newContent = newLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✓ 修复了 ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`✗ 修复 ${filePath} 时出错:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  console.log('开始批量修复 import 错误...\n');
  
  let fixedCount = 0;
  let totalFiles = 0;

  for (const file of filesToFix) {
    const fullPath = path.join(__dirname, file);
    totalFiles++;
    
    console.log(`处理文件: ${file}`);
    
    if (fixFile(fullPath)) {
      fixedCount++;
    }
    
    console.log(''); // 空行分隔
  }

  console.log(`\n修复完成！`);
  console.log(`总文件数: ${totalFiles}`);
  console.log(`修复文件数: ${fixedCount}`);
  console.log(`跳过文件数: ${totalFiles - fixedCount}`);
}

if (require.main === module) {
  main();
}

module.exports = { fixFile };
