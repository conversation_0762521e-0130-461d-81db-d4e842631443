#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要快速修复的常见问题
const quickFixes = [
  {
    // 修复重复的 antd import
    pattern: /import { Select, Switch, InputNumber, Form } from 'antd';/g,
    replacement: '',
    description: '移除重复的 antd import'
  },
  {
    // 修复 .mjs 导入
    pattern: /from ['"](.+?)\.mjs['"]/g,
    replacement: "from '$1'",
    description: '修复 .mjs 导入路径'
  },
  {
    // 修复自引用导入
    pattern: /import.*from ['"]\.\/AnimationEventEditor['"];/g,
    replacement: '',
    description: '移除 AnimationEventEditor 自引用'
  },
  {
    // 修复重复的 const 解构
    pattern: /const { Option } = Select;\s*import.*?\n\s*const { Option } = Select;/g,
    replacement: 'const { Option } = Select;',
    description: '修复重复的 Option 解构'
  }
];

// 需要修复的文件列表（基于错误最多的文件）
const priorityFiles = [
  'src/components/animation/AnimationEditor.tsx',
  'src/components/animation/AnimationEditorPanel.tsx', 
  'src/components/animation/AnimationEventEditor.tsx',
  'src/components/animation/BlendModeEditor.tsx',
  'src/components/animation/MaskEditor.tsx',
  'src/components/animation/SubClipEditor.tsx',
  'src/components/AnimationEditor/BlendSpace1DEditor.tsx',
  'src/components/AnimationEditor/BlendSpace2DEditor.tsx',
  'src/components/AnimationEditor/StateMachineEditor.tsx'
];

function quickFixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠ 文件不存在: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const originalLength = content.length;

    // 应用快速修复
    for (const fix of quickFixes) {
      const beforeLength = content.length;
      content = content.replace(fix.pattern, fix.replacement);
      if (content.length !== beforeLength) {
        modified = true;
        console.log(`  - ${fix.description}`);
      }
    }

    // 移除空行（连续的空行合并为单个空行）
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

    // 移除重复的import行（简单版本）
    const lines = content.split('\n');
    const newLines = [];
    const seenImports = new Set();

    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed.startsWith('import ') && trimmed.includes('from ')) {
        if (!seenImports.has(trimmed)) {
          seenImports.add(trimmed);
          newLines.push(line);
        } else {
          modified = true;
          console.log(`  - 移除重复import: ${trimmed.substring(0, 50)}...`);
        }
      } else {
        newLines.push(line);
      }
    }

    if (modified) {
      const newContent = newLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✓ 修复了 ${filePath} (${originalLength} -> ${newContent.length} 字符)`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`✗ 修复 ${filePath} 时出错:`, error.message);
    return false;
  }
}

function main() {
  console.log('开始快速修复常见错误...\n');
  
  let fixedCount = 0;
  
  for (const file of priorityFiles) {
    const fullPath = path.join(__dirname, file);
    console.log(`处理: ${file}`);
    
    if (quickFixFile(fullPath)) {
      fixedCount++;
    }
    
    console.log('');
  }

  console.log(`快速修复完成！修复了 ${fixedCount}/${priorityFiles.length} 个文件。`);
}

if (require.main === module) {
  main();
}

module.exports = { quickFixFile };
