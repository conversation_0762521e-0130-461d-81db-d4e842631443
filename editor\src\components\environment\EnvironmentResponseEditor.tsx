/**
 * 环境响应编辑器组件
 * 
 * 该组件允许用户编辑角色对环境的响应规则，包括条件和动作。
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Button, 
  Switch, 
  Space, 
  Collapse, 
  Divider, 
  InputNumber, 
  Tooltip, 
  Tag, 
  message 
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined, 
  CopyOutlined, 
  PlayCircleOutlined, 
  PauseCircleOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  ThunderboltOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
// 移除引擎直接导入
// 移除引擎直接导入
// 移除引擎直接导入

const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { Panel } = Collapse;

/**
 * 环境响应编辑器属性接口
 */
interface EnvironmentResponseEditorProps {
  entityId?: string;
  onSave?: (rules: EnvironmentResponseRule[]) => void;
  onTest?: (rule: EnvironmentResponseRule) => void;
  initialRules?: EnvironmentResponseRule[];
}

/**
 * 环境响应编辑器组件
 */
const EnvironmentResponseEditor: React.FC<EnvironmentResponseEditorProps> = ({
  entityId,
  onSave,
  onTest,
  initialRules = []
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [rules, setRules] = useState<EnvironmentResponseRule[]>(initialRules);
  const [editingRule, setEditingRule] = useState<EnvironmentResponseRule | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [presets, setPresets] = useState<EnvironmentResponseRule[]>([]);

  // 加载预设
  useEffect(() => {
    const allPresets = getAllEnvironmentPresets();
    setPresets(allPresets);
  }, []);

  // 初始化规则
  useEffect(() => {
    setRules(initialRules);
  }, [initialRules]);

  /**
   * 创建新规则
   */
  const handleCreateRule = () => {
    const newRule: EnvironmentResponseRule = {
      id: `rule_${Date.now()}`,
      name: t('environment.newRule'),
      description: '',
      responseType: ResponseType.ANIMATION,
      priority: ResponsePriority.MEDIUM,
      conditions: [],
      actions: [],
      enabled: true
    };
    
    setEditingRule(newRule);
    setIsEditing(true);
    form.resetFields();
  };

  /**
   * 编辑规则
   * @param rule 规则
   */
  const handleEditRule = (rule: EnvironmentResponseRule) => {
    setEditingRule({ ...rule });
    setIsEditing(true);
    form.setFieldsValue({
      name: rule.name,
      description: rule.description,
      responseType: rule.responseType,
      priority: rule.priority,
      cooldown: rule.cooldown,
      enabled: rule.enabled
    });
  };

  /**
   * 删除规则
   * @param ruleId 规则ID
   */
  const handleDeleteRule = (ruleId: string) => {
    setRules(rules.filter(rule => rule.id !== ruleId));
    if (editingRule && editingRule.id === ruleId) {
      setEditingRule(null);
      setIsEditing(false);
    }
  };

  /**
   * 复制规则
   * @param rule 规则
   */
  const handleDuplicateRule = (rule: EnvironmentResponseRule) => {
    const newRule: EnvironmentResponseRule = {
      ...rule,
      id: `rule_${Date.now()}`,
      name: `${rule.name} (${t('environment.copy')})`
    };
    setRules([...rules, newRule]);
    message.success(t('environment.ruleDuplicated'));
  };

  /**
   * 测试规则
   * @param rule 规则
   */
  const handleTestRule = (rule: EnvironmentResponseRule) => {
    if (onTest) {
      onTest(rule);
    } else {
      message.info(t('environment.testNotAvailable'));
    }
  };

  /**
   * 保存规则
   */
  const handleSaveRule = () => {
    form.validateFields().then(values => {
      if (!editingRule) return;
      
      const updatedRule: EnvironmentResponseRule = {
        ...editingRule,
        name: values.name,
        description: values.description,
        responseType: values.responseType,
        priority: values.priority,
        cooldown: values.cooldown,
        enabled: values.enabled
      };
      
      // 更新或添加规则
      const ruleIndex = rules.findIndex(r => r.id === updatedRule.id);
      if (ruleIndex !== -1) {
        const updatedRules = [...rules];
        updatedRules[ruleIndex] = updatedRule;
        setRules(updatedRules);
      } else {
        setRules([...rules, updatedRule]);
      }
      
      setEditingRule(null);
      setIsEditing(false);
      message.success(t('environment.ruleSaved'));
    });
  };

  /**
   * 取消编辑
   */
  const handleCancelEdit = () => {
    setEditingRule(null);
    setIsEditing(false);
  };

  /**
   * 添加条件
   * @param type 条件类型
   */
  const handleAddCondition = (type: string) => {
    if (!editingRule) return;
    
    let newCondition: EnvironmentCondition;
    
    switch (type) {
      case 'weather':
        newCondition = {
          type: 'weather',
          params: { weatherType: WeatherType.CLEAR },
          evaluate: (data) => data.weatherType === WeatherType.CLEAR
        };
        break;
      case 'environment':
        newCondition = {
          type: 'environment',
          params: { environmentType: EnvironmentType.OUTDOOR },
          evaluate: (data) => data.environmentType === EnvironmentType.OUTDOOR
        };
        break;
      case 'temperature':
        newCondition = {
          type: 'temperature',
          params: { minTemperature: 0, maxTemperature: 30 },
          evaluate: (data) => data.temperature >= 0 && data.temperature <= 30
        };
        break;
      case 'light':
        newCondition = {
          type: 'light',
          params: { minIntensity: 0.3, maxIntensity: 1.0 },
          evaluate: (data) => data.lightIntensity >= 0.3 && data.lightIntensity <= 1.0
        };
        break;
      case 'timeOfDay':
        newCondition = {
          type: 'timeOfDay',
          params: { minTime: 8, maxTime: 18 },
          evaluate: (data) => data.timeOfDay >= 8 && data.timeOfDay <= 18
        };
        break;
      default:
        newCondition = {
          type: 'custom',
          params: {},
          evaluate: (data) => true
        };
    }
    
    setEditingRule({
      ...editingRule,
      conditions: [...editingRule.conditions, newCondition]
    });
  };

  /**
   * 删除条件
   * @param index 条件索引
   */
  const handleDeleteCondition = (index: number) => {
    if (!editingRule) return;
    
    const updatedConditions = [...editingRule.conditions];
    updatedConditions.splice(index, 1);
    
    setEditingRule({
      ...editingRule,
      conditions: updatedConditions
    });
  };

  /**
   * 添加动作
   * @param type 动作类型
   */
  const handleAddAction = (type: string) => {
    if (!editingRule) return;
    
    let newAction: EnvironmentAction;
    
    switch (type) {
      case ResponseType.ANIMATION:
        newAction = {
          type: ResponseType.ANIMATION,
          params: { animationName: 'idle', blendTime: 0.5 },
          execute: (entity) => {
            const animator = entity.getAnimator();
            if (animator) {
              animator.playAnimation('idle', { blendTime: 0.5, loop: true });
            }
          },
          stop: (entity) => {
            const animator = entity.getAnimator();
            if (animator) {
              animator.stopAnimation('idle', { blendTime: 0.5 });
            }
          }
        };
        break;
      case ResponseType.SOUND:
        newAction = {
          type: ResponseType.SOUND,
          params: { soundName: 'ambient', volume: 1.0 },
          execute: (entity) => {
            const audioSource = entity.getAudioSource();
            if (audioSource) {
              audioSource.play('ambient', { volume: 1.0, loop: false });
            }
          },
          stop: (entity) => {
            const audioSource = entity.getAudioSource();
            if (audioSource) {
              audioSource.stop('ambient');
            }
          }
        };
        break;
      case ResponseType.EFFECT:
        newAction = {
          type: ResponseType.EFFECT,
          params: { effectName: 'particle', duration: 5.0 },
          execute: (entity) => {
            const effectSystem = entity.getEffectSystem();
            if (effectSystem) {
              effectSystem.playEffect('particle', { duration: 5.0 });
            }
          },
          stop: (entity) => {
            const effectSystem = entity.getEffectSystem();
            if (effectSystem) {
              effectSystem.stopEffect('particle');
            }
          }
        };
        break;
      default:
        newAction = {
          type: ResponseType.CUSTOM,
          params: {},
          execute: (entity) => {
            console.log('执行自定义动作', entity);
          }
        };
    }
    
    setEditingRule({
      ...editingRule,
      actions: [...editingRule.actions, newAction]
    });
  };

  /**
   * 删除动作
   * @param index 动作索引
   */
  const handleDeleteAction = (index: number) => {
    if (!editingRule) return;
    
    const updatedActions = [...editingRule.actions];
    updatedActions.splice(index, 1);
    
    setEditingRule({
      ...editingRule,
      actions: updatedActions
    });
  };

  /**
   * 应用预设
   * @param presetId 预设ID
   */
  const handleApplyPreset = (presetId: string) => {
    const preset = presets.find(p => p.id === presetId);
    if (!preset) return;
    
    // 检查是否已存在相同ID的规则
    const existingRuleIndex = rules.findIndex(r => r.id === preset.id);
    if (existingRuleIndex !== -1) {
      // 如果存在，创建一个新ID的副本
      const newPreset = {
        ...preset,
        id: `${preset.id}_${Date.now()}`,
        name: `${preset.name} (${t('environment.copy')})`
      };
      setRules([...rules, newPreset]);
    } else {
      setRules([...rules, preset]);
    }
    
    message.success(t('environment.presetApplied'));
  };

  /**
   * 保存所有规则
   */
  const handleSaveAllRules = () => {
    if (onSave) {
      onSave(rules);
      message.success(t('environment.allRulesSaved'));
    }
  };

  /**
   * 渲染规则列表
   */
  const renderRulesList = () => {
    return (
      <div className="rules-list">
        <div className="rules-header">
          <h3>{t('environment.rules')}</h3>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleCreateRule}
          >
            {t('environment.addRule')}
          </Button>
        </div>
        
        <Collapse>
          {rules.map(rule => (
            <Panel 
              key={rule.id} 
              header={
                <div className="rule-header">
                  <span>{rule.name}</span>
                  <Tag color={rule.enabled ? 'green' : 'red'}>
                    {rule.enabled ? t('environment.enabled') : t('environment.disabled')}
                  </Tag>
                </div>
              }
              extra={
                <Space>
                  <Tooltip title={t('environment.edit')}>
                    <Button 
                      type="text" 
                      icon={<SettingOutlined />} 
                      onClick={(e) => { e.stopPropagation(); handleEditRule(rule); }}
                    />
                  </Tooltip>
                  <Tooltip title={t('environment.test')}>
                    <Button 
                      type="text" 
                      icon={<PlayCircleOutlined />} 
                      onClick={(e) => { e.stopPropagation(); handleTestRule(rule); }}
                    />
                  </Tooltip>
                  <Tooltip title={t('environment.duplicate')}>
                    <Button 
                      type="text" 
                      icon={<CopyOutlined />} 
                      onClick={(e) => { e.stopPropagation(); handleDuplicateRule(rule); }}
                    />
                  </Tooltip>
                  <Tooltip title={t('environment.delete')}>
                    <Button 
                      type="text" 
                      danger 
                      icon={<DeleteOutlined />} 
                      onClick={(e) => { e.stopPropagation(); handleDeleteRule(rule.id); }}
                    />
                  </Tooltip>
                </Space>
              }
            >
              <div className="rule-details">
                {rule.description && (
                  <p className="rule-description">{rule.description}</p>
                )}
                
                <div className="rule-info">
                  <div>
                    <strong>{t('environment.type')}:</strong> {rule.responseType}
                  </div>
                  <div>
                    <strong>{t('environment.priority')}:</strong> {rule.priority}
                  </div>
                  {rule.cooldown && (
                    <div>
                      <strong>{t('environment.cooldown')}:</strong> {rule.cooldown / 1000}s
                    </div>
                  )}
                </div>
                
                <Divider>{t('environment.conditions')}</Divider>
                {rule.conditions.length > 0 ? (
                  <ul className="conditions-list">
                    {rule.conditions.map((condition, index) => (
                      <li key={index}>
                        <Tag color="blue">{condition.type}</Tag>
                        <span>{JSON.stringify(condition.params)}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p>{t('environment.noConditions')}</p>
                )}
                
                <Divider>{t('environment.actions')}</Divider>
                {rule.actions.length > 0 ? (
                  <ul className="actions-list">
                    {rule.actions.map((action, index) => (
                      <li key={index}>
                        <Tag color="green">{action.type}</Tag>
                        <span>{JSON.stringify(action.params)}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p>{t('environment.noActions')}</p>
                )}
              </div>
            </Panel>
          ))}
        </Collapse>
        
        {rules.length === 0 && (
          <div className="empty-rules">
            <p>{t('environment.noRules')}</p>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleCreateRule}
            >
              {t('environment.addRule')}
            </Button>
          </div>
        )}
        
        <div className="rules-footer">
          <Button 
            type="primary" 
            onClick={handleSaveAllRules}
          >
            {t('environment.saveAllRules')}
          </Button>
        </div>
      </div>
    );
  };

  /**
   * 渲染规则编辑器
   */
  const renderRuleEditor = () => {
    if (!editingRule) return null;
    
    return (
      <div className="rule-editor">
        <h3>{isEditing ? t('environment.editRule') : t('environment.newRule')}</h3>
        
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            name: editingRule.name,
            description: editingRule.description,
            responseType: editingRule.responseType,
            priority: editingRule.priority,
            cooldown: editingRule.cooldown,
            enabled: editingRule.enabled
          }}
        >
          <Form.Item
            name="name"
            label={t('environment.ruleName')}
            rules={[{ required: true, message: t('environment.ruleNameRequired') }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="description"
            label={t('environment.ruleDescription')}
          >
            <Input.TextArea rows={2} />
          </Form.Item>
          
          <Form.Item
            name="responseType"
            label={t('environment.responseType')}
            rules={[{ required: true, message: t('environment.responseTypeRequired') }]}
          >
            <Select>
              <Option value={ResponseType.ANIMATION}>{t('environment.responseTypes.animation')}</Option>
              <Option value={ResponseType.EFFECT}>{t('environment.responseTypes.effect')}</Option>
              <Option value={ResponseType.SOUND}>{t('environment.responseTypes.sound')}</Option>
              <Option value={ResponseType.BEHAVIOR}>{t('environment.responseTypes.behavior')}</Option>
              <Option value={ResponseType.CUSTOM}>{t('environment.responseTypes.custom')}</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="priority"
            label={t('environment.priority')}
            rules={[{ required: true, message: t('environment.priorityRequired') }]}
          >
            <Select>
              <Option value={ResponsePriority.LOW}>{t('environment.priorities.low')}</Option>
              <Option value={ResponsePriority.MEDIUM}>{t('environment.priorities.medium')}</Option>
              <Option value={ResponsePriority.HIGH}>{t('environment.priorities.high')}</Option>
              <Option value={ResponsePriority.CRITICAL}>{t('environment.priorities.critical')}</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="cooldown"
            label={t('environment.cooldown')}
            tooltip={t('environment.cooldownTooltip')}
          >
            <InputNumber min={0} step={1000} style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="enabled"
            label={t('environment.enabled')}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Divider>{t('environment.conditions')}</Divider>
          
          <div className="conditions-section">
            <div className="conditions-header">
              <h4>{t('environment.conditions')}</h4>
              <Select
                placeholder={t('environment.addCondition')}
                style={{ width: 200 }}
                onChange={handleAddCondition}
                value={undefined}
              >
                <Option value="weather">{t('environment.conditionTypes.weather')}</Option>
                <Option value="environment">{t('environment.conditionTypes.environment')}</Option>
                <Option value="temperature">{t('environment.conditionTypes.temperature')}</Option>
                <Option value="light">{t('environment.conditionTypes.light')}</Option>
                <Option value="timeOfDay">{t('environment.conditionTypes.timeOfDay')}</Option>
                <Option value="custom">{t('environment.conditionTypes.custom')}</Option>
              </Select>
            </div>
            
            {editingRule.conditions.length > 0 ? (
              <ul className="conditions-list">
                {editingRule.conditions.map((condition, index) => (
                  <li key={index} className="condition-item">
                    <div className="condition-content">
                      <Tag color="blue">{condition.type}</Tag>
                      <span>{JSON.stringify(condition.params)}</span>
                    </div>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteCondition(index)}
                    />
                  </li>
                ))}
              </ul>
            ) : (
              <p>{t('environment.noConditions')}</p>
            )}
          </div>
          
          <Divider>{t('environment.actions')}</Divider>
          
          <div className="actions-section">
            <div className="actions-header">
              <h4>{t('environment.actions')}</h4>
              <Select
                placeholder={t('environment.addAction')}
                style={{ width: 200 }}
                onChange={handleAddAction}
                value={undefined}
              >
                <Option value={ResponseType.ANIMATION}>{t('environment.responseTypes.animation')}</Option>
                <Option value={ResponseType.EFFECT}>{t('environment.responseTypes.effect')}</Option>
                <Option value={ResponseType.SOUND}>{t('environment.responseTypes.sound')}</Option>
                <Option value={ResponseType.BEHAVIOR}>{t('environment.responseTypes.behavior')}</Option>
                <Option value={ResponseType.CUSTOM}>{t('environment.responseTypes.custom')}</Option>
              </Select>
            </div>
            
            {editingRule.actions.length > 0 ? (
              <ul className="actions-list">
                {editingRule.actions.map((action, index) => (
                  <li key={index} className="action-item">
                    <div className="action-content">
                      <Tag color="green">{action.type}</Tag>
                      <span>{JSON.stringify(action.params)}</span>
                    </div>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteAction(index)}
                    />
                  </li>
                ))}
              </ul>
            ) : (
              <p>{t('environment.noActions')}</p>
            )}
          </div>
          
          <div className="form-actions">
            <Button onClick={handleCancelEdit}>{t('environment.cancel')}</Button>
            <Button type="primary" onClick={handleSaveRule}>{t('environment.save')}</Button>
          </div>
        </Form>
      </div>
    );
  };

  /**
   * 渲染预设面板
   */
  const renderPresets = () => {
    return (
      <div className="presets-panel">
        <h3>{t('environment.presets')}</h3>
        
        <div className="presets-list">
          {presets.map(preset => (
            <Card
              key={preset.id}
              size="small"
              title={preset.name}
              extra={
                <Button
                  type="primary"
                  size="small"
                  onClick={() => handleApplyPreset(preset.id)}
                >
                  {t('environment.apply')}
                </Button>
              }
              style={{ marginBottom: 10 }}
            >
              <p>{preset.description}</p>
              <div>
                <Tag color="blue">{t('environment.type')}: {preset.responseType}</Tag>
                <Tag color="green">{t('environment.priority')}: {preset.priority}</Tag>
                <Tag color="orange">{t('environment.conditions')}: {preset.conditions.length}</Tag>
                <Tag color="purple">{t('environment.actions')}: {preset.actions.length}</Tag>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="environment-response-editor">
      {isEditing ? (
        renderRuleEditor()
      ) : (
        <div className="editor-content">
          <div className="rules-section">
            {renderRulesList()}
          </div>
          <div className="presets-section">
            {renderPresets()}
          </div>
        </div>
      )}
    </div>
  );
};

export default EnvironmentResponseEditor;
