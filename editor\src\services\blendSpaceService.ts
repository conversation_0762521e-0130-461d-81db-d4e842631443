/**
 * 混合空间服务
 * 用于管理混合空间数据
 */
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 混合空间事件类型
 */
export enum BlendSpaceEventType {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  NODE_ADDED = 'node_added',
  NODE_UPDATED = 'node_updated',
  NODE_REMOVED = 'node_removed',
  POSITION_CHANGED = 'position_changed',
  ERROR = 'error'
}

/**
 * 混合空间节点
 */
export interface BlendSpaceNode {
  id: string;
  clipName: string;
  position: number | { x: number; y: number };
  weight: number;
}

/**
 * 1D混合空间配置
 */
export interface BlendSpace1DConfig {
  minValue: number;
  maxValue: number;
  normalizeInput: boolean;
  useSmoothing: boolean;
  smoothingFactor: number;
  enableExtrapolation: boolean;
}

/**
 * 2D混合空间配置
 */
export interface BlendSpace2DConfig {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
  normalizeInput: boolean;
  useTriangulation: boolean;
  xLabel?: string;
  yLabel?: string;
}

/**
 * 混合空间数据
 */
export interface BlendSpaceData {
  id: string;
  name: string;
  description?: string;
  type: '1D' | '2D';
  config: BlendSpace1DConfig | BlendSpace2DConfig;
  nodes: BlendSpaceNode[];
  position?: number | { x: number; y: number };
  nodeCount: number;
}

/**
 * 混合空间服务类
 */
class BlendSpaceService {
  private eventEmitter: EventEmitter;
  private blendSpaces: Map<string, Map<string, BlendSpaceData>>;

  /**
   * 构造函数
   */
  constructor() {
    this.eventEmitter = new EventEmitter();
    this.blendSpaces = new Map();
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器函数
   */
  public on(event: BlendSpaceEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器函数
   */
  public off(event: BlendSpaceEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 获取实体的所有混合空间
   * @param entityId 实体ID
   * @returns 混合空间列表
   */
  public async getBlendSpaces(entityId: string): Promise<BlendSpaceData[]> {
    try {
      // 如果是服务器模式，则从API获取
      if (process.env.NODE_ENV === 'production') {
        const response = await axios.get(`/api/entities/${entityId}/blend-spaces`);
        return response.data;
      }

      // 否则，从本地缓存获取
      if (!this.blendSpaces.has(entityId)) {
        return [];
      }

      return Array.from(this.blendSpaces.get(entityId)!.values());
    } catch (error) {
      console.error('获取混合空间失败:', error);
      this.eventEmitter.emit(BlendSpaceEventType.ERROR, error);
      throw error;
    }
  }

  /**
   * 获取混合空间
   * @param entityId 实体ID
   * @param blendSpaceId 混合空间ID
   * @returns 混合空间数据
   */
  public async getBlendSpace(entityId: string, blendSpaceId: string): Promise<BlendSpaceData | null> {
    try {
      // 如果是服务器模式，则从API获取
      if (process.env.NODE_ENV === 'production') {
        const response = await axios.get(`/api/entities/${entityId}/blend-spaces/${blendSpaceId}`);
        return response.data;
      }

      // 否则，从本地缓存获取
      if (!this.blendSpaces.has(entityId) || !this.blendSpaces.get(entityId)!.has(blendSpaceId)) {
        return null;
      }

      return this.blendSpaces.get(entityId)!.get(blendSpaceId)!;
    } catch (error) {
      console.error('获取混合空间失败:', error);
      this.eventEmitter.emit(BlendSpaceEventType.ERROR, error);
      throw error;
    }
  }

  /**
   * 创建1D混合空间
   * @param entityId 实体ID
   * @param name 名称
   * @param description 描述
   * @param config 配置
   * @returns 创建的混合空间
   */
  public async createBlendSpace1D(
    entityId: string,
    name: string,
    description: string = '',
    config: Partial<BlendSpace1DConfig> = {}
  ): Promise<BlendSpaceData> {
    try {
      const blendSpaceId = uuidv4();

      const blendSpace: BlendSpaceData = {
        id: blendSpaceId,
        name,
        description,
        type: '1D',
        config: {
          minValue: config.minValue !== undefined ? config.minValue : 0,
          maxValue: config.maxValue !== undefined ? config.maxValue : 1,
          normalizeInput: config.normalizeInput !== undefined ? config.normalizeInput : true,
          useSmoothing: config.useSmoothing !== undefined ? config.useSmoothing : true,
          smoothingFactor: config.smoothingFactor !== undefined ? config.smoothingFactor : 0.3,
          enableExtrapolation: config.enableExtrapolation !== undefined ? config.enableExtrapolation : false
        },
        nodes: [],
        position: 0,
        nodeCount: 0
      };

      // 如果是服务器模式，则保存到API
      if (process.env.NODE_ENV === 'production') {
        const response = await axios.post(`/api/entities/${entityId}/blend-spaces`, blendSpace);
        this.eventEmitter.emit(BlendSpaceEventType.CREATED, response.data);
        return response.data;
      }

      // 否则，保存到本地缓存
      if (!this.blendSpaces.has(entityId)) {
        this.blendSpaces.set(entityId, new Map());
      }

      this.blendSpaces.get(entityId)!.set(blendSpaceId, blendSpace);
      this.eventEmitter.emit(BlendSpaceEventType.CREATED, blendSpace);

      return blendSpace;
    } catch (error) {
      console.error('创建混合空间失败:', error);
      this.eventEmitter.emit(BlendSpaceEventType.ERROR, error);
      throw error;
    }
  }

  /**
   * 创建2D混合空间
   * @param entityId 实体ID
   * @param name 名称
   * @param description 描述
   * @param config 配置
   * @returns 创建的混合空间
   */
  public async createBlendSpace2D(
    entityId: string,
    name: string,
    description: string = '',
    config: Partial<BlendSpace2DConfig> = {}
  ): Promise<BlendSpaceData> {
    try {
      const blendSpaceId = uuidv4();

      const blendSpace: BlendSpaceData = {
        id: blendSpaceId,
        name,
        description,
        type: '2D',
        config: {
          minX: config.minX !== undefined ? config.minX : -1,
          maxX: config.maxX !== undefined ? config.maxX : 1,
          minY: config.minY !== undefined ? config.minY : -1,
          maxY: config.maxY !== undefined ? config.maxY : 1,
          normalizeInput: config.normalizeInput !== undefined ? config.normalizeInput : true,
          useTriangulation: config.useTriangulation !== undefined ? config.useTriangulation : true,
          xLabel: config.xLabel || 'X',
          yLabel: config.yLabel || 'Y'
        },
        nodes: [],
        position: { x: 0, y: 0 },
        nodeCount: 0
      };

      // 如果是服务器模式，则保存到API
      if (process.env.NODE_ENV === 'production') {
        const response = await axios.post(`/api/entities/${entityId}/blend-spaces`, blendSpace);
        this.eventEmitter.emit(BlendSpaceEventType.CREATED, response.data);
        return response.data;
      }

      // 否则，保存到本地缓存
      if (!this.blendSpaces.has(entityId)) {
        this.blendSpaces.set(entityId, new Map());
      }

      this.blendSpaces.get(entityId)!.set(blendSpaceId, blendSpace);
      this.eventEmitter.emit(BlendSpaceEventType.CREATED, blendSpace);

      return blendSpace;
    } catch (error) {
      console.error('创建混合空间失败:', error);
      this.eventEmitter.emit(BlendSpaceEventType.ERROR, error);
      throw error;
    }
  }

  /**
   * 更新混合空间
   * @param entityId 实体ID
   * @param blendSpaceId 混合空间ID
   * @param data 更新数据
   * @returns 更新后的混合空间
   */
  public async updateBlendSpace(
    entityId: string,
    blendSpaceId: string,
    data: Partial<Omit<BlendSpaceData, 'id' | 'type' | 'nodes' | 'nodeCount'>>
  ): Promise<BlendSpaceData | null> {
    try {
      // 如果是服务器模式，则从API更新
      if (process.env.NODE_ENV === 'production') {
        const response = await axios.patch(`/api/entities/${entityId}/blend-spaces/${blendSpaceId}`, data);
        this.eventEmitter.emit(BlendSpaceEventType.UPDATED, response.data);
        return response.data;
      }

      // 否则，从本地缓存更新
      if (!this.blendSpaces.has(entityId) || !this.blendSpaces.get(entityId)!.has(blendSpaceId)) {
        return null;
      }

      const blendSpace = this.blendSpaces.get(entityId)!.get(blendSpaceId)!;

      // 更新数据
      if (data.name !== undefined) blendSpace.name = data.name;
      if (data.description !== undefined) blendSpace.description = data.description;
      if (data.config !== undefined) {
        blendSpace.config = {
          ...blendSpace.config,
          ...data.config
        };
      }

      this.blendSpaces.get(entityId)!.set(blendSpaceId, blendSpace);
      this.eventEmitter.emit(BlendSpaceEventType.UPDATED, blendSpace);

      return blendSpace;
    } catch (error) {
      console.error('更新混合空间失败:', error);
      this.eventEmitter.emit(BlendSpaceEventType.ERROR, error);
      throw error;
    }
  }

  /**
   * 删除混合空间
   * @param entityId 实体ID
   * @param blendSpaceId 混合空间ID
   * @returns 是否成功
   */
  public async deleteBlendSpace(entityId: string, blendSpaceId: string): Promise<boolean> {
    try {
      // 如果是服务器模式，则从API删除
      if (process.env.NODE_ENV === 'production') {
        await axios.delete(`/api/entities/${entityId}/blend-spaces/${blendSpaceId}`);
        this.eventEmitter.emit(BlendSpaceEventType.DELETED, { entityId, blendSpaceId });
        return true;
      }

      // 否则，从本地缓存删除
      if (!this.blendSpaces.has(entityId) || !this.blendSpaces.get(entityId)!.has(blendSpaceId)) {
        return false;
      }

      this.blendSpaces.get(entityId)!.delete(blendSpaceId);
      this.eventEmitter.emit(BlendSpaceEventType.DELETED, { entityId, blendSpaceId });

      return true;
    } catch (error) {
      console.error('删除混合空间失败:', error);
      this.eventEmitter.emit(BlendSpaceEventType.ERROR, error);
      throw error;
    }
  }

  /**
   * 添加混合空间节点
   * @param entityId 实体ID
   * @param blendSpaceId 混合空间ID
   * @param data 节点数据
   * @returns 添加的节点
   */
  public async addBlendSpaceNode(
    entityId: string,
    blendSpaceId: string,
    data: Omit<BlendSpaceNode, 'id' | 'weight'>
  ): Promise<BlendSpaceNode | null> {
    try {
      const nodeId = uuidv4();

      const node: BlendSpaceNode = {
        id: nodeId,
        clipName: data.clipName,
        position: data.position,
        weight: 0
      };

      // 如果是服务器模式，则从API添加
      if (process.env.NODE_ENV === 'production') {
        const response = await axios.post(`/api/entities/${entityId}/blend-spaces/${blendSpaceId}/nodes`, node);
        this.eventEmitter.emit(BlendSpaceEventType.NODE_ADDED, { entityId, blendSpaceId, node: response.data });
        return response.data;
      }

      // 否则，从本地缓存添加
      if (!this.blendSpaces.has(entityId) || !this.blendSpaces.get(entityId)!.has(blendSpaceId)) {
        return null;
      }

      const blendSpace = this.blendSpaces.get(entityId)!.get(blendSpaceId)!;
      blendSpace.nodes.push(node);
      blendSpace.nodeCount = blendSpace.nodes.length;

      this.blendSpaces.get(entityId)!.set(blendSpaceId, blendSpace);
      this.eventEmitter.emit(BlendSpaceEventType.NODE_ADDED, { entityId, blendSpaceId, node });

      // 更新权重
      this.updateNodeWeights(entityId, blendSpaceId);

      return node;
    } catch (error) {
      console.error('添加混合空间节点失败:', error);
      this.eventEmitter.emit(BlendSpaceEventType.ERROR, error);
      throw error;
    }
  }

  /**
   * 更新混合空间节点
   * @param entityId 实体ID
   * @param blendSpaceId 混合空间ID
   * @param nodeId 节点ID
   * @param data 更新数据
   * @returns 更新后的节点
   */
  public async updateBlendSpaceNode(
    entityId: string,
    blendSpaceId: string,
    nodeId: string,
    data: Partial<Omit<BlendSpaceNode, 'id' | 'weight'>>
  ): Promise<BlendSpaceNode | null> {
    try {
      // 如果是服务器模式，则从API更新
      if (process.env.NODE_ENV === 'production') {
        const response = await axios.patch(`/api/entities/${entityId}/blend-spaces/${blendSpaceId}/nodes/${nodeId}`, data);
        this.eventEmitter.emit(BlendSpaceEventType.NODE_UPDATED, { entityId, blendSpaceId, node: response.data });
        return response.data;
      }

      // 否则，从本地缓存更新
      if (!this.blendSpaces.has(entityId) || !this.blendSpaces.get(entityId)!.has(blendSpaceId)) {
        return null;
      }

      const blendSpace = this.blendSpaces.get(entityId)!.get(blendSpaceId)!;
      const nodeIndex = blendSpace.nodes.findIndex(node => node.id === nodeId);

      if (nodeIndex === -1) {
        return null;
      }

      // 更新数据
      if (data.clipName !== undefined) blendSpace.nodes[nodeIndex].clipName = data.clipName;
      if (data.position !== undefined) blendSpace.nodes[nodeIndex].position = data.position;

      this.blendSpaces.get(entityId)!.set(blendSpaceId, blendSpace);
      this.eventEmitter.emit(BlendSpaceEventType.NODE_UPDATED, { entityId, blendSpaceId, node: blendSpace.nodes[nodeIndex] });

      // 更新权重
      this.updateNodeWeights(entityId, blendSpaceId);

      return blendSpace.nodes[nodeIndex];
    } catch (error) {
      console.error('更新混合空间节点失败:', error);
      this.eventEmitter.emit(BlendSpaceEventType.ERROR, error);
      throw error;
    }
  }

  /**
   * 删除混合空间节点
   * @param entityId 实体ID
   * @param blendSpaceId 混合空间ID
   * @param nodeId 节点ID
   * @returns 是否成功
   */
  public async removeBlendSpaceNode(
    entityId: string,
    blendSpaceId: string,
    nodeId: string
  ): Promise<boolean> {
    try {
      // 如果是服务器模式，则从API删除
      if (process.env.NODE_ENV === 'production') {
        await axios.delete(`/api/entities/${entityId}/blend-spaces/${blendSpaceId}/nodes/${nodeId}`);
        this.eventEmitter.emit(BlendSpaceEventType.NODE_REMOVED, { entityId, blendSpaceId, nodeId });
        return true;
      }

      // 否则，从本地缓存删除
      if (!this.blendSpaces.has(entityId) || !this.blendSpaces.get(entityId)!.has(blendSpaceId)) {
        return false;
      }

      const blendSpace = this.blendSpaces.get(entityId)!.get(blendSpaceId)!;
      const nodeIndex = blendSpace.nodes.findIndex(node => node.id === nodeId);

      if (nodeIndex === -1) {
        return false;
      }

      blendSpace.nodes.splice(nodeIndex, 1);
      blendSpace.nodeCount = blendSpace.nodes.length;

      this.blendSpaces.get(entityId)!.set(blendSpaceId, blendSpace);
      this.eventEmitter.emit(BlendSpaceEventType.NODE_REMOVED, { entityId, blendSpaceId, nodeId });

      // 更新权重
      this.updateNodeWeights(entityId, blendSpaceId);

      return true;
    } catch (error) {
      console.error('删除混合空间节点失败:', error);
      this.eventEmitter.emit(BlendSpaceEventType.ERROR, error);
      throw error;
    }
  }

  /**
   * 设置混合空间位置
   * @param entityId 实体ID
   * @param blendSpaceId 混合空间ID
   * @param position 位置
   * @returns 更新后的混合空间
   */
  public async setBlendSpacePosition(
    entityId: string,
    blendSpaceId: string,
    position: number | { x: number; y: number }
  ): Promise<BlendSpaceData | null> {
    try {
      // 如果是服务器模式，则从API更新
      if (process.env.NODE_ENV === 'production') {
        const response = await axios.patch(`/api/entities/${entityId}/blend-spaces/${blendSpaceId}/position`, { position });
        this.eventEmitter.emit(BlendSpaceEventType.POSITION_CHANGED, { entityId, blendSpaceId, position });
        return response.data;
      }

      // 否则，从本地缓存更新
      if (!this.blendSpaces.has(entityId) || !this.blendSpaces.get(entityId)!.has(blendSpaceId)) {
        return null;
      }

      const blendSpace = this.blendSpaces.get(entityId)!.get(blendSpaceId)!;
      blendSpace.position = position;

      this.blendSpaces.get(entityId)!.set(blendSpaceId, blendSpace);
      this.eventEmitter.emit(BlendSpaceEventType.POSITION_CHANGED, { entityId, blendSpaceId, position });

      // 更新权重
      this.updateNodeWeights(entityId, blendSpaceId);

      return blendSpace;
    } catch (error) {
      console.error('设置混合空间位置失败:', error);
      this.eventEmitter.emit(BlendSpaceEventType.ERROR, error);
      throw error;
    }
  }

  /**
   * 获取可用的动画片段
   * @param entityId 实体ID
   * @returns 动画片段名称列表
   */
  public async getAvailableAnimationClips(entityId: string): Promise<string[]> {
    try {
      // 如果是服务器模式，则从API获取
      if (process.env.NODE_ENV === 'production') {
        const response = await axios.get(`/api/entities/${entityId}/animation-clips`);
        return response.data;
      }

      // 否则，返回模拟数据
      return [
        'idle',
        'walk',
        'run',
        'jump',
        'attack',
        'death',
        'dance',
        'wave',
        'sit',
        'stand'
      ];
    } catch (error) {
      console.error('获取动画片段失败:', error);
      this.eventEmitter.emit(BlendSpaceEventType.ERROR, error);
      throw error;
    }
  }

  /**
   * 更新节点权重
   * @param entityId 实体ID
   * @param blendSpaceId 混合空间ID
   */
  private updateNodeWeights(entityId: string, blendSpaceId: string): void {
    if (!this.blendSpaces.has(entityId) || !this.blendSpaces.get(entityId)!.has(blendSpaceId)) {
      return;
    }

    const blendSpace = this.blendSpaces.get(entityId)!.get(blendSpaceId)!;

    if (blendSpace.nodes.length === 0) {
      return;
    }

    if (blendSpace.type === '1D') {
      this.update1DNodeWeights(blendSpace);
    } else if (blendSpace.type === '2D') {
      this.update2DNodeWeights(blendSpace);
    }
  }

  /**
   * 更新1D混合空间节点权重
   * @param blendSpace 混合空间
   */
  private update1DNodeWeights(blendSpace: BlendSpaceData): void {
    const position = blendSpace.position as number;
    const config = blendSpace.config as BlendSpace1DConfig;

    // 重置所有节点权重
    blendSpace.nodes.forEach(node => {
      node.weight = 0;
    });

    // 如果只有一个节点，则权重为1
    if (blendSpace.nodes.length === 1) {
      blendSpace.nodes[0].weight = 1;
      return;
    }

    // 按位置排序节点
    const sortedNodes = [...blendSpace.nodes].sort((a, b) => {
      return (a.position as number) - (b.position as number);
    });

    // 找到位置两侧的节点
    let leftNode = sortedNodes[0];
    let rightNode = sortedNodes[sortedNodes.length - 1];

    for (let i = 0; i < sortedNodes.length - 1; i++) {
      const current = sortedNodes[i];
      const next = sortedNodes[i + 1];

      if ((current.position as number) <= position && (next.position as number) >= position) {
        leftNode = current;
        rightNode = next;
        break;
      }
    }

    // 计算权重
    const range = (rightNode.position as number) - (leftNode.position as number);

    if (range <= 0.0001) {
      // 如果范围太小，则平均分配权重
      leftNode.weight = 0.5;
      rightNode.weight = 0.5;
    } else {
      // 线性插值
      const t = (position - (leftNode.position as number)) / range;
      leftNode.weight = 1 - t;
      rightNode.weight = t;
    }
  }

  /**
   * 更新2D混合空间节点权重
   * @param blendSpace 混合空间
   */
  private update2DNodeWeights(blendSpace: BlendSpaceData): void {
    const position = blendSpace.position as { x: number; y: number };
    const config = blendSpace.config as BlendSpace2DConfig;

    // 重置所有节点权重
    blendSpace.nodes.forEach(node => {
      node.weight = 0;
    });

    // 如果只有一个节点，则权重为1
    if (blendSpace.nodes.length === 1) {
      blendSpace.nodes[0].weight = 1;
      return;
    }

    // 如果使用三角形混合且有足够的节点
    if (config.useTriangulation && blendSpace.nodes.length >= 3) {
      this.updateTriangulationWeights(blendSpace);
    } else {
      this.updateDistanceWeights(blendSpace);
    }
  }

  /**
   * 使用三角形混合更新权重
   * @param blendSpace 混合空间
   */
  private updateTriangulationWeights(blendSpace: BlendSpaceData): void {
    // 这里简化实现，实际应该使用Delaunay三角剖分
    // 找到最近的三个节点
    const position = blendSpace.position as { x: number; y: number };

    // 计算每个节点到当前位置的距离
    const distances = blendSpace.nodes.map((node, index) => {
      const nodePos = node.position as { x: number; y: number };
      const dx = nodePos.x - position.x;
      const dy = nodePos.y - position.y;
      return {
        index,
        distance: Math.sqrt(dx * dx + dy * dy)
      };
    });

    // 按距离排序
    distances.sort((a, b) => a.distance - b.distance);

    // 取最近的三个节点
    const nearestIndices = distances.slice(0, 3).map(d => d.index);

    // 计算权重（简化为基于距离的反比例）
    let totalWeight = 0;

    for (let i = 0; i < nearestIndices.length; i++) {
      const index = nearestIndices[i];
      const distance = distances[i].distance;

      // 避免除以零
      if (distance < 0.0001) {
        blendSpace.nodes[index].weight = 1;
        return;
      }

      blendSpace.nodes[index].weight = 1 / distance;
      totalWeight += blendSpace.nodes[index].weight;
    }

    // 归一化权重
    for (let i = 0; i < nearestIndices.length; i++) {
      const index = nearestIndices[i];
      blendSpace.nodes[index].weight /= totalWeight;
    }
  }

  /**
   * 使用距离权重更新权重
   * @param blendSpace 混合空间
   */
  private updateDistanceWeights(blendSpace: BlendSpaceData): void {
    const position = blendSpace.position as { x: number; y: number };

    // 计算每个节点到当前位置的距离
    let totalWeight = 0;

    blendSpace.nodes.forEach(node => {
      const nodePos = node.position as { x: number; y: number };
      const dx = nodePos.x - position.x;
      const dy = nodePos.y - position.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      // 避免除以零
      if (distance < 0.0001) {
        node.weight = 1000;
      } else {
        node.weight = 1 / (distance * distance);
      }

      totalWeight += node.weight;
    });

    // 归一化权重
    if (totalWeight > 0) {
      blendSpace.nodes.forEach(node => {
        node.weight /= totalWeight;
      });
    }
  }
}

// 导出单例
export const blendSpaceService = new BlendSpaceService();
