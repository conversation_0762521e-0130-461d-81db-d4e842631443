/**
 * 场景状态切片
 */
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import sceneService from '../../services/SceneService';

// 定义实体接口
export interface Entity {
  id: string;
  name: string;
  type: string;
  parentId: string | null;
  visible: boolean;
  locked: boolean;
  transform: {
    position: [number, number, number];
    rotation: [number, number, number];
    scale: [number, number, number];
  };
  components: Record<string, any>;
}

// 定义场景状态
interface SceneState {
  entities: Entity[];
  selectedEntityId: string | null;
  activeSceneId: string | null;
  loading: boolean;
  error: string | null;
  undoStack: Entity[][];
  redoStack: Entity[][];
}

// 初始状态
const initialState: SceneState = {
  entities: [],
  selectedEntityId: null,
  activeSceneId: null,
  loading: false,
  error: null,
  undoStack: [],
  redoStack: []};

// 异步操作：加载场景
export const loadScene = createAsyncThunk(
  'scene/loadScene',
  async ({ projectId, sceneId }: { projectId: string; sceneId: string }, { rejectWithValue }) => {
    try {
      const response = await sceneService.loadScene(projectId, sceneId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：保存场景
export const saveScene = createAsyncThunk(
  'scene/saveScene',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { scene: SceneState };
      const { activeSceneId } = state.scene;

      if (!activeSceneId) {
        return rejectWithValue('没有活动场景');
      }

      const response = await sceneService.saveScene();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 创建场景切片
const sceneSlice = createSlice({
  name: 'scene',
  initialState,
  reducers: {
    // 选择实体
    selectEntity: (state, action: PayloadAction<string | null>) => {
      state.selectedEntityId = action.payload;
    },
    
    // 添加实体
    addEntity: (state, action: PayloadAction<Entity>) => {
      // 保存当前状态到撤销栈
      state.undoStack.push([...state.entities]);
      state.redoStack = [];
      
      state.entities.push(action.payload);
    },
    
    // 更新实体
    updateEntity: (state, action: PayloadAction<{ id: string; changes: Partial<Entity> }>) => {
      const { id, changes } = action.payload;
      const entityIndex = state.entities.findIndex(entity => entity.id === id);
      
      if (entityIndex !== -1) {
        // 保存当前状态到撤销栈
        state.undoStack.push([...state.entities]);
        state.redoStack = [];
        
        state.entities[entityIndex] = {
          ...state.entities[entityIndex],
          ...changes,
          // 处理嵌套对象
          transform: changes.transform
            ? { ...state.entities[entityIndex].transform, ...changes.transform }
            : state.entities[entityIndex].transform,
          components: changes.components
            ? { ...state.entities[entityIndex].components, ...changes.components }
            : state.entities[entityIndex].components};
      }
    },
    
    // 删除实体
    removeEntity: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      
      // 保存当前状态到撤销栈
      state.undoStack.push([...state.entities]);
      state.redoStack = [];
      
      // 递归删除子实体
      const removeEntityAndChildren = (entityId: string) => {
        // 找到所有子实体
        const childIds = state.entities
          .filter(entity => entity.parentId === entityId)
          .map(entity => entity.id);
        
        // 递归删除子实体
        childIds.forEach(childId => removeEntityAndChildren(childId));
        
        // 删除当前实体
        state.entities = state.entities.filter(entity => entity.id !== entityId);
      };
      
      removeEntityAndChildren(id);
      
      // 如果删除的是当前选中的实体，清除选择
      if (state.selectedEntityId === id) {
        state.selectedEntityId = null;
      }
    },
    
    // 撤销操作
    undo: (state) => {
      if (state.undoStack.length > 0) {
        // 保存当前状态到重做栈
        state.redoStack.push([...state.entities]);
        
        // 恢复上一个状态
        state.entities = state.undoStack.pop() || [];
      }
    },
    
    // 重做操作
    redo: (state) => {
      if (state.redoStack.length > 0) {
        // 保存当前状态到撤销栈
        state.undoStack.push([...state.entities]);
        
        // 恢复下一个状态
        state.entities = state.redoStack.pop() || [];
      }
    },
    
    // 清空场景
    clearScene: (state) => {
      // 保存当前状态到撤销栈
      if (state.entities.length > 0) {
        state.undoStack.push([...state.entities]);
        state.redoStack = [];
      }
      
      state.entities = [];
      state.selectedEntityId = null;
    }},
  extraReducers: (builder) => {
    builder
      // 加载场景
      .addCase(loadScene.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadScene.fulfilled, (state, action) => {
        state.loading = false;
        state.entities = action.payload.entities;
        state.activeSceneId = action.payload.id;
        state.undoStack = [];
        state.redoStack = [];
      })
      .addCase(loadScene.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 保存场景
      .addCase(saveScene.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(saveScene.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(saveScene.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }});

export const {
  selectEntity,
  addEntity,
  updateEntity,
  removeEntity,
  undo,
  redo,
  clearScene} = sceneSlice.actions;

export default sceneSlice.reducer;
