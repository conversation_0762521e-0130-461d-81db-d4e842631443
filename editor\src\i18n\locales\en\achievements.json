{"achievements": {"title": "Achievements", "unlocked": "Unlocked", "locked": "Locked", "progress": "Progress", "description": "Description", "reward": "<PERSON><PERSON>", "category": "Category", "rarity": {"common": "Common", "uncommon": "Uncommon", "rare": "Rare", "epic": "Epic", "legendary": "Legendary"}, "dateUnlocked": "Date Unlocked", "viewAll": "View All", "filter": "Filter", "search": "Search Achievements", "categories": {"general": "General", "modeling": "Modeling", "animation": "Animation", "materials": "Materials", "physics": "Physics", "scripting": "Scripting", "collaboration": "Collaboration", "optimization": "Optimization", "creativity": "Creativity", "mastery": "Mastery"}, "list": {"firstSteps": {"title": "First Steps", "description": "Complete your first tutorial", "category": "general", "rarity": "common"}, "sceneCreator": {"title": "Scene Creator", "description": "Create your first scene", "category": "general", "rarity": "common"}, "objectMaster": {"title": "Object Master", "description": "Place 100 objects in a scene", "category": "modeling", "rarity": "uncommon"}, "animationNovice": {"title": "Animation Novice", "description": "Create your first animation", "category": "animation", "rarity": "common"}, "keyframeKing": {"title": "Keyframe King", "description": "Set 1000 keyframes", "category": "animation", "rarity": "rare"}, "materialArtist": {"title": "Material Artist", "description": "Create 10 different materials", "category": "materials", "rarity": "uncommon"}, "physicsExplorer": {"title": "Physics Explorer", "description": "Create a simulation using the physics system", "category": "physics", "rarity": "uncommon"}, "scriptWizard": {"title": "<PERSON><PERSON>t Wizard", "description": "Write your first script", "category": "scripting", "rarity": "uncommon"}, "collaborator": {"title": "Collaborator", "description": "Collaborate with others on a project", "category": "collaboration", "rarity": "uncommon"}, "optimizer": {"title": "Optimization Expert", "description": "Optimize scene performance by 50%", "category": "optimization", "rarity": "rare"}, "creativeGenius": {"title": "Creative Genius", "description": "Receive over 100 community likes", "category": "creativity", "rarity": "epic"}, "masterBuilder": {"title": "Master Builder", "description": "Complete all modeling tutorials", "category": "mastery", "rarity": "epic"}, "animationMaster": {"title": "Animation Master", "description": "Complete all animation tutorials", "category": "mastery", "rarity": "epic"}, "perfectionist": {"title": "Perfectionist", "description": "Complete all tutorials with perfect scores", "category": "mastery", "rarity": "legendary"}, "speedRunner": {"title": "Speed Runner", "description": "Complete basic tutorials in under 30 minutes", "category": "general", "rarity": "rare"}, "nightOwl": {"title": "Night Owl", "description": "Work between 2 AM and 6 AM", "category": "general", "rarity": "uncommon"}, "earlyBird": {"title": "Early Bird", "description": "Start working before 6 AM", "category": "general", "rarity": "uncommon"}, "marathoner": {"title": "Marathoner", "description": "Work continuously for 8 hours", "category": "general", "rarity": "rare"}, "socialButterfly": {"title": "Social Butterfly", "description": "Post 50 comments in the forum", "category": "collaboration", "rarity": "uncommon"}, "helpfulHand": {"title": "Helpful Hand", "description": "Help 10 new users", "category": "collaboration", "rarity": "rare"}}, "notifications": {"unlocked": "Achievement Unlocked!", "progress": "Achievement Progress Updated", "newAchievement": "New Achievement Available", "congratulations": "Congratulations!"}, "stats": {"total": "Total Achievements", "unlocked": "Unlocked", "completion": "Completion", "points": "Achievement Points", "rank": "Rank", "nextRank": "Next Rank"}, "sharing": {"share": "Share Achievement", "shareText": "I unlocked the achievement: {achievement} in DL Engine Editor!", "copyLink": "Copy Link", "shareOnSocial": "Share on Social Media"}, "errors": {"loadFailed": "Failed to load achievements", "unlockFailed": "Failed to unlock achievement", "syncFailed": "Failed to sync achievements"}, "messages": {"achievementUnlocked": "Achievement unlocked: {achievement}", "progressUpdated": "Achievement progress updated", "allAchievementsUnlocked": "All achievements unlocked!", "rankUp": "Rank up!"}}}