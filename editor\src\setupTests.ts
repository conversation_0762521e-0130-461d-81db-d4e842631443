/**
 * 测试设置文件
 * 用于设置测试环境和全局模拟
 */
import '@testing-library/jest-dom';

// 模拟localStorage
const localStorageMock = {
  getItem: jest.fn<any, any>(),
  setItem: jest.fn<any, any>(),
  removeItem: jest.fn<any, any>(),
  clear: jest.fn<any, any>()};
global.localStorage = localStorageMock as any;

// 模拟sessionStorage
const sessionStorageMock = {
  getItem: jest.fn<any, any>(),
  setItem: jest.fn<any, any>(),
  removeItem: jest.fn<any, any>(),
  clear: jest.fn<any, any>()};
global.sessionStorage = sessionStorageMock as any;

// 模拟matchMedia
global.matchMedia = jest.fn<any, any>().mockImplementation(query => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: jest.fn<any, any>(),
  removeListener: jest.fn<any, any>(),
  addEventListener: jest.fn<any, any>(),
  removeEventListener: jest.fn<any, any>(),
  dispatchEvent: jest.fn<any, any>()}));

// 模拟ResizeObserver
global.ResizeObserver = jest.fn<any, any>().mockImplementation(() => ({
  observe: jest.fn<any, any>(),
  unobserve: jest.fn<any, any>(),
  disconnect: jest.fn<any, any>()}));

// 模拟IntersectionObserver
global.IntersectionObserver = jest.fn<any, any>().mockImplementation(() => ({
  observe: jest.fn<any, any>(),
  unobserve: jest.fn<any, any>(),
  disconnect: jest.fn<any, any>()}));

// 模拟requestAnimationFrame
global.requestAnimationFrame = jest.fn(callback => {
  return setTimeout(callback, 0);
});

// 模拟cancelAnimationFrame
global.cancelAnimationFrame = jest.fn(id => {
  clearTimeout(id);
});

// 模拟URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn<any, any>();

// 模拟控制台方法，防止测试输出过多日志
global.console = {
  ...console,
  log: jest.fn<any, any>(),
  error: jest.fn<any, any>(),
  warn: jest.fn<any, any>(),
  info: jest.fn<any, any>(),
  debug: jest.fn<any, any>()};

// 清除所有模拟的实现和调用历史
beforeEach(() => {
  jest.clearAllMocks();
});
