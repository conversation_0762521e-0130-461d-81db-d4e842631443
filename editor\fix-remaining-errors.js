#!/usr/bin/env node

/**
 * 修复剩余的TypeScript错误
 */

import fs from 'fs';
import path from 'path';

function fixRemainingErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    // 修复未使用的导入
    const unusedImports = [
      'fireEvent',
      'React',
      'useEffect',
      'useState',
      'useCallback',
      'useMemo'
    ];
    
    unusedImports.forEach(importName => {
      // 检查是否导入了但未使用
      const importRegex = new RegExp(`import\\s*{[^}]*\\b${importName}\\b[^}]*}\\s*from`, 'g');
      const usageRegex = new RegExp(`\\b${importName}\\b`, 'g');
      
      if (importRegex.test(content)) {
        const matches = content.match(usageRegex) || [];
        if (matches.length <= 1) { // 只有导入语句中的一次匹配
          // 从导入中移除
          content = content.replace(
            new RegExp(`(import\\s*{[^}]*),\\s*${importName}\\s*([^}]*})`, 'g'),
            '$1$2'
          );
          content = content.replace(
            new RegExp(`(import\\s*{)\\s*${importName}\\s*,([^}]*})`, 'g'),
            '$1$2'
          );
          content = content.replace(
            new RegExp(`import\\s*{\\s*${importName}\\s*}\\s*from[^;]+;\\s*`, 'g'),
            ''
          );
          changed = true;
        }
      }
    });
    
    // 修复jest.fn()类型问题
    content = content.replace(/jest\.fn\(\)\.mockResolvedValue\(void 0\)/g, 'jest.fn().mockResolvedValue(undefined)');
    content = content.replace(/jest\.fn\(\)\.mockResolvedValue\(([^)]+)\)\s+as\s+any/g, 'jest.fn().mockResolvedValue($1)');
    
    // 修复未使用的参数
    content = content.replace(/\(\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*any\s*\)\s*=>/g, (match, varName) => {
      // 简单检查：如果参数名在箭头函数后面没有出现，则添加下划线前缀
      const arrowIndex = content.indexOf(match);
      const afterArrow = content.substring(arrowIndex + match.length, arrowIndex + match.length + 200);
      if (!afterArrow.includes(varName)) {
        return match.replace(varName, '_' + varName);
      }
      return match;
    });
    
    // 修复隐式any类型
    content = content.replace(/\(\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\)\s*=>/g, '($1: any) =>');
    
    // 修复属性访问错误
    content = content.replace(/(\w+)\['instance'\]\s*=/g, '($1 as any)[\'instance\'] =');
    
    // 修复空的导入语句
    content = content.replace(/import\s*{\s*}\s*from\s*[^;]+;\s*/g, '');
    
    // 修复重复的空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    if (changed) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`已修复: ${filePath}`);
    }
  } catch (error) {
    console.error(`修复失败 ${filePath}:`, error.message);
  }
}

function findTsFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          traverse(fullPath);
        } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`无法读取目录 ${currentDir}:`, error.message);
    }
  }
  
  traverse(dir);
  return files;
}

// 查找所有TypeScript文件
const tsFiles = findTsFiles('./src');

console.log(`找到 ${tsFiles.length} 个TypeScript文件`);

// 修复每个文件
tsFiles.forEach(fixRemainingErrors);

console.log('修复完成!');
