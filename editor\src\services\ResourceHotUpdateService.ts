/**
 * 资源热更新服务
 * 负责管理资源热更新功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { message } from 'antd';
import { store } from '../store';
import axios from 'axios';
import {
  setUpdateStatus,
  setUpdateProgress,
  addUpdateHistory,
  setCurrentUpdate,
  setUpdateConfig,
  ResourceUpdateStatus,
  ResourceUpdateType,
  ResourceUpdateConfig,
  ResourceUpdateInfo
} from '../store/resources/resourceHotUpdateSlice';

/**
 * 资源热更新服务事件类型
 */
export enum ResourceHotUpdateServiceEventType {
  UPDATE_STARTED = 'updateStarted',
  UPDATE_PROGRESS = 'updateProgress',
  UPDATE_COMPLETED = 'updateCompleted',
  UPDATE_FAILED = 'updateFailed',
  UPDATE_CANCELLED = 'updateCancelled',
  CONFIG_CHANGED = 'configChanged',
  ROLLBACK_STARTED = 'rollbackStarted',
  ROLLBACK_COMPLETED = 'rollbackCompleted',
  ROLLBACK_FAILED = 'rollbackFailed'
}

/**
 * 资源热更新服务
 */
export class ResourceHotUpdateService extends EventEmitter {
  /** 单例实例 */
  private static instance: ResourceHotUpdateService;

  /** 是否正在更新 */
  private isUpdating: boolean = false;

  /** 是否正在回滚 */
  private isRollingBack: boolean = false;

  /** 更新配置 */
  private config: ResourceUpdateConfig = {
    autoCheckInterval: 3600, // 默认每小时自动检查更新
    autoDownloadUpdates: false, // 默认不自动下载更新
    autoApplyUpdates: false, // 默认不自动应用更新
    updateTypes: [ResourceUpdateType.ASSETS, ResourceUpdateType.SCRIPTS], // 默认更新资源和脚本
    maxConcurrentDownloads: 3, // 默认最大并发下载数
    retryCount: 3, // 默认重试次数
    retryDelay: 1000, // 默认重试延迟（毫秒）
    backupBeforeUpdate: true, // 默认更新前备份
    notifyOnUpdateAvailable: true, // 默认有更新时通知
    updateServer: '/api/updates', // 默认更新服务器地址
    updateChannel: 'stable', // 默认更新渠道
    enableDeltaUpdates: true, // 默认启用增量更新
    compressionType: 'gzip', // 默认压缩类型
    maxBackupCount: 5, // 默认最大备份数量
    cleanupAfterUpdate: true, // 默认更新后清理
  };

  /** 更新历史 */
  private updateHistory: ResourceUpdateInfo[] = [];

  /** 当前更新 */
  private currentUpdate: ResourceUpdateInfo | null = null;

  /** 更新检查定时器 */
  private updateCheckTimer: NodeJS.Timeout | null = null;

  /** 更新队列 */
  private updateQueue: { resourceId: string, version: string, retryCount?: number }[] = [];

  /** 下载中的资源 */
  private downloadingResources: Map<string, { 
    progress: number, 
    controller: AbortController 
  }> = new Map();

  /**
   * 私有构造函数
   */
  private constructor() {
    super();
    this.loadConfig();
    this.loadUpdateHistory();
    this.startUpdateCheckTimer();
  }

  /**
   * 获取单例实例
   * @returns 资源热更新服务实例
   */
  public static getInstance(): ResourceHotUpdateService {
    if (!ResourceHotUpdateService.instance) {
      ResourceHotUpdateService.instance = new ResourceHotUpdateService();
    }
    return ResourceHotUpdateService.instance;
  }

  /**
   * 加载配置
   */
  private loadConfig(): void {
    try {
      // 从本地存储加载配置
      const savedConfig = localStorage.getItem('resourceUpdateConfig');
      if (savedConfig) {
        this.config = { ...this.config, ...JSON.parse(savedConfig) };
      }

      // 更新Redux状态
      store.dispatch(setUpdateConfig(this.config));
    } catch (error) {
      console.error('加载资源更新配置失败:', error);
    }
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      // 保存到本地存储
      localStorage.setItem('resourceUpdateConfig', JSON.stringify(this.config));
    } catch (error) {
      console.error('保存资源更新配置失败:', error);
    }
  }

  /**
   * 加载更新历史
   */
  private loadUpdateHistory(): void {
    try {
      // 从本地存储加载更新历史
      const savedHistory = localStorage.getItem('resourceUpdateHistory');
      if (savedHistory) {
        this.updateHistory = JSON.parse(savedHistory);
        
        // 更新Redux状态
        this.updateHistory.forEach(update => {
          store.dispatch(addUpdateHistory(update));
        });
      }
    } catch (error) {
      console.error('加载资源更新历史失败:', error);
    }
  }

  /**
   * 保存更新历史
   */
  private saveUpdateHistory(): void {
    try {
      // 保存到本地存储
      localStorage.setItem('resourceUpdateHistory', JSON.stringify(this.updateHistory));
    } catch (error) {
      console.error('保存资源更新历史失败:', error);
    }
  }

  /**
   * 开始更新检查定时器
   */
  private startUpdateCheckTimer(): void {
    // 清除现有定时器
    if (this.updateCheckTimer) {
      clearInterval(this.updateCheckTimer);
    }

    // 如果自动检查间隔大于0，则设置定时器
    if (this.config.autoCheckInterval > 0) {
      this.updateCheckTimer = setInterval(() => {
        this.checkForUpdates();
      }, this.config.autoCheckInterval * 1000);
    }
  }

  /**
   * 检查更新
   * @returns Promise，解析为是否有可用更新
   */
  public async checkForUpdates(): Promise<boolean> {
    try {
      // 设置更新状态为检查中
      store.dispatch(setUpdateStatus(ResourceUpdateStatus.CHECKING));

      // 调用更新服务器API检查更新
      const response = await axios.get(`${this.config.updateServer}/check`, {
        params: {
          channel: this.config.updateChannel,
          version: '1.0.0', // 当前版本，实际应从应用配置获取
          types: this.config.updateTypes.join(',')
        }
      });

      // 解析响应
      const { hasUpdate, updates } = response.data;

      // 如果有更新
      if (hasUpdate && updates && updates.length > 0) {
        // 创建更新信息
        const updateInfo: ResourceUpdateInfo = {
          id: `update-${Date.now()}`,
          version: updates[0].version,
          timestamp: Date.now(),
          status: ResourceUpdateStatus.AVAILABLE,
          progress: 0,
          details: updates,
          size: updates.reduce((total: number, update: any) => total + (update.size || 0), 0),
          type: updates[0].type,
          description: updates[0].description || '新的更新可用',
          changelogs: updates[0].changelogs || []
        };

        // 设置当前更新
        this.currentUpdate = updateInfo;
        store.dispatch(setCurrentUpdate(updateInfo));

        // 设置更新状态为可用
        store.dispatch(setUpdateStatus(ResourceUpdateStatus.AVAILABLE));

        // 如果配置为自动下载更新，则开始下载
        if (this.config.autoDownloadUpdates) {
          this.downloadUpdate(updateInfo.id);
        }

        // 如果配置为有更新时通知，则显示通知
        if (this.config.notifyOnUpdateAvailable) {
          message.info(`发现新的更新: ${updateInfo.description}`);
        }

        // 发出更新可用事件
        this.emit(ResourceHotUpdateServiceEventType.UPDATE_STARTED, updateInfo);

        return true;
      } else {
        // 设置更新状态为最新
        store.dispatch(setUpdateStatus(ResourceUpdateStatus.UP_TO_DATE));
        return false;
      }
    } catch (error) {
      console.error('检查更新失败:', error);
      
      // 设置更新状态为错误
      store.dispatch(setUpdateStatus(ResourceUpdateStatus.ERROR));
      
      // 发出更新失败事件
      this.emit(ResourceHotUpdateServiceEventType.UPDATE_FAILED, error);
      
      return false;
    }
  }

  /**
   * 下载更新
   * @param updateId 更新ID
   * @returns Promise，解析为是否成功下载
   */
  public async downloadUpdate(updateId: string): Promise<boolean> {
    // 如果当前更新为空或ID不匹配，则返回失败
    if (!this.currentUpdate || this.currentUpdate.id !== updateId) {
      console.error('找不到要下载的更新:', updateId);
      return false;
    }

    // 如果已经在更新中，则返回失败
    if (this.isUpdating) {
      console.error('已有更新正在进行中');
      return false;
    }

    try {
      // 设置更新状态为下载中
      this.isUpdating = true;
      store.dispatch(setUpdateStatus(ResourceUpdateStatus.DOWNLOADING));

      // 获取更新详情
      const { details } = this.currentUpdate;

      // 创建下载队列
      this.updateQueue = details.map((detail: any) => ({
        resourceId: detail.resourceId,
        version: detail.version
      }));

      // 开始下载
      await this.processDownloadQueue();

      // 设置更新状态为已下载
      store.dispatch(setUpdateStatus(ResourceUpdateStatus.DOWNLOADED));

      // 更新当前更新状态
      this.currentUpdate.status = ResourceUpdateStatus.DOWNLOADED;
      this.currentUpdate.progress = 100;
      store.dispatch(setCurrentUpdate({ ...this.currentUpdate }));

      // 如果配置为自动应用更新，则应用更新
      if (this.config.autoApplyUpdates) {
        return this.applyUpdate(updateId);
      }

      return true;
    } catch (error) {
      console.error('下载更新失败:', error);
      
      // 设置更新状态为错误
      store.dispatch(setUpdateStatus(ResourceUpdateStatus.ERROR));
      
      // 更新当前更新状态
      if (this.currentUpdate) {
        this.currentUpdate.status = ResourceUpdateStatus.ERROR;
        store.dispatch(setCurrentUpdate({ ...this.currentUpdate }));
      }
      
      // 发出更新失败事件
      this.emit(ResourceHotUpdateServiceEventType.UPDATE_FAILED, error);
      
      return false;
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * 处理下载队列
   */
  private async processDownloadQueue(): Promise<void> {
    // 如果队列为空，则返回
    if (this.updateQueue.length === 0) {
      return;
    }

    // 计算总下载数量
    const totalCount = this.updateQueue.length;
    let completedCount = 0;

    // 创建并发下载任务
    const tasks: Promise<void>[] = [];
    const maxConcurrent = this.config.maxConcurrentDownloads;

    // 处理队列中的项目
    while (this.updateQueue.length > 0) {
      // 如果当前任务数量小于最大并发数，则创建新任务
      while (tasks.length < maxConcurrent && this.updateQueue.length > 0) {
        const item = this.updateQueue.shift()!;
        
        // 创建下载任务
        const task = this.downloadResource(item.resourceId, item.version)
          .then(() => {
            // 更新完成计数
            completedCount++;

            // 更新进度
            const progress = Math.floor((completedCount / totalCount) * 100);
            store.dispatch(setUpdateProgress(progress));

            // 更新当前更新进度
            if (this.currentUpdate) {
              this.currentUpdate.progress = progress;
              store.dispatch(setCurrentUpdate({ ...this.currentUpdate }));
            }

            // 发出进度更新事件
            this.emit(ResourceHotUpdateServiceEventType.UPDATE_PROGRESS, {
              progress,
              completed: completedCount,
              total: totalCount
            });
          })
          .catch(async (error) => {
            console.error(`下载资源失败: ${item.resourceId}`, error);

            // 如果配置了重试，则重新加入队列
            if (this.config.retryCount > 0) {
              item.retryCount = (item.retryCount || 0) + 1;

              if (item.retryCount <= this.config.retryCount) {
                console.log(`重试下载资源: ${item.resourceId}, 重试次数: ${item.retryCount}/${this.config.retryCount}`);
                this.updateQueue.push(item);

                // 等待重试延迟
                await new Promise<void>(resolve => setTimeout(resolve, this.config.retryDelay));
                return;
              }
            }

            // 更新完成计数
            completedCount++;

            // 更新进度
            const progress = Math.floor((completedCount / totalCount) * 100);
            store.dispatch(setUpdateProgress(progress));
          });
        
        tasks.push(task);
      }
      
      // 等待任务完成
      if (tasks.length > 0) {
        await Promise.race(tasks.map((task, index) =>
          task.then(() => {
            // 从任务列表中移除完成的任务
            tasks.splice(index, 1);
            return Promise.resolve();
          }).catch(() => {
            // 从任务列表中移除失败的任务
            tasks.splice(index, 1);
            return Promise.resolve();
          })
        ));
      }
    }
    
    // 等待所有剩余任务完成
    await Promise.all(tasks);
  }

  /**
   * 下载资源
   * @param resourceId 资源ID
   * @param version 版本
   */
  private async downloadResource(resourceId: string, version: string): Promise<void> {
    // 创建中止控制器
    const controller = new AbortController();
    
    // 添加到下载中资源映射
    this.downloadingResources.set(resourceId, { 
      progress: 0, 
      controller 
    });
    
    try {
      // 调用更新服务器API下载资源
      const response = await axios.get(`${this.config.updateServer}/download`, {
        params: {
          resourceId,
          version,
          delta: this.config.enableDeltaUpdates ? 'true' : 'false',
          compression: this.config.compressionType
        },
        responseType: 'blob',
        signal: controller.signal,
        onDownloadProgress: (progressEvent) => {
          // 计算下载进度
          const total = progressEvent.total || progressEvent.loaded;
          const progress = total > 0 ? Math.floor((progressEvent.loaded / total) * 100) : 0;

          // 更新下载中资源进度
          this.downloadingResources.set(resourceId, {
            progress,
            controller
          });
        }
      });

      // 处理下载的资源
      // 这里应该根据实际情况保存下载的资源
      const blob = response.data;
      console.log(`资源下载完成: ${resourceId}, 版本: ${version}, 大小: ${blob.size} 字节`);
      
      // 从下载中资源映射中移除
      this.downloadingResources.delete(resourceId);
    } catch (error) {
      // 从下载中资源映射中移除
      this.downloadingResources.delete(resourceId);
      
      // 重新抛出错误
      throw error;
    }
  }

  /**
   * 应用更新
   * @param updateId 更新ID
   * @returns Promise，解析为是否成功应用
   */
  public async applyUpdate(updateId: string): Promise<boolean> {
    // 如果当前更新为空或ID不匹配，则返回失败
    if (!this.currentUpdate || this.currentUpdate.id !== updateId) {
      console.error('找不到要应用的更新:', updateId);
      return false;
    }

    // 如果更新状态不是已下载，则返回失败
    if (this.currentUpdate.status !== ResourceUpdateStatus.DOWNLOADED) {
      console.error('更新尚未下载完成');
      return false;
    }

    try {
      // 设置更新状态为应用中
      store.dispatch(setUpdateStatus(ResourceUpdateStatus.APPLYING));

      // 更新当前更新状态
      this.currentUpdate.status = ResourceUpdateStatus.APPLYING;
      store.dispatch(setCurrentUpdate({ ...this.currentUpdate }));

      // 如果配置为更新前备份，则创建备份
      if (this.config.backupBeforeUpdate) {
        await this.createBackup();
      }

      // 调用更新服务器API应用更新
      await axios.post(`${this.config.updateServer}/apply`, {
        updateId: this.currentUpdate.id,
        version: this.currentUpdate.version
      });

      // 设置更新状态为已应用
      store.dispatch(setUpdateStatus(ResourceUpdateStatus.APPLIED));

      // 更新当前更新状态
      this.currentUpdate.status = ResourceUpdateStatus.APPLIED;
      store.dispatch(setCurrentUpdate({ ...this.currentUpdate }));

      // 添加到更新历史
      this.updateHistory.push(this.currentUpdate);
      store.dispatch(addUpdateHistory(this.currentUpdate));
      this.saveUpdateHistory();

      // 发出更新完成事件
      this.emit(ResourceHotUpdateServiceEventType.UPDATE_COMPLETED, this.currentUpdate);

      // 如果配置为更新后清理，则清理临时文件
      if (this.config.cleanupAfterUpdate) {
        this.cleanupAfterUpdate();
      }

      // 显示更新成功消息
      message.success('更新已成功应用');

      return true;
    } catch (error) {
      console.error('应用更新失败:', error);
      
      // 设置更新状态为错误
      store.dispatch(setUpdateStatus(ResourceUpdateStatus.ERROR));
      
      // 更新当前更新状态
      this.currentUpdate.status = ResourceUpdateStatus.ERROR;
      store.dispatch(setCurrentUpdate({ ...this.currentUpdate }));
      
      // 发出更新失败事件
      this.emit(ResourceHotUpdateServiceEventType.UPDATE_FAILED, error);
      
      // 显示更新失败消息
      message.error('应用更新失败');
      
      return false;
    }
  }

  /**
   * 创建备份
   */
  private async createBackup(): Promise<void> {
    try {
      // 调用更新服务器API创建备份
      await axios.post(`${this.config.updateServer}/backup`, {
        updateId: this.currentUpdate?.id,
        version: this.currentUpdate?.version
      });

      console.log('已创建更新前备份');
    } catch (error) {
      console.error('创建备份失败:', error);
      throw error;
    }
  }

  /**
   * 清理更新后的临时文件
   */
  private async cleanupAfterUpdate(): Promise<void> {
    try {
      // 调用更新服务器API清理临时文件
      await axios.post(`${this.config.updateServer}/cleanup`, {
        updateId: this.currentUpdate?.id
      });

      console.log('已清理更新临时文件');
    } catch (error) {
      console.error('清理临时文件失败:', error);
    }
  }

  /**
   * 回滚更新
   * @param updateId 更新ID
   * @returns Promise，解析为是否成功回滚
   */
  public async rollbackUpdate(updateId: string): Promise<boolean> {
    // 查找更新
    const update = this.updateHistory.find(u => u.id === updateId);

    if (!update) {
      console.error('找不到要回滚的更新:', updateId);
      return false;
    }

    // 如果已经在回滚中，则返回失败
    if (this.isRollingBack) {
      console.error('已有回滚正在进行中');
      return false;
    }

    try {
      // 设置回滚状态
      this.isRollingBack = true;

      // 发出回滚开始事件
      this.emit(ResourceHotUpdateServiceEventType.ROLLBACK_STARTED, update);

      // 调用更新服务器API回滚更新
      await axios.post(`${this.config.updateServer}/rollback`, {
        updateId: update.id,
        version: update.version
      });

      // 更新状态
      update.status = ResourceUpdateStatus.ROLLED_BACK;
      
      // 更新历史记录
      const index = this.updateHistory.findIndex(u => u.id === updateId);
      if (index >= 0) {
        this.updateHistory[index] = { ...update };
        this.saveUpdateHistory();
      }

      // 发出回滚完成事件
      this.emit(ResourceHotUpdateServiceEventType.ROLLBACK_COMPLETED, update);

      // 显示回滚成功消息
      message.success('已成功回滚更新');

      return true;
    } catch (error) {
      console.error('回滚更新失败:', error);
      
      // 发出回滚失败事件
      this.emit(ResourceHotUpdateServiceEventType.ROLLBACK_FAILED, error);
      
      // 显示回滚失败消息
      message.error('回滚更新失败');
      
      return false;
    } finally {
      this.isRollingBack = false;
    }
  }

  /**
   * 取消更新
   * @returns 是否成功取消
   */
  public cancelUpdate(): boolean {
    // 如果没有当前更新或不在更新中，则返回失败
    if (!this.currentUpdate || !this.isUpdating) {
      return false;
    }

    try {
      // 取消所有下载中的请求
      for (const [resourceId, { controller }] of this.downloadingResources.entries()) {
        controller.abort();
        this.downloadingResources.delete(resourceId);
      }

      // 清空更新队列
      this.updateQueue = [];

      // 设置更新状态为已取消
      store.dispatch(setUpdateStatus(ResourceUpdateStatus.CANCELLED));

      // 更新当前更新状态
      this.currentUpdate.status = ResourceUpdateStatus.CANCELLED;
      store.dispatch(setCurrentUpdate({ ...this.currentUpdate }));

      // 发出更新取消事件
      this.emit(ResourceHotUpdateServiceEventType.UPDATE_CANCELLED, this.currentUpdate);

      // 显示取消消息
      message.info('更新已取消');

      return true;
    } catch (error) {
      console.error('取消更新失败:', error);
      return false;
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * 更新配置
   * @param newConfig 新配置
   */
  public updateConfig(newConfig: Partial<ResourceUpdateConfig>): void {
    // 更新配置
    this.config = { ...this.config, ...newConfig };

    // 保存配置
    this.saveConfig();

    // 更新Redux状态
    store.dispatch(setUpdateConfig(this.config));

    // 重新启动更新检查定时器
    this.startUpdateCheckTimer();

    // 发出配置变更事件
    this.emit(ResourceHotUpdateServiceEventType.CONFIG_CHANGED, this.config);
  }

  /**
   * 获取配置
   * @returns 当前配置
   */
  public getConfig(): ResourceUpdateConfig {
    return { ...this.config };
  }

  /**
   * 获取更新历史
   * @returns 更新历史
   */
  public getUpdateHistory(): ResourceUpdateInfo[] {
    return [...this.updateHistory];
  }

  /**
   * 获取当前更新
   * @returns 当前更新
   */
  public getCurrentUpdate(): ResourceUpdateInfo | null {
    return this.currentUpdate ? { ...this.currentUpdate } : null;
  }

  /**
   * 清除更新历史
   */
  public clearUpdateHistory(): void {
    this.updateHistory = [];
    this.saveUpdateHistory();
  }
}

// 创建单例
export const resourceHotUpdateService = ResourceHotUpdateService.getInstance();
