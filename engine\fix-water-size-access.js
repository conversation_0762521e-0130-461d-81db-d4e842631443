#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 修复水体尺寸访问的脚本
 */

// 修复规则
const fixes = [
  // 修复 waterBody.size.width 为 waterBody.getSize().width
  {
    name: '修复waterBody.size.width访问',
    pattern: /waterBody\.size\.width/g,
    replacement: 'waterBody.getSize().width'
  },
  
  // 修复 waterBody.size.height 为 waterBody.getSize().height
  {
    name: '修复waterBody.size.height访问',
    pattern: /waterBody\.size\.height/g,
    replacement: 'waterBody.getSize().height'
  },
  
  // 修复 waterBody.size.depth 为 waterBody.getSize().depth
  {
    name: '修复waterBody.size.depth访问',
    pattern: /waterBody\.size\.depth/g,
    replacement: 'waterBody.getSize().depth'
  }
];

/**
 * 修复文件
 * @param {string} filePath 文件路径
 * @returns {boolean} 是否修复了文件
 */
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const appliedFixes = [];

    for (const fix of fixes) {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
        appliedFixes.push(fix.name);
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`已修复: ${filePath}`);
      if (appliedFixes.length > 0) {
        console.log(`  应用的修复: ${appliedFixes.join(', ')}`);
      }
      return true;
    }

    return false;
  } catch (error) {
    console.error(`修复文件失败: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  const targetFile = path.join(__dirname, 'src/rendering/water/UnderwaterParticleSystem.ts');
  
  if (!fs.existsSync(targetFile)) {
    console.error('目标文件不存在:', targetFile);
    process.exit(1);
  }
  
  console.log('开始修复水体尺寸访问问题...\n');
  
  if (fixFile(targetFile)) {
    console.log('\n修复完成！');
    console.log('\n建议运行以下命令检查编译状态：');
    console.log('npx tsc --noEmit');
  } else {
    console.log('\n没有发现需要修复的问题。');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFile };
