/**
 * 帮助面板组件
 * 显示帮助文档和教程
 */
import React, { useState, useEffect } from 'react';
import { Tabs, List, Typography, Button, Spin, Empty } from 'antd';
import { SearchOutlined, BookOutlined, QuestionCircleOutlined, VideoCameraOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import HelpService, { HelpTopic, HelpCategory } from '../../services/HelpService';
import MarkdownViewer from '../common/MarkdownViewer';
import './HelpPanel.less';

const { TabPane } = Tabs;
const { Search } = Input;
const { Title, Text } = Typography;
const { DirectoryTree } = Tree;

interface HelpPanelProps {
  contextId?: string;
}

const HelpPanel: React.FC<HelpPanelProps> = ({ contextId }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('browse');
  const [categories, setCategories] = useState<HelpCategory[]>([]);
  const [searchResults, setSearchResults] = useState<HelpTopic[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTopic, setSelectedTopic] = useState<HelpTopic | null>(null);
  const [topicContent, setTopicContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [treeData, setTreeData] = useState<any[]>([]);

  // 初始化
  useEffect(() => {
    // 获取帮助类别和主题
    const helpCategories = HelpService.getCategories();
    setCategories(helpCategories);
    
    // 构建树形数据
    const data = helpCategories.map(category => ({
      title: category.title,
      key: category.id,
      selectable: false,
      children: category.topics.map(topic => ({
        title: topic.title,
        key: topic.id,
        isLeaf: true}))
    }));
    setTreeData(data);
    
    // 如果有上下文ID，显示相关帮助
    if (contextId) {
      const contextTopics = HelpService.getTopicsByContextId(contextId);
      if (contextTopics.length > 0) {
        handleTopicSelect(contextTopics[0]);
      }
    }
    
    // 监听帮助服务事件
    HelpService.on('categoriesChanged', (newCategories) => {
      setCategories(newCategories);
    });
    
    return () => {
      // 清理事件监听
      HelpService.off('categoriesChanged', () => {});
    };
  }, [contextId]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    if (value.trim() === '') {
      setSearchResults([]);
      return;
    }
    
    const results = HelpService.searchTopics(value);
    setSearchResults(results);
    setActiveTab('search');
  };

  // 处理主题选择
  const handleTopicSelect = async (topic: HelpTopic) => {
    setSelectedTopic(topic);
    setLoading(true);
    
    try {
      // 获取主题内容
      const url = HelpService.getTopicUrl(topic.id);
      const response = await fetch(url);
      const content = await response.text();
      setTopicContent(content);
    } catch (error) {
      console.error('Failed to load topic content:', error);
      setTopicContent(t('help.errorLoadingContent'));
    } finally {
      setLoading(false);
    }
  };

  // 处理树节点选择
  const handleTreeSelect = (selectedKeys: React.Key[], info: any) => {
    if (selectedKeys.length > 0 && info.node.isLeaf) {
      const topic = HelpService.getTopicById(selectedKeys[0].toString());
      if (topic) {
        handleTopicSelect(topic);
      }
    }
  };

  // 渲染主题内容
  const renderTopicContent = () => {
    if (!selectedTopic) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t('help.selectTopic')}
        />
      );
    }
    
    if (loading) {
      return (
        <div className="help-panel-loading">
          <Spin size="large" />
          <Text>{t('help.loading')}</Text>
        </div>
      );
    }
    
    return (
      <div className="help-panel-content">
        <Title level={3}>{selectedTopic.title}</Title>
        <MarkdownViewer content={topicContent} />
      </div>
    );
  };

  return (
    <div className="help-panel">
      <div className="help-panel-header">
        <Search
          placeholder={t('help.searchPlaceholder')}
          onSearch={handleSearch}
          onChange={(e) => setSearchQuery(e.target.value)}
          value={searchQuery}
          style={{ width: '100%' }}
          allowClear
        />
      </div>
      
      <div className="help-panel-body">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <BookOutlined />
                {t('help.browse')}
              </span>
            }
            key="browse"
          >
            <DirectoryTree
              treeData={treeData}
              onSelect={handleTreeSelect}
              defaultExpandAll
            />
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <SearchOutlined />
                {t('help.search')}
              </span>
            }
            key="search"
          >
            {searchResults.length > 0 ? (
              <List
                itemLayout="horizontal"
                dataSource={searchResults}
                renderItem={(item) => (
                  <List.Item
                    actions={[
                      <Button
                        type="link"
                        onClick={() => handleTopicSelect(item)}
                      >
                        {t('help.view')}
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={item.title}
                      description={`${t('help.category')}: ${
                        categories.find((c) => c.id === item.category)?.title || item.category
                      }`}
                    />
                  </List.Item>
                )}
              />
            ) : searchQuery ? (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={t('help.noResults')}
              />
            ) : (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={t('help.enterSearchTerm')}
              />
            )}
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <QuestionCircleOutlined />
                {t('help.faq')}
              </span>
            }
            key="faq"
          >
            <List
              itemLayout="horizontal"
              dataSource={HelpService.getTopics().filter(topic => topic.category === 'faq')}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button
                      type="link"
                      onClick={() => handleTopicSelect(item)}
                    >
                      {t('help.view')}
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={item.title}
                  />
                </List.Item>
              )}
            />
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <VideoCameraOutlined />
                {t('help.tutorials')}
              </span>
            }
            key="tutorials"
          >
            <List
              itemLayout="horizontal"
              dataSource={HelpService.getTopics().filter(topic => topic.category === 'tutorials')}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button
                      type="link"
                      onClick={() => handleTopicSelect(item)}
                    >
                      {t('help.view')}
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={item.title}
                  />
                </List.Item>
              )}
            />
          </TabPane>
        </Tabs>
      </div>
      
      <div className="help-panel-content-container">
        {renderTopicContent()}
      </div>
    </div>
  );
};

export default HelpPanel;
