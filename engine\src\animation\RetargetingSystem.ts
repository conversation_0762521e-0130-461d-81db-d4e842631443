/**
 * 动画重定向系统
 * 用于管理和执行动画重定向操作
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { World } from '../core/World';
import type { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { AnimationRetargeting, BoneMapping, RetargetingConfig } from './AnimationRetargeting';
import { AnimationRetargeter } from './AnimationRetargeter';

/**
 * 重定向系统配置
 */
export interface RetargetingSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否自动创建骨骼映射 */
  autoCreateMapping?: boolean;
  /** 是否缓存重定向结果 */
  cacheResults?: boolean;
  /** 最大缓存数量 */
  maxCacheSize?: number;
}

/**
 * 重定向结果
 */
export interface RetargetingResult {
  /** 源动画片段名称 */
  sourceClipName: string;
  /** 目标动画片段名称 */
  targetClipName: string;
  /** 源实体ID */
  sourceEntityId: string;
  /** 目标实体ID */
  targetEntityId: string;
  /** 重定向后的动画片段 */
  clip: THREE.AnimationClip;
  /** 重定向配置 */
  config: RetargetingConfig;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 重定向事件类型
 */
export enum RetargetingEventType {
  /** 重定向开始 */
  RETARGET_START = 'retargetStart',
  /** 重定向完成 */
  RETARGET_COMPLETE = 'retargetComplete',
  /** 重定向错误 */
  RETARGET_ERROR = 'retargetError',
  /** 骨骼映射更新 */
  MAPPING_UPDATED = 'mappingUpdated',
  /** 配置更新 */
  CONFIG_UPDATED = 'configUpdated'
}

/**
 * 动画重定向系统
 * 用于管理和执行动画重定向操作
 */
export class RetargetingSystem extends System {
  /** 配置 */
  private config: Required<RetargetingSystemConfig>;
  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 重定向器映射 */
  private retargeters: Map<string, AnimationRetargeter>;
  /** 结果缓存 */
  private resultCache: Map<string, RetargetingResult>;
  /** 骨骼映射预设 */
  private mappingPresets: Map<string, BoneMapping[]>;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(config: RetargetingSystemConfig = {}) {
    super(200); // 设置优先级

    this.config = {
      debug: false,
      autoCreateMapping: true,
      cacheResults: true,
      maxCacheSize: 100,
      ...config
    };

    this.eventEmitter = new EventEmitter();
    this.retargeters = new Map();
    this.resultCache = new Map();
    this.mappingPresets = new Map();

    if (this.config.debug) {
      console.log('动画重定向系统初始化');
    }

    // 注册预设骨骼映射
    this.registerDefaultMappingPresets();
  }

  /**
   * 注册默认骨骼映射预设
   */
  private registerDefaultMappingPresets(): void {
    // Mixamo到VRM的骨骼映射
    const mixamoToVRM: BoneMapping[] = [
      { source: 'Hips', target: 'J_Bip_C_Hips' },
      { source: 'Spine', target: 'J_Bip_C_Spine' },
      { source: 'Spine1', target: 'J_Bip_C_Chest' },
      { source: 'Spine2', target: 'J_Bip_C_UpperChest' },
      { source: 'Neck', target: 'J_Bip_C_Neck' },
      { source: 'Head', target: 'J_Bip_C_Head' },
      { source: 'LeftShoulder', target: 'J_Bip_L_Shoulder' },
      { source: 'LeftArm', target: 'J_Bip_L_UpperArm' },
      { source: 'LeftForeArm', target: 'J_Bip_L_LowerArm' },
      { source: 'LeftHand', target: 'J_Bip_L_Hand' },
      { source: 'RightShoulder', target: 'J_Bip_R_Shoulder' },
      { source: 'RightArm', target: 'J_Bip_R_UpperArm' },
      { source: 'RightForeArm', target: 'J_Bip_R_LowerArm' },
      { source: 'RightHand', target: 'J_Bip_R_Hand' },
      { source: 'LeftUpLeg', target: 'J_Bip_L_UpperLeg' },
      { source: 'LeftLeg', target: 'J_Bip_L_LowerLeg' },
      { source: 'LeftFoot', target: 'J_Bip_L_Foot' },
      { source: 'LeftToeBase', target: 'J_Bip_L_ToeBase' },
      { source: 'RightUpLeg', target: 'J_Bip_R_UpperLeg' },
      { source: 'RightLeg', target: 'J_Bip_R_LowerLeg' },
      { source: 'RightFoot', target: 'J_Bip_R_Foot' },
      { source: 'RightToeBase', target: 'J_Bip_R_ToeBase' }
    ];
    this.registerMappingPreset('MixamoToVRM', mixamoToVRM);

    // 标准骨骼映射
    const standardMapping: BoneMapping[] = [
      { source: 'Hips', target: 'Hips' },
      { source: 'Spine', target: 'Spine' },
      { source: 'Chest', target: 'Chest' },
      { source: 'UpperChest', target: 'UpperChest' },
      { source: 'Neck', target: 'Neck' },
      { source: 'Head', target: 'Head' },
      { source: 'LeftShoulder', target: 'LeftShoulder' },
      { source: 'LeftUpperArm', target: 'LeftUpperArm' },
      { source: 'LeftLowerArm', target: 'LeftLowerArm' },
      { source: 'LeftHand', target: 'LeftHand' },
      { source: 'RightShoulder', target: 'RightShoulder' },
      { source: 'RightUpperArm', target: 'RightUpperArm' },
      { source: 'RightLowerArm', target: 'RightLowerArm' },
      { source: 'RightHand', target: 'RightHand' },
      { source: 'LeftUpperLeg', target: 'LeftUpperLeg' },
      { source: 'LeftLowerLeg', target: 'LeftLowerLeg' },
      { source: 'LeftFoot', target: 'LeftFoot' },
      { source: 'LeftToes', target: 'LeftToes' },
      { source: 'RightUpperLeg', target: 'RightUpperLeg' },
      { source: 'RightLowerLeg', target: 'RightLowerLeg' },
      { source: 'RightFoot', target: 'RightFoot' },
      { source: 'RightToes', target: 'RightToes' }
    ];
    this.registerMappingPreset('Standard', standardMapping);
  }

  /**
   * 注册骨骼映射预设
   * @param name 预设名称
   * @param mapping 骨骼映射
   */
  public registerMappingPreset(name: string, mapping: BoneMapping[]): void {
    this.mappingPresets.set(name, mapping);

    if (this.config.debug) {
      console.log(`注册骨骼映射预设: ${name}`);
    }
  }

  /**
   * 获取骨骼映射预设
   * @param name 预设名称
   * @returns 骨骼映射
   */
  public getMappingPreset(name: string): BoneMapping[] | null {
    return this.mappingPresets.get(name) || null;
  }

  /**
   * 获取所有骨骼映射预设名称
   * @returns 预设名称数组
   */
  public getMappingPresetNames(): string[] {
    return Array.from(this.mappingPresets.keys());
  }

  /**
   * 创建重定向器
   * @param sourceEntity 源实体
   * @param targetEntity 目标实体
   * @param config 重定向配置
   * @returns 重定向器
   */
  public createRetargeter(
    sourceEntity: Entity,
    targetEntity: Entity,
    config: Partial<RetargetingConfig> = {}
  ): AnimationRetargeter | null {
    // 创建唯一ID
    const id = `${sourceEntity.id}_${targetEntity.id}`;

    // 检查是否已存在
    if (this.retargeters.has(id)) {
      return this.retargeters.get(id)!;
    }

    // 获取源骨骼
    const sourceSkeleton = this.getSkeletonFromEntity(sourceEntity);
    if (!sourceSkeleton) {
      if (this.config.debug) {
        console.warn(`无法获取源骨骼: ${sourceEntity.id}`);
      }
      return null;
    }

    // 获取目标骨骼
    const targetSkeleton = this.getSkeletonFromEntity(targetEntity);
    if (!targetSkeleton) {
      if (this.config.debug) {
        console.warn(`无法获取目标骨骼: ${targetEntity.id}`);
      }
      return null;
    }

    // 创建默认配置
    const defaultConfig: RetargetingConfig = {
      boneMapping: [],
      preservePositionTracks: true,
      preserveScaleTracks: false,
      normalizeRotations: true,
      adjustRootHeight: true,
      adjustBoneLength: true,
      ...config
    };

    // 创建重定向器
    const retargeter = new AnimationRetargeter(
      sourceSkeleton,
      targetSkeleton,
      defaultConfig
    );

    // 如果启用自动创建骨骼映射，则创建
    if (this.config.autoCreateMapping && defaultConfig.boneMapping.length === 0) {
      retargeter.autoCreateBoneMapping();
    }

    // 添加到映射
    this.retargeters.set(id, retargeter);

    if (this.config.debug) {
      console.log(`创建重定向器: ${id}`);
    }

    return retargeter;
  }

  /**
   * 从实体获取骨骼
   * @param entity 实体
   * @returns 骨骼
   */
  private getSkeletonFromEntity(entity: Entity): THREE.Skeleton | null {
    // 这里需要根据实际的实体结构来实现
    // 例如，可以从实体的组件中获取骨骼信息

    // 示例：从TransformComponent中获取Object3D，然后查找SkinnedMesh
    const transform = entity.getComponent('Transform') as any as any as any;
    if (!transform) return null;

    // 使用类型断言访问getObject3D方法
    const transformWithObject3D = transform as any;
    if (typeof transformWithObject3D.getObject3D !== 'function') return null;

    const object3D = transformWithObject3D.getObject3D();
    if (!object3D) return null;

    // 查找SkinnedMesh
    let skinnedMesh: THREE.SkinnedMesh | null = null;
    object3D.traverse((object) => {
      if (object instanceof THREE.SkinnedMesh) {
        skinnedMesh = object;
      }
    });

    return skinnedMesh ? skinnedMesh.skeleton : null;
  }

  /**
   * 重定向动画片段
   * @param sourceClip 源动画片段
   * @param sourceEntity 源实体
   * @param targetEntity 目标实体
   * @param config 重定向配置
   * @returns 重定向后的动画片段
   */
  public retargetClip(
    sourceClip: THREE.AnimationClip,
    sourceEntity: Entity,
    targetEntity: Entity,
    config?: Partial<RetargetingConfig>
  ): THREE.AnimationClip | null {
    // 创建缓存键
    const cacheKey = `${sourceClip.name}_${sourceEntity.id}_${targetEntity.id}`;

    // 检查缓存
    if (this.config.cacheResults && this.resultCache.has(cacheKey)) {
      const cachedResult = this.resultCache.get(cacheKey)!;

      if (this.config.debug) {
        console.log(`使用缓存的重定向结果: ${cacheKey}`);
      }

      return cachedResult.clip;
    }

    // 获取或创建重定向器
    const retargeter = this.createRetargeter(sourceEntity, targetEntity, config);
    if (!retargeter) {
      if (this.config.debug) {
        console.warn(`无法创建重定向器: ${sourceEntity.id} -> ${targetEntity.id}`);
      }
      return null;
    }

    // 发出重定向开始事件
    this.eventEmitter.emit(RetargetingEventType.RETARGET_START, {
      sourceClip: sourceClip.name,
      sourceEntity: sourceEntity.id,
      targetEntity: targetEntity.id
    });

    try {
      // 重定向动画片段
      const retargetedClip = retargeter.retarget(sourceClip);

      // 缓存结果
      if (this.config.cacheResults) {
        // 如果缓存已满，删除最旧的结果
        if (this.resultCache.size >= this.config.maxCacheSize) {
          let oldestKey = '';
          let oldestTime = Date.now();

          for (const [key, result] of this.resultCache.entries()) {
            if (result.timestamp < oldestTime) {
              oldestTime = result.timestamp;
              oldestKey = key;
            }
          }

          if (oldestKey) {
            this.resultCache.delete(oldestKey);
          }
        }

        // 添加到缓存
        this.resultCache.set(cacheKey, {
          sourceClipName: sourceClip.name,
          targetClipName: retargetedClip.name,
          sourceEntityId: sourceEntity.id,
          targetEntityId: targetEntity.id,
          clip: retargetedClip,
          config: retargeter.getConfig(),
          timestamp: Date.now()
        });
      }

      // 发出重定向完成事件
      this.eventEmitter.emit(RetargetingEventType.RETARGET_COMPLETE, {
        sourceClip: sourceClip.name,
        targetClip: retargetedClip.name,
        sourceEntity: sourceEntity.id,
        targetEntity: targetEntity.id
      });

      return retargetedClip;
    } catch (error) {
      // 发出重定向错误事件
      this.eventEmitter.emit(RetargetingEventType.RETARGET_ERROR, {
        sourceClip: sourceClip.name,
        sourceEntity: sourceEntity.id,
        targetEntity: targetEntity.id,
        error
      });

      if (this.config.debug) {
        console.error(`重定向动画失败: ${sourceClip.name}`, error);
      }

      return null;
    }
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: RetargetingEventType, listener: (data: any) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: RetargetingEventType, listener: (data: any) => void): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.resultCache.clear();

    if (this.config.debug) {
      console.log('清除重定向缓存');
    }
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    // 重定向系统不需要每帧更新
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    this.retargeters.clear();
    this.resultCache.clear();
    this.mappingPresets.clear();

    if (this.config.debug) {
      console.log('销毁动画重定向系统');
    }
  }
}
