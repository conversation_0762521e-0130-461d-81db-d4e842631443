/**
 * 陀螺仪控制组件
 * 提供基于设备陀螺仪的相机控制功能
 */
import React, { useState, useEffect, useRef } from 'react';
import { Button, Switch, Slider, Card, Tooltip, message } from 'antd';
import { 
  CompassOutlined, 
  SettingOutlined, 
  ReloadOutlined, 
  EyeOutlined, 
  EyeInvisibleOutlined 
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import MobileDeviceService from '../../services/MobileDeviceService';
import { Vector3 } from 'dl-engine-core/math/Vector3';
import { Quaternion } from 'dl-engine-core/math/Quaternion';
import './GyroscopeControl.less';

// 陀螺仪控制属性接口
interface GyroscopeControlProps {
  // 是否启用
  enabled?: boolean;
  // 灵敏度
  sensitivity?: number;
  // 平滑度
  smoothing?: number;
  // 启用回调
  onEnable?: (enabled: boolean) => void;
  // 方向变化回调
  onOrientationChange?: (quaternion: Quaternion) => void;
}

/**
 * 陀螺仪控制组件
 */
const GyroscopeControl: React.FC<GyroscopeControlProps> = ({
  enabled = false,
  sensitivity = 1.0,
  smoothing = 0.8,
  onEnable,
  onOrientationChange
}) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [isEnabled, setIsEnabled] = useState<boolean>(enabled);
  const [isAvailable, setIsAvailable] = useState<boolean>(false);
  const [isCalibrating, setIsCalibrating] = useState<boolean>(false);
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const [currentSensitivity, setCurrentSensitivity] = useState<number>(sensitivity);
  const [currentSmoothing, setCurrentSmoothing] = useState<number>(smoothing);
  
  // 陀螺仪数据
  const [alpha, setAlpha] = useState<number>(0);
  const [beta, setBeta] = useState<number>(0);
  const [gamma, setGamma] = useState<number>(0);
  
  // 参考方向
  const referenceQuaternion = useRef<Quaternion>(new Quaternion());
  const currentQuaternion = useRef<Quaternion>(new Quaternion());
  
  // 检查陀螺仪可用性
  useEffect(() => {
    checkGyroscopeAvailability();
  }, []);
  
  // 启用/禁用陀螺仪
  useEffect(() => {
    if (isEnabled && isAvailable) {
      startGyroscope();
    } else {
      stopGyroscope();
    }
    
    return () => {
      stopGyroscope();
    };
  }, [isEnabled, isAvailable]);
  
  // 检查陀螺仪可用性
  const checkGyroscopeAvailability = () => {
    if (typeof window !== 'undefined' && window.DeviceOrientationEvent) {
      // 对于iOS 13+，需要请求权限
      if (
        DeviceOrientationEvent.requestPermission &&
        typeof DeviceOrientationEvent.requestPermission === 'function'
      ) {
        setIsAvailable(true);
      } 
      // 对于其他支持陀螺仪的设备
      else if ('ondeviceorientation' in window) {
        setIsAvailable(true);
      } else {
        setIsAvailable(false);
      }
    } else {
      setIsAvailable(false);
    }
  };
  
  // 请求陀螺仪权限
  const requestGyroscopePermission = async () => {
    if (
      typeof window !== 'undefined' &&
      window.DeviceOrientationEvent &&
      DeviceOrientationEvent.requestPermission &&
      typeof DeviceOrientationEvent.requestPermission === 'function'
    ) {
      try {
        const permission = await DeviceOrientationEvent.requestPermission();
        if (permission === 'granted') {
          setIsAvailable(true);
          message.success(t('gyroscope.permissionGranted'));
        } else {
          setIsAvailable(false);
          message.error(t('gyroscope.permissionDenied'));
        }
      } catch (error) {
        console.error('Error requesting gyroscope permission:', error);
        setIsAvailable(false);
        message.error(t('gyroscope.permissionError'));
      }
    } else {
      // 对于不需要权限的设备，直接设置为可用
      setIsAvailable(true);
    }
  };
  
  // 启动陀螺仪
  const startGyroscope = () => {
    if (typeof window !== 'undefined') {
      window.addEventListener('deviceorientation', handleDeviceOrientation);
    }
  };
  
  // 停止陀螺仪
  const stopGyroscope = () => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('deviceorientation', handleDeviceOrientation);
    }
  };
  
  // 处理设备方向变化
  const handleDeviceOrientation = (event: DeviceOrientationEvent) => {
    // 获取设备方向数据
    const newAlpha = event.alpha || 0; // Z轴旋转
    const newBeta = event.beta || 0;   // X轴旋转
    const newGamma = event.gamma || 0; // Y轴旋转
    
    // 应用平滑处理
    const smoothedAlpha = alpha * currentSmoothing + newAlpha * (1 - currentSmoothing);
    const smoothedBeta = beta * currentSmoothing + newBeta * (1 - currentSmoothing);
    const smoothedGamma = gamma * currentSmoothing + newGamma * (1 - currentSmoothing);
    
    // 更新状态
    setAlpha(smoothedAlpha);
    setBeta(smoothedBeta);
    setGamma(smoothedGamma);
    
    // 转换为四元数
    const alphaRad = (smoothedAlpha * Math.PI) / 180;
    const betaRad = (smoothedBeta * Math.PI) / 180;
    const gammaRad = (smoothedGamma * Math.PI) / 180;
    
    // 创建旋转四元数
    const qz = new Quaternion().setFromAxisAngle(new Vector3(0, 0, 1), alphaRad);
    const qx = new Quaternion().setFromAxisAngle(new Vector3(1, 0, 0), betaRad);
    const qy = new Quaternion().setFromAxisAngle(new Vector3(0, 1, 0), gammaRad);
    
    // 组合旋转
    const deviceQuaternion = new Quaternion().multiplyQuaternions(qz, qx).multiply(qy);
    
    // 如果正在校准，则设置参考四元数
    if (isCalibrating) {
      referenceQuaternion.current = deviceQuaternion.clone().inverse();
      setIsCalibrating(false);
      message.success(t('gyroscope.calibrationComplete'));
    }
    
    // 应用参考四元数和灵敏度
    currentQuaternion.current = new Quaternion().multiplyQuaternions(
      referenceQuaternion.current,
      deviceQuaternion
    );
    
    // 应用灵敏度
    const angle = currentQuaternion.current.getAngle() * currentSensitivity;
    const axis = currentQuaternion.current.getAxis();
    currentQuaternion.current.setFromAxisAngle(axis, angle);
    
    // 调用回调
    if (onOrientationChange) {
      onOrientationChange(currentQuaternion.current);
    }
  };
  
  // 切换启用状态
  const toggleEnabled = () => {
    // 如果陀螺仪不可用，则请求权限
    if (!isAvailable) {
      requestGyroscopePermission();
      return;
    }
    
    const newEnabled = !isEnabled;
    setIsEnabled(newEnabled);
    
    if (onEnable) {
      onEnable(newEnabled);
    }
    
    if (newEnabled) {
      message.info(t('gyroscope.enabled'));
    } else {
      message.info(t('gyroscope.disabled'));
    }
  };
  
  // 校准陀螺仪
  const calibrateGyroscope = () => {
    if (!isEnabled || !isAvailable) {
      return;
    }
    
    setIsCalibrating(true);
    message.loading(t('gyroscope.calibrating'));
  };
  
  // 切换设置面板
  const toggleSettings = () => {
    setShowSettings(!showSettings);
  };
  
  // 处理灵敏度变化
  const handleSensitivityChange = (value: number) => {
    setCurrentSensitivity(value);
  };
  
  // 处理平滑度变化
  const handleSmoothingChange = (value: number) => {
    setCurrentSmoothing(value);
  };
  
  return (
    <div className="gyroscope-control">
      <div className="gyroscope-buttons">
        <Tooltip title={isEnabled ? t('gyroscope.disable') : t('gyroscope.enable')}>
          <Button
            type={isEnabled ? 'primary' : 'default'}
            shape="circle"
            icon={<CompassOutlined />}
            onClick={toggleEnabled}
            className="gyroscope-button"
          />
        </Tooltip>
        
        <Tooltip title={t('gyroscope.calibrate')}>
          <Button
            type="default"
            shape="circle"
            icon={<ReloadOutlined />}
            onClick={calibrateGyroscope}
            disabled={!isEnabled || !isAvailable}
            className="gyroscope-button"
          />
        </Tooltip>
        
        <Tooltip title={t('gyroscope.settings')}>
          <Button
            type={showSettings ? 'primary' : 'default'}
            shape="circle"
            icon={<SettingOutlined />}
            onClick={toggleSettings}
            className="gyroscope-button"
          />
        </Tooltip>
      </div>
      
      {showSettings && (
        <Card className="gyroscope-settings" size="small" title={t('gyroscope.settings')}>
          <div className="setting-item">
            <div className="setting-label">{t('gyroscope.sensitivity')}</div>
            <Slider
              min={0.1}
              max={2.0}
              step={0.1}
              value={currentSensitivity}
              onChange={handleSensitivityChange}
              disabled={!isAvailable}
            />
          </div>
          
          <div className="setting-item">
            <div className="setting-label">{t('gyroscope.smoothing')}</div>
            <Slider
              min={0}
              max={0.95}
              step={0.05}
              value={currentSmoothing}
              onChange={handleSmoothingChange}
              disabled={!isAvailable}
            />
          </div>
          
          <div className="setting-item">
            <div className="setting-label">{t('gyroscope.enabled')}</div>
            <Switch
              checked={isEnabled}
              onChange={toggleEnabled}
              disabled={!isAvailable}
            />
          </div>
          
          {!isAvailable && (
            <div className="gyroscope-unavailable">
              {t('gyroscope.unavailable')}
              <Button
                type="primary"
                size="small"
                onClick={requestGyroscopePermission}
              >
                {t('gyroscope.requestPermission')}
              </Button>
            </div>
          )}
        </Card>
      )}
      
      {isEnabled && isAvailable && (
        <div className="gyroscope-indicator">
          <div className="gyroscope-values">
            <div>α: {alpha.toFixed(1)}°</div>
            <div>β: {beta.toFixed(1)}°</div>
            <div>γ: {gamma.toFixed(1)}°</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GyroscopeControl;
