/**
 * 操作历史组件
 */
import React from 'react';
import { List, Typography, Space, Tag, Empty } from 'antd';
import { useTranslation } from 'react-i18next';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  AimOutlined,
  HighlightOutlined,
  AppstoreOutlined,
  ToolOutlined
} from '@ant-design/icons';
import { 
  Operation, 
  OperationType, 
  CollaborationUser 
} from '../../services/CollaborationService';

const { Text } = Typography;

// 操作类型图标映射
const operationIcons = {
  [OperationType.ENTITY_CREATE]: <PlusOutlined style={{ color: '#52c41a' }} />,
  [OperationType.ENTITY_UPDATE]: <EditOutlined style={{ color: '#1890ff' }} />,
  [OperationType.ENTITY_DELETE]: <DeleteOutlined style={{ color: '#f5222d' }} />,
  [OperationType.COMPONENT_ADD]: <PlusOutlined style={{ color: '#722ed1' }} />,
  [OperationType.COMPONENT_UPDATE]: <EditOutlined style={{ color: '#722ed1' }} />,
  [OperationType.COMPONENT_REMOVE]: <DeleteOutlined style={{ color: '#722ed1' }} />,
  [OperationType.SCENE_UPDATE]: <AppstoreOutlined style={{ color: '#fa8c16' }} />,
  [OperationType.CURSOR_MOVE]: <AimOutlined style={{ color: '#8c8c8c' }} />,
  [OperationType.SELECTION_CHANGE]: <HighlightOutlined style={{ color: '#8c8c8c' }} />};

// 操作类型名称映射
const operationNames = {
  [OperationType.ENTITY_CREATE]: 'collaboration.operations.entityCreate',
  [OperationType.ENTITY_UPDATE]: 'collaboration.operations.entityUpdate',
  [OperationType.ENTITY_DELETE]: 'collaboration.operations.entityDelete',
  [OperationType.COMPONENT_ADD]: 'collaboration.operations.componentAdd',
  [OperationType.COMPONENT_UPDATE]: 'collaboration.operations.componentUpdate',
  [OperationType.COMPONENT_REMOVE]: 'collaboration.operations.componentRemove',
  [OperationType.SCENE_UPDATE]: 'collaboration.operations.sceneUpdate',
  [OperationType.CURSOR_MOVE]: 'collaboration.operations.cursorMove',
  [OperationType.SELECTION_CHANGE]: 'collaboration.operations.selectionChange'};

interface OperationHistoryProps {
  operations: Operation[];
  users: CollaborationUser[];
}

/**
 * 操作历史组件
 */
const OperationHistory: React.FC<OperationHistoryProps> = ({ operations, users }) => {
  const { t } = useTranslation();
  
  // 获取用户名称
  const getUserName = (userId: string): string => {
    const user = users.find(u => u.id === userId);
    return user ? user.name : userId;
  };
  
  // 获取用户颜色
  const getUserColor = (userId: string): string => {
    const user = users.find(u => u.id === userId);
    return user ? user.color : '#8c8c8c';
  };
  
  // 格式化时间
  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };
  
  // 获取操作描述
  const getOperationDescription = (operation: Operation): string => {
    switch (operation.type) {
      case OperationType.ENTITY_CREATE:
        return t('collaboration.operationDescriptions.entityCreate', { 
          name: operation.data.name || operation.data.id 
        });
        
      case OperationType.ENTITY_UPDATE:
        return t('collaboration.operationDescriptions.entityUpdate', { 
          name: operation.data.name || operation.data.id 
        });
        
      case OperationType.ENTITY_DELETE:
        return t('collaboration.operationDescriptions.entityDelete', { 
          name: operation.data.name || operation.data.id 
        });
        
      case OperationType.COMPONENT_ADD:
        return t('collaboration.operationDescriptions.componentAdd', { 
          component: operation.data.componentType,
          entity: operation.data.entityName || operation.data.entityId
        });
        
      case OperationType.COMPONENT_UPDATE:
        return t('collaboration.operationDescriptions.componentUpdate', { 
          component: operation.data.componentType,
          entity: operation.data.entityName || operation.data.entityId
        });
        
      case OperationType.COMPONENT_REMOVE:
        return t('collaboration.operationDescriptions.componentRemove', { 
          component: operation.data.componentType,
          entity: operation.data.entityName || operation.data.entityId
        });
        
      case OperationType.SCENE_UPDATE:
        return t('collaboration.operationDescriptions.sceneUpdate');
        
      case OperationType.CURSOR_MOVE:
        return t('collaboration.operationDescriptions.cursorMove');
        
      case OperationType.SELECTION_CHANGE:
        const count = operation.data.selectedIds?.length || 0;
        return t('collaboration.operationDescriptions.selectionChange', { count });
        
      default:
        return t('collaboration.operationDescriptions.unknown');
    }
  };
  
  // 过滤掉光标移动操作，除非是最新的
  const filteredOperations = operations.filter((op, index, arr) => {
    if (op.type !== OperationType.CURSOR_MOVE) {
      return true;
    }
    
    // 只保留每个用户最新的光标移动操作
    const latestCursorMoveIndex = arr
      .map((o, i) => ({ op: o, index: i }))
      .filter(item => item.op.type === OperationType.CURSOR_MOVE && item.op.userId === op.userId)
      .reduce((latest, current) => current.index > latest ? current.index : latest, -1);
    
    return index === latestCursorMoveIndex;
  });
  
  // 按时间倒序排序
  const sortedOperations = [...filteredOperations].sort((a, b) => b.timestamp - a.timestamp);
  
  if (sortedOperations.length === 0) {
    return (
      <Empty 
        image={Empty.PRESENTED_IMAGE_SIMPLE} 
        description={t('collaboration.noOperations')}
      />
    );
  }
  
  return (
    <List
      className="operation-history-list"
      itemLayout="horizontal"
      dataSource={sortedOperations}
      renderItem={(operation) => (
        <List.Item>
          <List.Item.Meta
            avatar={
              <div style={{ fontSize: 20 }}>
                {operationIcons[operation.type] || <ToolOutlined />}
              </div>
            }
            title={
              <Space>
                <Text>{t(operationNames[operation.type])}</Text>
                <Tag color={getUserColor(operation.userId)}>
                  {getUserName(operation.userId)}
                </Tag>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {formatTime(operation.timestamp)}
                </Text>
              </Space>
            }
            description={getOperationDescription(operation)}
          />
        </List.Item>
      )}
    />
  );
};

export default OperationHistory;
