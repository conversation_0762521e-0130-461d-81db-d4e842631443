# 权限策略服务 (PermissionPolicyService) 

## 概述

权限策略服务提供了一个强大而灵活的权限管理系统，支持多种策略类型和细粒度的权限控制。

## 已完成的TODO功能

### 1. 角色策略评估 ✅

**功能描述**: 基于用户角色进行权限评估

**实现方式**:
- 通过 `permissionService.getUserRole(userId)` 获取用户当前角色
- 检查用户角色是否在策略允许的角色列表中
- 支持多角色策略配置

**使用示例**:
```typescript
const rolePolicy = permissionPolicyService.createPolicy({
  name: '管理员编辑策略',
  type: PolicyType.ROLE,
  permissions: [Permission.SCENE_EDIT],
  roles: [CollaborationRole.ADMIN, CollaborationRole.OWNER],
  enabled: true,
  priority: 100
});
```

### 2. 组织策略评估 ✅

**功能描述**: 基于用户所属组织进行权限评估

**实现方式**:
- 通过 `organizationPermissionService.getUserNode(userId)` 获取用户组织节点
- 获取用户节点的所有祖先节点（支持组织层级继承）
- 检查用户是否属于策略指定的任何组织

**使用示例**:
```typescript
const orgPolicy = permissionPolicyService.createPolicy({
  name: '部门权限策略',
  type: PolicyType.ORGANIZATION,
  permissions: [Permission.ENTITY_CREATE],
  organizationIds: ['dept_engineering', 'dept_design'],
  enabled: true,
  priority: 120
});
```

### 3. 条件策略评估 ✅

**功能描述**: 基于动态条件进行权限评估

**支持的条件类型**:
- `EQUALS` / `NOT_EQUALS`: 等于/不等于比较
- `GREATER_THAN` / `LESS_THAN`: 数值大小比较
- `GREATER_THAN_OR_EQUAL` / `LESS_THAN_OR_EQUAL`: 数值范围比较
- `CONTAINS` / `NOT_CONTAINS`: 字符串包含检查
- `IN` / `NOT_IN`: 数组成员检查
- `REGEX`: 正则表达式匹配
- `EXISTS` / `NOT_EXISTS`: 存在性检查

**实现特性**:
- 支持嵌套路径访问（如 `user.role`, `resource.type`）
- 多条件AND逻辑组合
- 类型安全的条件评估

**使用示例**:
```typescript
const conditionPolicy = permissionPolicyService.createPolicy({
  name: '环境条件策略',
  type: PolicyType.CONDITION,
  permissions: [Permission.SCENE_EDIT],
  conditions: [
    {
      type: ConditionType.EQUALS,
      value: 'development' // 检查 context.environment === 'development'
    },
    {
      type: ConditionType.GREATER_THAN,
      value: 5 // 检查 context.userLevel > 5
    }
  ],
  enabled: true,
  priority: 80
});

// 使用时提供上下文
const hasPermission = permissionPolicyService.evaluatePermission(
  'user123',
  Permission.SCENE_EDIT,
  {
    environment: 'development',
    userLevel: 10
  }
);
```

## 新增的实用功能

### 1. 用户适用策略查询

```typescript
// 获取用户的所有适用策略
const userPolicies = permissionPolicyService.getUserApplicablePolicies('user123');

// 获取用户对特定权限的适用策略
const editPolicies = permissionPolicyService.getUserApplicablePolicies('user123', Permission.SCENE_EDIT);
```

### 2. 策略冲突检测

```typescript
// 检查所有策略的冲突
const conflictReport = permissionPolicyService.checkPolicyConflicts();

if (conflictReport.hasConflicts) {
  conflictReport.conflicts.forEach(conflict => {
    console.log(`冲突类型: ${conflict.conflictType}`);
    console.log(`描述: ${conflict.description}`);
  });
}
```

### 3. 策略统计信息

```typescript
const stats = permissionPolicyService.getPolicyStatistics();
console.log(`总策略数: ${stats.total}`);
console.log(`启用策略: ${stats.enabled}`);
console.log(`按类型分布:`, stats.byType);
console.log(`平均优先级: ${stats.averagePriority}`);
```

## 策略评估流程

1. **缓存检查**: 首先检查评估结果缓存
2. **策略筛选**: 获取适用于用户和权限的策略
3. **优先级排序**: 按策略优先级降序排列
4. **逐一评估**: 评估每个策略，任一策略通过即授权
5. **结果缓存**: 将评估结果缓存以提高性能

## 配置选项

```typescript
const service = new PermissionPolicyService({
  enabled: true,                    // 是否启用策略服务
  enableCache: true,                // 是否启用缓存
  cacheExpiration: 60000,           // 缓存过期时间（毫秒）
  enableEvaluationLog: true,        // 是否记录评估日志
  enableConflictDetection: true,    // 是否启用冲突检测
  enableVersioning: true,           // 是否启用版本控制
  maxPolicies: 1000                 // 最大策略数量
});
```

## 最佳实践

1. **优先级设置**: 为策略设置合理的优先级，避免冲突
2. **缓存利用**: 对频繁评估的权限启用缓存
3. **条件优化**: 条件策略应尽量简单，避免复杂的嵌套逻辑
4. **定期检查**: 定期运行冲突检测，确保策略一致性
5. **日志监控**: 启用评估日志，便于调试和审计

## 事件监听

服务支持以下事件：
- `policyCreated`: 策略创建时触发
- `policyUpdated`: 策略更新时触发
- `policyDeleted`: 策略删除时触发
- `policyApplied`: 策略应用时触发

```typescript
permissionPolicyService.on('policyCreated', (policy) => {
  console.log(`新策略已创建: ${policy.name}`);
});
```
