/**
 * 移动布局服务
 * 提供移动设备布局管理相关的功能
 */
import { LayoutData } from 'rc-dock';
import LayoutService from './LayoutService';
import { MobileDeviceService, DeviceType, ScreenOrientation } from './MobileDeviceService';
import { EventEmitter } from '../utils/EventEmitter';

// 移动布局事件类型
export enum MobileLayoutEventType {
  LAYOUT_CHANGED = 'layoutChanged',
  LAYOUT_OPTIMIZED = 'layoutOptimized'
}

// 移动布局配置接口
export interface MobileLayoutConfig {
  // 是否启用调试模式
  debug?: boolean;
  // 是否自动优化布局
  autoOptimize?: boolean;
  // 是否启用触摸优化
  enableTouchOptimization?: boolean;
  // 是否启用性能优化
  enablePerformanceOptimization?: boolean;
  // 是否启用屏幕方向响应
  enableOrientationResponse?: boolean;
}

// 移动设备预定义布局
export const mobileLayouts: Record<string, LayoutData> = {
  // 移动设备默认布局 - 竖屏
  mobilePortrait: {
    dockbox: {
      mode: 'vertical',
      children: [
        {
          size: 300,
          tabs: [
            { id: 'scene', title: '场景视图', content: 'scene' as any, closable: false }
          ]
        },
        {
          size: 200,
          tabs: [
            { id: 'hierarchy', title: '层级面板', content: 'hierarchy' as any, closable: false },
            { id: 'inspector', title: '属性面板', content: 'inspector' as any, closable: false },
            { id: 'assets', title: '资源面板', content: 'assets' as any, closable: false }
          ]
        }
      ]
    },
    floatbox: {
      mode: 'float',
      children: [
        {
          tabs: [
            { id: 'mobileToolbar', title: '工具栏', content: 'mobileToolbar' as any, closable: false }
          ],
          x: 0,
          y: 0,
          w: '100%' as any,
          h: 50
        }
      ]
    }
  } as LayoutData,
  
  // 移动设备默认布局 - 横屏
  mobileLandscape: {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          size: 200,
          tabs: [
            { id: 'hierarchy', title: '层级面板', content: 'hierarchy' as any, closable: false },
            { id: 'assets', title: '资源面板', content: 'assets' as any, closable: false }
          ]
        },
        {
          size: 500,
          tabs: [
            { id: 'scene', title: '场景视图', content: 'scene' as any, closable: false }
          ]
        },
        {
          size: 200,
          tabs: [
            { id: 'inspector', title: '属性面板', content: 'inspector' as any, closable: false }
          ]
        }
      ]
    },
    floatbox: {
      mode: 'float',
      children: [
        {
          tabs: [
            { id: 'mobileToolbar', title: '工具栏', content: 'mobileToolbar' as any, closable: false }
          ],
          x: 0,
          y: 0,
          w: '100%' as any,
          h: 50
        }
      ]
    }
  } as LayoutData,
  
  // 平板设备默认布局 - 竖屏
  tabletPortrait: {
    dockbox: {
      mode: 'vertical',
      children: [
        {
          size: 500,
          tabs: [
            { id: 'scene', title: '场景视图', content: 'scene' as any, closable: false }
          ]
        },
        {
          mode: 'horizontal',
          size: 300,
          children: [
            {
              size: 200,
              tabs: [
                { id: 'hierarchy', title: '层级面板', content: 'hierarchy' as any, closable: false }
              ]
            },
            {
              size: 300,
              tabs: [
                { id: 'inspector', title: '属性面板', content: 'inspector' as any, closable: false }
              ]
            },
            {
              size: 200,
              tabs: [
                { id: 'assets', title: '资源面板', content: 'assets' as any, closable: false }
              ]
            }
          ]
        }
      ]
    }
  } as LayoutData,
  
  // 平板设备默认布局 - 横屏
  tabletLandscape: {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          mode: 'vertical',
          size: 200,
          children: [
            {
              tabs: [
                { id: 'hierarchy', title: '层级面板', content: 'hierarchy' as any, closable: false }
              ]
            },
            {
              tabs: [
                { id: 'assets', title: '资源面板', content: 'assets' as any, closable: false }
              ]
            }
          ]
        },
        {
          mode: 'vertical',
          size: 600,
          children: [
            {
              tabs: [
                { id: 'scene', title: '场景视图', content: 'scene' as any, closable: false }
              ]
            }
          ]
        },
        {
          size: 250,
          tabs: [
            { id: 'inspector', title: '属性面板', content: 'inspector' as any, closable: false }
          ]
        }
      ]
    }
  } as LayoutData,
  
  // 触摸优化布局 - 大按钮和简化界面
  touchOptimized: {
    dockbox: {
      mode: 'vertical',
      children: [
        {
          size: 400,
          tabs: [
            { id: 'scene', title: '场景视图', content: 'scene' as any, closable: false }
          ]
        },
        {
          size: 200,
          tabs: [
            { id: 'touchControls', title: '触控面板', content: 'touchControls' as any, closable: false }
          ]
        }
      ]
    },
    floatbox: {
      mode: 'float',
      children: [
        {
          tabs: [
            { id: 'touchToolbar', title: '触控工具栏', content: 'touchToolbar' as any, closable: false }
          ],
          x: 0,
          y: 0,
          w: '100%' as any,
          h: 60
        }
      ]
    }
  } as LayoutData,
  
  // 性能优化布局 - 最小化面板数量
  performanceOptimized: {
    dockbox: {
      mode: 'vertical',
      children: [
        {
          size: 500,
          tabs: [
            { id: 'scene', title: '场景视图', content: 'scene' as any, closable: false }
          ]
        },
        {
          size: 200,
          tabs: [
            { id: 'hierarchy', title: '层级面板', content: 'hierarchy' as any, closable: false },
            { id: 'inspector', title: '属性面板', content: 'inspector' as any, closable: false },
            { id: 'assets', title: '资源面板', content: 'assets' as any, closable: false }
          ]
        }
      ]
    }
  } as LayoutData
};

/**
 * 移动布局服务类
 * 提供移动设备布局管理相关的功能
 */
export class MobileLayoutService extends EventEmitter {
  private static instance: MobileLayoutService;
  private layoutService: LayoutService;
  private mobileDeviceService: MobileDeviceService;
  private config: MobileLayoutConfig;
  private currentMobileLayout: string = '';

  /**
   * 获取单例实例
   */
  public static getInstance(): MobileLayoutService {
    if (!MobileLayoutService.instance) {
      MobileLayoutService.instance = new MobileLayoutService();
    }
    return MobileLayoutService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    super();
    this.layoutService = LayoutService.getInstance();
    this.mobileDeviceService = MobileDeviceService.getInstance();
    
    // 默认配置
    this.config = {
      debug: false,
      autoOptimize: true,
      enableTouchOptimization: true,
      enablePerformanceOptimization: true,
      enableOrientationResponse: true
    };
    
    // 监听设备变化事件
    this.mobileDeviceService.on('deviceChanged', this.handleDeviceChanged.bind(this));
    this.mobileDeviceService.on('orientationChanged', this.handleOrientationChanged.bind(this));
    
    // 初始化布局
    this.initializeLayout();
  }

  /**
   * 配置服务
   * @param config 配置
   */
  public configure(config: Partial<MobileLayoutConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (this.config.debug) {
      console.log('移动布局服务配置已更新', this.config);
    }
    
    // 重新初始化布局
    this.initializeLayout();
  }

  /**
   * 初始化布局
   */
  private initializeLayout(): void {
    // 获取设备信息
    const deviceInfo = this.mobileDeviceService.getDeviceInfo();
    
    // 根据设备类型和屏幕方向选择布局
    this.selectLayoutForDevice(deviceInfo.type, deviceInfo.orientation);
  }

  /**
   * 根据设备类型和屏幕方向选择布局
   * @param deviceType 设备类型
   * @param orientation 屏幕方向
   */
  private selectLayoutForDevice(deviceType: DeviceType, orientation: ScreenOrientation): void {
    let layoutName = '';
    
    // 根据设备类型和屏幕方向选择布局
    if (deviceType === DeviceType.MOBILE) {
      if (orientation === ScreenOrientation.PORTRAIT) {
        layoutName = 'mobilePortrait';
      } else {
        layoutName = 'mobileLandscape';
      }
    } else if (deviceType === DeviceType.TABLET) {
      if (orientation === ScreenOrientation.PORTRAIT) {
        layoutName = 'tabletPortrait';
      } else {
        layoutName = 'tabletLandscape';
      }
    } else {
      // 桌面设备使用默认布局
      return;
    }
    
    // 应用触摸优化
    if (this.config.enableTouchOptimization && this.mobileDeviceService.isTouchDevice()) {
      layoutName = 'touchOptimized';
    }
    
    // 应用性能优化
    if (this.config.enablePerformanceOptimization && deviceType === DeviceType.MOBILE) {
      layoutName = 'performanceOptimized';
    }
    
    // 如果布局已经是当前布局，则不需要更改
    if (layoutName === this.currentMobileLayout) {
      return;
    }
    
    this.currentMobileLayout = layoutName;
    
    // 应用布局
    this.applyMobileLayout(layoutName);
    
    if (this.config.debug) {
      console.log(`应用移动布局: ${layoutName}`);
    }
  }

  /**
   * 应用移动布局
   * @param layoutName 布局名称
   */
  public applyMobileLayout(layoutName: string): void {
    const layout = mobileLayouts[layoutName];
    
    if (!layout) {
      console.error(`未找到移动布局: ${layoutName}`);
      return;
    }
    
    // 获取DockLayout引用
    const dockLayoutRef = this.layoutService.getDockLayoutRef();
    
    if (dockLayoutRef) {
      // 加载布局
      dockLayoutRef.loadLayout(layout);
      
      // 发出布局变更事件
      this.emit(MobileLayoutEventType.LAYOUT_CHANGED, {
        layoutName,
        layout
      });
    }
  }

  /**
   * 优化当前布局
   */
  public optimizeCurrentLayout(): void {
    // 获取设备信息
    const deviceInfo = this.mobileDeviceService.getDeviceInfo();
    
    // 根据设备类型和屏幕方向选择布局
    this.selectLayoutForDevice(deviceInfo.type, deviceInfo.orientation);
    
    // 发出布局优化事件
    this.emit(MobileLayoutEventType.LAYOUT_OPTIMIZED, {
      deviceInfo,
      layoutName: this.currentMobileLayout
    });
  }

  /**
   * 处理设备变化事件
   * @param deviceInfo 设备信息
   */
  private handleDeviceChanged(deviceInfo: any): void {
    if (this.config.autoOptimize) {
      this.selectLayoutForDevice(deviceInfo.type, deviceInfo.orientation);
    }
  }

  /**
   * 处理屏幕方向变化事件
   * @param deviceInfo 设备信息
   */
  private handleOrientationChanged(deviceInfo: any): void {
    if (this.config.enableOrientationResponse && this.config.autoOptimize) {
      this.selectLayoutForDevice(deviceInfo.type, deviceInfo.orientation);
    }
  }

  /**
   * 获取当前移动布局名称
   * @returns 当前移动布局名称
   */
  public getCurrentMobileLayout(): string {
    return this.currentMobileLayout;
  }

  /**
   * 获取所有移动布局
   * @returns 所有移动布局
   */
  public getAllMobileLayouts(): Record<string, LayoutData> {
    return mobileLayouts;
  }
}

export default MobileLayoutService.getInstance();
