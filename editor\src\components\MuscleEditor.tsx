/**
 * 肌肉编辑器组件
 * 提供直观的面部肌肉编辑功能
 */
import React, { useState, useEffect, useRef } from 'react';
import { Button, Slider, Select, Input, Switch, Tooltip, message, Space, Divider, Table, Tag} from 'antd';
import { PlusOutlined, DeleteOutlined, EyeOutlined, EyeInvisibleOutlined, SettingOutlined, SaveOutlined, UndoOutlined, RedoOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
// 移除引擎直接导入
import { ColorPicker } from './ColorPicker';
import './MuscleEditor.css';

const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';

/**
 * 肌肉编辑器属性
 */
interface MuscleEditorProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 是否显示3D预览 */
  showPreview?: boolean;
  /** 是否使用物理驱动 */
  usePhysics?: boolean;
  /** 组件宽度 */
  width?: number | string;
  /** 组件高度 */
  height?: number | string;
  /** 肌肉变更回调 */
  onMuscleChange?: (muscleId: string, properties: any) => void;
  /** 肌肉添加回调 */
  onMuscleAdd?: (muscleType: MuscleType, properties: any) => void;
  /** 肌肉删除回调 */
  onMuscleDelete?: (muscleId: string) => void;
  /** 保存回调 */
  onSave?: () => void;
}

/**
 * 肌肉数据
 */
interface MuscleData {
  /** 肌肉ID */
  id: string;
  /** 肌肉名称 */
  name: string;
  /** 肌肉类型 */
  type: MuscleType;
  /** 位置 */
  position: { x: number, y: number, z: number };
  /** 方向 */
  direction: { x: number, y: number, z: number };
  /** 长度 */
  length: number;
  /** 强度 */
  strength: number;
  /** 弹性 */
  stiffness: number;
  /** 阻尼 */
  damping: number;
  /** 颜色 */
  color: string;
  /** 是否可见 */
  visible: boolean;
  /** 是否启用 */
  enabled: boolean;
  /** 自定义属性 */
  customProperties?: any;
}

/**
 * 肌肉编辑器组件
 */
export const MuscleEditor: React.FC<MuscleEditorProps> = ({
  entityId,
  editable = true,
  showPreview = true,
  usePhysics = true,
  width = '100%',
  height = '100%',
  onMuscleChange,
  onMuscleAdd,
  onMuscleDelete,
  onSave
}) => {
  // 状态
  const [muscles, setMuscles] = useState<MuscleData[]>([]);
  const [selectedMuscleId, setSelectedMuscleId] = useState<string | null>(null);
  const [editingMuscle, setEditingMuscle] = useState<MuscleData | null>(null);
  const [isAddingMuscle, setIsAddingMuscle] = useState<boolean>(false);
  const [newMuscleType, setNewMuscleType] = useState<MuscleType>(MuscleType.LINEAR);
  const [searchText, setSearchText] = useState<string>('');
  const [filterType, setFilterType] = useState<MuscleType | 'all'>('all');
  
  // 引用
  const previewRef = useRef<any>(null);
  
  // Redux
  const dispatch = useDispatch();
  const muscleState = useSelector((state: RootState) => state.facialMuscles);
  
  // 肌肉类型列表
  const muscleTypes = [
    { value: MuscleType.LINEAR, label: '线性肌肉' },
    { value: MuscleType.CIRCULAR, label: '环形肌肉' },
    { value: MuscleType.SHEET, label: '片状肌肉' },
    { value: MuscleType.SPHINCTER, label: '括约肌' }
  ];
  
  // 模拟数据
  useEffect(() => {
    // 这里应该从Redux或API获取数据
    // 这里使用模拟数据
    const mockMuscles: MuscleData[] = [
      {
        id: 'muscle_1',
        name: '眉毛提肌',
        type: MuscleType.LINEAR,
        position: { x: 0, y: 1.7, z: 0.1 },
        direction: { x: 0, y: 1, z: 0 },
        length: 0.05,
        strength: 1.0,
        stiffness: 0.8,
        damping: 0.5,
        color: '#ff0000',
        visible: true,
        enabled: true
      },
      {
        id: 'muscle_2',
        name: '眼轮匝肌',
        type: MuscleType.CIRCULAR,
        position: { x: 0.03, y: 1.65, z: 0.1 },
        direction: { x: 0, y: 0, z: 1 },
        length: 0.02,
        strength: 0.8,
        stiffness: 0.7,
        damping: 0.6,
        color: '#00ff00',
        visible: true,
        enabled: true
      },
      {
        id: 'muscle_3',
        name: '口轮匝肌',
        type: MuscleType.SPHINCTER,
        position: { x: 0, y: 1.55, z: 0.1 },
        direction: { x: 0, y: 0, z: 1 },
        length: 0.03,
        strength: 1.2,
        stiffness: 0.6,
        damping: 0.4,
        color: '#0000ff',
        visible: true,
        enabled: true
      }
    ];
    
    setMuscles(mockMuscles);
  }, []);
  
  // 处理肌肉选择
  const handleMuscleSelect = (muscleId: string) => {
    setSelectedMuscleId(muscleId);
    const muscle = muscles.find(m => m.id === muscleId);
    if (muscle) {
      setEditingMuscle({ ...muscle });
    }
  };
  
  // 处理肌肉属性变更
  const handleMusclePropertyChange = (property: string, value: any) => {
    if (!editingMuscle) return;
    
    const updatedMuscle = { ...editingMuscle, [property]: value };
    setEditingMuscle(updatedMuscle);
    
    // 如果是实时更新，则立即应用变更
    if (onMuscleChange) {
      onMuscleChange(updatedMuscle.id, { [property]: value });
    }
  };
  
  // 处理肌肉位置变更
  const handlePositionChange = (axis: 'x' | 'y' | 'z', value: number) => {
    if (!editingMuscle) return;
    
    const updatedPosition = { ...editingMuscle.position, [axis]: value };
    const updatedMuscle = { ...editingMuscle, position: updatedPosition };
    setEditingMuscle(updatedMuscle);
    
    // 如果是实时更新，则立即应用变更
    if (onMuscleChange) {
      onMuscleChange(updatedMuscle.id, { position: updatedPosition });
    }
  };
  
  // 处理肌肉方向变更
  const handleDirectionChange = (axis: 'x' | 'y' | 'z', value: number) => {
    if (!editingMuscle) return;
    
    const updatedDirection = { ...editingMuscle.direction, [axis]: value };
    const updatedMuscle = { ...editingMuscle, direction: updatedDirection };
    setEditingMuscle(updatedMuscle);
    
    // 如果是实时更新，则立即应用变更
    if (onMuscleChange) {
      onMuscleChange(updatedMuscle.id, { direction: updatedDirection });
    }
  };
  
  // 处理肌肉添加
  const handleAddMuscle = () => {
    if (!isAddingMuscle) {
      setIsAddingMuscle(true);
      return;
    }
    
    // 创建新肌肉
    const newMuscle: MuscleData = {
      id: `muscle_${Date.now()}`,
      name: `新肌肉 ${muscles.length + 1}`,
      type: newMuscleType,
      position: { x: 0, y: 1.6, z: 0 },
      direction: { x: 0, y: 1, z: 0 },
      length: 0.05,
      strength: 1.0,
      stiffness: 0.7,
      damping: 0.5,
      color: '#ff9900',
      visible: true,
      enabled: true
    };
    
    // 添加到列表
    setMuscles([...muscles, newMuscle]);
    setIsAddingMuscle(false);
    setSelectedMuscleId(newMuscle.id);
    setEditingMuscle(newMuscle);
    
    // 回调
    if (onMuscleAdd) {
      onMuscleAdd(newMuscleType, newMuscle);
    }
    
    message.success('添加肌肉成功');
  };
  
  // 处理肌肉删除
  const handleDeleteMuscle = (muscleId: string) => {
    // 从列表中删除
    const updatedMuscles = muscles.filter(m => m.id !== muscleId);
    setMuscles(updatedMuscles);
    
    // 如果删除的是当前选中的肌肉，则清除选中状态
    if (selectedMuscleId === muscleId) {
      setSelectedMuscleId(null);
      setEditingMuscle(null);
    }
    
    // 回调
    if (onMuscleDelete) {
      onMuscleDelete(muscleId);
    }
    
    message.success('删除肌肉成功');
  };
  
  // 处理肌肉可见性切换
  const handleToggleVisibility = (muscleId: string) => {
    const muscle = muscles.find(m => m.id === muscleId);
    if (!muscle) return;
    
    const updatedMuscle = { ...muscle, visible: !muscle.visible };
    const updatedMuscles = muscles.map(m => m.id === muscleId ? updatedMuscle : m);
    setMuscles(updatedMuscles);
    
    // 如果是当前编辑的肌肉，更新编辑状态
    if (selectedMuscleId === muscleId && editingMuscle) {
      setEditingMuscle(updatedMuscle);
    }
    
    // 回调
    if (onMuscleChange) {
      onMuscleChange(muscleId, { visible: updatedMuscle.visible });
    }
  };
  
  // 处理肌肉启用状态切换
  const handleToggleEnabled = (muscleId: string) => {
    const muscle = muscles.find(m => m.id === muscleId);
    if (!muscle) return;
    
    const updatedMuscle = { ...muscle, enabled: !muscle.enabled };
    const updatedMuscles = muscles.map(m => m.id === muscleId ? updatedMuscle : m);
    setMuscles(updatedMuscles);
    
    // 如果是当前编辑的肌肉，更新编辑状态
    if (selectedMuscleId === muscleId && editingMuscle) {
      setEditingMuscle(updatedMuscle);
    }
    
    // 回调
    if (onMuscleChange) {
      onMuscleChange(muscleId, { enabled: updatedMuscle.enabled });
    }
  };
  
  // 处理保存
  const handleSave = () => {
    if (onSave) {
      onSave();
    }
    message.success('保存成功');
  };
  
  // 过滤肌肉列表
  const filteredMuscles = muscles.filter(muscle => {
    // 按名称搜索
    const nameMatch = muscle.name.toLowerCase().includes(searchText.toLowerCase());
    
    // 按类型过滤
    const typeMatch = filterType === 'all' || muscle.type === filterType;
    
    return nameMatch && typeMatch;
  });
  
  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: MuscleData) => (
        <span style={{ color: record.enabled ? 'inherit' : '#999' }}>{text}</span>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: MuscleType) => {
        const muscleType = muscleTypes.find(t => t.value === type);
        return <Tag color={type === MuscleType.LINEAR ? 'blue' : type === MuscleType.CIRCULAR ? 'green' : type === MuscleType.SHEET ? 'orange' : 'purple'}>{muscleType?.label || type}</Tag>;
      }
    },
    {
      title: '强度',
      dataIndex: 'strength',
      key: 'strength',
      render: (strength: number) => strength.toFixed(1)
    },
    {
      title: '操作',
      key: 'action',
      render: (text: string, record: MuscleData) => (
        <Space size="small">
          <Tooltip title={record.visible ? '隐藏' : '显示'}>
            <Button
              type="text"
              icon={record.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={() => handleToggleVisibility(record.id)}
              disabled={!editable}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteMuscle(record.id)}
              disabled={!editable}
            />
          </Tooltip>
        </Space>
      )
    }
  ];
  
  return (
    <div className="muscle-editor" style={{ width, height }}>
      <div className="muscle-editor-header">
        <Space>
          <Input.Search
            placeholder="搜索肌肉"
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            style={{ width: 200 }}
          />
          
          <Select
            placeholder="肌肉类型"
            value={filterType}
            onChange={setFilterType}
            style={{ width: 120 }}
          >
            <Option value="all">所有类型</Option>
            {muscleTypes.map(type => (
              <Option key={type.value} value={type.value}>{type.label}</Option>
            ))}
          </Select>
          
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddMuscle}
            disabled={!editable}
          >
            {isAddingMuscle ? '确认添加' : '添加肌肉'}
          </Button>
          
          {isAddingMuscle && (
            <Select
              placeholder="选择肌肉类型"
              value={newMuscleType}
              onChange={setNewMuscleType}
              style={{ width: 150 }}
            >
              {muscleTypes.map(type => (
                <Option key={type.value} value={type.value}>{type.label}</Option>
              ))}
            </Select>
          )}
          
          <Button
            icon={<SaveOutlined />}
            onClick={handleSave}
            disabled={!editable}
          >
            保存
          </Button>
        </Space>
      </div>
      
      <div className="muscle-editor-content">
        <div className="muscle-list">
          <Table
            dataSource={filteredMuscles}
            columns={columns}
            rowKey="id"
            size="small"
            pagination={false}
            rowClassName={record => record.id === selectedMuscleId ? 'selected-row' : ''}
            onRow={record => ({
              onClick: () => handleMuscleSelect(record.id)
            })}
          />
        </div>
        
        {editingMuscle && (
          <div className="muscle-properties">
            <h3>肌肉属性</h3>
            <div className="property-group">
              <label>名称:</label>
              <Input
                value={editingMuscle.name}
                onChange={e => handleMusclePropertyChange('name', e.target.value)}
                disabled={!editable}
              />
            </div>
            
            <div className="property-group">
              <label>类型:</label>
              <Select
                value={editingMuscle.type}
                onChange={value => handleMusclePropertyChange('type', value)}
                disabled={!editable}
                style={{ width: '100%' }}
              >
                {muscleTypes.map(type => (
                  <Option key={type.value} value={type.value}>{type.label}</Option>
                ))}
              </Select>
            </div>
            
            <Divider orientation="left">位置</Divider>
            <div className="property-group">
              <label>X:</label>
              <Slider
                value={editingMuscle.position.x}
                onChange={value => handlePositionChange('x', value)}
                min={-1}
                max={1}
                step={0.01}
                disabled={!editable}
              />
            </div>
            
            <div className="property-group">
              <label>Y:</label>
              <Slider
                value={editingMuscle.position.y}
                onChange={value => handlePositionChange('y', value)}
                min={1}
                max={2}
                step={0.01}
                disabled={!editable}
              />
            </div>
            
            <div className="property-group">
              <label>Z:</label>
              <Slider
                value={editingMuscle.position.z}
                onChange={value => handlePositionChange('z', value)}
                min={-1}
                max={1}
                step={0.01}
                disabled={!editable}
              />
            </div>
            
            <Divider orientation="left">属性</Divider>
            <div className="property-group">
              <label>长度:</label>
              <Slider
                value={editingMuscle.length}
                onChange={value => handleMusclePropertyChange('length', value)}
                min={0.01}
                max={0.2}
                step={0.01}
                disabled={!editable}
              />
            </div>
            
            <div className="property-group">
              <label>强度:</label>
              <Slider
                value={editingMuscle.strength}
                onChange={value => handleMusclePropertyChange('strength', value)}
                min={0.1}
                max={2}
                step={0.1}
                disabled={!editable}
              />
            </div>
            
            <div className="property-group">
              <label>弹性:</label>
              <Slider
                value={editingMuscle.stiffness}
                onChange={value => handleMusclePropertyChange('stiffness', value)}
                min={0.1}
                max={1}
                step={0.1}
                disabled={!editable}
              />
            </div>
            
            <div className="property-group">
              <label>阻尼:</label>
              <Slider
                value={editingMuscle.damping}
                onChange={value => handleMusclePropertyChange('damping', value)}
                min={0.1}
                max={1}
                step={0.1}
                disabled={!editable}
              />
            </div>
            
            <div className="property-group">
              <label>颜色:</label>
              <ColorPicker
                color={editingMuscle.color}
                onChange={color => handleMusclePropertyChange('color', color)}
                disabled={!editable}
              />
            </div>
            
            <div className="property-group">
              <label>启用:</label>
              <Switch
                checked={editingMuscle.enabled}
                onChange={checked => handleMusclePropertyChange('enabled', checked)}
                disabled={!editable}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
