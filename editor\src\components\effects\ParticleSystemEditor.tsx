/**
 * 粒子系统编辑器
 */
import React from 'react';
import GenericEditor from '../common/GenericEditor';

/**
 * 粒子系统编辑器属性
 */
interface ParticleSystemEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * 粒子系统编辑器组件
 */
const ParticleSystemEditor: React.FC<ParticleSystemEditorProps> = ({ data, onChange }) => {
  return <GenericEditor title="粒子系统" data={data} onChange={onChange} />;
};

export default ParticleSystemEditor;
