/**
 * 资源服务
 * 负责加载和管理编辑器资源
 */
import { EventEmitter } from '../utils/EventEmitter';
import axios from 'axios';
import { ModelResource, AnimationResource } from '../components/resources/CharacterResourceBrowser';

class ResourceService extends EventEmitter {
  private modelCache: Map<string, ModelResource[]> = new Map();
  private animationCache: Map<string, AnimationResource[]> = new Map();
  private resourceBasePath: string = '/examples/assets';

  constructor() {
    super();
  }

  /**
   * 获取角色模型资源
   * @returns 角色模型资源列表
   */
  public async getCharacterModels(): Promise<ModelResource[]> {
    // 检查缓存
    if (this.modelCache.has('characters')) {
      return this.modelCache.get('characters')!;
    }

    try {
      // 加载模型资源配置
      const response = await axios.get(`${this.resourceBasePath}/models/characters/character-models.json`);
      const models = response.data.models as ModelResource[];

      // 处理路径
      models.forEach(model => {
        model.path = `${this.resourceBasePath}/models/characters/${model.path}`;
        model.thumbnail = `${this.resourceBasePath}/models/characters/${model.thumbnail}`;
      });

      // 缓存结果
      this.modelCache.set('characters', models);
      return models;
    } catch (error) {
      console.error('加载角色模型资源失败:', error);
      return [];
    }
  }

  /**
   * 获取动画资源
   * @returns 动画资源列表
   */
  public async getAnimationResources(): Promise<AnimationResource[]> {
    // 检查缓存
    if (this.animationCache.has('animations')) {
      return this.animationCache.get('animations')!;
    }

    try {
      // 加载动画资源配置
      const response = await axios.get(`${this.resourceBasePath}/animations/animation-resources.json`);
      const animations = response.data.animations as AnimationResource[];

      // 处理路径
      animations.forEach(animation => {
        animation.path = `${this.resourceBasePath}/animations/${animation.path}`;
        animation.thumbnail = `${this.resourceBasePath}/animations/${animation.thumbnail}`;
      });

      // 缓存结果
      this.animationCache.set('animations', animations);
      return animations;
    } catch (error) {
      console.error('加载动画资源失败:', error);
      return [];
    }
  }

  /**
   * 获取模型资源
   * @param id 模型ID
   * @returns 模型资源
   */
  public async getModelById(id: string): Promise<ModelResource | null> {
    const models = await this.getCharacterModels();
    return models.find(model => model.id === id) || null;
  }

  /**
   * 获取动画资源
   * @param id 动画ID
   * @returns 动画资源
   */
  public async getAnimationById(id: string): Promise<AnimationResource | null> {
    const animations = await this.getAnimationResources();
    return animations.find(animation => animation.id === id) || null;
  }

  /**
   * 加载模型
   * @param modelPath 模型路径
   * @returns 加载的模型
   */
  public async loadModel(modelPath: string): Promise<any> {
    try {
      // 这里应该使用引擎的模型加载器
      // 这里只是一个示例实现
      const engineAPI = (window as any).engineAPI;
      if (!engineAPI || !engineAPI.loadModel) {
        throw new Error('引擎API不可用');
      }

      return await engineAPI.loadModel(modelPath);
    } catch (error) {
      console.error('加载模型失败:', error);
      throw error;
    }
  }

  /**
   * 加载动画
   * @param animationPath 动画路径
   * @returns 加载的动画
   */
  public async loadAnimation(animationPath: string): Promise<any> {
    try {
      // 这里应该使用引擎的动画加载器
      // 这里只是一个示例实现
      const engineAPI = (window as any).engineAPI;
      if (!engineAPI || !engineAPI.loadAnimation) {
        throw new Error('引擎API不可用');
      }

      return await engineAPI.loadAnimation(animationPath);
    } catch (error) {
      console.error('加载动画失败:', error);
      throw error;
    }
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.modelCache.clear();
    this.animationCache.clear();
  }
}

// 创建单例
export const resourceService = new ResourceService();
