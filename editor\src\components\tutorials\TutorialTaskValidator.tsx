/**
 * 教程任务验证组件
 * 用于验证用户是否完成了特定的教程任务
 */
import React, { useEffect, useState } from 'react';
import { message } from 'antd';
import { CheckCircleOutlined } from '@ant-design/icons';
import { tutorialValidationService } from '../../services/TutorialValidationService';

/**
 * 教程任务验证组件属性
 */
interface TutorialTaskValidatorProps {
  tutorialId: string;
  taskId: string;
  actionId: string;
  children: React.ReactNode;
  validateOnClick?: boolean;
  validateOnMount?: boolean;
  showSuccessMessage?: boolean;
  successMessage?: string;
  onValidated?: () => void;
}

/**
 * 教程任务验证组件
 */
export const TutorialTaskValidator: React.FC<TutorialTaskValidatorProps> = ({
  tutorialId,
  taskId,
  actionId,
  children,
  validateOnClick = true,
  validateOnMount = false,
  showSuccessMessage = true,
  successMessage = '任务完成！',
  onValidated
}) => {
  const [validated, setValidated] = useState(false);
  
  // 验证任务
  const validateTask = () => {
    if (validated) return true;
    
    const result = tutorialValidationService.markActionPerformed(tutorialId, taskId, actionId);
    
    if (result) {
      setValidated(true);
      
      if (showSuccessMessage) {
        message.success({
          content: successMessage,
          icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />
        });
      }
      
      if (onValidated) {
        onValidated();
      }
    }
    
    return result;
  };
  
  // 挂载时验证
  useEffect(() => {
    if (validateOnMount) {
      validateTask();
    }
  }, []);
  
  // 如果是按钮，添加点击事件
  if (validateOnClick && React.isValidElement(children) && children.type === Button) {
    return React.cloneElement(children as React.ReactElement<any>, {
      onClick: (e: React.MouseEvent) => {
        // 调用原始的onClick
        if (children.props.onClick) {
          children.props.onClick(e);
        }
        
        // 验证任务
        validateTask();
      }
    });
  }
  
  // 如果是其他元素，包装在div中
  return (
    <div 
      onClick={validateOnClick ? validateTask : undefined}
      style={{ display: 'inline-block' }}
    >
      {children}
    </div>
  );
};

/**
 * 教程动作验证组件
 * 用于验证用户是否执行了特定的动作
 */
export const TutorialActionValidator: React.FC<{
  tutorialId: string;
  taskId: string;
  actionId: string;
}> = ({
  tutorialId,
  taskId,
  actionId
}) => {
  // 组件挂载时验证任务
  useEffect(() => {
    tutorialValidationService.markActionPerformed(tutorialId, taskId, actionId);
  }, [tutorialId, taskId, actionId]);
  
  // 不渲染任何内容
  return null;
};

export default TutorialTaskValidator;
