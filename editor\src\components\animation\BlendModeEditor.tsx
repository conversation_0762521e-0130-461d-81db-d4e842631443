/**
 * 混合模式编辑器
 */
import React from 'react';
import { Card, Select, Slider, Switch, InputNumber, Typography, Form } from 'antd';

const { Option } = Select;
const { Text } = Typography;

/**
 * 混合模式编辑器属性
 */
interface BlendModeEditorProps {
  /** 混合层 */
  layers: any[];
  /** 选中的层索引 */
  selectedLayer: number | null;
  /** 层更新回调 */
  onLayerUpdate: (index: number, layer: any) => void;
}

/**
 * 混合模式编辑器
 */
export const BlendModeEditor: React.FC<BlendModeEditorProps> = ({
  layers,
  selectedLayer,
  onLayerUpdate
}) => {
  const [form] = Form.useForm();

  // 当前选中的层
  const currentLayer = selectedLayer !== null ? layers[selectedLayer] : null;

  // 更新层属性
  const handleLayerChange = (field: string, value: any) => {
    if (selectedLayer !== null && currentLayer) {
      const updatedLayer = {
        ...currentLayer,
        [field]: value
      };
      onLayerUpdate(selectedLayer, updatedLayer);
    }
  };

  // 如果没有选中层，显示提示
  if (selectedLayer === null || !currentLayer) {
    return (
      <Card title="混合模式编辑器" size="small">
        <Text type="secondary">请选择一个混合层进行编辑</Text>
      </Card>
    );
  }

  return (
    <Card title="混合模式编辑器" size="small">
      <Form
        form={form}
        layout="vertical"
        initialValues={currentLayer}
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([field, value]) => {
            handleLayerChange(field, value);
          });
        }}
      >
        <Form.Item label="混合模式" name="blendMode">
          <Select>
            <Option value="override">覆盖 (Override)</Option>
            <Option value="additive">叠加 (Additive)</Option>
            <Option value="multiply">相乘 (Multiply)</Option>
            <Option value="screen">滤色 (Screen)</Option>
            <Option value="overlay">叠加 (Overlay)</Option>
          </Select>
        </Form.Item>

        <Form.Item label={`权重: ${currentLayer.weight || 1.0}`} name="weight">
          <Slider
            min={0}
            max={1}
            step={0.01}
            value={currentLayer.weight || 1.0}
            onChange={(value) => handleLayerChange('weight', value)}
          />
        </Form.Item>

        <Form.Item label={`时间缩放: ${currentLayer.timeScale || 1.0}`} name="timeScale">
          <Slider
            min={0.1}
            max={3.0}
            step={0.1}
            value={currentLayer.timeScale || 1.0}
            onChange={(value) => handleLayerChange('timeScale', value)}
          />
        </Form.Item>

        <Form.Item label="启用" name="enabled" valuePropName="checked">
          <Switch
            checked={currentLayer.enabled !== false}
            onChange={(checked) => handleLayerChange('enabled', checked)}
          />
        </Form.Item>

        <Form.Item label="循环" name="loop" valuePropName="checked">
          <Switch
            checked={currentLayer.loop || false}
            onChange={(checked) => handleLayerChange('loop', checked)}
          />
        </Form.Item>

        <Form.Item label="开始时间 (秒)" name="startTime">
          <InputNumber
            min={0}
            step={0.1}
            value={currentLayer.startTime || 0}
            onChange={(value) => handleLayerChange('startTime', value || 0)}
          />
        </Form.Item>

        <Form.Item label="结束时间 (秒)" name="endTime">
          <InputNumber
            min={0}
            step={0.1}
            value={currentLayer.endTime || 5}
            onChange={(value) => handleLayerChange('endTime', value || 5)}
          />
        </Form.Item>

        <Form.Item label="淡入时间 (秒)" name="fadeInTime">
          <InputNumber
            min={0}
            step={0.1}
            value={currentLayer.fadeInTime || 0}
            onChange={(value) => handleLayerChange('fadeInTime', value || 0)}
          />
        </Form.Item>

        <Form.Item label="淡出时间 (秒)" name="fadeOutTime">
          <InputNumber
            min={0}
            step={0.1}
            value={currentLayer.fadeOutTime || 0}
            onChange={(value) => handleLayerChange('fadeOutTime', value || 0)}
          />
        </Form.Item>
      </Form>

      <div style={{ marginTop: 16 }}>
        <Text strong>层信息:</Text>
        <div style={{ marginTop: 8 }}>
          <Text>动画片段: {currentLayer.clipName}</Text><br />
          <Text>混合模式: {currentLayer.blendMode || 'override'}</Text><br />
          <Text>权重: {(currentLayer.weight || 1.0).toFixed(2)}</Text><br />
          <Text>时间缩放: {(currentLayer.timeScale || 1.0).toFixed(2)}</Text>
        </div>
      </div>
    </Card>
  );
};

export default BlendModeEditor;
