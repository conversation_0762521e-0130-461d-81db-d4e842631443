/**
 * 地形算法参数组件
 * 用于显示不同地形算法的参数设置界面
 */
import React from 'react';
import { InputNumber, Slider, Button, Tooltip, Select, Switch } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { TerrainGenerationAlgorithm } from './TerrainGenerationEnums';
// 移除引擎直接导入
// 移除引擎直接导入
// 移除引擎直接导入
// 移除引擎直接导入

/**
 * 地形算法参数组件属性
 */
interface TerrainAlgorithmParamsProps {
  /** 算法类型 */
  algorithm: TerrainGenerationAlgorithm;
  /** 是否可编辑 */
  editable: boolean;
  /** 是否正在生成 */
  isGenerating: boolean;
  /** 热侵蚀参数 */
  thermalErosionParams: ThermalErosionParams;
  /** 热侵蚀参数变更回调 */
  onThermalErosionParamsChange: (params: ThermalErosionParams) => void;
  /** 水侵蚀参数 */
  hydraulicErosionParams: HydraulicErosionParams;
  /** 水侵蚀参数变更回调 */
  onHydraulicErosionParamsChange: (params: HydraulicErosionParams) => void;
  /** 河流参数 */
  riverParams: RiverGenerationParams;
  /** 河流参数变更回调 */
  onRiverParamsChange: (params: RiverGenerationParams) => void;
  /** 山脉参数 */
  mountainParams: MountainGenerationParams;
  /** 山脉参数变更回调 */
  onMountainParamsChange: (params: MountainGenerationParams) => void;
  /** 峡谷参数 */
  canyonParams: CanyonGenerationParams;
  /** 峡谷参数变更回调 */
  onCanyonParamsChange: (params: CanyonGenerationParams) => void;
  /** 洞穴参数 */
  caveParams: CaveGenerationParams;
  /** 洞穴参数变更回调 */
  onCaveParamsChange: (params: CaveGenerationParams) => void;
  /** 悬崖参数 */
  cliffParams: CliffGenerationParams;
  /** 悬崖参数变更回调 */
  onCliffParamsChange: (params: CliffGenerationParams) => void;
  /** 火山参数 */
  volcanoParams: VolcanoGenerationParams;
  /** 火山参数变更回调 */
  onVolcanoParamsChange: (params: VolcanoGenerationParams) => void;
  /** 地下河参数 */
  undergroundRiverParams: UndergroundRiverParams;
  /** 地下河参数变更回调 */
  onUndergroundRiverParamsChange: (params: UndergroundRiverParams) => void;
  /** 地下湖泊参数 */
  undergroundLakeParams: UndergroundLakeParams;
  /** 地下湖泊参数变更回调 */
  onUndergroundLakeParamsChange: (params: UndergroundLakeParams) => void;
  /** 特征组合参数 */
  featureCombinationParams: TerrainFeatureCombinationParams;
  /** 特征组合参数变更回调 */
  onFeatureCombinationParamsChange: (params: TerrainFeatureCombinationParams) => void;
}

/**
 * 地形算法参数组件
 */
const TerrainAlgorithmParams: React.FC<TerrainAlgorithmParamsProps> = ({
  algorithm,
  editable,
  isGenerating,
  thermalErosionParams,
  onThermalErosionParamsChange,
  hydraulicErosionParams,
  onHydraulicErosionParamsChange,
  riverParams,
  onRiverParamsChange,
  mountainParams,
  onMountainParamsChange,
  canyonParams,
  onCanyonParamsChange,
  caveParams,
  onCaveParamsChange,
  cliffParams,
  onCliffParamsChange,
  volcanoParams,
  onVolcanoParamsChange,
  undergroundRiverParams,
  onUndergroundRiverParamsChange,
  undergroundLakeParams,
  onUndergroundLakeParamsChange,
  featureCombinationParams,
  onFeatureCombinationParamsChange
}) => {
  const { t } = useTranslation();

  // 渲染地下河参数
  const renderUndergroundRiverParams = () => {
    return (
      <div className="terrain-generation-params">
        <h3>{t('terrain.generation.params.undergroundRiver')}</h3>
        <div className="param-group">
          <label>{t('terrain.generation.params.count')}</label>
          <InputNumber
            min={1}
            max={10}
            value={undergroundRiverParams.count}
            onChange={(value) => onUndergroundRiverParamsChange({ ...undergroundRiverParams, count: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.width')}</label>
          <Slider
            min={1}
            max={20}
            step={1}
            value={undergroundRiverParams.width}
            onChange={(value) => onUndergroundRiverParamsChange({ ...undergroundRiverParams, width: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.depth')}</label>
          <Slider
            min={0.1}
            max={0.5}
            step={0.1}
            value={undergroundRiverParams.depth}
            onChange={(value) => onUndergroundRiverParamsChange({ ...undergroundRiverParams, depth: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.sinuosity')}</label>
          <Slider
            min={0.1}
            max={1.0}
            step={0.1}
            value={undergroundRiverParams.sinuosity}
            onChange={(value) => onUndergroundRiverParamsChange({ ...undergroundRiverParams, sinuosity: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.branchProbability')}</label>
          <Slider
            min={0}
            max={1}
            step={0.1}
            value={undergroundRiverParams.branchProbability}
            onChange={(value) => onUndergroundRiverParamsChange({ ...undergroundRiverParams, branchProbability: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.minLength')}</label>
          <InputNumber
            min={10}
            max={100}
            value={undergroundRiverParams.minLength}
            onChange={(value) => onUndergroundRiverParamsChange({ ...undergroundRiverParams, minLength: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.maxLength')}</label>
          <InputNumber
            min={50}
            max={300}
            value={undergroundRiverParams.maxLength}
            onChange={(value) => onUndergroundRiverParamsChange({ ...undergroundRiverParams, maxLength: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.minDepthRatio')}</label>
          <Slider
            min={0.1}
            max={0.9}
            step={0.1}
            value={undergroundRiverParams.minDepthRatio}
            onChange={(value) => onUndergroundRiverParamsChange({ ...undergroundRiverParams, minDepthRatio: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.maxDepthRatio')}</label>
          <Slider
            min={0.1}
            max={0.9}
            step={0.1}
            value={undergroundRiverParams.maxDepthRatio}
            onChange={(value) => onUndergroundRiverParamsChange({ ...undergroundRiverParams, maxDepthRatio: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.caveProbability')}</label>
          <Slider
            min={0}
            max={1}
            step={0.1}
            value={undergroundRiverParams.caveProbability}
            onChange={(value) => onUndergroundRiverParamsChange({ ...undergroundRiverParams, caveProbability: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.seed')}</label>
          <InputNumber
            value={undergroundRiverParams.seed}
            onChange={(value) => onUndergroundRiverParamsChange({ ...undergroundRiverParams, seed: value as number })}
            disabled={!editable || isGenerating}
          />
          <Button
            onClick={() => onUndergroundRiverParamsChange({ ...undergroundRiverParams, seed: Math.floor(Math.random() * 1000) })}
            disabled={!editable || isGenerating}
          >
            {t('terrain.generation.params.randomSeed')}
          </Button>
        </div>
      </div>
    );
  };

  // 渲染地下湖泊参数
  const renderUndergroundLakeParams = () => {
    return (
      <div className="terrain-generation-params">
        <h3>{t('terrain.generation.params.undergroundLake')}</h3>
        <div className="param-group">
          <label>{t('terrain.generation.params.count')}</label>
          <InputNumber
            min={1}
            max={10}
            value={undergroundLakeParams.count}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, count: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.minRadius')}</label>
          <InputNumber
            min={5}
            max={50}
            value={undergroundLakeParams.minRadius}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, minRadius: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.maxRadius')}</label>
          <InputNumber
            min={10}
            max={100}
            value={undergroundLakeParams.maxRadius}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, maxRadius: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.depth')}</label>
          <Slider
            min={0.1}
            max={0.5}
            step={0.1}
            value={undergroundLakeParams.depth}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, depth: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.complexity')}</label>
          <Slider
            min={0}
            max={1}
            step={0.1}
            value={undergroundLakeParams.complexity}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, complexity: value as number })}
            disabled={!editable || isGenerating}
          />
          <Tooltip title={t('terrain.generation.params.complexityTooltip')}>
            <InfoCircleOutlined />
          </Tooltip>
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.depthVariation')}</label>
          <Slider
            min={0}
            max={1}
            step={0.1}
            value={undergroundLakeParams.depthVariation}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, depthVariation: value as number })}
            disabled={!editable || isGenerating}
          />
          <Tooltip title={t('terrain.generation.params.depthVariationTooltip')}>
            <InfoCircleOutlined />
          </Tooltip>
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.shapeType')}</label>
          <Select
            value={undergroundLakeParams.shapeType || 'circular'}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, shapeType: value })}
            disabled={!editable || isGenerating}
            style={{ width: '100%' }}
          >
            <Select.Option value="circular">{t('terrain.generation.params.shapeTypeCircular')}</Select.Option>
            <Select.Option value="irregular">{t('terrain.generation.params.shapeTypeIrregular')}</Select.Option>
            <Select.Option value="branching">{t('terrain.generation.params.shapeTypeBranching')}</Select.Option>
            <Select.Option value="connected">{t('terrain.generation.params.shapeTypeConnected')}</Select.Option>
            <Select.Option value="cavern">{t('terrain.generation.params.shapeTypeCavern')}</Select.Option>
          </Select>
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.generateStalactites')}</label>
          <Switch
            checked={undergroundLakeParams.generateStalactites || false}
            onChange={(checked) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, generateStalactites: checked })}
            disabled={!editable || isGenerating}
          />
        </div>
        {undergroundLakeParams.generateStalactites && (
          <div className="param-group">
            <label>{t('terrain.generation.params.stalactiteDensity')}</label>
            <Slider
              min={0.1}
              max={1.0}
              step={0.1}
              value={undergroundLakeParams.stalactiteDensity || 0.5}
              onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, stalactiteDensity: value as number })}
              disabled={!editable || isGenerating}
            />
          </div>
        )}
        <div className="param-group">
          <label>{t('terrain.generation.params.generateWaterfall')}</label>
          <Switch
            checked={undergroundLakeParams.generateWaterfall || false}
            onChange={(checked) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, generateWaterfall: checked })}
            disabled={!editable || isGenerating}
          />
        </div>
        {undergroundLakeParams.generateWaterfall && (
          <div className="param-group">
            <label>{t('terrain.generation.params.waterfallHeight')}</label>
            <Slider
              min={1}
              max={20}
              step={1}
              value={undergroundLakeParams.waterfallHeight || 10}
              onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, waterfallHeight: value as number })}
              disabled={!editable || isGenerating}
            />
          </div>
        )}
        <div className="param-group">
          <label>{t('terrain.generation.params.generateHotSpring')}</label>
          <Switch
            checked={undergroundLakeParams.generateHotSpring || false}
            onChange={(checked) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, generateHotSpring: checked })}
            disabled={!editable || isGenerating}
          />
        </div>
        {undergroundLakeParams.generateHotSpring && (
          <div className="param-group">
            <label>{t('terrain.generation.params.hotSpringTemperature')}</label>
            <Slider
              min={40}
              max={100}
              step={5}
              value={undergroundLakeParams.hotSpringTemperature || 80}
              onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, hotSpringTemperature: value as number })}
              disabled={!editable || isGenerating}
            />
          </div>
        )}
        <div className="param-group">
          <label>{t('terrain.generation.params.caveProbability')}</label>
          <Slider
            min={0}
            max={1}
            step={0.1}
            value={undergroundLakeParams.caveProbability}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, caveProbability: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.riverProbability')}</label>
          <Slider
            min={0}
            max={1}
            step={0.1}
            value={undergroundLakeParams.riverProbability}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, riverProbability: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.minDepthRatio')}</label>
          <Slider
            min={0.1}
            max={0.9}
            step={0.1}
            value={undergroundLakeParams.minDepthRatio}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, minDepthRatio: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.maxDepthRatio')}</label>
          <Slider
            min={0.1}
            max={0.9}
            step={0.1}
            value={undergroundLakeParams.maxDepthRatio}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, maxDepthRatio: value as number })}
            disabled={!editable || isGenerating}
          />
        </div>
        <div className="param-group">
          <label>{t('terrain.generation.params.seed')}</label>
          <InputNumber
            value={undergroundLakeParams.seed}
            onChange={(value) => onUndergroundLakeParamsChange({ ...undergroundLakeParams, seed: value as number })}
            disabled={!editable || isGenerating}
          />
          <Button
            onClick={() => onUndergroundLakeParamsChange({ ...undergroundLakeParams, seed: Math.floor(Math.random() * 1000) })}
            disabled={!editable || isGenerating}
          >
            {t('terrain.generation.params.randomSeed')}
          </Button>
        </div>
      </div>
    );
  };

  // 根据算法类型渲染不同的参数界面
  switch (algorithm) {
    case TerrainGenerationAlgorithm.UNDERGROUND_RIVER:
      return renderUndergroundRiverParams();
    case TerrainGenerationAlgorithm.UNDERGROUND_LAKE:
      return renderUndergroundLakeParams();
    default:
      return null;
  }
};

export default TerrainAlgorithmParams;
