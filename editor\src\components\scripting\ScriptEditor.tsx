/**
 * 脚本编辑器
 */
import React from 'react';
import GenericEditor from '../common/GenericEditor';

/**
 * 脚本编辑器属性
 */
interface ScriptEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * 脚本编辑器组件
 */
const ScriptEditor: React.FC<ScriptEditorProps> = ({ data, onChange }) => {
  return <GenericEditor title="脚本" data={data} onChange={onChange} />;
};

export default ScriptEditor;
