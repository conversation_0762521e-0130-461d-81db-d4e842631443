/**
 * 骨骼动画系统
 * 用于处理骨骼动画的加载、播放和混合
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
import type { Entity } from '../core/Entity';
import { AnimationClip } from './AnimationClip';
import { Animator } from './Animator';

/**
 * 骨骼动画选项
 */
export interface SkeletonAnimationOptions {
  /** 目标实体 */
  entity?: Entity;
  /** 骨骼模型 */
  skinnedMesh?: THREE.SkinnedMesh;
  /** 动画片段 */
  clips?: AnimationClip[];
  /** 是否自动播放 */
  autoPlay?: boolean;
  /** 默认混合时间（秒） */
  defaultBlendTime?: number;
  /** 时间缩放 */
  timeScale?: number;
}

/**
 * 骨骼动画组件
 */
export class SkeletonAnimation extends Component {
  /** 组件类型 */
  public static readonly type: string = 'SkeletonAnimation';

  /** 动画控制器 */
  private animator: Animator;

  /** 骨骼网格 */
  private skinnedMesh: THREE.SkinnedMesh | null = null;

  /** 骨骼 */
  private skeleton: THREE.Skeleton | null = null;

  /** 骨骼映射（名称到骨骼索引） */
  private boneMap: Map<string, number> = new Map();

  /** 原始骨骼位置 */
  private originalPositions: THREE.Vector3[] = [];

  /** 原始骨骼旋转 */
  private originalRotations: THREE.Quaternion[] = [];

  /** 原始骨骼缩放 */
  private originalScales: THREE.Vector3[] = [];

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建骨骼动画组件
   * @param options 骨骼动画选项
   */
  constructor(options: SkeletonAnimationOptions = {}) {
    super(SkeletonAnimation.type);

    // 创建动画控制器
    this.animator = new Animator({
      entity: options.entity,
      clips: options.clips,
      autoPlay: options.autoPlay,
      defaultBlendTime: options.defaultBlendTime,
      timeScale: options.timeScale
    });

    // 设置骨骼网格
    if (options.skinnedMesh) {
      this.setSkinnedMesh(options.skinnedMesh);
    } else if (options.entity) {
      this.setEntity(options.entity);
    }
  }

  /**
   * 设置目标实体
   * @param entity 实体
   */
  public setEntity(entity: Entity): void {
    this.animator.setEntity(entity);

    // 查找实体中的骨骼网格
    const transform = entity.getComponent('Transform') as any as any as any;
    if (transform) {
      // 使用类型断言访问getObject3D方法
      const transformWithObject3D = transform as any;
      if (typeof transformWithObject3D.getObject3D === 'function') {
        const object3D = transformWithObject3D.getObject3D();
        if (object3D) {
          const skinnedMesh = this.findSkinnedMesh(object3D);
          if (skinnedMesh) {
            this.setSkinnedMesh(skinnedMesh);
          }
        }
      }
    }
  }

  /**
   * 获取目标实体
   * @returns 实体
   */
  public getEntity(): Entity | null {
    return this.animator.getEntity();
  }

  /**
   * 设置骨骼网格
   * @param mesh 骨骼网格
   */
  public setSkinnedMesh(mesh: THREE.SkinnedMesh): void {
    this.skinnedMesh = mesh;
    this.skeleton = mesh.skeleton;

    // 初始化骨骼映射和原始变换
    this.initializeBones();
  }

  /**
   * 获取骨骼网格
   * @returns 骨骼网格
   */
  public getSkinnedMesh(): THREE.SkinnedMesh | null {
    return this.skinnedMesh;
  }

  /**
   * 获取骨骼
   * @returns 骨骼
   */
  public getSkeleton(): THREE.Skeleton | null {
    return this.skeleton;
  }

  /**
   * 添加动画片段
   * @param clip 动画片段
   */
  public addClip(clip: AnimationClip): void {
    this.animator.addClip(clip);
  }

  /**
   * 移除动画片段
   * @param name 动画片段名称
   * @returns 是否成功移除
   */
  public removeClip(name: string): boolean {
    return this.animator.removeClip(name);
  }

  /**
   * 获取动画片段
   * @param name 动画片段名称
   * @returns 动画片段，如果不存在则返回null
   */
  public getClip(name: string): AnimationClip | null {
    return this.animator.getClip(name);
  }

  /**
   * 获取所有动画片段
   * @returns 动画片段数组
   */
  public getClips(): AnimationClip[] {
    return this.animator.getClips();
  }

  /**
   * 播放动画
   * @param name 动画片段名称
   * @param blendTime 混合时间（秒），如果为0则立即切换
   * @returns 是否成功开始播放
   */
  public play(name: string, blendTime?: number): boolean {
    return this.animator.play(name, blendTime);
  }

  /**
   * 停止播放
   */
  public stop(): void {
    this.animator.stop();

    // 恢复骨骼到原始状态
    this.resetBones();
  }

  /**
   * 暂停播放
   */
  public pause(): void {
    this.animator.pause();
  }

  /**
   * 恢复播放
   */
  public resume(): void {
    this.animator.resume();
  }

  /**
   * 设置播放时间
   * @param time 时间（秒）
   */
  public setTime(time: number): void {
    this.animator.setTime(time);
  }

  /**
   * 获取播放时间
   * @returns 时间（秒）
   */
  public getTime(): number {
    return this.animator.getTime();
  }

  /**
   * 设置时间缩放
   * @param timeScale 时间缩放
   */
  public setTimeScale(timeScale: number): void {
    this.animator.setTimeScale(timeScale);
  }

  /**
   * 获取时间缩放
   * @returns 时间缩放
   */
  public getTimeScale(): number {
    return this.animator.getTimeScale();
  }

  /**
   * 设置循环模式
   * @param loop 是否循环
   */
  public setLoop(loop: boolean): void {
    this.animator.setLoop(loop);
  }

  /**
   * 获取循环模式
   * @returns 是否循环
   */
  public getLoop(): boolean {
    return this.animator.getLoop();
  }

  /**
   * 获取骨骼索引
   * @param name 骨骼名称
   * @returns 骨骼索引，如果不存在则返回-1
   */
  public getBoneIndex(name: string): number {
    return this.boneMap.get(name) ?? -1;
  }

  /**
   * 获取骨骼
   * @param name 骨骼名称
   * @returns 骨骼，如果不存在则返回null
   */
  public getBone(name: string): THREE.Bone | null {
    if (!this.skeleton) return null;

    const index = this.getBoneIndex(name);
    if (index === -1) return null;

    return this.skeleton.bones[index];
  }

  /**
   * 获取骨骼位置
   * @param name 骨骼名称
   * @returns 骨骼位置，如果不存在则返回null
   */
  public getBonePosition(name: string): THREE.Vector3 | null {
    const bone = this.getBone(name);
    return bone ? bone.position.clone() : null;
  }

  /**
   * 获取骨骼旋转
   * @param name 骨骼名称
   * @returns 骨骼旋转，如果不存在则返回null
   */
  public getBoneRotation(name: string): THREE.Quaternion | null {
    const bone = this.getBone(name);
    return bone ? bone.quaternion.clone() : null;
  }

  /**
   * 获取骨骼缩放
   * @param name 骨骼名称
   * @returns 骨骼缩放，如果不存在则返回null
   */
  public getBoneScale(name: string): THREE.Vector3 | null {
    const bone = this.getBone(name);
    return bone ? bone.scale.clone() : null;
  }

  /**
   * 设置骨骼位置
   * @param name 骨骼名称
   * @param position 位置
   * @returns 是否成功设置
   */
  public setBonePosition(name: string, position: THREE.Vector3): boolean {
    const bone = this.getBone(name);
    if (!bone) return false;

    bone.position.copy(position);
    return true;
  }

  /**
   * 设置骨骼旋转
   * @param name 骨骼名称
   * @param rotation 旋转
   * @returns 是否成功设置
   */
  public setBoneRotation(name: string, rotation: THREE.Quaternion): boolean {
    const bone = this.getBone(name);
    if (!bone) return false;

    bone.quaternion.copy(rotation);
    return true;
  }

  /**
   * 设置骨骼缩放
   * @param name 骨骼名称
   * @param scale 缩放
   * @returns 是否成功设置
   */
  public setBoneScale(name: string, scale: THREE.Vector3): boolean {
    const bone = this.getBone(name);
    if (!bone) return false;

    bone.scale.copy(scale);
    return true;
  }

  /**
   * 更新骨骼动画
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新动画控制器
    this.animator.update(deltaTime);

    // 更新骨骼
    if (this.skeleton) {
      this.skeleton.update();
    }
  }

  /**
   * 初始化骨骼
   */
  private initializeBones(): void {
    if (!this.skeleton || this.initialized) return;

    this.boneMap.clear();
    this.originalPositions = [];
    this.originalRotations = [];
    this.originalScales = [];

    // 遍历所有骨骼，创建映射和保存原始变换
    const bones = this.skeleton.bones;
    for (let i = 0; i < bones.length; i++) {
      const bone = bones[i];

      this.boneMap.set(bone.name, i);
      this.originalPositions[i] = bone.position.clone();
      this.originalRotations[i] = bone.quaternion.clone();
      this.originalScales[i] = bone.scale.clone();
    }

    this.initialized = true;
  }

  /**
   * 重置骨骼到原始状态
   */
  private resetBones(): void {
    if (!this.skeleton || !this.initialized) return;

    const bones = this.skeleton.bones;
    for (let i = 0; i < bones.length; i++) {
      const bone = bones[i];

      if (this.originalPositions[i]) {
        bone.position.copy(this.originalPositions[i]);
      }

      if (this.originalRotations[i]) {
        bone.quaternion.copy(this.originalRotations[i]);
      }

      if (this.originalScales[i]) {
        bone.scale.copy(this.originalScales[i]);
      }
    }

    this.skeleton.update();
  }

  /**
   * 在场景对象中查找骨骼网格
   * @param object 场景对象
   * @returns 骨骼网格，如果不存在则返回null
   */
  private findSkinnedMesh(object: THREE.Object3D): THREE.SkinnedMesh | null {
    // 如果当前对象是骨骼网格，则返回
    if (object instanceof THREE.SkinnedMesh) {
      return object;
    }

    // 递归查找子对象
    for (const child of object.children) {
      const result = this.findSkinnedMesh(child);
      if (result) {
        return result;
      }
    }

    return null;
  }

  /**
   * 从GLTF模型加载动画
   * @param gltf GLTF模型
   * @returns 加载的动画片段数量
   */
  public loadFromGLTF(gltf: any): number {
    if (!gltf.animations || gltf.animations.length === 0) {
      return 0;
    }

    // 设置骨骼网格
    if (!this.skinnedMesh) {
      const scene = gltf.scene || gltf;
      const skinnedMesh = this.findSkinnedMesh(scene);
      if (skinnedMesh) {
        this.setSkinnedMesh(skinnedMesh);
      }
    }

    // 加载动画片段
    let count = 0;
    for (const animation of gltf.animations) {
      const clip = AnimationClip.fromThreeAnimationClip(animation);
      this.addClip(clip);
      count++;
    }

    return count;
  }

  /**
   * 从FBX模型加载动画
   * @param fbx FBX模型
   * @returns 加载的动画片段数量
   */
  public loadFromFBX(fbx: any): number {
    if (!fbx.animations || fbx.animations.length === 0) {
      return 0;
    }

    // 设置骨骼网格
    if (!this.skinnedMesh) {
      const skinnedMesh = this.findSkinnedMesh(fbx);
      if (skinnedMesh) {
        this.setSkinnedMesh(skinnedMesh);
      }
    }

    // 加载动画片段
    let count = 0;
    for (const animation of fbx.animations) {
      const clip = AnimationClip.fromThreeAnimationClip(animation);
      this.addClip(clip);
      count++;
    }

    return count;
  }

  /**
   * 获取动画控制器
   * @returns 动画控制器
   */
  public getAnimator(): Animator {
    return this.animator;
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    (this.animator as any).dispose();
    this.skinnedMesh = null;
    this.skeleton = null;
    this.boneMap.clear();
    this.originalPositions = [];
    this.originalRotations = [];
    this.originalScales = [];
    this.initialized = false;

    super.dispose();
  }
}