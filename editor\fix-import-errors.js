#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 递归查找所有 .tsx 和 .ts 文件
function findFiles(dir, extensions = ['.tsx', '.ts']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // 跳过 node_modules 和其他不需要的目录
      if (!['node_modules', '.git', 'dist', 'build', 'coverage'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// 修复文件中的import错误
function fixImportErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 修复 dl-engine.mjs 导入为 dl-engine
    if (content.includes("from '../../libs/dl-engine.mjs'")) {
      content = content.replace(/from '\.\.\/\.\.\/libs\/dl-engine\.mjs'/g, "from '../../libs/dl-engine'");
      modified = true;
    }
    
    if (content.includes("from '../libs/dl-engine.mjs'")) {
      content = content.replace(/from '\.\.\/libs\/dl-engine\.mjs'/g, "from '../libs/dl-engine'");
      modified = true;
    }
    
    // 2. 移除重复的 import 语句
    const lines = content.split('\n');
    const imports = new Map();
    const newLines = [];
    let inImportBlock = false;
    let currentImportModule = '';
    let currentImportItems = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // 检测 import 语句开始
      if (line.trim().startsWith('import ') && line.includes('from ')) {
        // 单行 import
        const match = line.match(/import\s+(.+?)\s+from\s+['"](.+?)['"]/);
        if (match) {
          const [, importItems, modulePath] = match;
          if (!imports.has(modulePath)) {
            imports.set(modulePath, new Set());
            newLines.push(line);
          } else {
            // 跳过重复的 import
            modified = true;
          }
        } else {
          newLines.push(line);
        }
      } else if (line.trim().startsWith('import ') && line.includes('{') && !line.includes('}')) {
        // 多行 import 开始
        inImportBlock = true;
        const match = line.match(/import\s+\{(.+)/);
        if (match) {
          currentImportItems = [match[1].trim()];
        }
        newLines.push(line);
      } else if (inImportBlock && line.includes('}') && line.includes('from ')) {
        // 多行 import 结束
        inImportBlock = false;
        const match = line.match(/(.+?)\}\s+from\s+['"](.+?)['"]/);
        if (match) {
          currentImportItems.push(match[1].trim());
          currentImportModule = match[2];
        }
        newLines.push(line);
        currentImportItems = [];
        currentImportModule = '';
      } else if (inImportBlock) {
        // 多行 import 中间
        currentImportItems.push(line.trim().replace(',', ''));
        newLines.push(line);
      } else {
        newLines.push(line);
      }
    }
    
    // 3. 移除重复的 const { } = 解构语句
    const finalLines = [];
    const destructuringStatements = new Set();
    
    for (const line of newLines) {
      if (line.trim().startsWith('const {') && line.includes('} = ')) {
        const statement = line.trim();
        if (!destructuringStatements.has(statement)) {
          destructuringStatements.add(statement);
          finalLines.push(line);
        } else {
          modified = true;
        }
      } else {
        finalLines.push(line);
      }
    }
    
    if (modified) {
      const newContent = finalLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✓ 修复了 ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`✗ 修复 ${filePath} 时出错:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src 目录不存在');
    process.exit(1);
  }
  
  console.log('开始修复 import 错误...');
  
  const files = findFiles(srcDir);
  let fixedCount = 0;
  
  files.forEach(file => {
    if (fixImportErrors(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n修复完成！共修复了 ${fixedCount} 个文件。`);
}

if (require.main === module) {
  main();
}

module.exports = { fixImportErrors, findFiles };
