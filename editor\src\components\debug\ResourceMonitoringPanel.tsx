/**
 * 资源监控面板组件
 * 集成所有资源监控工具
 */
import React, { useState } from 'react';
import { Tabs, Card, Button, Space, Tooltip, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  AppstoreOutlined,
  AreaChartOutlined,
  WarningOutlined,
  BulbOutlined,
  SettingOutlined,
  ReloadOutlined,
  DashboardOutlined,
  ThunderboltOutlined,
  RocketOutlined,
  InfoCircleOutlined} from '@ant-design/icons';
import ResourceOptimizationPanel from './ResourceOptimizationPanel';
import ResourceBottleneckPanel from './ResourceBottleneckPanel';
import ResourceOptimizationSuggestionPanel from './ResourceOptimizationSuggestionPanel';
import MemoryAnalysisPanel from './MemoryAnalysisPanel';
import { PerformanceOptimizationService } from '../../services/PerformanceOptimizationService';
import './ResourceMonitoringPanel.less';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

/**
 * 资源监控面板属性接口
 */
interface ResourceMonitoringPanelProps {
  className?: string;
}

/**
 * 资源监控面板组件
 */
const ResourceMonitoringPanel: React.FC<ResourceMonitoringPanelProps> = ({ className }) => {
  const { t } = useTranslation();
  
  // 状态
  const [activeTab, setActiveTab] = useState('optimization');
  const [loading, setLoading] = useState(false);
  
  // 刷新所有数据
  const refreshAll = async () => {
    setLoading(true);
    
    try {
      // 获取性能优化服务
      const performanceService = PerformanceOptimizationService.getInstance();
      
      // 检测资源瓶颈
      await performanceService.detectResourceBottlenecks();
      
      // 生成优化建议
      await performanceService.generateOptimizationSuggestions();
      
      // 更新性能报告
      const report = await performanceService.getReport();
      
      setLoading(false);
    } catch (error) {
      console.error('刷新数据失败:', error);
      setLoading(false);
    }
  };
  
  // 自动检测瓶颈
  const detectBottlenecks = async () => {
    setActiveTab('bottlenecks');
    refreshAll();
  };
  
  // 查看优化建议
  const viewSuggestions = async () => {
    setActiveTab('suggestions');
    refreshAll();
  };
  
  // 分析内存使用
  const analyzeMemory = async () => {
    setActiveTab('memory');
    refreshAll();
  };
  
  return (
    <div className={`resource-monitoring-panel ${className || ''}`}>
      <div className="panel-header">
        <Space>
          <Title level={4}>{t('debug.resource.monitoringTitle')}</Title>
          <Tooltip title={t('debug.resource.refresh')}>
            <Button
              icon={<ReloadOutlined />}
              onClick={refreshAll}
              loading={loading}
            />
          </Tooltip>
        </Space>
        <Space>
          <Button
            type="primary"
            icon={<WarningOutlined />}
            onClick={detectBottlenecks}
          >
            {t('debug.resource.detectBottlenecks')}
          </Button>
          <Button
            type="primary"
            icon={<BulbOutlined />}
            onClick={viewSuggestions}
          >
            {t('debug.resource.viewSuggestions')}
          </Button>
          <Button
            icon={<AreaChartOutlined />}
            onClick={analyzeMemory}
          >
            {t('debug.resource.analyzeMemory')}
          </Button>
        </Space>
      </div>
      
      <Card className="resource-monitoring-card">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <AppstoreOutlined />
                {t('debug.resource.optimization')}
              </span>
            } 
            key="optimization"
          >
            <ResourceOptimizationPanel />
          </TabPane>
          <TabPane 
            tab={
              <span>
                <WarningOutlined />
                {t('debug.resource.bottlenecks')}
              </span>
            } 
            key="bottlenecks"
          >
            <ResourceBottleneckPanel />
          </TabPane>
          <TabPane 
            tab={
              <span>
                <BulbOutlined />
                {t('debug.resource.suggestions')}
              </span>
            } 
            key="suggestions"
          >
            <ResourceOptimizationSuggestionPanel />
          </TabPane>
          <TabPane 
            tab={
              <span>
                <AreaChartOutlined />
                {t('debug.resource.memory')}
              </span>
            } 
            key="memory"
          >
            <MemoryAnalysisPanel />
          </TabPane>
        </Tabs>
      </Card>
      
      <Card className="resource-monitoring-info" style={{ marginTop: 16 }}>
        <Space>
          <InfoCircleOutlined />
          <Text>{t('debug.resource.monitoringInfo')}</Text>
        </Space>
        <ul className="resource-monitoring-tips">
          <li>{t('debug.resource.tip1')}</li>
          <li>{t('debug.resource.tip2')}</li>
          <li>{t('debug.resource.tip3')}</li>
          <li>{t('debug.resource.tip4')}</li>
        </ul>
      </Card>
    </div>
  );
};

export default ResourceMonitoringPanel;
