/**
 * 材质状态切片
 */
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { materialService } from '../../services/materialService';

// 材质类型
export enum MaterialType {
  STANDARD = 'standard',
  PHYSICAL = 'physical',
  BASIC = 'basic',
  LAMBERT = 'lambert',
  PHONG = 'phong',
  TOON = 'toon'}

// 纹理接口
export interface Texture {
  id: string;
  type: string;
  url: string;
}

// 材质接口
export interface Material {
  id: string;
  name: string;
  type: MaterialType;
  color: string;
  metalness?: number;
  roughness?: number;
  emissive?: string;
  emissiveIntensity?: number;
  transparent?: boolean;
  opacity?: number;
  side?: 'front' | 'back' | 'double';
  textures?: Texture[];
  createdAt: string;
  updatedAt: string;
}

// 材质状态
interface MaterialsState {
  materials: Material[];
  selectedMaterialId: string | null;
  loading: boolean;
  error: string | null;
}

// 初始状态
const initialState: MaterialsState = {
  materials: [],
  selectedMaterialId: null,
  loading: false,
  error: null};

// 异步操作：获取所有材质
export const fetchMaterials = createAsyncThunk(
  'materials/fetchMaterials',
  async (_, { rejectWithValue }) => {
    try {
      const response = await materialService.getMaterials();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：获取单个材质
export const fetchMaterial = createAsyncThunk(
  'materials/fetchMaterial',
  async (materialId: string, { rejectWithValue }) => {
    try {
      const response = await materialService.getMaterial(materialId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：创建材质
export const createMaterial = createAsyncThunk(
  'materials/createMaterial',
  async (materialData: Omit<Material, 'id' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const response = await materialService.createMaterial(materialData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：更新材质
export const updateMaterial = createAsyncThunk(
  'materials/updateMaterial',
  async (materialData: Partial<Material> & { id: string }, { rejectWithValue }) => {
    try {
      const response = await materialService.updateMaterial(materialData.id, materialData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：删除材质
export const deleteMaterial = createAsyncThunk(
  'materials/deleteMaterial',
  async (materialId: string, { rejectWithValue }) => {
    try {
      await materialService.deleteMaterial(materialId);
      return materialId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 创建材质切片
const materialsSlice = createSlice({
  name: 'materials',
  initialState,
  reducers: {
    // 选择材质
    selectMaterial: (state, action: PayloadAction<string | null>) => {
      state.selectedMaterialId = action.payload;
    }},
  extraReducers: (builder) => {
    builder
      // 获取所有材质
      .addCase(fetchMaterials.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMaterials.fulfilled, (state, action) => {
        state.loading = false;
        state.materials = action.payload;
      })
      .addCase(fetchMaterials.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 获取单个材质
      .addCase(fetchMaterial.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMaterial.fulfilled, (state, action) => {
        state.loading = false;
        const materialIndex = state.materials.findIndex(material => material.id === action.payload.id);
        
        if (materialIndex !== -1) {
          state.materials[materialIndex] = action.payload;
        } else {
          state.materials.push(action.payload);
        }
      })
      .addCase(fetchMaterial.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 创建材质
      .addCase(createMaterial.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createMaterial.fulfilled, (state, action) => {
        state.loading = false;
        state.materials.push(action.payload);
      })
      .addCase(createMaterial.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 更新材质
      .addCase(updateMaterial.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateMaterial.fulfilled, (state, action) => {
        state.loading = false;
        const materialIndex = state.materials.findIndex(material => material.id === action.payload.id);
        
        if (materialIndex !== -1) {
          state.materials[materialIndex] = action.payload;
        }
      })
      .addCase(updateMaterial.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 删除材质
      .addCase(deleteMaterial.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteMaterial.fulfilled, (state, action) => {
        state.loading = false;
        state.materials = state.materials.filter(material => material.id !== action.payload);
        
        if (state.selectedMaterialId === action.payload) {
          state.selectedMaterialId = null;
        }
      })
      .addCase(deleteMaterial.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }});

export const { selectMaterial } = materialsSlice.actions;

export default materialsSlice.reducer;
