/**
 * 地形纹理预览组件
 * 用于预览地形纹理效果
 */
import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
// 移除引擎直接导入
// 移除引擎直接导入
import './TerrainTexturePreview.less';

/**
 * 地形纹理预览组件属性
 */
interface TerrainTexturePreviewProps {
  /** 是否启用 */
  enabled: boolean;
  /** 算法 */
  algorithm: string;
  /** 参数 */
  params: any;
  /** 纹理 */
  textures: {
    /** 漫反射纹理 */
    diffuse: string | null;
    /** 法线贴图 */
    normal: string | null;
    /** 粗糙度贴图 */
    roughness: string | null;
  };
  /** 宽度 */
  width?: number;
  /** 高度 */
  height?: number;
}

/**
 * 地形纹理预览组件
 */
const TerrainTexturePreview: React.FC<TerrainTexturePreviewProps> = ({
  enabled,
  algorithm,
  params,
  textures,
  width = 300,
  height = 300
}) => {
  // 容器引用
  const containerRef = useRef<HTMLDivElement>(null);
  // 场景引用
  const sceneRef = useRef<THREE.Scene | null>(null);
  // 相机引用
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  // 渲染器引用
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  // 控制器引用
  const controlsRef = useRef<OrbitControls | null>(null);
  // 地形网格引用
  const meshRef = useRef<THREE.Mesh | null>(null);
  // 是否已初始化
  const [isInitialized, setIsInitialized] = useState(false);
  // 分辨率
  const resolution = 64;

  // 初始化场景
  useEffect(() => {
    if (!containerRef.current || isInitialized) {
      return;
    }

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
    camera.position.set(0.5, 0.5, 1.5);
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(width, height);
    renderer.shadowMap.enabled = true;
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // 创建控制器
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.screenSpacePanning = false;
    controls.minDistance = 0.5;
    controls.maxDistance = 5;
    controls.maxPolarAngle = Math.PI / 2;
    controlsRef.current = controls;

    // 创建灯光
    const ambientLight = new THREE.AmbientLight(0x404040, 1);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 1, 1);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // 创建地形网格
    const geometry = new THREE.PlaneGeometry(1, 1, resolution - 1, resolution - 1);
    geometry.rotateX(-Math.PI / 2);

    // 创建材质
    const material = new THREE.MeshStandardMaterial({
      color: 0x808080,
      side: THREE.DoubleSide,
      wireframe: false
    });

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);
    mesh.receiveShadow = true;
    mesh.castShadow = true;
    scene.add(mesh);
    meshRef.current = mesh;

    // 创建坐标轴辅助
    const axesHelper = new THREE.AxesHelper(0.5);
    scene.add(axesHelper);

    // 动画循环
    const animate = () => {
      requestAnimationFrame(animate);
      if (controlsRef.current) {
        controlsRef.current.update();
      }
      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
    };
    animate();

    // 标记为已初始化
    setIsInitialized(true);

    // 清理函数
    return () => {
      if (rendererRef.current && containerRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
      }
      if (meshRef.current && sceneRef.current) {
        sceneRef.current.remove(meshRef.current);
      }
      if (controlsRef.current) {
        controlsRef.current.dispose();
      }
      if (rendererRef.current) {
        rendererRef.current.dispose();
      }
    };
  }, [width, height, isInitialized]);

  // 更新纹理
  useEffect(() => {
    if (!isInitialized || !meshRef.current || !enabled) {
      return;
    }

    // 获取材质
    const material = meshRef.current.material as THREE.MeshStandardMaterial;

    // 加载纹理
    const textureLoader = new THREE.TextureLoader();
    
    // 加载漫反射纹理
    if (textures.diffuse) {
      textureLoader.load(textures.diffuse, (texture) => {
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);
        material.map = texture;
        material.needsUpdate = true;
      });
    } else {
      material.map = null;
    }
    
    // 加载法线贴图
    if (textures.normal) {
      textureLoader.load(textures.normal, (texture) => {
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);
        material.normalMap = texture;
        material.needsUpdate = true;
      });
    } else {
      material.normalMap = null;
    }
    
    // 加载粗糙度贴图
    if (textures.roughness) {
      textureLoader.load(textures.roughness, (texture) => {
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);
        material.roughnessMap = texture;
        material.needsUpdate = true;
      });
    } else {
      material.roughnessMap = null;
      material.roughness = 0.5;
    }
    
    material.needsUpdate = true;
  }, [textures, isInitialized, enabled]);

  // 更新地形
  useEffect(() => {
    if (!isInitialized || !meshRef.current || !enabled) {
      return;
    }

    // 获取几何体
    const geometry = meshRef.current.geometry as THREE.BufferGeometry;
    const positions = geometry.attributes.position.array as Float32Array;

    // 创建高度数据
    const heightData = new Float32Array(resolution * resolution);

    // 根据算法生成高度数据
    generateHeightData(heightData, algorithm, params);

    // 更新几何体顶点高度
    for (let i = 0, j = 0; i < positions.length; i += 3, j++) {
      const x = Math.floor(j % resolution);
      const z = Math.floor(j / resolution);
      const index = z * resolution + x;
      positions[i + 1] = heightData[index] * 0.2; // 缩放高度以便于查看
    }

    // 更新几何体
    geometry.attributes.position.needsUpdate = true;
    geometry.computeVertexNormals();
    
    // 如果是地下河，添加可视化效果
    if (algorithm === 'underground_river' && meshRef.current) {
      // 移除之前的可视化效果
      const scene = meshRef.current.parent;
      if (scene) {
        const existingLines = scene.children.filter(child => child.name === 'underground-river-line');
        existingLines.forEach(line => scene.remove(line));
      }
      
      // 添加地下河可视化效果
      if (params && params.count && scene) {
        const riverCount = Math.min(params.count, 5); // 限制预览中的河流数量
        
        for (let i = 0; i < riverCount; i++) {
          // 创建随机河流路径
          const points = [];
          const startX = (Math.random() - 0.5) * 0.8;
          const startZ = (Math.random() - 0.5) * 0.8;
          let currentX = startX;
          let currentZ = startZ;
          let dirX = Math.random() - 0.5;
          let dirZ = Math.random() - 0.5;
          const length = Math.floor(10 + Math.random() * 20);
          
          // 标准化方向
          const dirLength = Math.sqrt(dirX * dirX + dirZ * dirZ);
          dirX /= dirLength;
          dirZ /= dirLength;
          
          // 生成路径点
          for (let j = 0; j < length; j++) {
            // 添加点
            points.push(new THREE.Vector3(currentX, 0.01, currentZ));
            
            // 更新方向（添加随机偏移）
            const angle = (Math.random() - 0.5) * params.sinuosity * 0.5;
            const newDirX = dirX * Math.cos(angle) - dirZ * Math.sin(angle);
            const newDirZ = dirX * Math.sin(angle) + dirZ * Math.cos(angle);
            dirX = newDirX;
            dirZ = newDirZ;
            
            // 更新位置
            currentX += dirX * 0.05;
            currentZ += dirZ * 0.05;
            
            // 确保位置在范围内
            if (currentX < -0.5 || currentX > 0.5 || currentZ < -0.5 || currentZ > 0.5) {
              break;
            }
          }
          
          // 创建线条几何体
          const geometry = new THREE.BufferGeometry().setFromPoints(points);
          const material = new THREE.LineBasicMaterial({ color: 0x0088ff, linewidth: 2 });
          const line = new THREE.Line(geometry, material);
          line.name = 'underground-river-line';
          scene.add(line);
        }
      }
    }
  }, [algorithm, params, isInitialized, enabled]);

  // 生成高度数据
  const generateHeightData = (heightData: Float32Array, algorithm: string, params: any) => {
    // 根据算法生成高度数据
    switch (algorithm) {
      case 'underground_river':
        generateUndergroundRiverHeightData(heightData, params);
        break;
      default:
        // 默认生成平坦地形
        for (let i = 0; i < heightData.length; i++) {
          heightData[i] = 0.1;
        }
        break;
    }
  };

  // 简化的地下河生成函数
  const generateUndergroundRiverHeightData = (heightData: Float32Array, params: any) => {
    // 实现简化的地下河生成逻辑
    // 先生成基础地形
    for (let i = 0; i < heightData.length; i++) {
      const x = i % resolution;
      const z = Math.floor(i / resolution);
      const nx = x / resolution - 0.5;
      const nz = z / resolution - 0.5;
      const distance = Math.sqrt(nx * nx + nz * nz);
      heightData[i] = 0.1 + 0.1 * Math.sin(distance * 20);
    }

    const { count = 3, width = 5, depth = 0.2, sinuosity = 0.6 } = params;

    // 地下河不会直接修改地形高度，但我们可以在预览中稍微降低一些区域来表示地下河的位置
    for (let i = 0; i < count; i++) {
      const startX = Math.floor(Math.random() * resolution);
      const startZ = Math.floor(Math.random() * resolution);
      const dirX = Math.cos(Math.random() * Math.PI * 2);
      const dirZ = Math.sin(Math.random() * Math.PI * 2);

      let currentX = startX;
      let currentZ = startZ;

      for (let step = 0; step < resolution * 0.3; step++) {
        const x = Math.floor(currentX);
        const z = Math.floor(currentZ);

        if (x < 0 || x >= resolution || z < 0 || z >= resolution) {
          break;
        }

        // 在地下河路径上稍微降低地形高度，仅用于预览
        for (let dz = -Math.ceil(width / 2); dz <= Math.ceil(width / 2); dz++) {
          for (let dx = -Math.ceil(width / 2); dx <= Math.ceil(width / 2); dx++) {
            const nx = x + dx;
            const nz = z + dz;

            if (nx < 0 || nx >= resolution || nz < 0 || nz >= resolution) {
              continue;
            }

            const distance = Math.sqrt(dx * dx + dz * dz);

            if (distance <= width / 2) {
              const index = nz * resolution + nx;
              const depthFactor = 0.05 * (1 - (distance / (width / 2)));
              heightData[index] -= depthFactor;
              heightData[index] = Math.max(0, heightData[index]);
            }
          }
        }

        // 更新位置
        const randomAngle = (Math.random() - 0.5) * sinuosity;
        const newDirX = dirX * Math.cos(randomAngle) - dirZ * Math.sin(randomAngle);
        const newDirZ = dirX * Math.sin(randomAngle) + dirZ * Math.cos(randomAngle);

        currentX += newDirX;
        currentZ += newDirZ;
      }
    }
  };

  return (
    <div className="terrain-texture-preview" ref={containerRef} style={{ width, height }}>
      {!enabled && (
        <div className="preview-disabled">
          <span>预览已禁用</span>
        </div>
      )}
    </div>
  );
};

export default TerrainTexturePreview;
