# 一般问题常见问答

本文档收集了关于DL（Digital Learning）引擎编辑器的一般性常见问题和解答，帮助您快速解决使用过程中遇到的基本问题。

## 安装和设置

### 如何安装DL（Digital Learning）引擎编辑器？

DL（Digital Learning）引擎编辑器可以通过以下步骤安装：

1. 访问官方网站下载最新版本的安装包
2. 运行安装程序并按照向导指示操作
3. 选择安装位置和组件
4. 完成安装后，启动编辑器
5. 首次启动时，按照初始设置向导配置编辑器

对于不同操作系统的详细安装说明，请参阅[安装指南](../getting-started/installation.md)。

### 编辑器的系统要求是什么？

DL（Digital Learning）引擎编辑器的最低系统要求：

- **操作系统**：Windows 10 64位、macOS 10.15+或Ubuntu 20.04+
- **处理器**：Intel Core i5-7500或AMD Ryzen 5 1600以上
- **内存**：8GB RAM（推荐16GB或更多）
- **显卡**：支持DirectX 11或OpenGL 4.3的显卡，2GB显存
- **存储**：10GB可用空间（SSD推荐）
- **网络**：宽带互联网连接（用于在线功能）

推荐系统配置：

- **处理器**：Intel Core i7-9700K或AMD Ryzen 7 3700X以上
- **内存**：32GB RAM
- **显卡**：NVIDIA GeForce RTX 2070或AMD Radeon RX 5700 XT以上，8GB显存
- **存储**：SSD，50GB可用空间

### 如何更新DL（Digital Learning）引擎编辑器？

更新DL（Digital Learning）引擎编辑器：

1. 打开编辑器
2. 点击主菜单中的"帮助 > 检查更新"
3. 如有可用更新，按照提示下载并安装
4. 重启编辑器完成更新

您也可以启用自动更新：

1. 点击"编辑 > 首选项"
2. 在"常规"选项卡中，找到"更新"部分
3. 勾选"自动检查更新"选项
4. 选择更新频率（每次启动、每天或每周）

### 如何激活许可证？

激活DL（Digital Learning）引擎编辑器许可证：

1. 打开编辑器
2. 点击主菜单中的"帮助 > 激活许可证"
3. 输入您的许可证密钥
4. 点击"激活"按钮
5. 如果您有互联网连接，许可证将自动激活
6. 对于离线激活，请按照屏幕上的离线激活指南操作

如果遇到激活问题，请联系*********************获取帮助。

## 项目管理

### 如何创建新项目？

创建新项目：

1. 打开DL（Digital Learning）引擎编辑器
2. 点击主菜单中的"文件 > 新建项目"
3. 选择项目模板（空白项目、基础场景或示例项目）
4. 设置项目名称和位置
5. 配置项目设置（如渲染设置、物理设置等）
6. 点击"创建"按钮

详细步骤请参阅[创建新项目](../getting-started/creating-projects.md)。

### 如何打开现有项目？

打开现有项目：

1. 打开DL（Digital Learning）引擎编辑器
2. 点击主菜单中的"文件 > 打开项目"
3. 浏览并选择项目文件（.irproj）
4. 点击"打开"按钮

或者，您可以：

1. 在启动界面上，点击"打开项目"
2. 从最近项目列表中选择项目
3. 或点击"浏览"找到项目文件

### 如何保存项目？

保存项目：

1. 点击主菜单中的"文件 > 保存项目"（或按Ctrl+S）
2. 如果是首次保存，输入项目名称和位置
3. 点击"保存"按钮

自动保存设置：

1. 点击"编辑 > 首选项"
2. 在"常规"选项卡中，找到"自动保存"部分
3. 勾选"启用自动保存"选项
4. 设置自动保存间隔（如5分钟）

### 如何管理项目版本？

DL（Digital Learning）引擎编辑器提供内置的版本控制功能：

1. 点击主菜单中的"文件 > 版本控制"
2. 选择以下选项之一：
   - **创建快照**：创建当前项目状态的快照
   - **查看历史**：查看项目的版本历史
   - **比较版本**：比较两个版本之间的差异
   - **恢复版本**：将项目恢复到之前的版本

您也可以集成外部版本控制系统（如Git）：

1. 点击"文件 > 版本控制 > 设置"
2. 选择版本控制系统
3. 配置存储库设置

详细信息请参阅[项目版本控制](../best-practices/collaboration-workflow.md#版本控制集成)。

## 界面和导航

### 如何自定义编辑器界面？

自定义编辑器界面：

1. 点击主菜单中的"视图 > 布局"
2. 选择预设布局或"自定义布局"
3. 拖动面板到所需位置
4. 右键点击面板标题可以浮动、停靠或关闭面板
5. 点击"视图 > 保存布局"保存自定义布局

自定义工具栏：

1. 右键点击工具栏区域
2. 选择"自定义工具栏"
3. 添加、移除或重新排列工具栏按钮

### 如何导航3D场景？

在3D场景中导航：

- **平移**：按住鼠标中键并拖动，或按住Alt键+左键并拖动
- **旋转**：按住Alt键+右键并拖动
- **缩放**：滚动鼠标滚轮，或按住Alt键+Shift+左键并上下拖动
- **聚焦**：选择对象后按F键将视图聚焦到该对象
- **正交视图**：按数字键1-6切换到不同的正交视图（顶视图、前视图等）
- **透视/正交切换**：按5键切换透视和正交视图

导航设置可在"编辑 > 首选项 > 导航"中自定义。

### 如何搜索和过滤项目资产？

搜索和过滤资产：

1. 在资产面板中，使用顶部的搜索框输入关键词
2. 使用过滤器按钮筛选特定类型的资产
3. 点击列标题可以按名称、类型、大小等排序
4. 使用标签系统为资产添加和筛选标签
5. 右键点击资产面板并选择"高级搜索"使用更复杂的搜索条件

### 如何使用快捷键提高工作效率？

DL（Digital Learning）引擎编辑器提供丰富的快捷键：

- **Ctrl+N**：新建项目
- **Ctrl+O**：打开项目
- **Ctrl+S**：保存项目
- **Ctrl+Z**：撤销
- **Ctrl+Y**：重做
- **Ctrl+D**：复制选中对象
- **Delete**：删除选中对象
- **F**：聚焦选中对象
- **W/E/R**：切换移动/旋转/缩放工具
- **Ctrl+1-9**：切换不同的编辑器面板

查看和自定义快捷键：

1. 点击"编辑 > 快捷键"
2. 浏览或搜索命令
3. 点击命令旁边的快捷键字段设置新快捷键

完整的快捷键列表请参阅[快捷键参考](../getting-started/keyboard-shortcuts.md)。

## 基本功能

### 如何导入资产？

导入资产到项目：

1. 点击主菜单中的"文件 > 导入 > 资产"
2. 或在资产面板中点击"导入"按钮
3. 选择要导入的文件
4. 在导入设置对话框中配置导入选项
5. 点击"导入"按钮

支持的资产类型包括：

- 3D模型（FBX、OBJ、glTF等）
- 纹理和图像（PNG、JPG、TGA、PSD等）
- 音频（WAV、MP3、OGG等）
- 动画（FBX、BVH等）
- 材质库（MTL等）

详细导入设置请参阅[资产导入指南](../best-practices/asset-management.md#资产导入)。

### 如何在场景中添加和操作对象？

添加对象到场景：

1. 点击主菜单中的"创建"
2. 选择要创建的对象类型（如几何体、光源、摄像机等）
3. 新对象将添加到场景中
4. 或从资产面板拖动模型到场景中

操作对象：

- **选择**：点击对象或在场景中框选
- **移动**：选择对象后使用移动工具(W)
- **旋转**：选择对象后使用旋转工具(E)
- **缩放**：选择对象后使用缩放工具(R)
- **复制**：选择对象后按Ctrl+D或右键选择"复制"
- **删除**：选择对象后按Delete键或右键选择"删除"

### 如何使用组件系统？

DL（Digital Learning）引擎使用组件系统构建对象功能：

1. 在场景中选择对象
2. 在属性面板中，点击"添加组件"按钮
3. 从组件列表中选择要添加的组件
4. 配置组件属性
5. 组件可以随时启用、禁用或移除

常用组件包括：

- **变换组件**：控制对象的位置、旋转和缩放
- **网格渲染器**：显示3D模型
- **碰撞体**：定义物理碰撞形状
- **刚体**：添加物理行为
- **音频源**：播放声音
- **脚本组件**：添加自定义行为

详细信息请参阅[组件系统指南](../features/component-system.md)。

### 如何保存和加载场景？

保存场景：

1. 点击主菜单中的"文件 > 保存场景"（或按Ctrl+S）
2. 如果是首次保存，输入场景名称
3. 点击"保存"按钮

加载场景：

1. 点击主菜单中的"文件 > 打开场景"
2. 选择要打开的场景文件
3. 点击"打开"按钮

管理多个场景：

1. 使用"文件 > 新建场景"创建新场景
2. 使用场景管理器（"窗口 > 场景管理器"）查看和管理项目中的所有场景
3. 场景可以嵌套：使用"场景引用"组件在一个场景中引用另一个场景

## 发布和分享

### 如何发布项目？

发布DL（Digital Learning）引擎项目：

1. 点击主菜单中的"文件 > 发布项目"
2. 选择目标平台（Web、Windows、macOS、移动设备等）
3. 配置发布设置（如分辨率、质量、压缩等）
4. 选择输出位置
5. 点击"发布"按钮

详细的发布选项和平台特定设置请参阅[发布指南](../getting-started/publishing.md)。

### 如何分享项目给其他人？

分享DL（Digital Learning）引擎项目：

1. **分享项目文件**：
   - 保存项目（"文件 > 保存项目"）
   - 将项目文件夹分享给其他DL（Digital Learning）引擎用户

2. **导出项目包**：
   - 点击"文件 > 导出 > 项目包"
   - 选择包含的资产和设置
   - 分享生成的.irpack文件

3. **发布可执行文件**：
   - 按照上述发布步骤生成可执行文件
   - 分享可执行文件给最终用户

4. **发布到Web**：
   - 发布为WebGL应用
   - 上传到网络服务器
   - 分享URL

### 如何获取帮助和支持？

获取DL（Digital Learning）引擎支持：

1. **内置帮助**：
   - 点击"帮助 > 文档"访问内置文档
   - 使用上下文帮助（按F1或点击属性面板中的"?"图标）

2. **在线资源**：
   - 访问官方网站的支持部分
   - 查阅在线文档和教程
   - 参与官方论坛讨论

3. **技术支持**：
   - 点击"帮助 > 联系支持"
   - 填写支持请求表单
   - 或发送邮件至*********************

4. **社区支持**：
   - 加入官方Discord社区
   - 参与社交媒体群组
   - 查看社区创建的教程和资源

## 其他常见问题

### 编辑器支持哪些语言？

DL（Digital Learning）引擎编辑器目前支持以下语言：

- 中文（简体）
- 中文（繁体）
- 英语
- 日语
- 韩语
- 法语
- 德语
- 西班牙语
- 俄语

更改语言设置：

1. 点击"编辑 > 首选项"
2. 选择"常规"选项卡
3. 在"语言"下拉菜单中选择所需语言
4. 点击"应用"并重启编辑器

### 如何报告错误或提出功能建议？

报告错误：

1. 点击"帮助 > 报告错误"
2. 填写错误报告表单，包括：
   - 错误描述
   - 重现步骤
   - 系统信息（自动收集）
   - 附加截图或日志文件
3. 点击"提交"按钮

提出功能建议：

1. 点击"帮助 > 提交建议"
2. 描述您的功能建议
3. 解释此功能如何改善工作流程
4. 点击"提交"按钮

您也可以在官方论坛或社区渠道分享您的想法和反馈。

### 如何备份项目？

备份DL（Digital Learning）引擎项目：

1. **手动备份**：
   - 点击"文件 > 保存项目副本"
   - 选择不同的位置保存项目副本
   - 或手动复制整个项目文件夹

2. **自动备份**：
   - 点击"编辑 > 首选项"
   - 在"常规"选项卡中，找到"备份"部分
   - 启用"自动备份"选项
   - 设置备份频率和保留数量

3. **云备份**：
   - 点击"文件 > 云存储 > 备份到云"
   - 选择云存储服务
   - 配置备份设置

建议定期备份重要项目，并使用多种备份方法确保数据安全。

## 相关资源

- [入门指南](../getting-started/getting-started.md)
- [性能问题FAQ](./performance-issues.md)
- [故障排除FAQ](./troubleshooting.md)
- [视频教程](../video-tutorials/)
