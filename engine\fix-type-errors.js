#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 修复TypeScript类型错误的脚本
 */

/**
 * 获取TypeScript编译错误
 * @returns {Array} 错误列表
 */
function getTypeScriptErrors() {
  try {
    execSync('./node_modules/.bin/tsc --noEmit', { stdio: 'pipe' });
    return [];
  } catch (error) {
    const output = error.stdout ? error.stdout.toString() : '';
    const lines = output.split('\n');
    const errors = [];
    
    for (const line of lines) {
      if (line.includes('error TS')) {
        const match = line.match(/^(.+?)\((\d+),(\d+)\): error TS(\d+): (.+)$/);
        if (match) {
          errors.push({
            file: match[1],
            line: parseInt(match[2]),
            column: parseInt(match[3]),
            code: match[4],
            message: match[5]
          });
        }
      }
    }
    
    return errors;
  }
}

/**
 * 修复Debug.log调用的参数类型错误
 * @param {string} filePath 文件路径
 * @returns {boolean} 是否修复了文件
 */
function fixDebugLogErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 修复Debug.log调用中的参数类型错误
    // 将对象参数转换为字符串
    const debugLogPattern = /Debug\.(log|warn|error)\(([^,]+),\s*([^)]+)\)/g;
    
    content = content.replace(debugLogPattern, (match, method, tag, message) => {
      // 如果message不是字符串字面量，尝试修复
      if (!message.trim().startsWith('"') && !message.trim().startsWith("'") && !message.trim().startsWith('`')) {
        // 检查是否是简单的变量或this
        if (message.trim() === 'this') {
          return `Debug.${method}(${tag}, 'this')`;
        } else if (message.trim().match(/^\w+$/)) {
          // 简单变量名，转换为字符串
          return `Debug.${method}(${tag}, String(${message.trim()}))`;
        } else {
          // 复杂表达式，使用JSON.stringify
          return `Debug.${method}(${tag}, JSON.stringify(${message.trim()}))`;
        }
      }
      return match;
    });
    
    if (content !== fs.readFileSync(filePath, 'utf8')) {
      fs.writeFileSync(filePath, content, 'utf8');
      modified = true;
    }
    
    return modified;
  } catch (error) {
    console.error(`修复文件失败: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 修复特定文件的类型错误
 * @param {string} filePath 文件路径
 * @param {Array} errors 错误列表
 * @returns {boolean} 是否修复了文件
 */
function fixFileTypeErrors(filePath, errors) {
  const fileErrors = errors.filter(e => e.file === filePath);
  if (fileErrors.length === 0) return false;
  
  let modified = false;
  
  // 修复Debug.log相关错误
  if (fileErrors.some(e => e.message.includes('not assignable to parameter of type \'string\''))) {
    if (fixDebugLogErrors(filePath)) {
      modified = true;
    }
  }
  
  return modified;
}

/**
 * 主函数
 */
function main() {
  console.log('开始修复TypeScript类型错误...\n');
  
  // 获取当前错误
  const errors = getTypeScriptErrors();
  console.log(`找到 ${errors.length} 个错误`);
  
  if (errors.length === 0) {
    console.log('没有发现错误！');
    return;
  }
  
  // 按文件分组错误
  const errorsByFile = {};
  for (const error of errors) {
    if (!errorsByFile[error.file]) {
      errorsByFile[error.file] = [];
    }
    errorsByFile[error.file].push(error);
  }
  
  console.log(`涉及 ${Object.keys(errorsByFile).length} 个文件\n`);
  
  let fixedFiles = 0;
  
  // 修复每个文件
  for (const [filePath, fileErrors] of Object.entries(errorsByFile)) {
    console.log(`检查文件: ${filePath} (${fileErrors.length} 个错误)`);
    
    if (fixFileTypeErrors(filePath, fileErrors)) {
      fixedFiles++;
      console.log(`  已修复: ${filePath}`);
    }
  }
  
  console.log(`\n修复完成！共修复了 ${fixedFiles} 个文件`);
  
  // 重新检查错误数量
  const remainingErrors = getTypeScriptErrors();
  console.log(`剩余错误数量: ${remainingErrors.length}`);
  
  if (remainingErrors.length < errors.length) {
    console.log(`减少了 ${errors.length - remainingErrors.length} 个错误`);
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixDebugLogErrors, getTypeScriptErrors };
