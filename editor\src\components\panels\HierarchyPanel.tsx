/**
 * 层级面板组件
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Button, Dropdown, Menu, Empty, Space, message} from 'antd';
import {
  FolderOutlined,
  FileOutlined,
  SearchOutlined,
  PlusOutlined,
  EllipsisOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  DeleteOutlined,
  CopyOutlined,
  ScissorOutlined,
  SnippetsOutlined,
  CameraOutlined,
  BulbOutlined,
  AppstoreOutlined,
  ApartmentOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import { setSelectedObject, setSelectedObjects } from '../../store/editor/editorSlice';

// 导入服务
import EngineService, { EngineEventType, SelectionMode } from '../../services/EngineService';
import SceneService, { SceneEventType, SceneGraphNode } from '../../services/SceneService';

const { Search } = Input;
const { DirectoryTree } = Tree;
const { confirm } = Modal;

// 树节点接口
interface TreeNode {
  key: string;
  title: string;
  icon?: React.ReactNode;
  isLeaf?: boolean;
  children?: TreeNode[];
  visible?: boolean;
  locked?: boolean;
  entityId?: string;
  entityType?: string;
}

const HierarchyPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const { sceneGraph, selectedObject, selectedObjects } = useAppSelector((state) => state.editor);

  const [searchValue, setSearchValue] = useState('');
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [treeData, setTreeData] = useState<TreeNode[]>([]);

  // 将场景图转换为树数据
  const convertSceneGraphToTreeData = useCallback((sceneGraph: SceneGraphNode | null): TreeNode[] => {
    if (!sceneGraph) {
      return [];
    }

    // 递归构建树节点
    const buildTreeNode = (node: SceneGraphNode): TreeNode => {
      // 确定图标
      let icon;
      if (node.components.includes('Camera')) {
        icon = <CameraOutlined />;
      } else if (node.components.some(c => c.includes('Light'))) {
        icon = <BulbOutlined />;
      } else if (node.children.length > 0) {
        icon = <FolderOutlined />;
      } else {
        icon = <AppstoreOutlined />;
      }

      // 构建树节点
      const treeNode: TreeNode = {
        key: node.id,
        title: node.name,
        icon,
        isLeaf: node.children.length === 0,
        visible: node.visible,
        locked: node.locked,
        entityId: node.id,
        entityType: node.type};

      // 处理子节点
      if (node.children.length > 0) {
        treeNode.children = node.children.map(child => buildTreeNode(child));
      }

      return treeNode;
    };

    return [buildTreeNode(sceneGraph)];
  }, []);

  // 监听场景图变化
  useEffect(() => {
    if (sceneGraph) {
      const newTreeData = convertSceneGraphToTreeData(sceneGraph);
      setTreeData(newTreeData);

      // 默认展开根节点
      if (newTreeData.length > 0) {
        setExpandedKeys([newTreeData[0].key]);
      }
    }
  }, [sceneGraph, convertSceneGraphToTreeData]);

  // 监听引擎事件
  useEffect(() => {
    // 监听对象选择事件
    const handleObjectSelected = (entity: any) => {
      // 展开到选中的节点
      if (entity && entity.getId) {
        const entityId = entity.getId();
        const path = findEntityPath(entityId);
        if (path.length > 0) {
          setExpandedKeys(path);
          setAutoExpandParent(true);
        }
      }
    };

    EngineService.on(EngineEventType.OBJECT_SELECTED, handleObjectSelected);

    return () => {
      EngineService.off(EngineEventType.OBJECT_SELECTED, handleObjectSelected);
    };
  }, [treeData]);

  // 查找实体路径
  const findEntityPath = (entityId: string): string[] => {
    const path: string[] = [];

    const findPath = (nodes: TreeNode[], id: string, currentPath: string[]): boolean => {
      for (const node of nodes) {
        const newPath = [...currentPath, node.key as string];

        if (node.entityId === id) {
          path.push(...newPath);
          return true;
        }

        if (node.children && findPath(node.children, id, newPath)) {
          return true;
        }
      }

      return false;
    };

    findPath(treeData, entityId, []);
    return path;
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);

    if (!value) {
      setExpandedKeys([]);
      setAutoExpandParent(false);
      return;
    }

    // 查找匹配的节点并展开其父节点
    const expandKeys: React.Key[] = [];

    const searchTree = (nodes: TreeNode[]) => {
      nodes.forEach((node) => {
        if (node.title.toLowerCase().includes(value.toLowerCase())) {
          expandKeys.push(node.key);
        }

        if (node.children) {
          searchTree(node.children);
        }
      });
    };

    searchTree(treeData);

    setExpandedKeys(expandKeys);
    setAutoExpandParent(true);
  };

  // 处理展开/折叠
  const handleExpand = (keys: React.Key[]) => {
    setExpandedKeys(keys);
    setAutoExpandParent(false);
  };

  // 处理选择
  const handleSelect = (selectedKeys: React.Key[], info: any) => {
    if (selectedKeys.length > 0) {
      const entityId = info.node.entityId;
      if (entityId) {
        // 获取实体并选择
        const scene = EngineService.getActiveScene();
        if (scene) {
          const entity = scene.getEntityById(entityId);
          if (entity) {
            EngineService.selectEntity(entity, SelectionMode.SINGLE);
          }
        }
      }
    } else {
      EngineService.clearSelection();
      dispatch(setSelectedObject(null));
    }
  };

  // 处理右键菜单
  const handleRightClick = ({ event, node }: any) => {
    event.preventDefault();

    const entityId = node.entityId;
    if (entityId) {
      // 获取实体并选择
      const scene = EngineService.getActiveScene();
      if (scene) {
        const entity = scene.getEntityById(entityId);
        if (entity) {
          EngineService.selectEntity(entity, SelectionMode.SINGLE);
        }
      }
    }
  };

  // 处理删除对象
  const handleDeleteObject = () => {
    if (!selectedObject || !selectedObject.entityId) return;

    confirm({
      title: t('editor.confirmDelete'),
      content: t('editor.deleteObjectConfirm', { name: selectedObject.title }),
      okText: t('common.delete'),
      okType: 'danger',
      cancelText: t('common.cancel'),
      onOk() {
        const scene = EngineService.getActiveScene();
        if (scene) {
          const entity = scene.getEntityById(selectedObject.entityId);
          if (entity) {
            EngineService.removeEntity(entity);
            message.success(t('editor.objectDeleted'));
          }
        }
      }});
  };

  // 处理添加对象
  const handleAddObject = (type: string) => {
    const scene = EngineService.getActiveScene();
    if (!scene) return;

    let entity;
    let parentEntity;

    // 如果有选中的对象，使用它作为父对象
    if (selectedObject && selectedObject.entityId) {
      parentEntity = scene.getEntityById(selectedObject.entityId);
    }

    // 根据类型创建不同的对象
    switch (type) {
      case 'empty':
        entity = EngineService.createEntity(t('editor.newObject'), parentEntity);
        break;

      case 'cube':
        entity = EngineService.createEntity(t('editor.cube'), parentEntity);
        // 添加立方体组件
        // entity.addComponent('MeshRenderer', { geometry: 'cube' });
        break;

      case 'sphere':
        entity = EngineService.createEntity(t('editor.sphere'), parentEntity);
        // 添加球体组件
        // entity.addComponent('MeshRenderer', { geometry: 'sphere' });
        break;

      case 'plane':
        entity = EngineService.createEntity(t('editor.plane'), parentEntity);
        // 添加平面组件
        // entity.addComponent('MeshRenderer', { geometry: 'plane' });
        break;

      case 'directionalLight':
        entity = EngineService.createEntity(t('editor.directionalLight'), parentEntity);
        // 添加定向光组件
        // entity.addComponent('DirectionalLight');
        break;

      case 'pointLight':
        entity = EngineService.createEntity(t('editor.pointLight'), parentEntity);
        // 添加点光源组件
        // entity.addComponent('PointLight');
        break;

      case 'spotLight':
        entity = EngineService.createEntity(t('editor.spotLight'), parentEntity);
        // 添加聚光灯组件
        // entity.addComponent('SpotLight');
        break;

      case 'ambientLight':
        entity = EngineService.createEntity(t('editor.ambientLight'), parentEntity);
        // 添加环境光组件
        // entity.addComponent('AmbientLight');
        break;

      case 'camera':
        entity = EngineService.createEntity(t('editor.camera'), parentEntity);
        // 添加相机组件
        // entity.addComponent('Camera');
        break;
    }

    if (entity) {
      // 选择新创建的实体
      EngineService.selectEntity(entity);
      message.success(t('editor.objectCreated'));
    }
  };

  // 处理可见性切换
  const handleToggleVisibility = (node: TreeNode, e: React.MouseEvent) => {
    e.stopPropagation();

    if (!node.entityId) return;

    const scene = EngineService.getActiveScene();
    if (scene) {
      const entity = scene.getEntityById(node.entityId);
      if (entity) {
        entity.setVisible(!node.visible);
        // 更新场景图
        SceneService.updateSceneGraph();
      }
    }
  };

  // 处理锁定切换
  const handleToggleLock = (node: TreeNode, e: React.MouseEvent) => {
    e.stopPropagation();

    if (!node.entityId) return;

    const scene = EngineService.getActiveScene();
    if (scene) {
      const entity = scene.getEntityById(node.entityId);
      if (entity) {
        entity.setLocked(!node.locked);
        // 更新场景图
        SceneService.updateSceneGraph();
      }
    }
  };

  // 右键菜单
  const contextMenu = (
    <Menu>
      <Menu.Item key="rename" icon={<FileOutlined />}>
        {t('editor.rename')}
      </Menu.Item>
      <Menu.Item key="duplicate" icon={<CopyOutlined />}>
        {t('editor.duplicate')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="cut" icon={<ScissorOutlined />}>
        {t('editor.cut')}
      </Menu.Item>
      <Menu.Item key="copy" icon={<CopyOutlined />}>
        {t('editor.copy')}
      </Menu.Item>
      <Menu.Item key="paste" icon={<SnippetsOutlined />}>
        {t('editor.paste')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="delete" icon={<DeleteOutlined />} onClick={handleDeleteObject}>
        {t('editor.delete')}
      </Menu.Item>
    </Menu>
  );

  // 添加菜单
  const addMenu = (
    <Menu>
      <Menu.Item key="empty" icon={<ApartmentOutlined />} onClick={() => handleAddObject('empty')}>
        {t('editor.emptyObject')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="cube" icon={<AppstoreOutlined />} onClick={() => handleAddObject('cube')}>
        {t('editor.cube')}
      </Menu.Item>
      <Menu.Item key="sphere" icon={<AppstoreOutlined />} onClick={() => handleAddObject('sphere')}>
        {t('editor.sphere')}
      </Menu.Item>
      <Menu.Item key="plane" icon={<AppstoreOutlined />} onClick={() => handleAddObject('plane')}>
        {t('editor.plane')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="directionalLight" icon={<BulbOutlined />} onClick={() => handleAddObject('directionalLight')}>
        {t('editor.directionalLight')}
      </Menu.Item>
      <Menu.Item key="pointLight" icon={<BulbOutlined />} onClick={() => handleAddObject('pointLight')}>
        {t('editor.pointLight')}
      </Menu.Item>
      <Menu.Item key="spotLight" icon={<BulbOutlined />} onClick={() => handleAddObject('spotLight')}>
        {t('editor.spotLight')}
      </Menu.Item>
      <Menu.Item key="ambientLight" icon={<BulbOutlined />} onClick={() => handleAddObject('ambientLight')}>
        {t('editor.ambientLight')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="camera" icon={<CameraOutlined />} onClick={() => handleAddObject('camera')}>
        {t('editor.camera')}
      </Menu.Item>
    </Menu>
  );

  // 自定义节点渲染
  const renderTreeNode = (nodeData: TreeNode): React.ReactNode => {
    const { title, key, visible, locked } = nodeData;

    const isSelected = selectedObject && selectedObject.entityId === nodeData.entityId;

    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '0 4px',
          backgroundColor: isSelected ? 'rgba(24, 144, 255, 0.1)' : 'transparent'}}
      >
        <span>{title}</span>
        <Space size={4}>
          <Button
            type="text"
            size="small"
            icon={visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
            style={{ padding: 0 }}
            onClick={(e) => handleToggleVisibility(nodeData, e)}
          />
          <Button
            type="text"
            size="small"
            icon={locked ? <LockOutlined /> : <UnlockOutlined />}
            style={{ padding: 0 }}
            onClick={(e) => handleToggleLock(nodeData, e)}
          />
        </Space>
      </div>
    );
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <Search
          placeholder={t('editor.searchObjects')}
          allowClear
          onChange={(e) => handleSearch(e.target.value)}
          style={{ width: '100%' }}
        />
      </div>
      <div style={{ display: 'flex', justifyContent: 'space-between', padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <Dropdown overlay={addMenu} placement="bottomLeft">
          <Button type="primary" icon={<PlusOutlined />} size="small">
            {t('editor.add')}
          </Button>
        </Dropdown>
        <Button
          type="text"
          icon={<DeleteOutlined />}
          size="small"
          disabled={!selectedObject}
          onClick={handleDeleteObject}
        />
      </div>
      <div style={{ flex: 1, overflow: 'auto' }}>
        {treeData.length > 0 ? (
          <DirectoryTree
            showIcon
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onExpand={handleExpand}
            onSelect={handleSelect}
            onRightClick={handleRightClick}
            titleRender={renderTreeNode}
            treeData={treeData}
            blockNode
            draggable
          />
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t('editor.noObjects')}
            style={{ margin: '20px 0' }}
          />
        )}
      </div>
    </div>
  );
};

export default HierarchyPanel;
