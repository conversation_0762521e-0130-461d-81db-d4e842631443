# AI模型、网络安全和调试工具集成示例

本示例展示了如何在dl-engine中集成AI模型、网络安全和调试工具，创建一个安全的AI驱动多用户应用。

## 功能概述

本示例实现了以下功能：

1. **AI模型集成**：
   - 加载和使用Stable Diffusion模型生成图像
   - 加载和使用BERT模型进行情感分析
   - 加载和使用GPT模型生成文本

2. **网络安全**：
   - 端到端加密通信
   - 安全的用户认证和会话管理
   - 数据签名和验证

3. **调试工具**：
   - 断点和单步执行
   - 性能分析
   - 变量监视

## 示例场景：安全的AI聊天室

这个示例创建了一个安全的AI聊天室，用户可以在其中：
- 使用GPT模型生成文本回复
- 使用Stable Diffusion模型生成图像
- 使用BERT模型分析消息情感
- 所有通信都经过端到端加密
- 所有用户都经过安全认证
- 开发者可以使用调试工具进行故障排除和性能优化

## 实现步骤

### 1. 设置AI模型

首先，我们需要设置AI模型管理器并加载所需的模型：

```typescript
// 创建AI模型管理器
const aiModelManager = new AIModelManager(world, {
  debug: true,
  useLocalModels: false,
  apiKeys: {
    [AIModelType.GPT]: 'your-gpt-api-key',
    [AIModelType.STABLE_DIFFUSION]: 'your-sd-api-key',
    [AIModelType.BERT]: 'your-bert-api-key'
  },
  baseUrls: {
    [AIModelType.GPT]: 'https://api.openai.com/v1',
    [AIModelType.STABLE_DIFFUSION]: 'https://api.stability.ai/v1',
    [AIModelType.BERT]: 'https://api.huggingface.co/models/bert-base-uncased'
  }
});

// 加载GPT模型
const gptModel = await aiModelManager.loadModel(AIModelType.GPT, {
  version: '3.5-turbo',
  temperature: 0.7,
  maxTokens: 100
});

// 加载Stable Diffusion模型
const sdModel = await aiModelManager.loadModel(AIModelType.STABLE_DIFFUSION, {
  version: 'stable-diffusion-xl-1.0',
  guidanceScale: 7.5
});

// 加载BERT模型
const bertModel = await aiModelManager.loadModel(AIModelType.BERT, {
  version: 'bert-base-uncased'
});
```

### 2. 设置网络安全系统

接下来，我们设置网络安全系统：

```typescript
// 创建网络安全系统
const securitySystem = new NetworkSecuritySystem(world, {
  debug: true,
  enableEndToEndEncryption: true,
  enableSecureKeyExchange: true,
  enableMessageSigning: true,
  enableSessionManagement: true,
  enableAccessControl: true,
  defaultEncryptionAlgorithm: EncryptionAlgorithm.AES,
  defaultHashAlgorithm: HashAlgorithm.SHA256
});

// 创建用户会话
const sessionId = securitySystem.createSession(userId, {
  role: 'user',
  permissions: ['chat', 'generate-image', 'analyze-sentiment']
});

// 创建安全令牌
const token = securitySystem.createToken(userId, ['chat', 'generate-image', 'analyze-sentiment']);
```

### 3. 设置调试工具

然后，我们设置调试工具：

```typescript
// 创建断点管理器
const breakpointManager = new BreakpointManager();

// 创建变量监视器
const variableWatcher = new VariableWatcher();

// 创建性能分析器
const performanceAnalyzer = new PerformanceAnalyzer({
  enabled: true,
  recordNodeExecutionTime: true,
  recordGraphExecutionTime: true
});

// 创建调试器
const debugger_ = new VisualScriptDebugger(
  graph,
  breakpointManager,
  variableWatcher,
  {
    debug: true,
    enableBreakpoints: true,
    enableStepping: true,
    enableVariableWatching: true,
    enablePerformanceAnalysis: true
  }
);

// 添加断点
debugger_.addBreakpoint('node1', graph.id, BreakpointType.CONDITIONAL, {
  condition: 'message.length > 100'
});

// 添加变量监视
debugger_.addVariableWatch('message', graph.id, {
  recordHistory: true
});
```

### 4. 创建视觉脚本图

现在，我们创建一个视觉脚本图来处理聊天消息：

```typescript
// 创建视觉脚本图
const graph = new Graph('chat-processor');

// 添加节点
const receiveMessageNode = graph.addNode('network/receiveMessage');
const decryptMessageNode = graph.addNode('network/security/decryptData');
const verifySignatureNode = graph.addNode('network/security/verifySignature');
const validateSessionNode = graph.addNode('network/security/validateSession');
const analyzeEmotionNode = graph.addNode('ai/emotion/analyze');
const generateTextNode = graph.addNode('ai/model/generateText');
const generateImageNode = graph.addNode('ai/model/generateImage');
const encryptResponseNode = graph.addNode('network/security/encryptData');
const signResponseNode = graph.addNode('network/security/generateSignature');
const sendResponseNode = graph.addNode('network/sendMessage');
const logNode = graph.addNode('debug/log');
const performanceTimerNode = graph.addNode('debug/performanceTimer');
const variableWatchNode = graph.addNode('debug/variableWatch');
const assertNode = graph.addNode('debug/assert');

// 连接节点
graph.connect(receiveMessageNode, 'message', decryptMessageNode, 'data');
graph.connect(decryptMessageNode, 'success', verifySignatureNode, 'flow');
graph.connect(verifySignatureNode, 'valid', validateSessionNode, 'flow');
graph.connect(validateSessionNode, 'valid', performanceTimerNode, 'start');
graph.connect(performanceTimerNode, 'startFlow', analyzeEmotionNode, 'flow');
graph.connect(analyzeEmotionNode, 'success', variableWatchNode, 'flow');
graph.connect(variableWatchNode, 'flow', generateTextNode, 'flow');
graph.connect(generateTextNode, 'success', performanceTimerNode, 'end');
graph.connect(performanceTimerNode, 'endFlow', encryptResponseNode, 'flow');
graph.connect(encryptResponseNode, 'flow', signResponseNode, 'flow');
graph.connect(signResponseNode, 'flow', sendResponseNode, 'flow');
graph.connect(sendResponseNode, 'success', logNode, 'flow');

// 设置节点属性
analyzeEmotionNode.setInputValue('model', bertModel);
generateTextNode.setInputValue('model', gptModel);
generateImageNode.setInputValue('model', sdModel);
```

### 5. 执行图并进行调试

最后，我们执行图并进行调试：

```typescript
// 开始调试
debugger_.start();

// 执行图
graph.execute({
  message: '你好，请生成一张猫的图片。',
  sessionId: sessionId,
  userId: userId
});

// 暂停执行
debugger_.pause();

// 单步执行
debugger_.step(StepType.STEP_INTO);

// 继续执行
debugger_.continue();

// 获取性能数据
const performanceData = performanceAnalyzer.getNodeExecutionTimeStatistics();
console.log('性能数据:', performanceData);

// 获取变量历史记录
const messageHistory = variableWatcher.getHistory('message', graph.id);
console.log('消息历史:', messageHistory);
```

## 完整示例

完整的示例代码可以在 `newsystem/examples/ai-security-debug` 目录中找到。该示例包括：

- `index.ts`：主入口文件
- `models/`：AI模型配置
- `security/`：网络安全配置
- `debug/`：调试工具配置
- `graphs/`：视觉脚本图定义
- `ui/`：用户界面组件

## 运行示例

要运行此示例，请执行以下命令：

```bash
cd newsystem/examples/ai-security-debug
npm install
npm start
```

然后在浏览器中打开 http://localhost:3000 查看示例应用。

## 学习资源

- [AI模型集成文档](../ai/index.md)
- [网络安全系统文档](../network/security.md)
- [调试工具文档](../visualscript/debug.md)
- [视觉脚本系统文档](../visualscript/index.md)

## 注意事项

- 在生产环境中，请确保使用安全的API密钥管理方式
- 定期更新加密算法和安全策略
- 使用性能分析工具优化AI模型的执行性能
- 为敏感操作添加适当的断点和日志
