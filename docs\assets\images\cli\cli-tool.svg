<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="700" height="400" xmlns="http://www.w3.org/2000/svg">
  <style>
    .terminal {
      fill: #1e1e1e;
      stroke: #333;
      stroke-width: 1;
      rx: 5;
      ry: 5;
    }
    .terminal-header {
      fill: #333;
      stroke: none;
      rx: 5 5 0 0;
      ry: 5 5 0 0;
    }
    .terminal-title {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
      fill: #fff;
      text-anchor: middle;
      dominant-baseline: middle;
    }
    .terminal-button {
      fill: #555;
      stroke: none;
      rx: 50%;
      ry: 50%;
    }
    .terminal-text {
      font-family: 'Consolas', monospace;
      font-size: 14px;
      fill: #f0f0f0;
    }
    .command {
      fill: #f0f0f0;
    }
    .prompt {
      fill: #4ec9b0;
    }
    .output {
      fill: #cccccc;
    }
    .success {
      fill: #4ec9b0;
    }
    .warning {
      fill: #ce9178;
    }
    .error {
      fill: #f44747;
    }
    .highlight {
      fill: #569cd6;
    }
  </style>
  
  <!-- Terminal Window -->
  <rect x="50" y="50" width="600" height="300" class="terminal" />
  
  <!-- Terminal Header -->
  <rect x="50" y="50" width="600" height="30" class="terminal-header" />
  <text x="350" y="65" class="terminal-title">命令行终端 - IR-CLI</text>
  
  <!-- Terminal Buttons -->
  <circle cx="70" cy="65" r="6" fill="#ff5f57" class="terminal-button" />
  <circle cx="90" cy="65" r="6" fill="#febc2e" class="terminal-button" />
  <circle cx="110" cy="65" r="6" fill="#28c840" class="terminal-button" />
  
  <!-- Terminal Content -->
  <text x="70" y="100" class="terminal-text prompt">user@dl-engine:~$</text>
  <text x="200" y="100" class="terminal-text command">ir-cli --version</text>
  
  <text x="70" y="120" class="terminal-text output">IR Engine CLI v2.5.0</text>
  <text x="70" y="140" class="terminal-text output">IR Engine Core v3.2.1</text>
  
  <text x="70" y="170" class="terminal-text prompt">user@dl-engine:~$</text>
  <text x="200" y="170" class="terminal-text command">ir-cli build --platform web --mode production</text>
  
  <text x="70" y="190" class="terminal-text output">[1/5] 初始化构建环境...</text>
  <text x="70" y="210" class="terminal-text success">[2/5] 编译脚本... 完成</text>
  <text x="70" y="230" class="terminal-text success">[3/5] 处理资产... 完成</text>
  <text x="70" y="250" class="terminal-text success">[4/5] 优化输出... 完成</text>
  <text x="70" y="270" class="terminal-text success">[5/5] 生成输出文件... 完成</text>
  
  <text x="70" y="290" class="terminal-text success">✓ 构建成功! 输出目录: ./dist</text>
  <text x="70" y="310" class="terminal-text output">总大小: 15.2 MB (压缩后: 4.8 MB)</text>
  <text x="70" y="330" class="terminal-text output">构建时间: 8.5s</text>
  
  <!-- Cursor -->
  <text x="70" y="350" class="terminal-text prompt">user@dl-engine:~$</text>
  <rect x="200" y="340" width="8" height="16" fill="#fff" opacity="0.7" />
</svg>
