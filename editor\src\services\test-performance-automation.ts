/**
 * 性能测试自动化服务测试文件
 * 用于验证修复后的服务是否正常工作
 */
import { PerformanceTestAutomationService, TestConfig, PerformanceTestEventType } from './PerformanceTestAutomationService';

// 创建测试配置
const testConfig: TestConfig = {
  name: '修复验证测试',
  description: '验证性能测试自动化服务修复是否成功',
  scenes: [
    {
      id: 'test-scene',
      name: '测试场景',
      path: 'scenes/test.json',
      duration: 2000,
      warmupDuration: 500}
  ],
  repetitions: 1,
  autoSaveReport: false};

// 测试服务
function testPerformanceAutomationService() {
  console.log('开始测试性能测试自动化服务...');
  
  try {
    // 获取服务实例
    const service = PerformanceTestAutomationService.getInstance();
    console.log('✓ 成功获取服务实例');
    
    // 添加事件监听器
    service.on(PerformanceTestEventType.TEST_STARTED, (data) => {
      console.log('✓ 测试开始事件触发:', data);
    });
    
    service.on(PerformanceTestEventType.TEST_COMPLETED, (data) => {
      console.log('✓ 测试完成事件触发:', data);
      console.log('✓ 所有测试通过！性能测试自动化服务修复成功！');
    });
    
    service.on(PerformanceTestEventType.TEST_FAILED, (data) => {
      console.log('✗ 测试失败事件触发:', data);
    });
    
    // 获取预定义测试
    const predefinedTests = service.getPredefinedTests();
    console.log('✓ 成功获取预定义测试，数量:', predefinedTests.size);
    
    // 运行测试
    const success = service.runTest(testConfig);
    if (success) {
      console.log('✓ 成功启动测试');
    } else {
      console.log('✗ 启动测试失败');
    }
    
    // 检查测试状态
    const isRunning = service.isRunningTest();
    console.log('✓ 测试运行状态:', isRunning);
    
  } catch (error) {
    console.error('✗ 测试过程中发生错误:', error);
  }
}

// 导出测试函数
export { testPerformanceAutomationService };

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).testPerformanceAutomationService = testPerformanceAutomationService;
  console.log('测试函数已添加到 window.testPerformanceAutomationService');
  console.log('可以在浏览器控制台中运行: testPerformanceAutomationService()');
}
