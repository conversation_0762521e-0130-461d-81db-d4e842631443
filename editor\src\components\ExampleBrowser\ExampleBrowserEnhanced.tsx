/**
 * 增强版示例项目浏览器组件
 * 用于浏览、搜索和加载示例项目
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Select, Button, Row, Col, Spin, Empty, message, Tabs, Badge, Tooltip, Card, Tag} from 'antd';
import { 
  SearchOutlined, 
  FilterOutlined, 
  ReloadOutlined, 
  StarOutlined, 
  StarFilled,
  HistoryOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  SortAscendingOutlined,
  EyeOutlined,
  DownloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import ExampleCard from './ExampleCard';
import ExampleFilter from './ExampleFilter';
import ExampleDetail from './ExampleDetail';
import ImportDialog from './ImportDialog';
import { fetchExamples, fetchExampleById, toggleFavorite } from '../../services/exampleService';
import { Example, ExampleCategory } from '../../types/example';
import './ExampleBrowser.less';

const { Header, Content, Sider } = Layout;
const { Search } = Input;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { TabPane } = Tabs;

/**
 * 增强版示例项目浏览器组件
 */
const ExampleBrowserEnhanced: React.FC = () => {
  const { t } = useTranslation();
  const [examples, setExamples] = useState<Example[]>([]);
  const [filteredExamples, setFilteredExamples] = useState<Example[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchText, setSearchText] = useState<string>('');
  const [selectedExample, setSelectedExample] = useState<Example | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [importDialogVisible, setImportDialogVisible] = useState<boolean>(false);
  const [sortBy, setSortBy] = useState<string>('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState<string>('all');
  const [recentlyViewed, setRecentlyViewed] = useState<Example[]>([]);
  const [favorites, setFavorites] = useState<Example[]>([]);

  /**
   * 加载示例项目数据
   */
  useEffect(() => {
    loadExamples();
    loadRecentlyViewed();
    loadFavorites();
  }, []);

  /**
   * 加载示例项目
   */
  const loadExamples = async () => {
    setLoading(true);
    try {
      const data = await fetchExamples();
      setExamples(data);
      setFilteredExamples(data);
    } catch (error) {
      console.error('加载示例项目失败:', error);
      message.error(t('exampleBrowser.loadError'));
    }
    setLoading(false);
  };

  /**
   * 加载最近查看的示例项目
   */
  const loadRecentlyViewed = () => {
    try {
      const recentlyViewedJson = localStorage.getItem('recentlyViewedExamples');
      if (recentlyViewedJson) {
        const recentlyViewedIds = JSON.parse(recentlyViewedJson);
        // 这里需要从服务器获取完整的示例项目数据
        // 简化实现，实际应该调用API
        fetchExamples().then(allExamples => {
          const recentExamples = recentlyViewedIds
            .map((id: string) => allExamples.find(example => example.id === id))
            .filter(Boolean);
          setRecentlyViewed(recentExamples);
        });
      }
    } catch (error) {
      console.error('加载最近查看的示例项目失败:', error);
    }
  };

  /**
   * 加载收藏的示例项目
   */
  const loadFavorites = () => {
    try {
      const favoritesJson = localStorage.getItem('favoriteExamples');
      if (favoritesJson) {
        const favoriteIds = JSON.parse(favoritesJson);
        // 这里需要从服务器获取完整的示例项目数据
        // 简化实现，实际应该调用API
        fetchExamples().then(allExamples => {
          const favoriteExamples = favoriteIds
            .map((id: string) => allExamples.find(example => example.id === id))
            .filter(Boolean);
          setFavorites(favoriteExamples);
        });
      }
    } catch (error) {
      console.error('加载收藏的示例项目失败:', error);
    }
  };

  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    loadExamples();
    message.success(t('exampleBrowser.refreshSuccess'));
  };

  /**
   * 处理搜索
   * @param value 搜索文本
   */
  const handleSearch = (value: string) => {
    setSearchText(value);
    filterExamples(selectedCategory, selectedTags, value);
  };

  /**
   * 处理排序方式变更
   * @param value 排序方式
   */
  const handleSortChange = (value: string) => {
    setSortBy(value);
    sortExamples(filteredExamples, value);
  };

  /**
   * 处理视图模式变更
   * @param mode 视图模式
   */
  const handleViewModeChange = (mode: 'grid' | 'list') => {
    setViewMode(mode);
  };

  /**
   * 处理标签页变更
   * @param key 标签页键值
   */
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    if (key === 'all') {
      setFilteredExamples(examples);
    } else if (key === 'recent') {
      setFilteredExamples(recentlyViewed);
    } else if (key === 'favorites') {
      setFilteredExamples(favorites);
    }
  };

  /**
   * 处理示例项目选择
   * @param example 示例项目
   */
  const handleExampleSelect = (example: Example) => {
    setSelectedExample(example);
    
    // 添加到最近查看
    try {
      const recentlyViewedJson = localStorage.getItem('recentlyViewedExamples');
      let recentlyViewedIds = recentlyViewedJson ? JSON.parse(recentlyViewedJson) : [];
      
      // 移除已存在的相同ID
      recentlyViewedIds = recentlyViewedIds.filter((id: string) => id !== example.id);
      
      // 添加到开头
      recentlyViewedIds.unshift(example.id);
      
      // 限制最多10个
      if (recentlyViewedIds.length > 10) {
        recentlyViewedIds = recentlyViewedIds.slice(0, 10);
      }
      
      localStorage.setItem('recentlyViewedExamples', JSON.stringify(recentlyViewedIds));
      
      // 更新最近查看列表
      loadRecentlyViewed();
    } catch (error) {
      console.error('保存最近查看的示例项目失败:', error);
    }
  };

  /**
   * 处理收藏切换
   * @param example 示例项目
   * @param favorited 是否收藏
   */
  const handleToggleFavorite = (example: Example, favorited: boolean) => {
    try {
      // 调用API切换收藏状态
      toggleFavorite(example.id, favorited);
      
      // 更新本地存储
      const favoritesJson = localStorage.getItem('favoriteExamples');
      let favoriteIds = favoritesJson ? JSON.parse(favoritesJson) : [];
      
      if (favorited) {
        // 添加到收藏
        if (!favoriteIds.includes(example.id)) {
          favoriteIds.push(example.id);
        }
      } else {
        // 从收藏中移除
        favoriteIds = favoriteIds.filter((id: string) => id !== example.id);
      }
      
      localStorage.setItem('favoriteExamples', JSON.stringify(favoriteIds));
      
      // 更新收藏列表
      loadFavorites();
      
      // 更新示例项目的收藏状态
      const updatedExamples = examples.map(item => {
        if (item.id === example.id) {
          return { ...item, favorited };
        }
        return item;
      });
      
      setExamples(updatedExamples);
      
      // 如果当前选中的示例项目是被切换的，也需要更新
      if (selectedExample && selectedExample.id === example.id) {
        setSelectedExample({ ...selectedExample, favorited });
      }
      
      message.success(favorited ? t('exampleBrowser.addedToFavorites') : t('exampleBrowser.removedFromFavorites'));
    } catch (error) {
      console.error('切换收藏状态失败:', error);
      message.error(t('exampleBrowser.favoriteError'));
    }
  };

  /**
   * 处理导入点击
   */
  const handleImportClick = () => {
    if (selectedExample) {
      setImportDialogVisible(true);
    }
  };

  /**
   * 处理导入确认
   * @param projectName 项目名称
   * @param location 位置
   */
  const handleImportConfirm = (projectName: string, location: string) => {
    // 这里应该调用API导入示例项目
    message.success(`${t('exampleBrowser.importSuccess')}: ${projectName} ${t('exampleBrowser.to')} ${location}`);
    setImportDialogVisible(false);
  };

  /**
   * 过滤示例项目
   * @param category 类别
   * @param tags 标签列表
   * @param text 搜索文本
   */
  const filterExamples = (category: string, tags: string[], text: string) => {
    let filtered = [...examples];
    
    // 按类别过滤
    if (category && category !== 'all') {
      filtered = filtered.filter(example => example.category === category);
    }
    
    // 按标签过滤
    if (tags && tags.length > 0) {
      filtered = filtered.filter(example => 
        tags.every(tag => example.tags.includes(tag))
      );
    }
    
    // 按文本搜索
    if (text) {
      const lowerText = text.toLowerCase();
      filtered = filtered.filter(example => 
        example.title.toLowerCase().includes(lowerText) || 
        example.description.toLowerCase().includes(lowerText) ||
        example.tags.some(tag => tag.toLowerCase().includes(lowerText))
      );
    }
    
    // 排序
    sortExamples(filtered, sortBy);
  };

  /**
   * 排序示例项目
   * @param examples 示例项目列表
   * @param sortBy 排序方式
   */
  const sortExamples = (examples: Example[], sortBy: string) => {
    let sorted = [...examples];
    
    switch (sortBy) {
      case 'newest':
        sorted.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
        break;
      case 'oldest':
        sorted.sort((a, b) => new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime());
        break;
      case 'name':
        sorted.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'popular':
        sorted.sort((a, b) => b.popularity - a.popularity);
        break;
      default:
        break;
    }
    
    setFilteredExamples(sorted);
  };

  /**
   * 渲染示例项目列表
   */
  const renderExampleList = () => {
    if (filteredExamples.length === 0) {
      return (
        <Empty
          description={t('exampleBrowser.noExamples')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    if (viewMode === 'grid') {
      return (
        <Row gutter={[16, 16]} className="example-grid">
          {filteredExamples.map(example => (
            <Col key={example.id} xs={24} sm={12} md={8} lg={6}>
              <ExampleCard
                example={example}
                onClick={() => handleExampleSelect(example)}
                onFavoriteToggle={(favorited) => handleToggleFavorite(example, favorited)}
              />
            </Col>
          ))}
        </Row>
      );
    } else {
      return (
        <div className="example-list">
          {filteredExamples.map(example => (
            <Card 
              key={example.id}
              className="example-list-item"
              onClick={() => handleExampleSelect(example)}
            >
              <div className="example-list-content">
                <img 
                  src={example.previewImage} 
                  alt={example.title} 
                  className="example-list-image"
                />
                <div className="example-list-info">
                  <h3>{example.title}</h3>
                  <p>{example.description}</p>
                  <div className="example-list-tags">
                    {example.tags.slice(0, 3).map(tag => (
                      <Tag key={tag}>{tag}</Tag>
                    ))}
                    {example.tags.length > 3 && (
                      <Tooltip title={example.tags.slice(3).join(', ')}>
                        <Tag>+{example.tags.length - 3}</Tag>
                      </Tooltip>
                    )}
                  </div>
                </div>
                <div className="example-list-actions">
                  <Button 
                    type="text" 
                    icon={example.favorited ? <StarFilled /> : <StarOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleToggleFavorite(example, !example.favorited);
                    }}
                  />
                  <Button 
                    type="text" 
                    icon={<EyeOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(example.previewUrl, '_blank');
                    }}
                  />
                  <Button 
                    type="text" 
                    icon={<DownloadOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleImportClick();
                    }}
                  />
                </div>
              </div>
            </Card>
          ))}
        </div>
      );
    }
  };

  return (
    <Layout className="example-browser-enhanced">
      <Header className="example-browser-header">
        <div className="header-title">
          <h2>{t('exampleBrowser.title')}</h2>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
            title={t('exampleBrowser.refresh')}
          />
        </div>
        <div className="header-search">
          <Search
            placeholder={t('exampleBrowser.searchPlaceholder')}
            onSearch={handleSearch}
            onChange={(e) => setSearchText(e.target.value)}
            value={searchText}
            allowClear
            style={{ width: 300 }}
            prefix={<SearchOutlined />}
          />
        </div>
        <div className="header-actions">
          <Button.Group>
            <Button
              icon={<AppstoreOutlined />}
              type={viewMode === 'grid' ? 'primary' : 'default'}
              onClick={() => handleViewModeChange('grid')}
            />
            <Button
              icon={<UnorderedListOutlined />}
              type={viewMode === 'list' ? 'primary' : 'default'}
              onClick={() => handleViewModeChange('list')}
            />
          </Button.Group>
          <Select 
            defaultValue="newest" 
            style={{ width: 120 }} 
            onChange={handleSortChange}
            value={sortBy}
          >
            <Option value="newest">{t('exampleBrowser.sortNewest')}</Option>
            <Option value="oldest">{t('exampleBrowser.sortOldest')}</Option>
            <Option value="name">{t('exampleBrowser.sortName')}</Option>
            <Option value="popular">{t('exampleBrowser.sortPopular')}</Option>
          </Select>
        </div>
      </Header>
      <Layout>
        <Sider width={250} className="example-browser-sider">
          <ExampleFilter
            selectedCategory={selectedCategory}
            selectedTags={selectedTags}
            onCategoryChange={setSelectedCategory}
            onTagsChange={setSelectedTags}
          />
        </Sider>
        <Content className="example-browser-content">
          {loading ? (
            <div className="loading-container">
              <Spin size="large" />
              <p>{t('exampleBrowser.loading')}</p>
            </div>
          ) : selectedExample ? (
            <ExampleDetail
              example={selectedExample}
              onBack={() => setSelectedExample(null)}
              onImport={handleImportClick}
              onFavoriteToggle={(favorited) => handleToggleFavorite(selectedExample, favorited)}
            />
          ) : (
            <Tabs activeKey={activeTab} onChange={handleTabChange}>
              <TabPane 
                tab={
                  <span>
                    <AppstoreOutlined />
                    {t('exampleBrowser.allExamples')}
                  </span>
                } 
                key="all"
              >
                {renderExampleList()}
              </TabPane>
              <TabPane 
                tab={
                  <span>
                    <StarFilled />
                    {t('exampleBrowser.favorites')}
                    <Badge count={favorites.length} style={{ marginLeft: 5 }} />
                  </span>
                } 
                key="favorites"
              >
                {activeTab === 'favorites' && renderExampleList()}
              </TabPane>
              <TabPane 
                tab={
                  <span>
                    <HistoryOutlined />
                    {t('exampleBrowser.recentlyViewed')}
                    <Badge count={recentlyViewed.length} style={{ marginLeft: 5 }} />
                  </span>
                } 
                key="recent"
              >
                {activeTab === 'recent' && renderExampleList()}
              </TabPane>
            </Tabs>
          )}
        </Content>
      </Layout>
      <ImportDialog
        visible={importDialogVisible}
        example={selectedExample}
        onCancel={() => setImportDialogVisible(false)}
        onConfirm={handleImportConfirm}
      />
    </Layout>
  );
};

export default ExampleBrowserEnhanced;
