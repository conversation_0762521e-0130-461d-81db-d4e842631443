/**
 * InspectorPanel组件测试
 */
import { render, screen} from '../../../__tests__/utils/test-utils';
import InspectorPanel from '../InspectorPanel';
import { jest } from '@jest/globals';

// 模拟Redux状态
const mockSelectedObject = {
  key: 'test-entity',
  title: 'Test Entity',
  components: [
    {
      type: 'Transform',
      properties: {
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }}},
    {
      type: 'MeshRenderer',
      properties: {
        material: 'default',
        visible: true}},
  ]};

jest.mock('../../../store', () => ({
  useAppDispatch: () => jest.fn() as any,
  useAppSelector: () => ({
    selectedObject: mockSelectedObject,
    selectedObjects: []})}));

// 模拟antd组件
jest.mock('antd', async () => {
  const actual = await jest.importActual('antd');
  return {
    ...actual,
    Form: {
      useForm: () => [
        {
          setFieldsValue: jest.fn() as any,
          getFieldsValue: jest.fn() as any,
          resetFields: jest.fn() as any},
      ],
      Item: ({ name, label, children }: any) => (
        <div data-testid={`form-item-${name}`}>
          <label>{label}</label>
          {children}
        </div>
      )},
    Collapse: ({ children }: any) => (
      <div data-testid="mock-collapse">{children}</div>
    ),
    Tabs: ({ children }: any) => (
      <div data-testid="mock-tabs">{children}</div>
    ),
    TabPane: ({ tab, children }: any) => (
      <div data-testid={`mock-tab-pane-${tab}`}>{children}</div>
    ),
    Input: ({ value, onChange }: any) => (
      <input
        data-testid="mock-input"
        value={value}
        onChange={(e: any) => onChange && onChange(e)}
      />
    ),
    InputNumber: ({ value, onChange }: any) => (
      <input
        data-testid="mock-input-number"
        type="number"
        value={value}
        onChange={(e: any) => onChange && onChange(parseFloat(e.target.value))}
      />
    ),
    Select: ({ value, onChange, children }: any) => (
      <select
        data-testid="mock-select"
        value={value}
        onChange={(e: any) => onChange && onChange(e.target.value)}
      >
        {children}
      </select>
    ),
    Switch: ({ checked, onChange }: any) => (
      <input
        data-testid="mock-switch"
        type="checkbox"
        checked={checked}
        onChange={(e: any) => onChange && onChange(e.target.checked)}
      />
    ),
    Button: ({ children, onClick, icon }: any) => (
      <button data-testid="mock-button" onClick={onClick}>
        {icon && <span data-testid="mock-button-icon">{icon.type.name}</span>}
        {children}
      </button>
    ),
    Space: ({ children }: any) => (
      <div data-testid="mock-space">{children}</div>
    ),
    Typography: {
      Text: ({ children, strong, type }: any) => (
        <span
          data-testid="mock-typography-text"
          style={{ fontWeight: strong ? 'bold' : 'normal' }}
        >
          {children}
        </span>
      )},
    Divider: () => <hr data-testid="mock-divider" />};
});

describe('InspectorPanel组件', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染InspectorPanel组件', () => {
    render(<InspectorPanel />);
    
    // 验证选中对象的标题已渲染
    expect(screen.getByText('Test Entity')).toBeInTheDocument();
    
    // 验证选中对象的ID已渲染
    expect(screen.getByText('(test-entity)')).toBeInTheDocument();
    
    // 验证选项卡已渲染
    expect(screen.getByTestId('mock-tabs')).toBeInTheDocument();
  });

  it('应该显示空状态当没有选中对象时', () => {
    // 修改模拟以返回无选中对象
    jest.mock('../../../store', () => ({
      useAppDispatch: () => jest.fn() as any,
      useAppSelector: () => ({
        selectedObject: null,
        selectedObjects: []})}), { virtual: true });
    
    render(<InspectorPanel />);
    
    // 验证空状态消息已渲染
    expect(screen.getByText('editor.noObjectSelected')).toBeInTheDocument();
  });

  it('应该显示多选状态当选中多个对象时', () => {
    // 修改模拟以返回多个选中对象
    jest.mock('../../../store', () => ({
      useAppDispatch: () => jest.fn() as any,
      useAppSelector: () => ({
        selectedObject: null,
        selectedObjects: [
          { key: 'entity1', title: 'Entity 1' },
          { key: 'entity2', title: 'Entity 2' },
        ]})}), { virtual: true });
    
    render(<InspectorPanel />);
    
    // 验证多选状态消息已渲染
    expect(screen.getByText('editor.multipleObjectsSelected')).toBeInTheDocument();
  });
});
