/**
 * 瀑布组件（简化版）
 * 用于表示瀑布及其基本物理属性
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyType } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
import { Debug } from '../../utils/Debug';

/**
 * 瀑布类型
 */
export enum WaterfallType {
  /** 高瀑布 */
  HIGH = 'high',
  /** 宽瀑布 */
  WIDE = 'wide',
  /** 小瀑布 */
  SMALL = 'small'
}

/**
 * 瀑布配置接口
 */
export interface WaterfallConfig {
  /** 瀑布类型 */
  type?: WaterfallType;
  /** 宽度 */
  width?: number;
  /** 高度 */
  height?: number;
  /** 深度 */
  depth?: number;
  /** 位置 */
  position?: THREE.Vector3;
  /** 旋转 */
  rotation?: THREE.Euler;
  /** 颜色 */
  color?: THREE.Color;
  /** 透明度 */
  opacity?: number;
  /** 流速 */
  flowSpeed?: number;
  /** 流向 */
  flowDirection?: { x: number; y: number; z: number };
  /** 湍流强度 */
  turbulenceStrength?: number;
  /** 湍流频率 */
  turbulenceFrequency?: number;
  /** 湍流速度 */
  turbulenceSpeed?: number;
}

/**
 * 瀑布组件类
 * 继承自水体组件，添加瀑布特有的功能
 */
export class WaterfallComponent extends WaterBodyComponent {
  /** 瀑布类型 */
  private waterfallType: WaterfallType = WaterfallType.HIGH;

  /** 水流路径点 */
  private flowPathPoints: THREE.Vector3[] = [];

  /** 水流网格 */
  private flowMesh: THREE.Mesh | null = null;

  /** 湍流强度 */
  private turbulenceStrength: number = 1.0;

  /** 湍流频率 */
  private turbulenceFrequency: number = 2.0;

  /** 湍流速度 */
  private turbulenceSpeed: number = 1.0;

  /**
   * 创建瀑布组件
   * @param entity 实体
   * @param config 瀑布配置
   */
  constructor(entity: Entity, config: WaterfallConfig = {}) {
    // 创建水体配置
    const waterBodyConfig = {
      type: WaterBodyType.WATERFALL,
      size: config.width && config.height && config.depth ? {
        width: config.width,
        height: config.height,
        depth: config.depth
      } : { width: 10, height: 20, depth: 5 },
      flowSpeed: config.flowSpeed || 2.0,
      flowDirection: config.flowDirection || { x: 0, y: -1, z: 0 }
    };

    super(entity, waterBodyConfig);

    // 应用配置
    this.applyConfig(config);
  }

  /**
   * 获取瀑布类型
   * @returns 瀑布类型
   */
  public getWaterfallType(): WaterfallType {
    return this.waterfallType;
  }

  /**
   * 设置瀑布类型
   * @param type 瀑布类型
   */
  public setWaterfallType(type: WaterfallType): void {
    this.waterfallType = type;
  }

  /**
   * 应用配置
   * @param config 瀑布配置
   */
  private applyConfig(config: WaterfallConfig): void {
    // 应用瀑布类型
    if (config.type !== undefined) this.waterfallType = config.type;

    // 应用基本配置
    if (config.width !== undefined || config.height !== undefined || config.depth !== undefined) {
      const currentSize = this.getSize();
      this.setSize({
        width: config.width !== undefined ? config.width : currentSize.width,
        height: config.height !== undefined ? config.height : currentSize.height,
        depth: config.depth !== undefined ? config.depth : currentSize.depth
      });
    }
    if (config.position) this.setPosition(config.position);
    if (config.rotation) this.setRotation(config.rotation);
    if (config.color) this.setColor(config.color);
    if (config.opacity !== undefined) this.setOpacity(config.opacity);
    if (config.flowSpeed !== undefined) this.setFlowSpeed(config.flowSpeed);
    if (config.flowDirection) this.setFlowDirection(config.flowDirection);

    // 应用瀑布特有配置
    if (config.turbulenceStrength !== undefined) this.turbulenceStrength = config.turbulenceStrength;
    if (config.turbulenceFrequency !== undefined) this.turbulenceFrequency = config.turbulenceFrequency;
    if (config.turbulenceSpeed !== undefined) this.turbulenceSpeed = config.turbulenceSpeed;
  }

  /**
   * 初始化瀑布组件
   */
  public override initialize(): void {
    // 调用父类初始化
    super.initialize();

    // 计算水流路径
    this.calculateFlowPath();

    // 创建水流网格
    this.createFlowMesh();

    Debug.log('WaterfallComponent', '瀑布组件初始化完成');
  }

  /**
   * 计算水流路径
   * 根据瀑布的位置、旋转和尺寸计算水流的路径点
   */
  private calculateFlowPath(): void {
    // 清空路径点
    this.flowPathPoints = [];

    // 获取瀑布的位置和尺寸
    const position = this.getPosition();
    const size = this.getSize();

    // 计算瀑布顶部中心点
    const topCenter = new THREE.Vector3(position.x, position.y + size.height / 2, position.z);
    this.flowPathPoints.push(topCenter);

    // 计算瀑布底部中心点
    const bottomCenter = new THREE.Vector3(position.x, position.y - size.height / 2, position.z);
    this.flowPathPoints.push(bottomCenter);

    // 计算落水点
    const flowDirection = this.getFlowDirection();
    const flowDistance = size.height * 2;
    const landingPoint = new THREE.Vector3().copy(bottomCenter).add(
      new THREE.Vector3(flowDirection.x, flowDirection.y, flowDirection.z).multiplyScalar(flowDistance)
    );
    this.flowPathPoints.push(landingPoint);

    Debug.log('WaterfallComponent', `计算水流路径: ${this.flowPathPoints.length}个点`);
  }

  /**
   * 创建水流网格
   * 根据水流路径创建水流的网格
   */
  private createFlowMesh(): void {
    // 如果路径点不足，则不创建网格
    if (this.flowPathPoints.length < 2) return;

    // 创建曲线
    const curve = new THREE.CatmullRomCurve3(this.flowPathPoints);

    // 获取尺寸
    const size = this.getSize();

    // 创建管道几何体
    const geometry = new THREE.TubeGeometry(
      curve,
      20, // 分段数
      size.width / 2, // 管道半径
      8, // 管道周围分段数
      false // 是否闭合
    );

    // 创建水流材质
    const material = new THREE.MeshStandardMaterial({
      color: this.getColor(),
      transparent: true,
      opacity: this.getOpacity(),
      side: THREE.DoubleSide,
      roughness: 0.1,
      metalness: 0.2
    });

    // 创建网格
    this.flowMesh = new THREE.Mesh(geometry, material);

    Debug.log('WaterfallComponent', '创建水流网格完成');
  }

  /**
   * 更新瀑布组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public override update(deltaTime: number): void {
    // 调用父类更新
    super.update(deltaTime);

    // 更新水流动力学
    this.updateFluidDynamics(deltaTime);
  }

  /**
   * 更新水流动力学
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateFluidDynamics(deltaTime: number): void {
    // 如果没有水流网格，则不更新
    if (!this.flowMesh) return;

    // 获取水流材质
    const material = this.flowMesh.material as THREE.MeshStandardMaterial;
    if (!material) return;

    // 更新水流材质的位移贴图偏移，模拟水流动态
    if (!material.userData.time) {
      material.userData.time = 0;
    }
    material.userData.time += deltaTime * this.getFlowSpeed();

    // 如果材质有位移贴图，更新偏移
    if (material.displacementMap) {
      material.displacementMap.offset.y = -material.userData.time;
    }

    // 如果材质有法线贴图，更新偏移
    if (material.normalMap) {
      material.normalMap.offset.y = -material.userData.time;
    }
  }

  /**
   * 获取水流网格
   * @returns 水流网格
   */
  public getFlowMesh(): THREE.Mesh | null {
    return this.flowMesh;
  }

  /**
   * 获取水流路径点
   * @returns 水流路径点数组
   */
  public getFlowPathPoints(): THREE.Vector3[] {
    return [...this.flowPathPoints];
  }

  /**
   * 获取湍流强度
   * @returns 湍流强度
   */
  public getTurbulenceStrength(): number {
    return this.turbulenceStrength;
  }

  /**
   * 设置湍流强度
   * @param strength 湍流强度
   */
  public setTurbulenceStrength(strength: number): void {
    this.turbulenceStrength = strength;
  }

  /**
   * 获取湍流频率
   * @returns 湍流频率
   */
  public getTurbulenceFrequency(): number {
    return this.turbulenceFrequency;
  }

  /**
   * 设置湍流频率
   * @param frequency 湍流频率
   */
  public setTurbulenceFrequency(frequency: number): void {
    this.turbulenceFrequency = frequency;
  }

  /**
   * 获取湍流速度
   * @returns 湍流速度
   */
  public getTurbulenceSpeed(): number {
    return this.turbulenceSpeed;
  }

  /**
   * 设置湍流速度
   * @param speed 湍流速度
   */
  public setTurbulenceSpeed(speed: number): void {
    this.turbulenceSpeed = speed;
  }
}
