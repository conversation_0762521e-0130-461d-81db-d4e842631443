#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 递归查找所有 .tsx 和 .ts 文件
function findAllFiles(dir, extensions = ['.tsx', '.ts']) {
  let results = [];
  try {
    const list = fs.readdirSync(dir);
    
    list.forEach(file => {
      const filePath = path.join(dir, file);
      try {
        const stat = fs.statSync(filePath);
        
        if (stat && stat.isDirectory()) {
          // 跳过不需要的目录
          if (!['node_modules', '.git', 'dist', 'build', 'coverage', '__tests__'].includes(file)) {
            results = results.concat(findAllFiles(filePath, extensions));
          }
        } else {
          const ext = path.extname(file);
          if (extensions.includes(ext)) {
            results.push(filePath);
          }
        }
      } catch (err) {
        // 忽略无法访问的文件
      }
    });
  } catch (err) {
    // 忽略无法访问的目录
  }
  
  return results;
}

// 修复单个文件的常见问题
function fixCommonIssues(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const originalLength = content.length;

    // 1. 修复 .mjs 导入
    if (content.includes('.mjs')) {
      content = content.replace(/from ['"](.+?)\.mjs['"]/g, "from '$1'");
      modified = true;
    }

    // 2. 移除重复的 antd import 行
    const duplicateAntdImport = /import { Select, Switch, InputNumber, Form } from 'antd';/g;
    if (duplicateAntdImport.test(content)) {
      content = content.replace(duplicateAntdImport, '');
      modified = true;
    }

    // 3. 处理重复的import和const解构
    const lines = content.split('\n');
    const newLines = [];
    const seenImports = new Set();
    const seenConsts = new Set();

    for (const line of lines) {
      const trimmed = line.trim();
      
      // 跳过空行
      if (!trimmed) {
        newLines.push(line);
        continue;
      }

      // 处理import语句
      if (trimmed.startsWith('import ') && trimmed.includes('from ')) {
        if (!seenImports.has(trimmed)) {
          seenImports.add(trimmed);
          newLines.push(line);
        } else {
          modified = true;
          // 跳过重复的import
        }
      }
      // 处理const解构语句
      else if (trimmed.startsWith('const {') && trimmed.includes('} = ')) {
        if (!seenConsts.has(trimmed)) {
          seenConsts.add(trimmed);
          newLines.push(line);
        } else {
          modified = true;
          // 跳过重复的const解构
        }
      }
      else {
        newLines.push(line);
      }
    }

    // 4. 移除自引用导入
    const fileName = path.basename(filePath, path.extname(filePath));
    const selfImportPattern = new RegExp(`import.*from ['"]\\.\/${fileName}['"]`, 'g');
    if (selfImportPattern.test(content)) {
      content = content.replace(selfImportPattern, '');
      modified = true;
    }

    if (modified) {
      const newContent = newLines.join('\n');
      // 清理多余的空行
      const cleanContent = newContent.replace(/\n\s*\n\s*\n/g, '\n\n');
      
      fs.writeFileSync(filePath, cleanContent, 'utf8');
      return true;
    }

    return false;
  } catch (error) {
    console.error(`错误处理文件 ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src 目录不存在');
    process.exit(1);
  }
  
  console.log('开始批量修复所有import错误...');
  
  const files = findAllFiles(srcDir);
  let fixedCount = 0;
  let totalFiles = files.length;
  
  console.log(`找到 ${totalFiles} 个文件需要检查`);
  
  files.forEach((file, index) => {
    const relativePath = path.relative(__dirname, file);
    
    if (fixCommonIssues(file)) {
      console.log(`✓ [${index + 1}/${totalFiles}] 修复了 ${relativePath}`);
      fixedCount++;
    } else {
      // 只显示前10个未修复的文件，避免输出过多
      if (fixedCount < 10) {
        console.log(`- [${index + 1}/${totalFiles}] 跳过 ${relativePath}`);
      }
    }
  });
  
  console.log(`\n批量修复完成！`);
  console.log(`总文件数: ${totalFiles}`);
  console.log(`修复文件数: ${fixedCount}`);
  console.log(`跳过文件数: ${totalFiles - fixedCount}`);
  
  if (fixedCount > 0) {
    console.log('\n建议运行 TypeScript 编译检查以验证修复效果：');
    console.log('npx tsc --noEmit');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixCommonIssues, findAllFiles };
