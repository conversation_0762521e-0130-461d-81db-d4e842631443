/**
 * 面部表情类型枚举
 * 定义了各种面部表情的类型
 */

export enum FacialExpressionType {
  /** 中性表情 */
  NEUTRAL = 'neutral',
  
  /** 快乐 */
  HAPPY = 'happy',
  
  /** 悲伤 */
  SAD = 'sad',
  
  /** 愤怒 */
  ANGRY = 'angry',
  
  /** 惊讶 */
  SURPRISED = 'surprised',
  
  /** 恐惧 */
  FEAR = 'fear',
  
  /** 厌恶 */
  DISGUSTED = 'disgusted',
  
  /** 厌恶（别名） */
  DISGUST = 'disgusted',
  
  /** 困惑 */
  CONFUSED = 'confused',
  
  /** 兴奋 */
  EXCITED = 'excited',
  
  /** 疲惫 */
  TIRED = 'tired',
  
  /** 专注 */
  FOCUSED = 'focused',
  
  /** 放松 */
  RELAXED = 'relaxed',
  
  /** 紧张 */
  NERVOUS = 'nervous',
  
  /** 自信 */
  CONFIDENT = 'confident',
  
  /** 害羞 */
  SHY = 'shy',
  
  /** 骄傲 */
  PROUD = 'proud',
  
  /** 嫉妒 */
  JEALOUS = 'jealous',
  
  /** 爱意 */
  LOVING = 'loving',
  
  /** 痛苦 */
  PAIN = 'pain',
  
  /** 思考 */
  THINKING = 'thinking',
  
  /** 微笑 */
  SMILE = 'smile',
  
  /** 大笑 */
  LAUGH = 'laugh',
  
  /** 哭泣 */
  CRY = 'cry',
  
  /** 眨眼 */
  BLINK = 'blink',
  
  /** 眯眼 */
  SQUINT = 'squint',
  
  /** 皱眉 */
  FROWN = 'frown',
  
  /** 撅嘴 */
  POUT = 'pout',
  
  /** 张嘴 */
  MOUTH_OPEN = 'mouth_open',
  
  /** 闭嘴 */
  MOUTH_CLOSED = 'mouth_closed'
}

/**
 * 面部表情强度枚举
 */
export enum FacialExpressionIntensity {
  /** 无 */
  NONE = 0,
  
  /** 轻微 */
  LIGHT = 0.25,
  
  /** 中等 */
  MEDIUM = 0.5,
  
  /** 强烈 */
  STRONG = 0.75,
  
  /** 极强 */
  EXTREME = 1.0
}

/**
 * 面部区域枚举
 */
export enum FacialRegion {
  /** 眼部 */
  EYES = 'eyes',
  
  /** 眉毛 */
  EYEBROWS = 'eyebrows',
  
  /** 嘴部 */
  MOUTH = 'mouth',
  
  /** 鼻子 */
  NOSE = 'nose',
  
  /** 脸颊 */
  CHEEKS = 'cheeks',
  
  /** 下巴 */
  CHIN = 'chin',
  
  /** 额头 */
  FOREHEAD = 'forehead',
  
  /** 全脸 */
  FULL_FACE = 'full_face'
}

/**
 * 面部表情配置接口
 */
export interface FacialExpressionConfig {
  /** 表情类型 */
  type: FacialExpressionType;
  
  /** 表情强度 */
  intensity: number;
  
  /** 影响区域 */
  regions: FacialRegion[];
  
  /** 持续时间（毫秒） */
  duration?: number;
  
  /** 是否循环 */
  loop?: boolean;
  
  /** 混合权重 */
  blendWeight?: number;
}

/**
 * 面部表情工具类
 */
export class FacialExpressionUtils {
  /**
   * 获取表情的默认强度
   */
  public static getDefaultIntensity(type: FacialExpressionType): number {
    switch (type) {
      case FacialExpressionType.NEUTRAL:
        return 0;
      case FacialExpressionType.BLINK:
        return 1.0;
      case FacialExpressionType.SMILE:
      case FacialExpressionType.HAPPY:
        return 0.7;
      case FacialExpressionType.LAUGH:
        return 0.9;
      case FacialExpressionType.SAD:
      case FacialExpressionType.CRY:
        return 0.8;
      case FacialExpressionType.ANGRY:
        return 0.8;
      case FacialExpressionType.SURPRISED:
        return 0.9;
      case FacialExpressionType.FEAR:
        return 0.7;
      case FacialExpressionType.DISGUSTED:
        return 0.6;
      default:
        return 0.5;
    }
  }

  /**
   * 获取表情影响的主要区域
   */
  public static getPrimaryRegions(type: FacialExpressionType): FacialRegion[] {
    switch (type) {
      case FacialExpressionType.BLINK:
      case FacialExpressionType.SQUINT:
        return [FacialRegion.EYES];
      case FacialExpressionType.SMILE:
      case FacialExpressionType.LAUGH:
      case FacialExpressionType.FROWN:
      case FacialExpressionType.POUT:
        return [FacialRegion.MOUTH];
      case FacialExpressionType.SURPRISED:
        return [FacialRegion.EYES, FacialRegion.EYEBROWS, FacialRegion.MOUTH];
      case FacialExpressionType.ANGRY:
        return [FacialRegion.EYEBROWS, FacialRegion.EYES, FacialRegion.MOUTH];
      case FacialExpressionType.HAPPY:
        return [FacialRegion.MOUTH, FacialRegion.EYES, FacialRegion.CHEEKS];
      case FacialExpressionType.SAD:
        return [FacialRegion.MOUTH, FacialRegion.EYEBROWS, FacialRegion.EYES];
      default:
        return [FacialRegion.FULL_FACE];
    }
  }

  /**
   * 检查两个表情是否兼容（可以同时播放）
   */
  public static areCompatible(type1: FacialExpressionType, type2: FacialExpressionType): boolean {
    const regions1 = this.getPrimaryRegions(type1);
    const regions2 = this.getPrimaryRegions(type2);
    
    // 检查是否有重叠的区域
    for (const region1 of regions1) {
      for (const region2 of regions2) {
        if (region1 === region2 || region1 === FacialRegion.FULL_FACE || region2 === FacialRegion.FULL_FACE) {
          return false;
        }
      }
    }
    
    return true;
  }

  /**
   * 创建表情配置
   */
  public static createConfig(
    type: FacialExpressionType,
    intensity?: number,
    duration?: number
  ): FacialExpressionConfig {
    return {
      type,
      intensity: intensity ?? this.getDefaultIntensity(type),
      regions: this.getPrimaryRegions(type),
      duration,
      loop: false,
      blendWeight: 1.0
    };
  }
}
