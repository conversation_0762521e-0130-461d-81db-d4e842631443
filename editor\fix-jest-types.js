#!/usr/bin/env node

/**
 * 修复jest类型错误和未使用的导入
 */

import fs from 'fs';
import path from 'path';

function fixJestTypes(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    // 修复jest.fn().mockResolvedValue类型错误
    // 将所有jest.fn()替换为jest.fn<any, any>()
    content = content.replace(/jest\.fn\(\)/g, 'jest.fn<any, any>()');
    
    // 修复mockResolvedValue类型错误
    content = content.replace(/\.mockResolvedValue\(/g, '.mockResolvedValue(');
    
    // 修复未使用的导入 - 移除未使用的变量
    const unusedImports = [
      'Tooltip', 'Option', 'Text', 'Slider', 'Input', 'Switch', 'Table',
      'Button', 'Card', 'Space', 'List', 'Modal', 'Form', 'Select',
      'Checkbox', 'Radio', 'DatePicker', 'TimePicker', 'Upload',
      'Progress', 'Spin', 'Alert', 'Message', 'Notification',
      'Drawer', 'Popover', 'Popconfirm', 'Dropdown', 'Menu',
      'Tabs', 'Steps', 'Breadcrumb', 'Pagination', 'Rate',
      'Slider', 'TreeSelect', 'Cascader', 'AutoComplete',
      'Transfer', 'Tree', 'Calendar', 'Badge', 'Avatar',
      'Tag', 'Divider', 'Anchor', 'BackTop', 'ConfigProvider'
    ];
    
    unusedImports.forEach(importName => {
      // 检查是否在代码中使用了这个导入
      const usageRegex = new RegExp(`<${importName}[\\s>]|${importName}\\.`, 'g');
      const matches = content.match(usageRegex);
      
      if (!matches || matches.length === 0) {
        // 从导入语句中移除
        content = content.replace(
          new RegExp(`(import\\s*{[^}]*),\\s*${importName}\\s*([^}]*})`, 'g'),
          '$1$2'
        );
        content = content.replace(
          new RegExp(`(import\\s*{)\\s*${importName}\\s*,([^}]*})`, 'g'),
          '$1$2'
        );
        content = content.replace(
          new RegExp(`(import\\s*{[^}]*\\s+)${importName}\\s*,`, 'g'),
          '$1'
        );
        content = content.replace(
          new RegExp(`(import\\s*{)\\s*${importName}\\s*(})`, 'g'),
          '$1$2'
        );
        changed = true;
      }
    });
    
    // 清理空的导入语句
    content = content.replace(/import\s*{\s*}\s*from\s*[^;]+;\s*/g, '');
    content = content.replace(/import\s*{\s*,\s*}\s*from\s*[^;]+;\s*/g, '');
    content = content.replace(/import\s*{\s*,([^}]+)}\s*from/g, 'import {$1} from');
    content = content.replace(/import\s*{([^}]+),\s*}\s*from/g, 'import {$1} from');
    
    // 清理多余的逗号
    content = content.replace(/{\s*,/g, '{');
    content = content.replace(/,\s*}/g, '}');
    content = content.replace(/,\s*,/g, ',');
    
    if (changed) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`已修复: ${filePath}`);
    }
  } catch (error) {
    console.error(`修复失败 ${filePath}:`, error.message);
  }
}

function findTsFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          traverse(fullPath);
        } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`无法读取目录 ${currentDir}:`, error.message);
    }
  }
  
  traverse(dir);
  return files;
}

// 查找所有TypeScript文件
const tsFiles = findTsFiles('./src');

console.log(`找到 ${tsFiles.length} 个TypeScript文件`);

// 修复每个文件
tsFiles.forEach(fixJestTypes);

console.log('修复完成!');
