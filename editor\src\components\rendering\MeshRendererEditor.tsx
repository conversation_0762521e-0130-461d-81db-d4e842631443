/**
 * 网格渲染器编辑器
 */
import React from 'react';
import { Card, Form, Select, InputNumber, Typography } from 'antd';

const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { Text } = Typography;

/**
 * 网格渲染器编辑器属性
 */
interface MeshRendererEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * 网格渲染器编辑器组件
 */
const MeshRendererEditor: React.FC<MeshRendererEditorProps> = ({ data, onChange }) => {
  const [form] = Form.useForm();

  // 默认数据
  const defaultData = {
    enabled: true,
    castShadows: true,
    receiveShadows: true,
    material: 'default',
    renderOrder: 0,
    ...data
  };

  // 处理数据变化
  const handleChange = (field: string, value: any) => {
    const newData = { ...defaultData, [field]: value };
    if (onChange) {
      onChange(newData);
    }
  };

  return (
    <Card title="网格渲染器" size="small">
      <Form
        form={form}
        layout="vertical"
        initialValues={defaultData}
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([field, value]) => {
            handleChange(field, value);
          });
        }}
      >
        <Form.Item label="启用" name="enabled" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="材质" name="material">
          <Select>
            <Option value="default">默认材质</Option>
            <Option value="standard">标准材质</Option>
            <Option value="physical">物理材质</Option>
          </Select>
        </Form.Item>

        <Form.Item label="投射阴影" name="castShadows" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="接收阴影" name="receiveShadows" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="渲染顺序" name="renderOrder">
          <InputNumber min={0} max={100} />
        </Form.Item>
      </Form>
    </Card>
  );
};

export default MeshRendererEditor;
