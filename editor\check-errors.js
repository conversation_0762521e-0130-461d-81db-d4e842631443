#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 检查文件中的常见错误
function checkFileErrors(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return [];
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const errors = [];
    const lines = content.split('\n');

    // 检查重复的import
    const imports = new Set();
    const duplicateImports = [];
    
    lines.forEach((line, index) => {
      const trimmed = line.trim();
      
      // 检查重复的import
      if (trimmed.startsWith('import ') && trimmed.includes('from ')) {
        if (imports.has(trimmed)) {
          duplicateImports.push(`Line ${index + 1}: 重复的import - ${trimmed}`);
        } else {
          imports.add(trimmed);
        }
      }
      
      // 检查.mjs导入
      if (trimmed.includes('.mjs')) {
        errors.push(`Line ${index + 1}: 使用了.mjs导入 - ${trimmed}`);
      }
      
      // 检查重复的antd import
      if (trimmed === "import { Select, Switch, InputNumber, Form } from 'antd';") {
        errors.push(`Line ${index + 1}: 重复的antd组件导入 - ${trimmed}`);
      }
    });

    return [...errors, ...duplicateImports];
  } catch (error) {
    return [`文件读取错误: ${error.message}`];
  }
}

// 检查目录中的所有文件
function checkDirectory(dir) {
  const results = {};
  
  try {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'build'].includes(file)) {
        Object.assign(results, checkDirectory(filePath));
      } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        const errors = checkFileErrors(filePath);
        if (errors.length > 0) {
          results[path.relative(__dirname, filePath)] = errors;
        }
      }
    });
  } catch (error) {
    console.error(`检查目录 ${dir} 时出错:`, error.message);
  }
  
  return results;
}

// 主函数
function main() {
  console.log('检查常见的import错误...\n');
  
  const srcDir = path.join(__dirname, 'src');
  const results = checkDirectory(srcDir);
  
  let totalErrors = 0;
  let totalFiles = 0;
  
  for (const [file, errors] of Object.entries(results)) {
    totalFiles++;
    totalErrors += errors.length;
    
    console.log(`\n${file} (${errors.length} 个错误):`);
    errors.forEach(error => {
      console.log(`  - ${error}`);
    });
  }
  
  console.log(`\n总结:`);
  console.log(`有错误的文件数: ${totalFiles}`);
  console.log(`总错误数: ${totalErrors}`);
  
  if (totalErrors === 0) {
    console.log('✅ 没有发现常见的import错误！');
  } else {
    console.log('❌ 发现了一些需要修复的错误。');
  }
}

// 如果是直接运行的脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { checkFileErrors, checkDirectory };
