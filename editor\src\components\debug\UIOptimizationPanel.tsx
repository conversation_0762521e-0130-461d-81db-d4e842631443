/**
 * UI优化面板组件
 * 用于优化UI响应速度
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Tabs, Button, Slider, Divider, Space, Typography, Table, Progress, Alert, Collapse, Statistic, Tooltip, message, Checkbox, List } from 'antd';
import { useTranslation } from 'react-i18next';
import { Line, Bar } from '@ant-design/charts';
import {
  ReloadOutlined,
  DeleteOutlined,
  DesktopOutlined,
  MobileOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  AppstoreOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  RocketOutlined,
  FireOutlined,
  BulbOutlined,
  ClockCircleOutlined,
  ToolOutlined,
  InfoCircleOutlined,
  SyncOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LayoutOutlined,
  CodeOutlined,
  BranchesOutlined} from '@ant-design/icons';
import { EngineService } from '../../services/EngineService';
import './UIOptimizationPanel.less';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { Search } = Input;
const { Group: RadioGroup } = Radio;

/**
 * UI组件类型
 */
enum UIComponentType {
  PANEL = 'panel',
  BUTTON = 'button',
  TEXT = 'text',
  IMAGE = 'image',
  INPUT = 'input',
  LIST = 'list',
  TABLE = 'table',
  CHART = 'chart',
  OTHER = 'other'}

/**
 * UI组件信息
 */
interface UIComponentInfo {
  id: string;
  name: string;
  type: UIComponentType;
  renderTime: number;
  updateTime: number;
  eventCount: number;
  visible: boolean;
  children: number;
  depth: number;
  memoryUsage: number;
}

/**
 * UI优化设置
 */
interface UIOptimizationSettings {
  enableBatchRendering: boolean;
  enableEventDelegation: boolean;
  enableLazyLoading: boolean;
  enableVirtualization: boolean;
  enableMemoization: boolean;
  enableCulling: boolean;
  batchSize: number;
  updateInterval: number;
  cullingDistance: number;
  lazyLoadDistance: number;
  maxEventListeners: number;
  useWebWorkers: boolean;
  useOffscreenCanvas: boolean;
  useRequestAnimationFrame: boolean;
  useWebGL: boolean;
}

/**
 * UI优化面板组件
 */
const UIOptimizationPanel: React.FC = () => {
  const { t } = useTranslation();

  // 状态
  const [components, setComponents] = useState<UIComponentInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedComponents, setSelectedComponents] = useState<string[]>([]);
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState<UIComponentType | 'all'>('all');
  const [sortField, setSortField] = useState<string>('renderTime');
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend'>('descend');
  const [performanceData, setPerformanceData] = useState({
    totalRenderTime: 0,
    averageRenderTime: 0,
    totalComponents: 0,
    visibleComponents: 0,
    eventListeners: 0,
    memoryUsage: 0,
    history: [] as any[]});
  const [optimizationSettings, setOptimizationSettings] = useState<UIOptimizationSettings>({
    enableBatchRendering: true,
    enableEventDelegation: true,
    enableLazyLoading: true,
    enableVirtualization: true,
    enableMemoization: true,
    enableCulling: true,
    batchSize: 10,
    updateInterval: 16,
    cullingDistance: 1000,
    lazyLoadDistance: 500,
    maxEventListeners: 100,
    useWebWorkers: false,
    useOffscreenCanvas: false,
    useRequestAnimationFrame: true,
    useWebGL: false});
  const [optimizationResults, setOptimizationResults] = useState<any>(null);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationProgress, setOptimizationProgress] = useState(0);

  // 引用
  const timerRef = useRef<number | null>(null);

  // 初始化
  useEffect(() => {
    // 加载UI组件列表
    loadUIComponents();

    // 设置定时器定期刷新
    timerRef.current = window.setInterval(() => {
      loadUIComponents();
    }, 5000);

    // 清理函数
    return () => {
      if (timerRef.current !== null) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);

  // 加载UI组件列表
  const loadUIComponents = async () => {
    setLoading(true);

    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取UI系统
      const uiSystem = engine.getSystem('UISystem');
      if (!uiSystem) {
        throw new Error('UI系统未初始化');
      }

      // 获取UI组件列表
      const componentList = await uiSystem.getComponents();

      // 转换为组件使用的格式
      const formattedComponents: UIComponentInfo[] = componentList.map((component: any) => ({
        id: component.id,
        name: component.name || component.id,
        type: mapComponentType(component.type),
        renderTime: component.renderTime || 0,
        updateTime: component.updateTime || 0,
        eventCount: component.eventListeners?.length || 0,
        visible: component.visible !== false,
        children: component.children?.length || 0,
        depth: component.depth || 0,
        memoryUsage: component.memoryUsage || 0}));

      // 更新组件列表
      setComponents(formattedComponents);

      // 更新性能数据
      updatePerformanceData(formattedComponents);

      // 获取优化设置
      const settings = await uiSystem.getOptimizationSettings();
      if (settings) {
        setOptimizationSettings(settings);
      }
    } catch (error) {
      console.error('加载UI组件列表失败:', error);
      message.error(`加载UI组件列表失败: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  // 更新性能数据
  const updatePerformanceData = (componentList: UIComponentInfo[]) => {
    const totalRenderTime = componentList.reduce((sum, c) => sum + c.renderTime, 0);
    const visibleComponents = componentList.filter(c => c.visible).length;
    const eventListeners = componentList.reduce((sum, c) => sum + c.eventCount, 0);
    const memoryUsage = componentList.reduce((sum, c) => sum + c.memoryUsage, 0);

    const now = new Date().toLocaleTimeString();

    setPerformanceData({
      totalRenderTime,
      averageRenderTime: componentList.length > 0 ? totalRenderTime / componentList.length : 0,
      totalComponents: componentList.length,
      visibleComponents,
      eventListeners,
      memoryUsage,
      history: [
        ...performanceData.history,
        {
          time: now,
          renderTime: totalRenderTime,
          components: componentList.length,
          visible: visibleComponents,
          events: eventListeners}
      ].slice(-20), // 保留最近20个数据点
    });
  };

  // 映射组件类型
  const mapComponentType = (type: string): UIComponentType => {
    switch (type?.toLowerCase()) {
      case 'panel':
      case 'container':
      case 'div':
        return UIComponentType.PANEL;
      case 'button':
      case 'btn':
        return UIComponentType.BUTTON;
      case 'text':
      case 'label':
      case 'span':
        return UIComponentType.TEXT;
      case 'image':
      case 'img':
      case 'icon':
        return UIComponentType.IMAGE;
      case 'input':
      case 'textfield':
      case 'textarea':
        return UIComponentType.INPUT;
      case 'list':
      case 'listview':
        return UIComponentType.LIST;
      case 'table':
      case 'grid':
        return UIComponentType.TABLE;
      case 'chart':
      case 'graph':
        return UIComponentType.CHART;
      default:
        return UIComponentType.OTHER;
    }
  };

  // 格式化时间（毫秒）
  const formatTime = (ms: number): string => {
    if (ms < 1) {
      return `${(ms * 1000).toFixed(2)} μs`;
    } else if (ms < 1000) {
      return `${ms.toFixed(2)} ms`;
    } else {
      return `${(ms / 1000).toFixed(2)} s`;
    }
  };

  // 格式化内存大小
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取组件类型图标
  const getComponentTypeIcon = (type: UIComponentType) => {
    switch (type) {
      case UIComponentType.PANEL:
        return <LayoutOutlined />;
      case UIComponentType.BUTTON:
        return <ToolOutlined />;
      case UIComponentType.TEXT:
        return <FontOutlined />;
      case UIComponentType.IMAGE:
        return <FileImageOutlined />;
      case UIComponentType.INPUT:
        return <EditOutlined />;
      case UIComponentType.LIST:
        return <UnorderedListOutlined />;
      case UIComponentType.TABLE:
        return <TableOutlined />;
      case UIComponentType.CHART:
        return <BarChartOutlined />;
      default:
        return <AppstoreOutlined />;
    }
  };

  // 过滤组件
  const getFilteredComponents = () => {
    return components.filter(component => {
      // 搜索文本过滤
      if (searchText && !component.name.toLowerCase().includes(searchText.toLowerCase()) && !component.id.toLowerCase().includes(searchText.toLowerCase())) {
        return false;
      }

      // 类型过滤
      if (filterType !== 'all' && component.type !== filterType) {
        return false;
      }

      return true;
    });
  };

  // 优化选定组件
  const optimizeSelectedComponents = async () => {
    if (selectedComponents.length === 0) {
      message.info(t('debug.ui.noComponentSelected'));
      return;
    }

    setIsOptimizing(true);
    setOptimizationProgress(0);

    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取UI系统
      const uiSystem = engine.getSystem('UISystem');
      if (!uiSystem) {
        throw new Error('UI系统未初始化');
      }

      // 优化选定组件
      const results = await uiSystem.optimizeComponents(selectedComponents, {
        enableBatchRendering: optimizationSettings.enableBatchRendering,
        enableEventDelegation: optimizationSettings.enableEventDelegation,
        enableLazyLoading: optimizationSettings.enableLazyLoading,
        enableVirtualization: optimizationSettings.enableVirtualization,
        enableMemoization: optimizationSettings.enableMemoization,
        enableCulling: optimizationSettings.enableCulling});

      // 更新优化结果
      setOptimizationResults(results);

      // 刷新组件列表
      loadUIComponents();

      // 清除选择
      setSelectedComponents([]);

      // 显示成功消息
      message.success(t('debug.ui.optimizationSuccess'));
    } catch (error) {
      console.error('优化组件失败:', error);
      message.error(`${t('debug.ui.optimizationFailed')}: ${(error as Error).message}`);
    } finally {
      setIsOptimizing(false);
      setOptimizationProgress(100);
    }
  };

  // 应用优化设置
  const applyOptimizationSettings = async () => {
    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取UI系统
      const uiSystem = engine.getSystem('UISystem');
      if (!uiSystem) {
        throw new Error('UI系统未初始化');
      }

      // 应用设置
      await uiSystem.updateOptimizationSettings(optimizationSettings);

      // 显示成功消息
      message.success(t('debug.ui.settingsApplied'));
    } catch (error) {
      console.error('应用设置失败:', error);
      message.error(`${t('debug.ui.settingsApplyFailed')}: ${(error as Error).message}`);
    }
  };

  // 优化所有组件
  const optimizeAllComponents = async () => {
    setIsOptimizing(true);
    setOptimizationProgress(0);

    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取UI系统
      const uiSystem = engine.getSystem('UISystem');
      if (!uiSystem) {
        throw new Error('UI系统未初始化');
      }

      // 优化所有组件
      const results = await uiSystem.optimizeAllComponents(optimizationSettings);

      // 更新优化结果
      setOptimizationResults(results);

      // 刷新组件列表
      loadUIComponents();

      // 显示成功消息
      message.success(t('debug.ui.optimizationSuccess'));
    } catch (error) {
      console.error('优化组件失败:', error);
      message.error(`${t('debug.ui.optimizationFailed')}: ${(error as Error).message}`);
    } finally {
      setIsOptimizing(false);
      setOptimizationProgress(100);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: t('debug.ui.name'),
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: UIComponentInfo) => (
        <Space>
          {getComponentTypeIcon(record.type)}
          <Tooltip title={record.id}>
            <Text>{text}</Text>
          </Tooltip>
        </Space>
      )},
    {
      title: t('debug.ui.type'),
      dataIndex: 'type',
      key: 'type',
      filters: [
        { text: t('debug.ui.panel'), value: UIComponentType.PANEL },
        { text: t('debug.ui.button'), value: UIComponentType.BUTTON },
        { text: t('debug.ui.text'), value: UIComponentType.TEXT },
        { text: t('debug.ui.image'), value: UIComponentType.IMAGE },
        { text: t('debug.ui.input'), value: UIComponentType.INPUT },
        { text: t('debug.ui.list'), value: UIComponentType.LIST },
        { text: t('debug.ui.table'), value: UIComponentType.TABLE },
        { text: t('debug.ui.chart'), value: UIComponentType.CHART },
        { text: t('debug.ui.other'), value: UIComponentType.OTHER },
      ],
      onFilter: (value: string, record: UIComponentInfo) => record.type === value,
      render: (type: UIComponentType) => t(`debug.ui.${type}`)},
    {
      title: t('debug.ui.renderTime'),
      dataIndex: 'renderTime',
      key: 'renderTime',
      sorter: (a: UIComponentInfo, b: UIComponentInfo) => a.renderTime - b.renderTime,
      render: (time: number) => formatTime(time)},
    {
      title: t('debug.ui.updateTime'),
      dataIndex: 'updateTime',
      key: 'updateTime',
      sorter: (a: UIComponentInfo, b: UIComponentInfo) => a.updateTime - b.updateTime,
      render: (time: number) => formatTime(time)},
    {
      title: t('debug.ui.eventCount'),
      dataIndex: 'eventCount',
      key: 'eventCount',
      sorter: (a: UIComponentInfo, b: UIComponentInfo) => a.eventCount - b.eventCount},
    {
      title: t('debug.ui.visible'),
      dataIndex: 'visible',
      key: 'visible',
      filters: [
        { text: t('common.yes'), value: true },
        { text: t('common.no'), value: false },
      ],
      onFilter: (value: boolean, record: UIComponentInfo) => record.visible === value,
      render: (visible: boolean) => (
        visible ? <EyeOutlined style={{ color: '#52c41a' }} /> : <EyeInvisibleOutlined style={{ color: '#f5222d' }} />
      )},
    {
      title: t('debug.ui.children'),
      dataIndex: 'children',
      key: 'children',
      sorter: (a: UIComponentInfo, b: UIComponentInfo) => a.children - b.children},
    {
      title: t('debug.ui.depth'),
      dataIndex: 'depth',
      key: 'depth',
      sorter: (a: UIComponentInfo, b: UIComponentInfo) => a.depth - b.depth},
    {
      title: t('debug.ui.memoryUsage'),
      dataIndex: 'memoryUsage',
      key: 'memoryUsage',
      sorter: (a: UIComponentInfo, b: UIComponentInfo) => a.memoryUsage - b.memoryUsage,
      render: (memory: number) => formatBytes(memory)},
  ];

  // 渲染性能概览
  const renderPerformanceOverview = () => {
    return (
      <div className="performance-overview">
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('debug.ui.totalRenderTime')}
                value={formatTime(performanceData.totalRenderTime)}
                precision={2}
                valueStyle={{ color: performanceData.totalRenderTime > 16 ? '#f5222d' : '#52c41a' }}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('debug.ui.averageRenderTime')}
                value={formatTime(performanceData.averageRenderTime)}
                precision={2}
                valueStyle={{ color: performanceData.averageRenderTime > 1 ? '#f5222d' : '#52c41a' }}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('debug.ui.totalComponents')}
                value={performanceData.totalComponents}
                precision={0}
                valueStyle={{ color: performanceData.totalComponents > 1000 ? '#faad14' : '#52c41a' }}
                prefix={<AppstoreOutlined />}
                suffix={`/ ${performanceData.visibleComponents} ${t('debug.ui.visible')}`}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('debug.ui.eventListeners')}
                value={performanceData.eventListeners}
                precision={0}
                valueStyle={{ color: performanceData.eventListeners > 500 ? '#f5222d' : '#52c41a' }}
                prefix={<ThunderboltOutlined />}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染性能图表
  const renderPerformanceCharts = () => {
    const renderTimeConfig = {
      data: performanceData.history,
      xField: 'time',
      yField: 'renderTime',
      point: {
        size: 3,
        shape: 'circle'},
      color: '#1890ff',
      yAxis: {
        title: {
          text: t('debug.ui.renderTime')}}};

    const componentsConfig = {
      data: performanceData.history,
      xField: 'time',
      yField: 'components',
      point: {
        size: 3,
        shape: 'circle'},
      color: '#52c41a',
      yAxis: {
        title: {
          text: t('debug.ui.components')}}};

    return (
      <div className="performance-charts">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card title={t('debug.ui.renderTimeChart')}>
              <Line {...renderTimeConfig} />
            </Card>
          </Col>
          <Col span={12}>
            <Card title={t('debug.ui.componentsChart')}>
              <Line {...componentsConfig} />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染优化结果
  const renderOptimizationResults = () => {
    if (!optimizationResults) {
      return (
        <Alert message={t('debug.ui.noOptimizationResults')} type="info" showIcon />
      );
    }

    const {
      optimizedComponents,
      batchedComponents,
      memoizedComponents,
      virtualizedComponents,
      lazyLoadedComponents,
      culledComponents,
      delegatedEvents,
      removedEventListeners,
      renderTimeReduction,
      memoryReduction
    } = optimizationResults;

    return (
      <div className="optimization-results">
        <Card title={t('debug.ui.optimizationResults')}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Statistic
                title={t('debug.ui.optimizedComponents')}
                value={optimizedComponents}
                precision={0}
                valueStyle={{ color: '#52c41a' }}
                prefix={<CheckCircleOutlined />}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title={t('debug.ui.renderTimeReduction')}
                value={renderTimeReduction}
                precision={2}
                valueStyle={{ color: '#52c41a' }}
                prefix={<ClockCircleOutlined />}
                suffix="%"
              />
            </Col>
          </Row>
          <Divider />
          <List
            size="small"
            bordered
            dataSource={[
              { title: t('debug.ui.batchedComponents'), value: batchedComponents },
              { title: t('debug.ui.memoizedComponents'), value: memoizedComponents },
              { title: t('debug.ui.virtualizedComponents'), value: virtualizedComponents },
              { title: t('debug.ui.lazyLoadedComponents'), value: lazyLoadedComponents },
              { title: t('debug.ui.culledComponents'), value: culledComponents },
              { title: t('debug.ui.delegatedEvents'), value: delegatedEvents },
              { title: t('debug.ui.removedEventListeners'), value: removedEventListeners },
              { title: t('debug.ui.memoryReduction'), value: `${memoryReduction.toFixed(2)}%` },
            ]}
            renderItem={item => (
              <List.Item>
                <Text>{item.title}</Text>
                <Text strong>{item.value}</Text>
              </List.Item>
            )}
          />
        </Card>
      </div>
    );
  };

  // 渲染设置面板
  const renderSettingsPanel = () => {
    return (
      <div className="settings-panel">
        <Card title={t('debug.ui.optimizationSettings')}>
          <Collapse defaultActiveKey={['renderingSettings', 'eventSettings', 'advancedSettings']}>
            <Panel header={t('debug.ui.renderingSettings')} key="renderingSettings">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="setting-item">
                    <Checkbox
                      checked={optimizationSettings.enableBatchRendering}
                      onChange={(e) => setOptimizationSettings({ ...optimizationSettings, enableBatchRendering: e.target.checked })}
                    >
                      {t('debug.ui.enableBatchRendering')}
                    </Checkbox>
                    <Text type="secondary">{t('debug.ui.enableBatchRenderingDesc')}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="setting-item">
                    <Checkbox
                      checked={optimizationSettings.enableMemoization}
                      onChange={(e) => setOptimizationSettings({ ...optimizationSettings, enableMemoization: e.target.checked })}
                    >
                      {t('debug.ui.enableMemoization')}
                    </Checkbox>
                    <Text type="secondary">{t('debug.ui.enableMemoizationDesc')}</Text>
                  </div>
                </Col>
              </Row>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="setting-item">
                    <Text>{t('debug.ui.batchSize')}</Text>
                    <Slider
                      min={1}
                      max={50}
                      step={1}
                      value={optimizationSettings.batchSize}
                      onChange={(value) => setOptimizationSettings({ ...optimizationSettings, batchSize: value })}
                      disabled={!optimizationSettings.enableBatchRendering}
                    />
                    <Text type="secondary">{optimizationSettings.batchSize}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="setting-item">
                    <Text>{t('debug.ui.updateInterval')}</Text>
                    <Slider
                      min={1}
                      max={100}
                      step={1}
                      value={optimizationSettings.updateInterval}
                      onChange={(value) => setOptimizationSettings({ ...optimizationSettings, updateInterval: value })}
                    />
                    <Text type="secondary">{optimizationSettings.updateInterval} ms</Text>
                  </div>
                </Col>
              </Row>
            </Panel>
            <Panel header={t('debug.ui.eventSettings')} key="eventSettings">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="setting-item">
                    <Checkbox
                      checked={optimizationSettings.enableEventDelegation}
                      onChange={(e) => setOptimizationSettings({ ...optimizationSettings, enableEventDelegation: e.target.checked })}
                    >
                      {t('debug.ui.enableEventDelegation')}
                    </Checkbox>
                    <Text type="secondary">{t('debug.ui.enableEventDelegationDesc')}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="setting-item">
                    <Text>{t('debug.ui.maxEventListeners')}</Text>
                    <Slider
                      min={10}
                      max={1000}
                      step={10}
                      value={optimizationSettings.maxEventListeners}
                      onChange={(value) => setOptimizationSettings({ ...optimizationSettings, maxEventListeners: value })}
                      disabled={!optimizationSettings.enableEventDelegation}
                    />
                    <Text type="secondary">{optimizationSettings.maxEventListeners}</Text>
                  </div>
                </Col>
              </Row>
            </Panel>
            <Panel header={t('debug.ui.advancedSettings')} key="advancedSettings">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="setting-item">
                    <Checkbox
                      checked={optimizationSettings.enableVirtualization}
                      onChange={(e) => setOptimizationSettings({ ...optimizationSettings, enableVirtualization: e.target.checked })}
                    >
                      {t('debug.ui.enableVirtualization')}
                    </Checkbox>
                    <Text type="secondary">{t('debug.ui.enableVirtualizationDesc')}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="setting-item">
                    <Checkbox
                      checked={optimizationSettings.enableLazyLoading}
                      onChange={(e) => setOptimizationSettings({ ...optimizationSettings, enableLazyLoading: e.target.checked })}
                    >
                      {t('debug.ui.enableLazyLoading')}
                    </Checkbox>
                    <Text type="secondary">{t('debug.ui.enableLazyLoadingDesc')}</Text>
                  </div>
                </Col>
              </Row>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="setting-item">
                    <Checkbox
                      checked={optimizationSettings.enableCulling}
                      onChange={(e) => setOptimizationSettings({ ...optimizationSettings, enableCulling: e.target.checked })}
                    >
                      {t('debug.ui.enableCulling')}
                    </Checkbox>
                    <Text type="secondary">{t('debug.ui.enableCullingDesc')}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="setting-item">
                    <Text>{t('debug.ui.cullingDistance')}</Text>
                    <Slider
                      min={100}
                      max={5000}
                      step={100}
                      value={optimizationSettings.cullingDistance}
                      onChange={(value) => setOptimizationSettings({ ...optimizationSettings, cullingDistance: value })}
                      disabled={!optimizationSettings.enableCulling}
                    />
                    <Text type="secondary">{optimizationSettings.cullingDistance} {t('debug.ui.units')}</Text>
                  </div>
                </Col>
              </Row>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="setting-item">
                    <Checkbox
                      checked={optimizationSettings.useRequestAnimationFrame}
                      onChange={(e) => setOptimizationSettings({ ...optimizationSettings, useRequestAnimationFrame: e.target.checked })}
                    >
                      {t('debug.ui.useRequestAnimationFrame')}
                    </Checkbox>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="setting-item">
                    <Checkbox
                      checked={optimizationSettings.useWebGL}
                      onChange={(e) => setOptimizationSettings({ ...optimizationSettings, useWebGL: e.target.checked })}
                    >
                      {t('debug.ui.useWebGL')}
                    </Checkbox>
                  </div>
                </Col>
              </Row>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="setting-item">
                    <Checkbox
                      checked={optimizationSettings.useWebWorkers}
                      onChange={(e) => setOptimizationSettings({ ...optimizationSettings, useWebWorkers: e.target.checked })}
                    >
                      {t('debug.ui.useWebWorkers')}
                    </Checkbox>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="setting-item">
                    <Checkbox
                      checked={optimizationSettings.useOffscreenCanvas}
                      onChange={(e) => setOptimizationSettings({ ...optimizationSettings, useOffscreenCanvas: e.target.checked })}
                    >
                      {t('debug.ui.useOffscreenCanvas')}
                    </Checkbox>
                  </div>
                </Col>
              </Row>
            </Panel>
          </Collapse>
          <div className="settings-actions">
            <Button type="primary" onClick={applyOptimizationSettings}>
              {t('debug.ui.applySettings')}
            </Button>
          </div>
        </Card>
      </div>
    );
  };

  return (
    <div className="ui-optimization-panel">
      <div className="panel-header">
        <Space>
          <Title level={4}>{t('debug.ui.title')}</Title>
          <Tooltip title={t('debug.ui.refresh')}>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadUIComponents}
              loading={loading}
            />
          </Tooltip>
          <Tooltip title={t('debug.ui.optimizeSelected')}>
            <Button
              icon={<ThunderboltOutlined />}
              onClick={optimizeSelectedComponents}
              disabled={selectedComponents.length === 0 || isOptimizing}
            />
          </Tooltip>
          <Tooltip title={t('debug.ui.optimizeAll')}>
            <Button
              type="primary"
              icon={<RocketOutlined />}
              onClick={optimizeAllComponents}
              loading={isOptimizing}
              disabled={isOptimizing}
            />
          </Tooltip>
        </Space>
        <div className="search-container">
          <Search
            placeholder={t('debug.ui.searchPlaceholder')}
            onSearch={value => setSearchText(value)}
            style={{ width: 250 }}
          />
        </div>
      </div>

      {isOptimizing && (
        <div className="optimization-progress">
          <Progress percent={optimizationProgress} status="active" />
        </div>
      )}

      <Tabs defaultActiveKey="components">
        <TabPane tab={<span><AppstoreOutlined />{t('debug.ui.components')}</span>} key="components">
          <Table
            dataSource={getFilteredComponents()}
            columns={columns}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
            rowSelection={{
              selectedRowKeys: selectedComponents,
              onChange: (selectedRowKeys) => setSelectedComponents(selectedRowKeys as string[])}}
          />
        </TabPane>
        <TabPane tab={<span><DashboardOutlined />{t('debug.ui.performance')}</span>} key="performance">
          {renderPerformanceOverview()}
          {renderPerformanceCharts()}
        </TabPane>
        <TabPane tab={<span><RocketOutlined />{t('debug.ui.optimization')}</span>} key="optimization">
          {renderOptimizationResults()}
        </TabPane>
        <TabPane tab={<span><SettingOutlined />{t('debug.ui.settings')}</span>} key="settings">
          {renderSettingsPanel()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default UIOptimizationPanel;
