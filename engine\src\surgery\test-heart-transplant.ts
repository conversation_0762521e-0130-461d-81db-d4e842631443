/**
 * 心脏移植手术测试文件
 * 用于验证 HeartTransplantProcedure 类的功能
 */
import { 
  HeartTransplantProcedure, 
  SurgeryStage, 
  SurgicalToolSystem, 
  PatientMonitoringSystem 
} from './HeartTransplantProcedure';
import { Entity } from '../core/Entity';
import { VascularSystem } from '../physics/softbody/specialized/VascularSystem';

/**
 * 测试心脏移植手术流程
 */
function testHeartTransplantProcedure(): void {
  console.log('开始测试心脏移植手术流程...');

  // 创建测试实体
  const patientEntity = new Entity('患者');
  const donorHeartEntity = new Entity('供体心脏');

  // 创建系统（模拟）
  const toolSystem = new SurgicalToolSystem();
  const vascularSystem = new VascularSystem();
  const monitoringSystem = new PatientMonitoringSystem();

  // 创建心脏移植手术流程管理器
  const transplantProcedure = new HeartTransplantProcedure(
    patientEntity,
    donorHeartEntity,
    toolSystem,
    vascularSystem,
    monitoringSystem
  );

  // 测试初始状态
  console.log('初始手术阶段:', transplantProcedure.getCurrentStage());
  console.log('初始手术进度:', transplantProcedure.getProgress());

  // 执行手术流程
  console.log('\n执行心脏移植手术流程...');
  transplantProcedure.performTransplantStages();

  // 测试最终状态
  console.log('\n最终手术阶段:', transplantProcedure.getCurrentStage());
  console.log('最终手术进度:', transplantProcedure.getProgress());

  console.log('\n心脏移植手术流程测试完成！');
}

// 运行测试
if (typeof window === 'undefined') {
  // Node.js 环境
  testHeartTransplantProcedure();
}

export { testHeartTransplantProcedure };
