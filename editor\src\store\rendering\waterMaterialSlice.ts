/**
 * 水体材质状态切片
 * 用于管理水体材质相关的状态
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { AppDispatch, RootState } from '../index';

// 定义 AppThunk 类型
export type AppThunk<ReturnType = void> = (
  dispatch: AppDispatch,
  getState: () => RootState
) => ReturnType;

/**
 * 水体类型枚举
 */
export enum WaterBodyType {
  OCEAN = 'ocean',
  LAKE = 'lake',
  RIVER = 'river',
  POOL = 'pool',
  PUDDLE = 'puddle',
  UNDERGROUND = 'underground'
}

/**
 * 水体材质状态接口
 */
export interface WaterMaterialState {
  /** 实体ID */
  entityId: string;
  /** 水体类型 */
  type: WaterBodyType;
  /** 颜色 */
  color: string;
  /** 不透明度 */
  opacity: number;
  /** 反射率 */
  reflectivity: number;
  /** 折射率 */
  refractionRatio: number;
  /** 波动强度 */
  waveStrength: number;
  /** 波动速度 */
  waveSpeed: number;
  /** 波动尺寸 */
  waveScale: number;
  /** 波动方向 */
  waveDirection: {
    x: number;
    y: number;
  };
  /** 深度 */
  depth: number;
  /** 深水颜色 */
  depthColor: string;
  /** 浅水颜色 */
  shallowColor: string;
  /** 是否启用因果波纹 */
  enableCaustics: boolean;
  /** 因果波纹强度 */
  causticsIntensity: number;
  /** 是否启用泡沫 */
  enableFoam: boolean;
  /** 泡沫强度 */
  foamIntensity: number;
  /** 是否启用水下雾 */
  enableUnderwaterFog: boolean;
  /** 水下雾密度 */
  underwaterFogDensity: number;
  /** 是否启用水下扭曲 */
  enableUnderwaterDistortion: boolean;
  /** 水下扭曲强度 */
  underwaterDistortionStrength: number;
  /** 法线贴图 */
  normalMap: string | null;
  /** 反射贴图 */
  reflectionMap: string | null;
  /** 折射贴图 */
  refractionMap: string | null;
  /** 深度贴图 */
  depthMap: string | null;
  /** 因果波纹贴图 */
  causticsMap: string | null;
  /** 泡沫贴图 */
  foamMap: string | null;
}

/**
 * 水体材质状态接口
 */
export interface WaterMaterialsState {
  /** 水体材质列表 */
  waterMaterials: WaterMaterialState[];
  /** 是否加载中 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
}

/**
 * 初始状态
 */
const initialState: WaterMaterialsState = {
  waterMaterials: [],
  loading: false,
  error: null
};

/**
 * 水体材质状态切片
 */
const waterMaterialSlice = createSlice({
  name: 'waterMaterial',
  initialState,
  reducers: {
    /**
     * 设置加载状态
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    
    /**
     * 设置错误信息
     */
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    /**
     * 设置水体材质列表
     */
    setWaterMaterials: (state, action: PayloadAction<WaterMaterialState[]>) => {
      state.waterMaterials = action.payload;
    },
    
    /**
     * 添加水体材质
     */
    addWaterMaterial: (state, action: PayloadAction<WaterMaterialState>) => {
      state.waterMaterials.push(action.payload);
    },
    
    /**
     * 更新水体材质
     */
    updateWaterMaterial: (state, action: PayloadAction<{ entityId: string; properties: Partial<WaterMaterialState> }>) => {
      const { entityId, properties } = action.payload;
      const index = state.waterMaterials.findIndex(wm => wm.entityId === entityId);
      
      if (index !== -1) {
        state.waterMaterials[index] = {
          ...state.waterMaterials[index],
          ...properties
        };
      }
    },
    
    /**
     * 删除水体材质
     */
    removeWaterMaterial: (state, action: PayloadAction<string>) => {
      state.waterMaterials = state.waterMaterials.filter(wm => wm.entityId !== action.payload);
    }
  }
});

// 导出动作创建器
export const {
  setLoading,
  setError,
  setWaterMaterials,
  addWaterMaterial,
  updateWaterMaterial,
  removeWaterMaterial
} = waterMaterialSlice.actions;

/**
 * 获取水体材质列表
 */
export const fetchWaterMaterials = (): AppThunk => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    // 这里应该从引擎获取水体材质列表
    // const waterMaterials = await api.getWaterMaterials();
    // dispatch(setWaterMaterials(waterMaterials));

    dispatch(setLoading(false));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '获取水体材质列表失败';
    dispatch(setError(errorMessage));
    dispatch(setLoading(false));
  }
};

/**
 * 创建水体材质
 */
export const createWaterMaterial = (waterMaterialData: Omit<WaterMaterialState, 'entityId'>): AppThunk => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    // 这里应该在引擎中创建水体材质
    // const createdWaterMaterial = await api.createWaterMaterial(waterMaterialData);
    // dispatch(addWaterMaterial(createdWaterMaterial));

    // 临时使用传入的数据（实际应该使用API返回的数据）
    console.log('创建水体材质:', waterMaterialData);

    dispatch(setLoading(false));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '创建水体材质失败';
    dispatch(setError(errorMessage));
    dispatch(setLoading(false));
  }
};

/**
 * 更新水体材质属性
 */
export const updateWaterMaterialProperties = (entityId: string, properties: Partial<WaterMaterialState>): AppThunk => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    // 这里应该在引擎中更新水体材质属性
    // await api.updateWaterMaterial(entityId, properties);
    dispatch(updateWaterMaterial({ entityId, properties }));

    dispatch(setLoading(false));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '更新水体材质属性失败';
    dispatch(setError(errorMessage));
    dispatch(setLoading(false));
  }
};

/**
 * 删除水体材质
 */
export const deleteWaterMaterial = (entityId: string): AppThunk => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    // 这里应该在引擎中删除水体材质
    // await api.deleteWaterMaterial(entityId);
    dispatch(removeWaterMaterial(entityId));

    dispatch(setLoading(false));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '删除水体材质失败';
    dispatch(setError(errorMessage));
    dispatch(setLoading(false));
  }
};

export default waterMaterialSlice.reducer;
