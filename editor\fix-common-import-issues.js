#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 递归查找所有 .tsx 和 .ts 文件
function findFiles(dir, extensions = ['.tsx', '.ts']) {
  let results = [];
  try {
    const list = fs.readdirSync(dir);
    
    list.forEach(file => {
      const filePath = path.join(dir, file);
      try {
        const stat = fs.statSync(filePath);
        
        if (stat && stat.isDirectory()) {
          // 跳过 node_modules 和其他不需要的目录
          if (!['node_modules', '.git', 'dist', 'build', 'coverage'].includes(file)) {
            results = results.concat(findFiles(filePath, extensions));
          }
        } else {
          const ext = path.extname(file);
          if (extensions.includes(ext)) {
            results.push(filePath);
          }
        }
      } catch (err) {
        // 忽略无法访问的文件
      }
    });
  } catch (err) {
    // 忽略无法访问的目录
  }
  
  return results;
}

// 修复单个文件
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 修复 .mjs 导入
    if (content.includes('.mjs')) {
      content = content.replace(/from ['"](.+?)\.mjs['"]/g, "from '$1'");
      modified = true;
    }
    
    // 2. 移除自引用导入
    const fileName = path.basename(filePath, path.extname(filePath));
    const selfImportPattern = new RegExp(`import\\s+.*?from\\s+['"]\\.\\/\\${fileName}['"]`, 'g');
    if (selfImportPattern.test(content)) {
      content = content.replace(selfImportPattern, '');
      modified = true;
    }
    
    // 3. 修复重复的 import 行
    const lines = content.split('\n');
    const newLines = [];
    const seenImports = new Set();
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // 检查是否是重复的 import 行
      if (trimmedLine.startsWith('import ') && trimmedLine.includes('from ')) {
        if (!seenImports.has(trimmedLine)) {
          seenImports.add(trimmedLine);
          newLines.push(line);
        } else {
          modified = true;
          // 跳过重复的 import
        }
      } else if (trimmedLine.startsWith('const {') && trimmedLine.includes('} = ')) {
        // 检查重复的解构语句
        if (!seenImports.has(trimmedLine)) {
          seenImports.add(trimmedLine);
          newLines.push(line);
        } else {
          modified = true;
          // 跳过重复的解构
        }
      } else {
        newLines.push(line);
      }
    }
    
    if (modified) {
      const newContent = newLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`错误处理文件 ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src 目录不存在');
    process.exit(1);
  }
  
  console.log('开始修复常见的 import 问题...');
  
  const files = findFiles(srcDir);
  let fixedCount = 0;
  
  console.log(`找到 ${files.length} 个文件`);
  
  files.forEach(file => {
    if (fixFile(file)) {
      console.log(`✓ 修复了 ${path.relative(__dirname, file)}`);
      fixedCount++;
    }
  });
  
  console.log(`\n修复完成！共修复了 ${fixedCount} 个文件。`);
}

if (require.main === module) {
  main();
}

module.exports = { fixFile, findFiles };
