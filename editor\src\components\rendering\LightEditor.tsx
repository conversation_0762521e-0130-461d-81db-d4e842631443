/**
 * 光源编辑器
 */
import React from 'react';
import { Card, Form, Select, InputNumber, ColorPicker, Typography } from 'antd';

const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { Text } = Typography;

/**
 * 光源编辑器属性
 */
interface LightEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * 光源编辑器组件
 */
const LightEditor: React.FC<LightEditorProps> = ({ data, onChange }) => {
  const [form] = Form.useForm();

  // 默认数据
  const defaultData = {
    enabled: true,
    type: 'directional',
    color: '#ffffff',
    intensity: 1.0,
    range: 10,
    angle: 30,
    castShadows: true,
    ...data
  };

  // 处理数据变化
  const handleChange = (field: string, value: any) => {
    const newData = { ...defaultData, [field]: value };
    if (onChange) {
      onChange(newData);
    }
  };

  return (
    <Card title="光源" size="small">
      <Form
        form={form}
        layout="vertical"
        initialValues={defaultData}
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([field, value]) => {
            handleChange(field, value);
          });
        }}
      >
        <Form.Item label="启用" name="enabled" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="类型" name="type">
          <Select>
            <Option value="directional">方向光</Option>
            <Option value="point">点光源</Option>
            <Option value="spot">聚光灯</Option>
            <Option value="ambient">环境光</Option>
          </Select>
        </Form.Item>

        <Form.Item label="颜色" name="color">
          <ColorPicker />
        </Form.Item>

        <Form.Item label="强度" name="intensity">
          <InputNumber min={0} max={10} step={0.1} />
        </Form.Item>

        <Form.Item label="范围" name="range">
          <InputNumber min={0} max={100} step={1} />
        </Form.Item>

        <Form.Item label="角度" name="angle">
          <InputNumber min={0} max={180} step={1} />
        </Form.Item>

        <Form.Item label="投射阴影" name="castShadows" valuePropName="checked">
          <Switch />
        </Form.Item>
      </Form>
    </Card>
  );
};

export default LightEditor;
