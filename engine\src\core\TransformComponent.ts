/**
 * 变换组件
 * 管理实体的位置、旋转和缩放
 */

import * as THREE from 'three';
import { Component } from './Component';

export class TransformComponent extends Component {
  public static readonly type: string = 'Transform';
  
  /** Three.js 对象 */
  public object3D: THREE.Object3D;
  
  /** 位置 */
  public position: THREE.Vector3;
  
  /** 旋转 */
  public rotation: THREE.Euler;
  
  /** 四元数旋转 */
  public quaternion: THREE.Quaternion;
  
  /** 缩放 */
  public scale: THREE.Vector3;
  
  /** 变换矩阵 */
  public matrix: THREE.Matrix4;
  
  /** 世界变换矩阵 */
  public matrixWorld: THREE.Matrix4;

  constructor() {
    super(TransformComponent.type);
    
    // 创建 Three.js 对象
    this.object3D = new THREE.Object3D();
    
    // 获取引用
    this.position = this.object3D.position;
    this.rotation = this.object3D.rotation;
    this.quaternion = this.object3D.quaternion;
    this.scale = this.object3D.scale;
    this.matrix = this.object3D.matrix;
    this.matrixWorld = this.object3D.matrixWorld;
  }

  /**
   * 设置位置
   */
  public setPosition(x: number, y: number, z: number): void {
    this.position.set(x, y, z);
  }

  /**
   * 设置旋转（欧拉角）
   */
  public setRotation(x: number, y: number, z: number): void {
    (this as any).setRotationQuaternion(x, y, z);
  }

  /**
   * 设置旋转（四元数）
   */
  public setQuaternion(x: number, y: number, z: number, w: number): void {
    (this as any).setRotationQuaternion(x, y, z, w);
  }

  /**
   * 设置缩放
   */
  public setScale(x: number, y: number, z: number): void {
    this.scale.set(x, y, z);
  }

  /**
   * 获取位置
   */
  public getPosition(): THREE.Vector3 {
    return this.position.clone();
  }

  /**
   * 获取旋转
   */
  public getRotation(): THREE.Euler {
    return this.rotation.clone();
  }

  /**
   * 获取四元数
   */
  public getQuaternion(): THREE.Quaternion {
    return this.quaternion.clone();
  }

  /**
   * 获取缩放
   */
  public getScale(): THREE.Vector3 {
    return this.scale.clone();
  }

  /**
   * 获取世界位置
   */
  public getWorldPosition(): THREE.Vector3 {
    const worldPosition = new THREE.Vector3();
    this.object3D.getWorldPosition(worldPosition);
    return worldPosition;
  }

  /**
   * 获取世界旋转
   */
  public getWorldQuaternion(): THREE.Quaternion {
    const worldQuaternion = new THREE.Quaternion();
    this.object3D.getWorldQuaternion(worldQuaternion);
    return worldQuaternion;
  }

  /**
   * 获取世界缩放
   */
  public getWorldScale(): THREE.Vector3 {
    const worldScale = new THREE.Vector3();
    this.object3D.getWorldScale(worldScale);
    return worldScale;
  }

  /**
   * 向前移动
   */
  public translateZ(distance: number): void {
    this.object3D.translateZ(distance);
  }

  /**
   * 向右移动
   */
  public translateX(distance: number): void {
    this.object3D.translateX(distance);
  }

  /**
   * 向上移动
   */
  public translateY(distance: number): void {
    this.object3D.translateY(distance);
  }

  /**
   * 绕X轴旋转
   */
  public rotateX(angle: number): void {
    this.object3D.rotateX(angle);
  }

  /**
   * 绕Y轴旋转
   */
  public rotateY(angle: number): void {
    this.object3D.rotateY(angle);
  }

  /**
   * 绕Z轴旋转
   */
  public rotateZ(angle: number): void {
    this.object3D.rotateZ(angle);
  }

  /**
   * 看向目标
   */
  public lookAt(target: THREE.Vector3): void {
    this.object3D.lookAt(target);
  }

  /**
   * 添加子对象
   */
  public add(object: THREE.Object3D): void {
    this.object3D.add(object);
  }

  /**
   * 移除子对象
   */
  public remove(object: THREE.Object3D): void {
    this.object3D.remove(object);
  }

  /**
   * 更新变换矩阵
   */
  public updateMatrix(): void {
    this.object3D.updateMatrix();
  }

  /**
   * 更新世界变换矩阵
   */
  public updateMatrixWorld(force?: boolean): void {
    this.object3D.updateMatrixWorld(force);
  }

  /**
   * 更新组件
   */
  protected onUpdate(deltaTime: number): void {
    // 自动更新矩阵
    this.object3D.updateMatrixWorld();
  }

  /**
   * 销毁组件
   */
  protected onDispose(): void {
    // 清理 Three.js 对象
    this.object3D.clear();
    this.object3D.removeFromParent();
  }
}
