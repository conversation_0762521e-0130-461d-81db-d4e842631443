/**
 * dl-engine 模拟实现
 * 用于测试环境
 */

// 模拟 EventEmitter 类
class MockEventEmitter {
  constructor() {
    this.events = {};
  }

  on(event, listener) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
  }

  off(event, listener) {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter(l => l !== listener);
    }
  }

  emit(event, ...args) {
    if (this.events[event]) {
      this.events[event].forEach(listener => listener(...args));
    }
  }

  addEventListener(event, listener) {
    this.on(event, listener);
  }

  removeEventListener(event, listener) {
    this.off(event, listener);
  }
}

// 模拟引擎模块
const mockEngine = {
  init: jest.fn().mockResolvedValue(true),
  createScene: jest.fn().mockReturnValue({
    id: 'mock-scene',
    entities: [],
  }),
  loadScene: jest.fn().mockResolvedValue({
    id: 'mock-scene',
    entities: [],
  }),
  saveScene: jest.fn().mockResolvedValue(true),
  addEntity: jest.fn().mockReturnValue({
    id: 'mock-entity',
    name: 'Mock Entity',
  }),
  removeEntity: jest.fn().mockReturnValue(true),
  updateEntity: jest.fn().mockReturnValue(true),
  render: jest.fn(),
  dispose: jest.fn(),
  EventEmitter: MockEventEmitter,
};

module.exports = mockEngine;
