/**
 * 创建基本编辑器组件的脚本
 */
const fs = require('fs');
const path = require('path');

// 编辑器模板
const createEditorTemplate = (componentName, title, fields) => {
  const fieldsCode = fields.map(field => {
    switch (field.type) {
      case 'switch':
        return `        <Form.Item label="${field.label}" name="${field.name}" valuePropName="checked">
          <Switch />
        </Form.Item>`;
      case 'select':
        const options = field.options.map(opt => `            <Option value="${opt.value}">${opt.label}</Option>`).join('\n');
        return `        <Form.Item label="${field.label}" name="${field.name}">
          <Select>
${options}
          </Select>
        </Form.Item>`;
      case 'number':
        return `        <Form.Item label="${field.label}" name="${field.name}">
          <InputNumber min={${field.min || 0}} max={${field.max || 100}} step={${field.step || 1}} />
        </Form.Item>`;
      case 'color':
        return `        <Form.Item label="${field.label}" name="${field.name}">
          <ColorPicker />
        </Form.Item>`;
      default:
        return `        <Form.Item label="${field.label}" name="${field.name}">
          <Input />
        </Form.Item>`;
    }
  }).join('\n\n');

  return `/**
 * ${title}编辑器
 */
import React from 'react';
import { Card, Form, Select, Switch, InputNumber, ColorPicker, Input, Typography } from 'antd';

const { Option } = Select;
const { Text } = Typography;

/**
 * ${title}编辑器属性
 */
interface ${componentName}Props {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * ${title}编辑器组件
 */
const ${componentName}: React.FC<${componentName}Props> = ({ data, onChange }) => {
  const [form] = Form.useForm();

  // 默认数据
  const defaultData = {
    enabled: true,
    ...data
  };

  // 处理数据变化
  const handleChange = (field: string, value: any) => {
    const newData = { ...defaultData, [field]: value };
    if (onChange) {
      onChange(newData);
    }
  };

  return (
    <Card title="${title}" size="small">
      <Form
        form={form}
        layout="vertical"
        initialValues={defaultData}
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([field, value]) => {
            handleChange(field, value);
          });
        }}
      >
        <Form.Item label="启用" name="enabled" valuePropName="checked">
          <Switch />
        </Form.Item>

${fieldsCode}
      </Form>
    </Card>
  );
};

export default ${componentName};`;
};

// 编辑器配置
const editors = [
  {
    name: 'CameraEditor',
    title: '相机',
    path: 'src/components/rendering/CameraEditor.tsx',
    fields: [
      { name: 'type', label: '类型', type: 'select', options: [
        { value: 'perspective', label: '透视相机' },
        { value: 'orthographic', label: '正交相机' }
      ]},
      { name: 'fov', label: '视野角度', type: 'number', min: 1, max: 180, step: 1 },
      { name: 'near', label: '近裁剪面', type: 'number', min: 0.01, max: 10, step: 0.01 },
      { name: 'far', label: '远裁剪面', type: 'number', min: 1, max: 1000, step: 1 }
    ]
  },
  {
    name: 'AnimationEditor',
    title: '动画',
    path: 'src/components/animation/AnimationEditor.tsx',
    fields: [
      { name: 'autoPlay', label: '自动播放', type: 'switch' },
      { name: 'loop', label: '循环播放', type: 'switch' },
      { name: 'speed', label: '播放速度', type: 'number', min: 0.1, max: 5, step: 0.1 }
    ]
  },
  {
    name: 'AudioSourceEditor',
    title: '音频源',
    path: 'src/components/audio/AudioSourceEditor.tsx',
    fields: [
      { name: 'volume', label: '音量', type: 'number', min: 0, max: 1, step: 0.1 },
      { name: 'loop', label: '循环播放', type: 'switch' },
      { name: 'autoPlay', label: '自动播放', type: 'switch' }
    ]
  },
  {
    name: 'ScriptEditor',
    title: '脚本',
    path: 'src/components/scripting/ScriptEditor.tsx',
    fields: [
      { name: 'scriptPath', label: '脚本路径', type: 'input' }
    ]
  },
  {
    name: 'ParticleSystemEditor',
    title: '粒子系统',
    path: 'src/components/effects/ParticleSystemEditor.tsx',
    fields: [
      { name: 'maxParticles', label: '最大粒子数', type: 'number', min: 1, max: 10000, step: 1 },
      { name: 'emissionRate', label: '发射速率', type: 'number', min: 1, max: 1000, step: 1 },
      { name: 'autoPlay', label: '自动播放', type: 'switch' }
    ]
  },
  {
    name: 'UIElementEditor',
    title: 'UI元素',
    path: 'src/components/ui/UIElementEditor.tsx',
    fields: [
      { name: 'visible', label: '可见', type: 'switch' },
      { name: 'interactive', label: '可交互', type: 'switch' }
    ]
  },
  {
    name: 'InteractionEditor',
    title: '交互',
    path: 'src/components/interaction/InteractionEditor.tsx',
    fields: [
      { name: 'interactionType', label: '交互类型', type: 'select', options: [
        { value: 'click', label: '点击' },
        { value: 'hover', label: '悬停' },
        { value: 'grab', label: '抓取' }
      ]}
    ]
  },
  {
    name: 'NetworkComponentEditor',
    title: '网络组件',
    path: 'src/components/network/NetworkComponentEditor.tsx',
    fields: [
      { name: 'syncTransform', label: '同步变换', type: 'switch' },
      { name: 'syncRate', label: '同步频率', type: 'number', min: 1, max: 60, step: 1 }
    ]
  }
];

// 创建目录
const createDir = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
};

// 创建编辑器文件
editors.forEach(editor => {
  const filePath = path.join(__dirname, editor.path);
  const dirPath = path.dirname(filePath);
  
  createDir(dirPath);
  
  const content = createEditorTemplate(editor.name, editor.title, editor.fields);
  fs.writeFileSync(filePath, content, 'utf8');
  
  console.log(`Created: ${editor.path}`);
});

console.log('All basic editors created successfully!');
