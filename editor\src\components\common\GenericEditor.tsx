/**
 * 通用编辑器组件
 */
import React from 'react';
import { Card, Form, Switch, Typography } from 'antd';

const { Text } = Typography;

/**
 * 通用编辑器属性
 */
interface GenericEditorProps {
  /** 标题 */
  title: string;
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * 通用编辑器组件
 */
const GenericEditor: React.FC<GenericEditorProps> = ({ title, data, onChange }) => {
  const [form] = Form.useForm();

  // 默认数据
  const defaultData = {
    enabled: true,
    ...data
  };

  // 处理数据变化
  const handleChange = (field: string, value: any) => {
    const newData = { ...defaultData, [field]: value };
    if (onChange) {
      onChange(newData);
    }
  };

  return (
    <Card title={title} size="small">
      <Form
        form={form}
        layout="vertical"
        initialValues={defaultData}
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([field, value]) => {
            handleChange(field, value);
          });
        }}
      >
        <Form.Item label="启用" name="enabled" valuePropName="checked">
          <Switch />
        </Form.Item>
        
        <Text type="secondary">
          {title}组件编辑器 - 功能开发中
        </Text>
      </Form>
    </Card>
  );
};

export default GenericEditor;
