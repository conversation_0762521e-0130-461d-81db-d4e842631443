/**
 * 资源优化建议面板组件
 * 用于提供资源优化建议
 */
import React, { useState, useEffect, useRef } from 'react';
import { Row, Col, List, Typography, Space, Button, Alert, Tag, Collapse, Tooltip, Steps, message } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  BulbOutlined,
  RocketOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  FileImageOutlined,
  AppstoreOutlined,
  SoundOutlined,
  CodeOutlined,
  BgColorsOutlined,
  FileOutlined,
  ReloadOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  SearchOutlined,
  FireOutlined,
  ClockCircleOutlined,
  ToolOutlined,
  QuestionCircleOutlined,
  CheckOutlined,
  CloseOutlined} from '@ant-design/icons';
import { PerformanceOptimizationService } from '../../services/PerformanceOptimizationService';
import { EngineService } from '../../services/EngineService';
import './ResourceOptimizationSuggestionPanel.less';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Step } = Steps;
const { confirm } = Modal;

/**
 * 优化建议接口
 */
interface OptimizationSuggestion {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: 'texture' | 'geometry' | 'memory' | 'loading' | 'other';
  steps: string[];
  benefits: string[];
  automated: boolean;
  applied: boolean;
}

/**
 * 资源优化建议面板属性接口
 */
interface ResourceOptimizationSuggestionPanelProps {
  className?: string;
}

/**
 * 资源优化建议面板组件
 */
const ResourceOptimizationSuggestionPanel: React.FC<ResourceOptimizationSuggestionPanelProps> = ({ className }) => {
  const { t } = useTranslation();
  
  // 引用
  const performanceService = useRef(new PerformanceOptimizationService());
  
  // 状态
  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState<string | null>(null);
  const [applyingId, setApplyingId] = useState<string | null>(null);
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterImpact, setFilterImpact] = useState<string>('all');
  
  // 初始化
  useEffect(() => {
    loadSuggestions();
  }, []);
  
  // 加载优化建议
  const loadSuggestions = async () => {
    setLoading(true);
    
    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }
      
      // 获取性能优化服务
      const optimizationService = performanceService.current;
      
      // 生成优化建议
      const report = await optimizationService.generateOptimizationSuggestions();
      
      // 转换为组件使用的格式
      const formattedSuggestions: OptimizationSuggestion[] = [
        {
          id: 'texture-compression',
          title: t('debug.suggestion.textureCompression'),
          description: t('debug.suggestion.textureCompressionDesc'),
          impact: 'high',
          category: 'texture',
          steps: [
            t('debug.suggestion.textureCompressionStep1'),
            t('debug.suggestion.textureCompressionStep2'),
            t('debug.suggestion.textureCompressionStep3'),
          ],
          benefits: [
            t('debug.suggestion.textureCompressionBenefit1'),
            t('debug.suggestion.textureCompressionBenefit2'),
          ],
          automated: true,
          applied: false},
        {
          id: 'texture-atlas',
          title: t('debug.suggestion.textureAtlas'),
          description: t('debug.suggestion.textureAtlasDesc'),
          impact: 'medium',
          category: 'texture',
          steps: [
            t('debug.suggestion.textureAtlasStep1'),
            t('debug.suggestion.textureAtlasStep2'),
          ],
          benefits: [
            t('debug.suggestion.textureAtlasBenefit1'),
            t('debug.suggestion.textureAtlasBenefit2'),
          ],
          automated: true,
          applied: false},
        {
          id: 'geometry-lod',
          title: t('debug.suggestion.geometryLOD'),
          description: t('debug.suggestion.geometryLODDesc'),
          impact: 'high',
          category: 'geometry',
          steps: [
            t('debug.suggestion.geometryLODStep1'),
            t('debug.suggestion.geometryLODStep2'),
            t('debug.suggestion.geometryLODStep3'),
          ],
          benefits: [
            t('debug.suggestion.geometryLODBenefit1'),
            t('debug.suggestion.geometryLODBenefit2'),
          ],
          automated: true,
          applied: false},
        {
          id: 'geometry-instancing',
          title: t('debug.suggestion.geometryInstancing'),
          description: t('debug.suggestion.geometryInstancingDesc'),
          impact: 'medium',
          category: 'geometry',
          steps: [
            t('debug.suggestion.geometryInstancingStep1'),
            t('debug.suggestion.geometryInstancingStep2'),
          ],
          benefits: [
            t('debug.suggestion.geometryInstancingBenefit1'),
            t('debug.suggestion.geometryInstancingBenefit2'),
          ],
          automated: true,
          applied: false},
        {
          id: 'memory-cleanup',
          title: t('debug.suggestion.memoryCleanup'),
          description: t('debug.suggestion.memoryCleanupDesc'),
          impact: 'high',
          category: 'memory',
          steps: [
            t('debug.suggestion.memoryCleanupStep1'),
            t('debug.suggestion.memoryCleanupStep2'),
          ],
          benefits: [
            t('debug.suggestion.memoryCleanupBenefit1'),
            t('debug.suggestion.memoryCleanupBenefit2'),
          ],
          automated: true,
          applied: false},
        {
          id: 'lazy-loading',
          title: t('debug.suggestion.lazyLoading'),
          description: t('debug.suggestion.lazyLoadingDesc'),
          impact: 'medium',
          category: 'loading',
          steps: [
            t('debug.suggestion.lazyLoadingStep1'),
            t('debug.suggestion.lazyLoadingStep2'),
            t('debug.suggestion.lazyLoadingStep3'),
          ],
          benefits: [
            t('debug.suggestion.lazyLoadingBenefit1'),
            t('debug.suggestion.lazyLoadingBenefit2'),
          ],
          automated: true,
          applied: false},
        {
          id: 'resource-pooling',
          title: t('debug.suggestion.resourcePooling'),
          description: t('debug.suggestion.resourcePoolingDesc'),
          impact: 'medium',
          category: 'memory',
          steps: [
            t('debug.suggestion.resourcePoolingStep1'),
            t('debug.suggestion.resourcePoolingStep2'),
          ],
          benefits: [
            t('debug.suggestion.resourcePoolingBenefit1'),
            t('debug.suggestion.resourcePoolingBenefit2'),
          ],
          automated: false,
          applied: false},
        {
          id: 'shader-optimization',
          title: t('debug.suggestion.shaderOptimization'),
          description: t('debug.suggestion.shaderOptimizationDesc'),
          impact: 'low',
          category: 'other',
          steps: [
            t('debug.suggestion.shaderOptimizationStep1'),
            t('debug.suggestion.shaderOptimizationStep2'),
          ],
          benefits: [
            t('debug.suggestion.shaderOptimizationBenefit1'),
            t('debug.suggestion.shaderOptimizationBenefit2'),
          ],
          automated: false,
          applied: false},
      ];
      
      // 更新建议列表
      setSuggestions(formattedSuggestions);
    } catch (error) {
      console.error('加载优化建议失败:', error);
      message.error(`加载优化建议失败: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // 应用优化建议
  const applySuggestion = async (suggestion: OptimizationSuggestion) => {
    confirm({
      title: t('debug.suggestion.confirmApply'),
      icon: <QuestionCircleOutlined />,
      content: t('debug.suggestion.confirmApplyContent', { title: suggestion.title }),
      onOk: async () => {
        setApplyingId(suggestion.id);
        
        try {
          // 获取引擎
          const engine = EngineService.getInstance().getEngine();
          if (!engine) {
            throw new Error('引擎未初始化');
          }
          
          // 获取性能优化服务
          const optimizationService = performanceService.current;
          
          // 应用优化
          let result;
          switch (suggestion.id) {
            case 'texture-compression':
              result = await optimizationService.optimizeTextures();
              break;
            case 'texture-atlas':
              result = await optimizationService.createTextureAtlases();
              break;
            case 'geometry-lod':
              result = await optimizationService.generateLODs();
              break;
            case 'geometry-instancing':
              result = await optimizationService.enableInstancing();
              break;
            case 'memory-cleanup':
              result = await optimizationService.cleanupMemory();
              break;
            case 'lazy-loading':
              result = await optimizationService.enableLazyLoading();
              break;
            default:
              throw new Error('不支持的优化建议');
          }
          
          // 更新建议状态
          setSuggestions(prev => prev.map(s => 
            s.id === suggestion.id ? { ...s, applied: true } : s
          ));
          
          // 显示成功消息
          message.success(t('debug.suggestion.applySuccess', { title: suggestion.title }));
        } catch (error) {
          console.error('应用优化建议失败:', error);
          message.error(`应用优化建议失败: ${(error as Error).message}`);
        } finally {
          setApplyingId(null);
        }
      }});
  };
  
  // 获取过滤后的建议
  const getFilteredSuggestions = () => {
    return suggestions.filter(suggestion => {
      // 类别过滤
      if (filterCategory !== 'all' && suggestion.category !== filterCategory) {
        return false;
      }
      
      // 影响过滤
      if (filterImpact !== 'all' && suggestion.impact !== filterImpact) {
        return false;
      }
      
      return true;
    });
  };
  
  // 获取影响标签
  const getImpactTag = (impact: string) => {
    switch (impact) {
      case 'high':
        return <Tag color="red">{t('debug.suggestion.highImpact')}</Tag>;
      case 'medium':
        return <Tag color="orange">{t('debug.suggestion.mediumImpact')}</Tag>;
      case 'low':
        return <Tag color="blue">{t('debug.suggestion.lowImpact')}</Tag>;
      default:
        return <Tag>{t('debug.suggestion.unknownImpact')}</Tag>;
    }
  };
  
  // 获取类别图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'texture':
        return <FileImageOutlined />;
      case 'geometry':
        return <AppstoreOutlined />;
      case 'memory':
        return <ThunderboltOutlined />;
      case 'loading':
        return <ClockCircleOutlined />;
      default:
        return <FileOutlined />;
    }
  };
  
  // 渲染建议列表
  const renderSuggestionList = () => {
    const filteredSuggestions = getFilteredSuggestions();
    
    return (
      <List
        className="suggestion-list"
        itemLayout="vertical"
        dataSource={filteredSuggestions}
        loading={loading}
        renderItem={suggestion => (
          <List.Item
            key={suggestion.id}
            className={selectedSuggestion === suggestion.id ? 'selected-suggestion' : ''}
            onClick={() => setSelectedSuggestion(suggestion.id)}
            actions={[
              <Space>
                {suggestion.applied ? (
                  <Tag icon={<CheckCircleOutlined />} color="success">
                    {t('debug.suggestion.applied')}
                  </Tag>
                ) : (
                  <Button
                    type="primary"
                    icon={<RocketOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      applySuggestion(suggestion);
                    }}
                    loading={applyingId === suggestion.id}
                    disabled={!suggestion.automated}
                  >
                    {t('debug.suggestion.apply')}
                  </Button>
                )}
                {!suggestion.automated && (
                  <Tooltip title={t('debug.suggestion.manualOnly')}>
                    <InfoCircleOutlined />
                  </Tooltip>
                )}
              </Space>
            ]}
          >
            <List.Item.Meta
              avatar={getCategoryIcon(suggestion.category)}
              title={
                <Space>
                  <Text strong>{suggestion.title}</Text>
                  {getImpactTag(suggestion.impact)}
                </Space>
              }
              description={suggestion.description}
            />
            <Collapse ghost>
              <Panel header={t('debug.suggestion.implementationSteps')} key="steps">
                <Steps direction="vertical" size="small" current={-1}>
                  {suggestion.steps.map((step, index) => (
                    <Step key={index} title={step} />
                  ))}
                </Steps>
              </Panel>
              <Panel header={t('debug.suggestion.benefits')} key="benefits">
                <ul className="benefits-list">
                  {suggestion.benefits.map((benefit, index) => (
                    <li key={index}><Text>{benefit}</Text></li>
                  ))}
                </ul>
              </Panel>
            </Collapse>
          </List.Item>
        )}
      />
    );
  };
  
  return (
    <div className={`resource-optimization-suggestion-panel ${className || ''}`}>
      <div className="panel-header">
        <Space>
          <Title level={4}>{t('debug.suggestion.title')}</Title>
          <Tooltip title={t('debug.suggestion.refresh')}>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadSuggestions}
              loading={loading}
            />
          </Tooltip>
        </Space>
        <Space>
          <span>{t('debug.suggestion.filterCategory')}:</span>
          <Button.Group>
            <Button
              type={filterCategory === 'all' ? 'primary' : 'default'}
              onClick={() => setFilterCategory('all')}
            >
              {t('debug.suggestion.all')}
            </Button>
            <Button
              type={filterCategory === 'texture' ? 'primary' : 'default'}
              onClick={() => setFilterCategory('texture')}
              icon={<FileImageOutlined />}
            >
              {t('debug.suggestion.texture')}
            </Button>
            <Button
              type={filterCategory === 'geometry' ? 'primary' : 'default'}
              onClick={() => setFilterCategory('geometry')}
              icon={<AppstoreOutlined />}
            >
              {t('debug.suggestion.geometry')}
            </Button>
            <Button
              type={filterCategory === 'memory' ? 'primary' : 'default'}
              onClick={() => setFilterCategory('memory')}
              icon={<ThunderboltOutlined />}
            >
              {t('debug.suggestion.memory')}
            </Button>
            <Button
              type={filterCategory === 'loading' ? 'primary' : 'default'}
              onClick={() => setFilterCategory('loading')}
              icon={<ClockCircleOutlined />}
            >
              {t('debug.suggestion.loading')}
            </Button>
          </Button.Group>
        </Space>
      </div>
      
      <Alert
        message={t('debug.suggestion.alertTitle')}
        description={t('debug.suggestion.alertDescription')}
        type="info"
        showIcon
        closable
        style={{ marginBottom: 16 }}
      />
      
      {renderSuggestionList()}
    </div>
  );
};

export default ResourceOptimizationSuggestionPanel;
