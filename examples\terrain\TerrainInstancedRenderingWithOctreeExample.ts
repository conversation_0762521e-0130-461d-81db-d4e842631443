/**
 * 地形实例化渲染系统八叉树功能示例
 * 
 * 本示例展示了如何使用八叉树优化地形实例化渲染系统的性能
 */
import * as THREE from 'three';
import {
  Engine,
  World,
  Entity,
  Camera,
  Scene,
  Transform,
  Light,
  LightType,
  RenderSystem,
  CameraSystem,
  InputSystem
} from '../../engine/src/core';

import {
  TerrainComponent,
  TerrainSystem,
  TerrainInstancedRenderingSystem,
  TerrainInstanceData
} from '../../engine/src/terrain';

/**
 * 地形实例化渲染八叉树示例类
 */
export class TerrainInstancedRenderingWithOctreeExample {
  private engine: Engine;
  private world: World;
  private terrainSystem: TerrainInstancedRenderingSystem;
  private instanceIds: string[] = [];

  constructor() {
    // 创建引擎和世界
    this.engine = new Engine();
    this.world = new World(this.engine);

    // 初始化系统
    this.initializeSystems();
    
    // 创建场景
    this.createScene();
    
    // 创建地形和实例
    this.createTerrainWithInstances();
  }

  /**
   * 初始化系统
   */
  private initializeSystems(): void {
    // 添加核心系统
    this.world.addSystem(new RenderSystem());
    this.world.addSystem(new CameraSystem());
    this.world.addSystem(new InputSystem());
    this.world.addSystem(new TerrainSystem());

    // 创建地形实例化渲染系统，启用八叉树
    this.terrainSystem = new TerrainInstancedRenderingSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      maxBatchSize: 1000,
      useFrustumCulling: true,
      useOctree: true, // 启用八叉树
      useInstanceLOD: true,
      useInstanceShadow: true,
      useDebugVisualization: false
    });

    this.world.addSystem(this.terrainSystem);

    // 初始化世界
    this.world.initialize();
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建相机实体
    const cameraEntity = this.world.createEntity('主相机');
    const cameraTransform = cameraEntity.getComponent<Transform>('Transform')!;
    cameraTransform.setPosition(new THREE.Vector3(0, 50, 100));
    cameraTransform.lookAt(new THREE.Vector3(0, 0, 0));

    const camera = new Camera({
      type: 'perspective' as any,
      fov: 75,
      near: 0.1,
      far: 2000
    });
    cameraEntity.addComponent(camera);

    // 创建光源
    const lightEntity = this.world.createEntity('主光源');
    const lightTransform = lightEntity.getComponent<Transform>('Transform')!;
    lightTransform.setPosition(new THREE.Vector3(100, 100, 100));

    const light = new Light({
      type: LightType.DIRECTIONAL,
      color: new THREE.Color(0xffffff),
      intensity: 1.0
    });
    lightEntity.addComponent(light);
  }

  /**
   * 创建地形和实例
   */
  private createTerrainWithInstances(): void {
    // 创建地形实体
    const terrainEntity = this.world.createEntity('地形');
    const terrainComponent = new TerrainComponent({
      width: 1000,
      height: 1000,
      widthSegments: 100,
      heightSegments: 100
    });
    terrainEntity.addComponent(terrainComponent);

    // 将地形添加到实例化渲染系统
    this.terrainSystem.addTerrainEntity(terrainEntity, terrainComponent);

    // 创建实例几何体和材质
    const geometry = new THREE.BoxGeometry(2, 2, 2);
    const material = new THREE.MeshLambertMaterial({ color: 0x00ff00 });

    // 在地形上创建大量实例
    this.createInstances(geometry, material, 5000);

    // 输出八叉树统计信息
    this.logOctreeStats();
  }

  /**
   * 创建实例
   * @param geometry 几何体
   * @param material 材质
   * @param count 实例数量
   */
  private createInstances(geometry: THREE.BufferGeometry, material: THREE.Material, count: number): void {
    console.log(`开始创建 ${count} 个实例...`);

    for (let i = 0; i < count; i++) {
      // 随机位置
      const position = new THREE.Vector3(
        (Math.random() - 0.5) * 800,
        Math.random() * 20,
        (Math.random() - 0.5) * 800
      );

      // 随机旋转
      const rotation = new THREE.Euler(
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2
      );

      // 随机缩放
      const scale = new THREE.Vector3(
        0.5 + Math.random() * 1.5,
        0.5 + Math.random() * 1.5,
        0.5 + Math.random() * 1.5
      );

      // 随机颜色
      const color = new THREE.Color(Math.random(), Math.random(), Math.random());

      // 创建实例数据
      const instanceData: TerrainInstanceData = {
        position,
        rotation,
        scale,
        color,
        visible: true,
        userData: { id: i }
      };

      // 添加实例
      const instanceId = this.terrainSystem.addInstance(geometry, material, instanceData);
      this.instanceIds.push(instanceId);
    }

    console.log(`成功创建 ${count} 个实例`);
  }

  /**
   * 输出八叉树统计信息
   */
  private logOctreeStats(): void {
    const stats = this.terrainSystem.getOctreeStats();
    if (stats) {
      console.log('八叉树统计信息:', {
        总节点数: stats.totalNodes,
        总对象数: stats.totalObjects,
        最大深度: stats.maxDepth,
        平均每节点对象数: stats.averageObjectsPerNode.toFixed(2)
      });
    }
  }

  /**
   * 演示八叉树查询功能
   */
  public demonstrateOctreeQueries(): void {
    console.log('\n=== 八叉树查询演示 ===');

    // 在指定区域内查找实例
    const searchBox = new THREE.Box3(
      new THREE.Vector3(-100, -10, -100),
      new THREE.Vector3(100, 30, 100)
    );

    const instancesInBox = this.terrainSystem.getInstancesInBox(searchBox);
    console.log(`在指定包围盒内找到 ${instancesInBox.length} 个实例`);

    // 在指定球体内查找实例
    const searchSphere = new THREE.Sphere(new THREE.Vector3(0, 10, 0), 50);
    const instancesInSphere = this.terrainSystem.getInstancesInSphere(searchSphere);
    console.log(`在指定球体内找到 ${instancesInSphere.length} 个实例`);

    // 重建八叉树演示
    console.log('\n重建八叉树...');
    this.terrainSystem.rebuildOctree();
    this.logOctreeStats();
  }

  /**
   * 演示动态更新实例
   */
  public demonstrateDynamicUpdates(): void {
    console.log('\n=== 动态更新演示 ===');

    // 随机选择一些实例进行更新
    const updateCount = Math.min(100, this.instanceIds.length);
    const indicesToUpdate = [];

    for (let i = 0; i < updateCount; i++) {
      indicesToUpdate.push(Math.floor(Math.random() * this.instanceIds.length));
    }

    console.log(`更新 ${updateCount} 个实例的位置...`);

    for (const index of indicesToUpdate) {
      const instanceId = this.instanceIds[index];
      
      // 新的随机位置
      const newPosition = new THREE.Vector3(
        (Math.random() - 0.5) * 800,
        Math.random() * 20,
        (Math.random() - 0.5) * 800
      );

      // 更新实例（这会自动更新八叉树）
      // 注意：这里需要知道实例所在的组ID，实际使用中应该保存这个信息
      // 为了演示，我们假设所有实例都在同一个组中
      // this.terrainSystem.updateInstance(groupId, instanceId, { position: newPosition });
    }

    console.log('实例更新完成');
    this.logOctreeStats();
  }

  /**
   * 启动示例
   */
  public start(): void {
    console.log('地形实例化渲染系统八叉树示例启动');
    
    // 启动引擎
    this.engine.start();

    // 演示八叉树功能
    setTimeout(() => {
      this.demonstrateOctreeQueries();
    }, 2000);

    setTimeout(() => {
      this.demonstrateDynamicUpdates();
    }, 4000);
  }

  /**
   * 停止示例
   */
  public stop(): void {
    this.engine.stop();
    console.log('示例已停止');
  }
}

// 创建并启动示例
const example = new TerrainInstancedRenderingWithOctreeExample();
example.start();

// 导出示例类
export default TerrainInstancedRenderingWithOctreeExample;
