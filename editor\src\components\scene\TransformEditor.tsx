/**
 * 变换组件编辑器
 */
import React from 'react';
import { Card, InputNumber, Space, Typography } from 'antd';

const { Text } = Typography;

/**
 * 变换编辑器属性
 */
interface TransformEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * 变换编辑器组件
 */
const TransformEditor: React.FC<TransformEditorProps> = ({ data, onChange }) => {
  const [form] = Form.useForm();

  // 默认数据
  const defaultData = {
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    scale: { x: 1, y: 1, z: 1 },
    ...data
  };

  // 处理数据变化
  const handleChange = (field: string, value: any) => {
    const newData = { ...defaultData, [field]: value };
    if (onChange) {
      onChange(newData);
    }
  };

  return (
    <Card title="变换" size="small">
      <Form
        form={form}
        layout="vertical"
        initialValues={defaultData}
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([field, value]) => {
            handleChange(field, value);
          });
        }}
      >
        <Form.Item label="位置">
          <Space>
            <div>
              <Text>X</Text>
              <Form.Item name={['position', 'x']} noStyle>
                <InputNumber size="small" step={0.1} />
              </Form.Item>
            </div>
            <div>
              <Text>Y</Text>
              <Form.Item name={['position', 'y']} noStyle>
                <InputNumber size="small" step={0.1} />
              </Form.Item>
            </div>
            <div>
              <Text>Z</Text>
              <Form.Item name={['position', 'z']} noStyle>
                <InputNumber size="small" step={0.1} />
              </Form.Item>
            </div>
          </Space>
        </Form.Item>

        <Form.Item label="旋转">
          <Space>
            <div>
              <Text>X</Text>
              <Form.Item name={['rotation', 'x']} noStyle>
                <InputNumber size="small" step={1} min={-360} max={360} />
              </Form.Item>
            </div>
            <div>
              <Text>Y</Text>
              <Form.Item name={['rotation', 'y']} noStyle>
                <InputNumber size="small" step={1} min={-360} max={360} />
              </Form.Item>
            </div>
            <div>
              <Text>Z</Text>
              <Form.Item name={['rotation', 'z']} noStyle>
                <InputNumber size="small" step={1} min={-360} max={360} />
              </Form.Item>
            </div>
          </Space>
        </Form.Item>

        <Form.Item label="缩放">
          <Space>
            <div>
              <Text>X</Text>
              <Form.Item name={['scale', 'x']} noStyle>
                <InputNumber size="small" step={0.1} min={0.01} />
              </Form.Item>
            </div>
            <div>
              <Text>Y</Text>
              <Form.Item name={['scale', 'y']} noStyle>
                <InputNumber size="small" step={0.1} min={0.01} />
              </Form.Item>
            </div>
            <div>
              <Text>Z</Text>
              <Form.Item name={['scale', 'z']} noStyle>
                <InputNumber size="small" step={0.1} min={0.01} />
              </Form.Item>
            </div>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default TransformEditor;
