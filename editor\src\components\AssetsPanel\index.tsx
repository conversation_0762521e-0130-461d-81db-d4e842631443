/**
 * 资产面板组件
 */
import React, { useState, useEffect } from 'react';
import { Tabs, Button, Upload, Card, List, Tag, Dropdown, message, Modal } from 'antd';
import {
  UploadOutlined,
  DeleteOutlined,
  EditOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  MoreOutlined,
  SearchOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { fetchAssets, removeAsset } from '../../store/assets/assetsSlice';
import './AssetsPanel.less';

// 定义资产类型接口
// 这里我们使用 any 类型来避免类型冲突
// 在实际项目中，应该使用正确的类型定义
type Asset = {
  id: string;
  name: string;
  type: string;
  tags: string[];
  thumbnail: string;
  thumbnailUrl?: string; // 添加可选的 thumbnailUrl 属性
  url: string;
  [key: string]: any; // 允许任何其他属性
}

const { TabPane } = Tabs;
const { Search } = Input;
const { Meta } = Card;

const AssetsPanel: React.FC = () => {
  const { t } = useTranslation();
  // 使用 any 类型来避免 dispatch 类型错误
  // 在实际项目中，应该使用正确的 AppDispatch 类型
  const dispatch = useDispatch() as any;
  // 从 Redux 状态中获取资产数据
  // 根据实际的 Redux 状态结构调整选择器
  // 使用类型断言将 Redux 状态中的资产转换为我们定义的 Asset 类型
  const assets = useSelector((state: RootState) =>
    // 使用双重类型断言来避免类型检查错误
    (state.asset.assets || []) as unknown as Asset[]
  );
  const loading = useSelector((state: RootState) =>
    // 假设 state.asset.isLoading 是加载状态，如果结构不同，请调整
    state.asset.isLoading || false
  );
  const [searchValue, setSearchValue] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('all');
  const [isUploadModalVisible, setIsUploadModalVisible] = useState<boolean>(false);

  // 加载资产
  useEffect(() => {
    dispatch(fetchAssets());
  }, [dispatch]);

  // 过滤资产
  const filterAssets = () => {
    let filtered = [...assets];

    // 按类型过滤
    if (activeTab !== 'all') {
      filtered = filtered.filter(asset => asset.type === activeTab);
    }

    // 按搜索词过滤
    if (searchValue) {
      filtered = filtered.filter(asset =>
        asset.name.toLowerCase().includes(searchValue.toLowerCase()) ||
        asset.tags.some((tag: string) => tag.toLowerCase().includes(searchValue.toLowerCase()))
      );
    }

    return filtered;
  };

  // 处理上传
  const handleUpload = (info: any) => {
    if (info.file.status === 'done') {
      message.success(`${info.file.name} 上传成功`);
      dispatch(fetchAssets());
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
    }
  };

  // 处理删除
  const handleDelete = (assetId: string) => {
    Modal.confirm({
      title: t('editor.assets.confirmDelete'),
      content: t('editor.assets.confirmDeleteContent'),
      okText: t('editor.assets.delete'),
      okType: 'danger',
      cancelText: t('editor.assets.cancel'),
      onOk: () => {
        dispatch(removeAsset(assetId));
        message.success(t('editor.assets.deleteSuccess'));
      }});
  };

  // 渲染资产列表
  const renderAssetList = () => {
    const filteredAssets = filterAssets();

    return (
      <List
        grid={{ gutter: 16, column: 2 }}
        dataSource={filteredAssets}
        loading={loading}
        renderItem={asset => (
          <List.Item>
            <Card
              hoverable
              cover={
                <div className="asset-thumbnail">
                  <img alt={asset.name} src={asset.thumbnailUrl || asset.thumbnail || '/placeholder.png'} />
                </div>
              }
              actions={[
                <Button
                  type="text"
                  icon={<InfoCircleOutlined />}
                  onClick={() => message.info(`查看资产: ${asset.name}`)}
                />,
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'edit',
                        icon: <EditOutlined />,
                        label: t('editor.assets.edit'),
                        onClick: () => message.info(`编辑资产: ${asset.name}`)},
                      {
                        key: 'download',
                        icon: <DownloadOutlined />,
                        label: t('editor.assets.download'),
                        onClick: () => message.info(`下载资产: ${asset.name}`)},
                      {
                        key: 'delete',
                        icon: <DeleteOutlined />,
                        label: t('editor.assets.delete'),
                        danger: true,
                        onClick: () => handleDelete(asset.id)},
                    ]
                  }}
                  trigger={['click']}
                >
                  <Button type="text" icon={<MoreOutlined />} />
                </Dropdown>,
              ]}
            >
              <Meta
                title={asset.name}
                description={
                  <div className="asset-tags">
                    {asset.tags.map((tag: string) => (
                      <Tag key={tag}>{tag}</Tag>
                    ))}
                  </div>
                }
              />
            </Card>
          </List.Item>
        )}
      />
    );
  };

  return (
    <div className="assets-panel">
      <div className="panel-header">
        <h3>{t('editor.panels.assets')}</h3>
        <Button
          type="primary"
          size="small"
          icon={<UploadOutlined />}
          onClick={() => setIsUploadModalVisible(true)}
        >
          {t('editor.assets.upload')}
        </Button>
      </div>

      <Search
        placeholder={String(t('editor.assets.search'))}
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        className="search-input"
        prefix={<SearchOutlined />}
      />

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={t('editor.assets.all')} key="all">
          {renderAssetList()}
        </TabPane>
        <TabPane tab={t('editor.assets.models')} key="model">
          {renderAssetList()}
        </TabPane>
        <TabPane tab={t('editor.assets.textures')} key="texture">
          {renderAssetList()}
        </TabPane>
        <TabPane tab={t('editor.assets.audio')} key="audio">
          {renderAssetList()}
        </TabPane>
      </Tabs>

      {/* 上传模态框 */}
      <Modal
        title={t('editor.assets.upload')}
        open={isUploadModalVisible}
        onCancel={() => setIsUploadModalVisible(false)}
        footer={null}
      >
        <Upload.Dragger
          name="file"
          action="/api/assets/upload"
          onChange={handleUpload}
          multiple
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">{t('editor.assets.uploadText')}</p>
          <p className="ant-upload-hint">{t('editor.assets.uploadHint')}</p>
        </Upload.Dragger>
      </Modal>
    </div>
  );
};

export default AssetsPanel;
