#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 修复剩余TypeScript错误的脚本
 */

// 修复规则
const fixes = [
  // 修复错误的super调用
  {
    name: '修复super调用',
    pattern: /\(super as any\)\.dispose\(\)/g,
    replacement: 'super.dispose()'
  },
  
  // 修复错误的导入语句
  {
    name: '修复重复type导入',
    pattern: /import \{ type type ([^}]+) \}/g,
    replacement: 'import type { $1 }'
  },
  
  // 修复错误的属性访问
  {
    name: '修复错误的属性访问1',
    pattern: /(\w+)\.\((\w+) as any\)\.(\w+)\(/g,
    replacement: '$1.$2.$3('
  },
  
  // 修复错误的属性访问2
  {
    name: '修复错误的属性访问2',
    pattern: /(\w+)\.\((\w+) as any\)\.(\w+)/g,
    replacement: '$1.$2.$3'
  },
  
  // 修复错误的方法调用
  {
    name: '修复getPosition调用',
    pattern: /\((\w+) as any\)\.getPosition\(\)/g,
    replacement: '$1.position'
  },
  
  // 修复错误的setPosition调用
  {
    name: '修复setPosition调用',
    pattern: /\((\w+) as any\)\.setPosition\(([^)]+)\)/g,
    replacement: '$1.position.set($2)'
  },
  
  // 修复错误的setScale调用
  {
    name: '修复setScale调用',
    pattern: /\((\w+) as any\)\.setScale\(([^)]+)\)/g,
    replacement: '$1.scale.set($2)'
  },
  
  // 修复错误的语法
  {
    name: '修复this.()语法错误',
    pattern: /this\.\((\w+) as any\)/g,
    replacement: 'this.$1'
  },
  
  // 修复错误的属性访问语法
  {
    name: '修复object.()语法错误',
    pattern: /(\w+)\.\((\w+) as any\)/g,
    replacement: '$1.$2'
  }
];

/**
 * 递归获取所有TypeScript文件
 * @param {string} dir 目录路径
 * @returns {string[]} 文件路径数组
 */
function getAllTsFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 跳过node_modules和其他不需要的目录
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          traverse(fullPath);
        }
      } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

/**
 * 修复文件
 * @param {string} filePath 文件路径
 * @returns {boolean} 是否修复了文件
 */
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const appliedFixes = [];

    for (const fix of fixes) {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
        appliedFixes.push(fix.name);
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`已修复: ${path.relative(process.cwd(), filePath)}`);
      if (appliedFixes.length > 0) {
        console.log(`  应用的修复: ${appliedFixes.join(', ')}`);
      }
      return true;
    }

    return false;
  } catch (error) {
    console.error(`修复文件失败: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src目录不存在:', srcDir);
    process.exit(1);
  }
  
  console.log('开始修复剩余的TypeScript错误...\n');
  
  const tsFiles = getAllTsFiles(srcDir);
  console.log(`找到 ${tsFiles.length} 个TypeScript文件`);
  
  let fixedCount = 0;
  
  for (const file of tsFiles) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n修复完成！共修复了 ${fixedCount} 个文件`);
  console.log('\n建议运行以下命令检查编译状态：');
  console.log('npx tsc --noEmit');
}

if (require.main === module) {
  main();
}

module.exports = { fixFile, getAllTsFiles };
