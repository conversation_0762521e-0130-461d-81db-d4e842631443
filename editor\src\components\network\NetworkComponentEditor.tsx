/**
 * 网络组件编辑器
 */
import React from 'react';
import GenericEditor from '../common/GenericEditor';

/**
 * 网络组件编辑器属性
 */
interface NetworkComponentEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * 网络组件编辑器组件
 */
const NetworkComponentEditor: React.FC<NetworkComponentEditorProps> = ({ data, onChange }) => {
  return <GenericEditor title="网络组件" data={data} onChange={onChange} />;
};

export default NetworkComponentEditor;
