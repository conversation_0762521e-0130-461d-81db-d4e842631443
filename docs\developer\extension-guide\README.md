# DL（Digital Learning）引擎编辑器扩展指南

## 概述

DL（Digital Learning）引擎编辑器提供了丰富的扩展机制，允许开发者扩展编辑器的功能。本指南将介绍如何开发编辑器扩展，包括面板扩展、工具扩展、组件编辑器扩展、资产类型扩展和命令扩展。

## 目录

- [扩展类型](#扩展类型)
- [开发环境设置](#开发环境设置)
- [面板扩展](#面板扩展)
- [工具扩展](#工具扩展)
- [组件编辑器扩展](#组件编辑器扩展)
- [资产类型扩展](#资产类型扩展)
- [命令扩展](#命令扩展)
- [最佳实践](#最佳实践)

## 扩展类型

DL（Digital Learning）引擎编辑器支持以下类型的扩展：

1. **面板扩展**：添加新的面板到编辑器界面。
2. **工具扩展**：添加新的工具到编辑器工具栏。
3. **组件编辑器扩展**：为自定义组件添加编辑器界面。
4. **资产类型扩展**：添加对新资产类型的支持。
5. **命令扩展**：添加新的命令到编辑器命令系统。

## 开发环境设置

在开始开发扩展之前，需要设置开发环境：

1. 克隆DL（Digital Learning）引擎编辑器仓库：

```bash
git clone https://github.com/your-username/dl-engine.git
cd dl-engine/newsystem/editor
```

2. 安装依赖：

```bash
npm install
```

3. 启动开发服务器：

```bash
npm run dev
```

## 面板扩展

面板扩展允许开发者添加新的面板到编辑器界面。

### 创建面板组件

首先，创建一个面板组件：

```typescript
// src/components/panels/CustomPanel.tsx
import React, { useState, useEffect } from 'react';
import { Card, Button, Input, List } from 'antd';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';

const CustomPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [items, setItems] = useState<string[]>([]);
  const [newItem, setNewItem] = useState<string>('');

  // 添加项目
  const handleAddItem = () => {
    if (newItem.trim()) {
      setItems([...items, newItem.trim()]);
      setNewItem('');
    }
  };

  // 删除项目
  const handleDeleteItem = (index: number) => {
    const newItems = [...items];
    newItems.splice(index, 1);
    setItems(newItems);
  };

  return (
    <Card title={t('panels.custom.title')} className="custom-panel">
      <div className="custom-panel-content">
        <div className="custom-panel-input">
          <Input
            value={newItem}
            onChange={(e) => setNewItem(e.target.value)}
            placeholder={t('panels.custom.inputPlaceholder')}
            onPressEnter={handleAddItem}
          />
          <Button type="primary" onClick={handleAddItem}>
            {t('panels.custom.addButton')}
          </Button>
        </div>
        <List
          className="custom-panel-list"
          bordered
          dataSource={items}
          renderItem={(item, index) => (
            <List.Item
              actions={[
                <Button
                  key="delete"
                  danger
                  onClick={() => handleDeleteItem(index)}
                >
                  {t('panels.custom.deleteButton')}
                </Button>
              ]}
            >
              {item}
            </List.Item>
          )}
        />
      </div>
    </Card>
  );
};

export default CustomPanel;
```

### 注册面板

然后，在面板注册器中注册面板：

```typescript
// src/components/panels/PanelRegistry.tsx
import { AppstoreOutlined } from '@ant-design/icons';
import CustomPanel from './CustomPanel';

// 自定义面板标签
export const CustomPanelTab: TabData = {
  id: PanelType.CUSTOM,
  title: '自定义面板',
  content: <CustomPanel />,
  icon: <AppstoreOutlined />,
};

// 在面板组件映射中添加自定义面板
const panelComponents = {
  // 其他面板...
  [PanelType.CUSTOM]: CustomPanel,
};
```

### 添加面板类型

在UI状态切片中添加面板类型：

```typescript
// src/store/ui/uiSlice.ts
export enum PanelType {
  // 其他面板类型...
  CUSTOM = 'custom',
}
```

### 添加面板到默认布局

在布局服务中添加面板到默认布局：

```typescript
// src/services/LayoutService.ts
import { PanelType } from '../store/ui/uiSlice';
import { CustomPanelTab } from '../components/panels/PanelRegistry';

export const getDefaultLayout = () => {
  return {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          mode: 'vertical',
          size: 200,
          children: [
            {
              tabs: [
                // 其他面板...
                CustomPanelTab,
              ],
            },
          ],
        },
        // 其他布局...
      ],
    },
  };
};
```

## 工具扩展

工具扩展允许开发者添加新的工具到编辑器工具栏。

### 创建工具组件

首先，创建一个工具组件：

```typescript
// src/components/tools/CustomTool.tsx
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { EngineService } from '../../services/EngineService';

const CustomTool: React.FC = () => {
  const dispatch = useDispatch();
  const engineService = EngineService.getInstance();
  const selectedEntities = useSelector((state: RootState) => state.entity.selectedEntities);

  // 工具激活时的处理
  useEffect(() => {
    console.log('自定义工具已激活');
    
    // 在这里添加工具激活时的逻辑
    
    return () => {
      console.log('自定义工具已停用');
      
      // 在这里添加工具停用时的清理逻辑
    };
  }, []);

  // 处理鼠标事件
  const handleMouseDown = (event: MouseEvent) => {
    // 在这里添加鼠标按下时的逻辑
  };

  const handleMouseMove = (event: MouseEvent) => {
    // 在这里添加鼠标移动时的逻辑
  };

  const handleMouseUp = (event: MouseEvent) => {
    // 在这里添加鼠标释放时的逻辑
  };

  // 添加鼠标事件监听
  useEffect(() => {
    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
    
    return () => {
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [selectedEntities]);

  return null; // 工具组件通常不渲染任何UI
};

export default CustomTool;
```

### 注册工具

然后，在工具注册器中注册工具：

```typescript
// src/components/tools/ToolRegistry.tsx
import { ToolOutlined } from '@ant-design/icons';
import CustomTool from './CustomTool';

// 在工具组件映射中添加自定义工具
const toolComponents = {
  // 其他工具...
  [ToolType.CUSTOM]: CustomTool,
};

// 工具配置
export const toolConfigs = {
  // 其他工具配置...
  [ToolType.CUSTOM]: {
    name: '自定义工具',
    icon: <ToolOutlined />,
    shortcut: 'C',
  },
};
```

### 添加工具类型

在工具状态切片中添加工具类型：

```typescript
// src/store/tool/toolSlice.ts
export enum ToolType {
  // 其他工具类型...
  CUSTOM = 'custom',
}
```

## 组件编辑器扩展

组件编辑器扩展允许开发者为自定义组件添加编辑器界面。

### 创建组件

首先，创建一个自定义组件：

```typescript
// src/types/components/CustomComponent.ts
import { Component, ComponentType } from '../component';

export interface CustomComponentData {
  value: number;
  enabled: boolean;
  name: string;
}

export class CustomComponent extends Component {
  public static readonly type: ComponentType = 'CustomComponent';
  public data: CustomComponentData;

  constructor(data: Partial<CustomComponentData> = {}) {
    super();
    this.data = {
      value: data.value ?? 0,
      enabled: data.enabled ?? true,
      name: data.name ?? '自定义组件',
    };
  }

  public clone(): CustomComponent {
    return new CustomComponent(this.data);
  }

  public toJSON(): any {
    return {
      type: CustomComponent.type,
      data: this.data,
    };
  }

  public static fromJSON(json: any): CustomComponent {
    return new CustomComponent(json.data);
  }
}
```

### 创建组件编辑器

然后，创建组件编辑器：

```typescript
// src/components/editors/CustomComponentEditor.tsx
import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Switch, Button } from 'antd';
import { useTranslation } from 'react-i18next';
import { CustomComponent, CustomComponentData } from '../../types/components/CustomComponent';

interface CustomComponentEditorProps {
  component: CustomComponent;
  onChange: (component: CustomComponent) => void;
}

const CustomComponentEditor: React.FC<CustomComponentEditorProps> = ({ component, onChange }) => {
  const { t } = useTranslation();
  const [data, setData] = useState<CustomComponentData>(component.data);

  // 数据变化时更新组件
  useEffect(() => {
    const newComponent = component.clone();
    newComponent.data = data;
    onChange(newComponent);
  }, [data]);

  // 处理值变化
  const handleValueChange = (value: number) => {
    setData({ ...data, value });
  };

  // 处理启用状态变化
  const handleEnabledChange = (enabled: boolean) => {
    setData({ ...data, enabled });
  };

  // 处理名称变化
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setData({ ...data, name: e.target.value });
  };

  return (
    <Form layout="vertical">
      <Form.Item label={t('components.custom.name')}>
        <Input value={data.name} onChange={handleNameChange} />
      </Form.Item>
      <Form.Item label={t('components.custom.value')}>
        <InputNumber value={data.value} onChange={handleValueChange} />
      </Form.Item>
      <Form.Item label={t('components.custom.enabled')}>
        <Switch checked={data.enabled} onChange={handleEnabledChange} />
      </Form.Item>
    </Form>
  );
};

export default CustomComponentEditor;
```

### 注册组件编辑器

最后，在组件编辑器注册器中注册组件编辑器：

```typescript
// src/components/editors/ComponentEditorRegistry.tsx
import CustomComponentEditor from './CustomComponentEditor';
import { CustomComponent } from '../../types/components/CustomComponent';

// 在组件编辑器映射中添加自定义组件编辑器
const componentEditors = {
  // 其他组件编辑器...
  [CustomComponent.type]: CustomComponentEditor,
};

// 获取组件编辑器
export const getComponentEditor = (componentType: string): React.ComponentType<any> => {
  return componentEditors[componentType] || (() => <div>未知组件类型</div>);
};

// 注册组件编辑器
export const registerComponentEditor = (componentType: string, editor: React.ComponentType<any>): void => {
  componentEditors[componentType] = editor;
};
```

## 资产类型扩展

资产类型扩展允许开发者添加对新资产类型的支持。

### 创建资产类型

首先，创建一个资产类型：

```typescript
// src/types/assets/CustomAsset.ts
import { Asset, AssetType } from '../asset';

export interface CustomAssetData {
  content: string;
  metadata: Record<string, any>;
}

export class CustomAsset extends Asset {
  public static readonly type: AssetType = 'custom-asset';
  public data: CustomAssetData;

  constructor(id: string, name: string, data: Partial<CustomAssetData> = {}) {
    super(id, name, CustomAsset.type);
    this.data = {
      content: data.content ?? '',
      metadata: data.metadata ?? {},
    };
  }

  public clone(): CustomAsset {
    return new CustomAsset(this.id, this.name, this.data);
  }

  public toJSON(): any {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      data: this.data,
    };
  }

  public static fromJSON(json: any): CustomAsset {
    return new CustomAsset(json.id, json.name, json.data);
  }
}
```

### 创建资产导入器

然后，创建资产导入器：

```typescript
// src/components/assets/importers/CustomAssetImporter.tsx
import React, { useState } from 'react';
import { Modal, Form, Input, Button, Upload, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { CustomAsset } from '../../../types/assets/CustomAsset';
import { AssetService } from '../../../services/AssetService';

interface CustomAssetImporterProps {
  visible: boolean;
  onClose: () => void;
  onImport: (asset: CustomAsset) => void;
}

const CustomAssetImporter: React.FC<CustomAssetImporterProps> = ({ visible, onClose, onImport }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [fileContent, setFileContent] = useState<string>('');
  const assetService = AssetService.getInstance();

  // 处理文件上传
  const handleFileUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      setFileContent(e.target?.result as string);
    };
    reader.readAsText(file);
    return false; // 阻止自动上传
  };

  // 处理导入
  const handleImport = async () => {
    try {
      const values = await form.validateFields();
      const asset = new CustomAsset('', values.name, {
        content: fileContent,
        metadata: {
          description: values.description,
        },
      });
      
      // 保存资产
      const savedAsset = await assetService.saveAsset(asset);
      
      // 调用导入回调
      onImport(savedAsset);
      
      // 关闭对话框
      onClose();
      
      // 显示成功消息
      message.success(t('assets.import.success'));
    } catch (error) {
      console.error('导入失败:', error);
      message.error(t('assets.import.error'));
    }
  };

  return (
    <Modal
      title={t('assets.import.custom.title')}
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          {t('common.cancel')}
        </Button>,
        <Button key="import" type="primary" onClick={handleImport}>
          {t('assets.import.button')}
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="name"
          label={t('assets.name')}
          rules={[{ required: true, message: t('assets.name.required') }]}
        >
          <Input />
        </Form.Item>
        <Form.Item name="description" label={t('assets.description')}>
          <Input.TextArea rows={4} />
        </Form.Item>
        <Form.Item label={t('assets.file')}>
          <Upload
            beforeUpload={handleFileUpload}
            accept=".txt,.json,.xml,.yaml,.yml"
            maxCount={1}
          >
            <Button icon={<UploadOutlined />}>{t('assets.upload')}</Button>
          </Upload>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CustomAssetImporter;
```

### 创建资产查看器

接下来，创建资产查看器：

```typescript
// src/components/assets/viewers/CustomAssetViewer.tsx
import React from 'react';
import { Card, Tabs, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { CustomAsset } from '../../../types/assets/CustomAsset';

const { TabPane } = Tabs;
const { Title, Paragraph, Text } = Typography;

interface CustomAssetViewerProps {
  asset: CustomAsset;
}

const CustomAssetViewer: React.FC<CustomAssetViewerProps> = ({ asset }) => {
  const { t } = useTranslation();

  return (
    <Card title={asset.name} className="asset-viewer">
      <Tabs defaultActiveKey="content">
        <TabPane tab={t('assets.viewer.content')} key="content">
          <pre className="asset-content">{asset.data.content}</pre>
        </TabPane>
        <TabPane tab={t('assets.viewer.metadata')} key="metadata">
          <Typography>
            <Title level={5}>{t('assets.viewer.metadata')}</Title>
            {Object.entries(asset.data.metadata).map(([key, value]) => (
              <Paragraph key={key}>
                <Text strong>{key}:</Text> {JSON.stringify(value)}
              </Paragraph>
            ))}
          </Typography>
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default CustomAssetViewer;
```

### 注册资产类型

最后，在资产类型注册器中注册资产类型：

```typescript
// src/components/assets/AssetTypeRegistry.tsx
import { FileTextOutlined } from '@ant-design/icons';
import { CustomAsset } from '../../types/assets/CustomAsset';
import CustomAssetImporter from './importers/CustomAssetImporter';
import CustomAssetViewer from './viewers/CustomAssetViewer';

// 在资产类型配置中添加自定义资产类型
export const assetTypeConfigs = {
  // 其他资产类型...
  [CustomAsset.type]: {
    name: '自定义资产',
    icon: <FileTextOutlined />,
    extensions: ['.txt', '.json', '.xml', '.yaml', '.yml'],
    importer: CustomAssetImporter,
    viewer: CustomAssetViewer,
  },
};

// 获取资产类型配置
export const getAssetTypeConfig = (type: string) => {
  return assetTypeConfigs[type] || null;
};

// 注册资产类型
export const registerAssetType = (type: string, config: any) => {
  assetTypeConfigs[type] = config;
};
```

## 命令扩展

命令扩展允许开发者添加新的命令到编辑器命令系统。

### 创建命令

首先，创建一个命令：

```typescript
// src/commands/CustomCommand.ts
import { Command } from './Command';
import { EngineService } from '../services/EngineService';
import { NotificationService } from '../services/NotificationService';

export class CustomCommand implements Command {
  public readonly id = 'custom-command';
  public readonly name = '自定义命令';
  public readonly shortcut = 'Ctrl+Shift+C';
  
  private engineService = EngineService.getInstance();
  private notificationService = NotificationService.getInstance();

  public execute(): void {
    // 在这里添加命令执行逻辑
    console.log('执行自定义命令');
    
    // 示例：获取选中的实体
    const selectedEntities = this.engineService.getSelectedEntities();
    
    // 示例：显示通知
    this.notificationService.info('自定义命令', `已选中 ${selectedEntities.length} 个实体`);
  }

  public canExecute(): boolean {
    // 在这里添加命令是否可执行的逻辑
    return true;
  }
}
```

### 注册命令

然后，在命令注册器中注册命令：

```typescript
// src/commands/CommandRegistry.ts
import { CustomCommand } from './CustomCommand';

// 创建命令实例
const customCommand = new CustomCommand();

// 在命令映射中添加自定义命令
const commands = {
  // 其他命令...
  [customCommand.id]: customCommand,
};

// 获取命令
export const getCommand = (id: string): Command | null => {
  return commands[id] || null;
};

// 注册命令
export const registerCommand = (command: Command): void => {
  commands[command.id] = command;
};

// 执行命令
export const executeCommand = (id: string): void => {
  const command = getCommand(id);
  if (command && command.canExecute()) {
    command.execute();
  }
};
```

### 添加命令到菜单

最后，将命令添加到菜单：

```typescript
// src/components/layout/Header.tsx
import { CustomCommand } from '../../commands/CustomCommand';
import { executeCommand } from '../../commands/CommandRegistry';

// 菜单项
const menuItems = [
  // 其他菜单项...
  {
    key: 'tools',
    label: t('menu.tools'),
    children: [
      // 其他工具菜单项...
      {
        key: 'custom-command',
        label: t('menu.tools.customCommand'),
        onClick: () => executeCommand('custom-command'),
      },
    ],
  },
];
```

## 最佳实践

在开发编辑器扩展时，请遵循以下最佳实践：

1. **遵循命名约定**：使用一致的命名约定，如组件使用PascalCase，变量和函数使用camelCase。
2. **编写文档**：为扩展提供详细的文档，包括用途、用法和示例。
3. **添加国际化支持**：使用i18next为扩展添加国际化支持，默认使用中文。
4. **编写测试**：为扩展编写单元测试和集成测试，确保其正常工作。
5. **优化性能**：注意扩展的性能影响，避免不必要的计算和渲染。
6. **错误处理**：妥善处理错误，提供有用的错误消息。
7. **遵循编辑器风格**：遵循编辑器的设计风格和交互模式，保持一致的用户体验。
