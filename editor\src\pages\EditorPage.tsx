/**
 * 编辑器页面
 */
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Spin, message} from 'antd';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../store';
import { fetchProjectById } from '../store/project/projectSlice';
import { loadScene } from '../store/editor/editorSlice';
import { EditorLayout } from '../components/layout/EditorLayout';

export const EditorPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { projectId, sceneId } = useParams<{ projectId: string; sceneId: string }>();
  
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  const { currentProject, currentScene, isLoading: projectLoading, error: projectError } = useAppSelector((state) => state.project);
  const { isLoading: editorLoading, error: editorError } = useAppSelector((state) => state.editor);
  
  const [isInitialized, setIsInitialized] = useState(false);
  
  // 检查认证状态
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: `/editor/${projectId}/${sceneId}` } });
    }
  }, [isAuthenticated, navigate, projectId, sceneId]);
  
  // 加载项目和场景
  useEffect(() => {
    if (!projectId || !sceneId) {
      navigate('/projects');
      return;
    }
    
    // 加载项目
    dispatch(fetchProjectById(projectId))
      .unwrap()
      .then(() => {
        // 加载场景
        return dispatch(loadScene({ projectId, sceneId })).unwrap();
      })
      .then(() => {
        setIsInitialized(true);
      })
      .catch((error) => {
        message.error(error || t('editor.loadError'));
        navigate('/projects');
      });
  }, [dispatch, navigate, projectId, sceneId, t]);
  
  // 处理错误
  useEffect(() => {
    if (projectError) {
      message.error(projectError);
    }
    
    if (editorError) {
      message.error(editorError);
    }
  }, [projectError, editorError]);
  
  // 处理离开编辑器
  const handleBeforeUnload = (e: BeforeUnloadEvent) => {
    const message = t('editor.unsavedChanges');
    e.returnValue = message;
    return message;
  };
  
  // 添加离开提示
  useEffect(() => {
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);
  
  // 如果正在加载，显示加载状态
  if (projectLoading || editorLoading || !isInitialized) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip={t('editor.loading')} />
      </div>
    );
  }
  
  // 如果没有项目或场景，显示错误
  if (!currentProject || !currentScene) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <div style={{ textAlign: 'center' }}>
          <h2>{t('editor.loadError')}</h2>
          <p>{t('editor.projectOrSceneNotFound')}</p>
          <button onClick={() => navigate('/projects')}>{t('editor.backToProjects')}</button>
        </div>
      </div>
    );
  }
  
  return (
    <EditorLayout projectId={projectId!} sceneId={sceneId!} />
  );
};
