/**
 * 错误边界组件
 * 用于捕获子组件中的JavaScript错误，并显示备用UI
 */
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null};
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新状态，下次渲染时显示备用UI
    return {
      hasError: true,
      error};
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 可以在这里记录错误信息
    console.error('错误边界捕获到错误:', error, errorInfo);
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // 渲染备用UI
      return this.props.fallback;
    }

    return this.props.children;
  }
}
