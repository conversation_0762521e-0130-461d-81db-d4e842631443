/**
 * 简单的DataCompressor测试
 * 验证修复后的功能是否正常工作
 */

// 模拟Debug类
const Debug = {
  log: (tag, message, ...args) => console.log(`[${tag}] ${message}`, ...args),
  warn: (tag, message, ...args) => console.warn(`[${tag}] ${message}`, ...args),
  error: (tag, message, ...args) => console.error(`[${tag}] ${message}`, ...args)
};

// 模拟压缩算法枚举
const CompressionAlgorithm = {
  NONE: 'none',
  LZ_STRING: 'lz_string',
  MSGPACK: 'msgpack',
  CBOR: 'cbor',
  BROTLI: 'brotli',
  DEFLATE: 'deflate',
  INCREMENTAL: 'incremental',
  CUSTOM: 'custom'
};

// 模拟压缩级别枚举
const CompressionLevel = {
  NONE: 0,
  LOW: 1,
  MEDIUM: 2,
  HIGH: 3,
  HIGHEST: 4
};

// 简化的DataCompressor类用于测试
class DataCompressor {
  constructor(options = {}) {
    const defaultIncrementalOptions = {
      enabled: true,
      maxDepth: 10,
      includePathInfo: true,
      useBinaryDiff: false,
      compressIncrementalData: true,
      useFieldFiltering: false,
      includedFields: [],
      excludedFields: []
    };

    this.options = {
      algorithm: CompressionAlgorithm.LZ_STRING,
      level: CompressionLevel.MEDIUM,
      adaptive: true,
      minSize: 100,
      useBinaryFormat: false,
      useTypedArrayOptimization: true,
      useDictionaryCompression: false,
      compressionDictionary: undefined,
      incremental: defaultIncrementalOptions,
      customCompressFunction: undefined,
      customDecompressFunction: undefined,
      ...options,
      incremental: {
        ...defaultIncrementalOptions,
        ...(options.incremental || {})
      }
    };

    this.stats = {
      compressionCount: 0,
      decompressionCount: 0,
      totalOriginalSize: 0,
      totalCompressedSize: 0,
      totalCompressionTime: 0,
      totalDecompressionTime: 0,
      incrementalCompressionCount: 0,
      incrementalSavedBytes: 0,
      adaptiveAlgorithmSwitchCount: 0
    };

    this.lastCompressedDataCache = new Map();
    this.dependenciesLoaded = false;
    this.compressionLibs = {};
  }

  // 测试增量数据创建
  async createIncrementalData(newData, oldData) {
    if (!this.options.incremental.enabled) {
      return newData;
    }

    const incremental = {
      __incremental: true,
      __version: 2,
      __timestamp: Date.now()
    };

    if (!oldData) {
      incremental.__complete = true;
      incremental.__data = newData;
      return incremental;
    }

    const changes = this.createDeepIncrementalChanges(
      newData,
      oldData,
      '',
      0,
      this.options.incremental.maxDepth
    );

    if (Object.keys(changes).length === 0) {
      incremental.__empty = true;
      return incremental;
    }

    incremental.__data = changes;
    return incremental;
  }

  // 测试应用增量数据
  async applyIncrementalData(incrementalData, currentData) {
    if (!incrementalData || !incrementalData.__incremental) {
      return incrementalData;
    }

    const version = incrementalData.__version || 1;

    if (incrementalData.__complete) {
      return incrementalData.__data || incrementalData;
    }

    // 修复后的代码：使用 __empty 而不是 __isEmpty
    if (incrementalData.__empty) {
      return currentData;
    }

    let incrementalChanges = incrementalData.__data;

    let newData = { ...currentData };
    this.applyDeepIncrementalChanges(newData, incrementalChanges);

    return newData;
  }

  // 创建深度增量变更（修复后的版本）
  createDeepIncrementalChanges(newData, oldData, path, depth, maxDepth) {
    if (depth >= maxDepth) {
      return newData;
    }

    if (newData === null || newData === undefined || oldData === null || oldData === undefined) {
      return newData !== oldData ? newData : {};
    }

    if (typeof newData !== typeof oldData) {
      return newData;
    }

    if (typeof newData !== 'object') {
      return newData !== oldData ? newData : {};
    }

    if (Array.isArray(newData)) {
      if (!Array.isArray(oldData)) {
        return newData;
      }

      if (newData.length !== oldData.length) {
        return newData;
      }

      const changes = {};
      let hasChanges = false;

      for (let i = 0; i < newData.length; i++) {
        const elementChanges = this.createDeepIncrementalChanges(
          newData[i],
          oldData[i],
          `${path}[${i}]`,
          depth + 1,
          maxDepth
        );

        if (Object.keys(elementChanges).length > 0) {
          changes[i] = elementChanges;
          hasChanges = true;
        }
      }

      if (hasChanges) {
        return { __array: true, __changes: changes };
      }

      return {};
    }

    // 对象比较（修复后的版本）
    const changes = {};
    let hasChanges = false;

    for (const key in newData) {
      if (!(key in oldData)) {
        changes[key] = newData[key];
        hasChanges = true;
        continue;
      }

      const propertyChanges = this.createDeepIncrementalChanges(
        newData[key],
        oldData[key],
        path ? `${path}.${key}` : key,
        depth + 1,
        maxDepth
      );

      if (Object.keys(propertyChanges).length > 0) {
        changes[key] = propertyChanges;
        hasChanges = true;
      }
    }

    for (const key in oldData) {
      if (!(key in newData)) {
        changes[key] = { __deleted: true };
        hasChanges = true;
      }
    }

    // 修复：正确使用 hasChanges 变量
    return hasChanges ? changes : {};
  }

  // 应用深度增量变更
  applyDeepIncrementalChanges(target, changes) {
    for (const key in changes) {
      if (changes[key] && typeof changes[key] === 'object' && changes[key].__deleted) {
        delete target[key];
        continue;
      }

      if (changes[key] && typeof changes[key] === 'object' && changes[key].__array) {
        if (!Array.isArray(target[key])) {
          target[key] = [];
        }

        const arrayChanges = changes[key].__changes;
        for (const index in arrayChanges) {
          const i = parseInt(index, 10);

          while (target[key].length <= i) {
            target[key].push(undefined);
          }

          if (typeof arrayChanges[index] === 'object' && !Array.isArray(arrayChanges[index]) &&
              Object.keys(arrayChanges[index]).length > 0) {
            if (target[key][i] === undefined) {
              target[key][i] = {};
            }

            this.applyDeepIncrementalChanges(target[key][i], arrayChanges[index]);
          } else {
            target[key][i] = arrayChanges[index];
          }
        }

        continue;
      }

      if (changes[key] && typeof changes[key] === 'object' && Object.keys(changes[key]).length > 0 &&
          target[key] && typeof target[key] === 'object') {
        this.applyDeepIncrementalChanges(target[key], changes[key]);
      } else {
        target[key] = changes[key];
      }
    }
  }
}

// 测试函数
async function testDataCompressor() {
  console.log('开始测试DataCompressor修复...');

  const compressor = new DataCompressor();

  // 测试1：基本增量数据创建
  console.log('\n测试1：基本增量数据创建');
  const oldData = { name: 'test', value: 100 };
  const newData = { name: 'test', value: 200, extra: 'new' };

  const incremental = await compressor.createIncrementalData(newData, oldData);
  console.log('增量数据:', JSON.stringify(incremental, null, 2));

  // 测试2：应用增量数据
  console.log('\n测试2：应用增量数据');
  const result = await compressor.applyIncrementalData(incremental, oldData);
  console.log('应用结果:', JSON.stringify(result, null, 2));
  console.log('是否正确:', JSON.stringify(result) === JSON.stringify(newData));

  // 测试3：空增量数据
  console.log('\n测试3：空增量数据');
  const emptyIncremental = await compressor.createIncrementalData(oldData, oldData);
  console.log('空增量数据:', JSON.stringify(emptyIncremental, null, 2));
  console.log('是否标记为空:', emptyIncremental.__empty === true);

  const emptyResult = await compressor.applyIncrementalData(emptyIncremental, oldData);
  console.log('空增量应用结果:', JSON.stringify(emptyResult, null, 2));
  console.log('是否保持原数据:', JSON.stringify(emptyResult) === JSON.stringify(oldData));

  console.log('\n测试完成！');
}

// 运行测试
testDataCompressor().catch(console.error);
