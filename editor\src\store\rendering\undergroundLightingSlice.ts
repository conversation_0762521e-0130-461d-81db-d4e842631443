/**
 * 地下环境光照状态切片
 * 用于管理地下环境光照相关的状态
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { AppDispatch, RootState } from '../index';

// 定义 AppThunk 类型
export type AppThunk<ReturnType = void> = (
  dispatch: AppDispatch,
  getState: () => RootState
) => ReturnType;

/**
 * 洞穴光照配置
 */
export interface CaveLightingConfig {
  /** ID */
  id: string;
  /** 位置 */
  position: {
    x: number;
    y: number;
    z: number;
  };
  /** 颜色 */
  color: string;
  /** 强度 */
  intensity: number;
  /** 大小 */
  size: {
    x: number;
    y: number;
    z: number;
  };
  /** 衰减 */
  decay: number;
  /** 是否投射阴影 */
  castShadow: boolean;
}

/**
 * 钟乳石反射光配置
 */
export interface StalactiteReflectionConfig {
  /** ID */
  id: string;
  /** 位置 */
  position: {
    x: number;
    y: number;
    z: number;
  };
  /** 颜色 */
  color: string;
  /** 强度 */
  intensity: number;
  /** 大小 */
  size: number;
  /** 闪烁速度 */
  flickerSpeed: number;
  /** 闪烁强度 */
  flickerIntensity: number;
}

/**
 * 水面反射光配置
 */
export interface WaterReflectionConfig {
  /** ID */
  id: string;
  /** 位置 */
  position: {
    x: number;
    y: number;
    z: number;
  };
  /** 颜色 */
  color: string;
  /** 强度 */
  intensity: number;
  /** 大小 */
  size: {
    width: number;
    height: number;
  };
  /** 波动频率 */
  waveFrequency: number;
  /** 波动强度 */
  waveIntensity: number;
}

/**
 * 体积光配置
 */
export interface VolumetricLightConfig {
  /** ID */
  id: string;
  /** 位置 */
  position: {
    x: number;
    y: number;
    z: number;
  };
  /** 方向 */
  direction: {
    x: number;
    y: number;
    z: number;
  };
  /** 颜色 */
  color: string;
  /** 强度 */
  intensity: number;
  /** 长度 */
  length: number;
  /** 角度 */
  angle: number;
  /** 衰减 */
  decay: number;
  /** 密度 */
  density: number;
}

/**
 * 体积雾配置
 */
export interface VolumetricFogConfig {
  /** ID */
  id: string;
  /** 位置 */
  position: {
    x: number;
    y: number;
    z: number;
  };
  /** 大小 */
  size: {
    x: number;
    y: number;
    z: number;
  };
  /** 颜色 */
  color: string;
  /** 密度 */
  density: number;
  /** 是否启用噪声 */
  enableNoise: boolean;
  /** 噪声尺寸 */
  noiseScale: number;
  /** 噪声速度 */
  noiseSpeed: number;
}

/**
 * 地下环境光照状态接口
 */
export interface UndergroundLightingState {
  /** 实体ID */
  entityId: string;
  /** 是否启用 */
  enabled: boolean;
  /** 是否自动更新 */
  autoUpdate: boolean;
  /** 更新频率 */
  updateFrequency: number;
  /** 是否启用洞穴光照 */
  enableCaveLighting: boolean;
  /** 是否启用钟乳石反射光 */
  enableStalactiteReflection: boolean;
  /** 是否启用水面反射光 */
  enableWaterReflection: boolean;
  /** 是否启用体积光 */
  enableVolumetricLight: boolean;
  /** 是否启用体积雾 */
  enableVolumetricFog: boolean;
  /** 是否启用调试可视化 */
  enableDebugVisualization: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring: boolean;
  /** 洞穴光照列表 */
  caveLights: CaveLightingConfig[];
  /** 钟乳石反射光列表 */
  stalactiteReflections: StalactiteReflectionConfig[];
  /** 水面反射光列表 */
  waterReflections: WaterReflectionConfig[];
  /** 体积光列表 */
  volumetricLights: VolumetricLightConfig[];
  /** 体积雾列表 */
  volumetricFogs: VolumetricFogConfig[];
}

/**
 * 地下环境光照状态接口
 */
export interface UndergroundLightingsState {
  /** 地下环境光照列表 */
  undergroundLighting: UndergroundLightingState[];
  /** 是否加载中 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
}

/**
 * 初始状态
 */
const initialState: UndergroundLightingsState = {
  undergroundLighting: [],
  loading: false,
  error: null
};

/**
 * 地下环境光照状态切片
 */
const undergroundLightingSlice = createSlice({
  name: 'undergroundLighting',
  initialState,
  reducers: {
    /**
     * 设置加载状态
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    
    /**
     * 设置错误信息
     */
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    /**
     * 设置地下环境光照列表
     */
    setUndergroundLightings: (state, action: PayloadAction<UndergroundLightingState[]>) => {
      state.undergroundLighting = action.payload;
    },
    
    /**
     * 添加地下环境光照
     */
    addUndergroundLighting: (state, action: PayloadAction<UndergroundLightingState>) => {
      state.undergroundLighting.push(action.payload);
    },
    
    /**
     * 更新地下环境光照
     */
    updateUndergroundLighting: (state, action: PayloadAction<{ entityId: string; properties: Partial<UndergroundLightingState> }>) => {
      const { entityId, properties } = action.payload;
      const index = state.undergroundLighting.findIndex(ul => ul.entityId === entityId);
      
      if (index !== -1) {
        state.undergroundLighting[index] = {
          ...state.undergroundLighting[index],
          ...properties
        };
      }
    },
    
    /**
     * 删除地下环境光照
     */
    removeUndergroundLighting: (state, action: PayloadAction<string>) => {
      state.undergroundLighting = state.undergroundLighting.filter(ul => ul.entityId !== action.payload);
    },
    
    /**
     * 添加洞穴光照
     */
    addCaveLight: (state, action: PayloadAction<{ entityId: string; light: CaveLightingConfig }>) => {
      const { entityId, light } = action.payload;
      const index = state.undergroundLighting.findIndex(ul => ul.entityId === entityId);
      
      if (index !== -1) {
        state.undergroundLighting[index].caveLights.push(light);
      }
    },
    
    /**
     * 更新洞穴光照
     */
    updateCaveLight: (state, action: PayloadAction<{ entityId: string; lightId: string; properties: Partial<CaveLightingConfig> }>) => {
      const { entityId, lightId, properties } = action.payload;
      const index = state.undergroundLighting.findIndex(ul => ul.entityId === entityId);
      
      if (index !== -1) {
        const lightIndex = state.undergroundLighting[index].caveLights.findIndex(light => light.id === lightId);
        
        if (lightIndex !== -1) {
          state.undergroundLighting[index].caveLights[lightIndex] = {
            ...state.undergroundLighting[index].caveLights[lightIndex],
            ...properties
          };
        }
      }
    },
    
    /**
     * 删除洞穴光照
     */
    removeCaveLight: (state, action: PayloadAction<{ entityId: string; lightId: string }>) => {
      const { entityId, lightId } = action.payload;
      const index = state.undergroundLighting.findIndex(ul => ul.entityId === entityId);
      
      if (index !== -1) {
        state.undergroundLighting[index].caveLights = state.undergroundLighting[index].caveLights.filter(light => light.id !== lightId);
      }
    }
    
    // 类似地，可以添加钟乳石反射光、水面反射光、体积光和体积雾的操作
  }
});

// 导出动作创建器
export const {
  setLoading,
  setError,
  setUndergroundLightings,
  addUndergroundLighting,
  updateUndergroundLighting,
  removeUndergroundLighting,
  addCaveLight,
  updateCaveLight,
  removeCaveLight
} = undergroundLightingSlice.actions;

/**
 * 获取地下环境光照列表
 */
export const fetchUndergroundLightings = (): AppThunk => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    // 这里应该从引擎获取地下环境光照列表
    // const undergroundLightings = await api.getUndergroundLightings();
    // dispatch(setUndergroundLightings(undergroundLightings));

    dispatch(setLoading(false));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '获取地下环境光照列表失败';
    dispatch(setError(errorMessage));
    dispatch(setLoading(false));
  }
};

/**
 * 创建地下环境光照
 */
export const createUndergroundLighting = (undergroundLightingData: Omit<UndergroundLightingState, 'entityId'>): AppThunk => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    // 这里应该在引擎中创建地下环境光照
    // const createdUndergroundLighting = await api.createUndergroundLighting(undergroundLightingData);
    // dispatch(addUndergroundLighting(createdUndergroundLighting));

    // 临时使用传入的数据（实际应该使用API返回的数据）
    console.log('创建地下环境光照:', undergroundLightingData);

    dispatch(setLoading(false));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '创建地下环境光照失败';
    dispatch(setError(errorMessage));
    dispatch(setLoading(false));
  }
};

/**
 * 更新地下环境光照属性
 */
export const updateUndergroundLightingProperties = (entityId: string, properties: Partial<UndergroundLightingState>): AppThunk => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    // 这里应该在引擎中更新地下环境光照属性
    // await api.updateUndergroundLighting(entityId, properties);
    dispatch(updateUndergroundLighting({ entityId, properties }));

    dispatch(setLoading(false));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '更新地下环境光照属性失败';
    dispatch(setError(errorMessage));
    dispatch(setLoading(false));
  }
};

/**
 * 删除地下环境光照
 */
export const deleteUndergroundLighting = (entityId: string): AppThunk => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    // 这里应该在引擎中删除地下环境光照
    // await api.deleteUndergroundLighting(entityId);
    dispatch(removeUndergroundLighting(entityId));

    dispatch(setLoading(false));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '删除地下环境光照失败';
    dispatch(setError(errorMessage));
    dispatch(setLoading(false));
  }
};

export default undergroundLightingSlice.reducer;
