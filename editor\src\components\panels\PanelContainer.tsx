/**
 * 面板容器组件
 * 用于包装各种面板组件，提供统一的标题栏和上下文菜单
 */
import React, { ReactNode } from 'react';
import { Dropdown, Menu } from 'antd';
import { 
  MoreOutlined, 
  CloseOutlined, 
  ExpandOutlined, 
  ShrinkOutlined,
  PushpinOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './PanelContainer.less';

export interface PanelContainerProps {
  title: string;
  icon?: ReactNode;
  children: ReactNode;
  onClose?: () => void;
  onMaximize?: () => void;
  onRestore?: () => void;
  onPin?: () => void;
  onUnpin?: () => void;
  onHide?: () => void;
  isMaximized?: boolean;
  isPinned?: boolean;
  isVisible?: boolean;
  className?: string;
}

const PanelContainer: React.FC<PanelContainerProps> = ({
  title,
  icon,
  children,
  onClose,
  onMaximize,
  onRestore,
  onPin,
  onUnpin,
  onHide,
  isMaximized = false,
  isPinned = false,
  isVisible = true,
  className = ''}) => {
  const { t } = useTranslation();

  // 上下文菜单项
  const menuItems = [
    {
      key: 'maximize',
      label: isMaximized ? t('editor.panel.restore') : t('editor.panel.maximize'),
      icon: isMaximized ? <ShrinkOutlined /> : <ExpandOutlined />,
      onClick: isMaximized ? onRestore : onMaximize},
    {
      key: 'pin',
      label: isPinned ? t('editor.panel.unpin') : t('editor.panel.pin'),
      icon: <PushpinOutlined />,
      onClick: isPinned ? onUnpin : onPin},
    {
      key: 'visibility',
      label: isVisible ? t('editor.panel.hide') : t('editor.panel.show'),
      icon: isVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />,
      onClick: onHide},
    {
      key: 'close',
      label: t('editor.panel.close'),
      icon: <CloseOutlined />,
      onClick: onClose},
  ];

  return (
    <div className={`panel-container ${className}`}>
      <div className="panel-header">
        <div className="panel-title">
          {icon && <span className="panel-icon">{icon}</span>}
          <span className="panel-title-text">{title}</span>
        </div>
        <div className="panel-actions">
          <Dropdown 
            overlay={<Menu items={menuItems} />} 
            trigger={['click']} 
            placement="bottomRight"
          >
            <MoreOutlined className="panel-action-button" />
          </Dropdown>
        </div>
      </div>
      <div className="panel-content">
        {children}
      </div>
    </div>
  );
};

export default PanelContainer;
