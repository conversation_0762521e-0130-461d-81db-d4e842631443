/**
 * 音频监听器
 * 用于处理3D音频的空间位置和方向
 */
import * as THREE from 'three';

/**
 * 音频监听器选项
 */
export interface AudioListenerOptions {
  /** 位置 */
  position?: THREE.Vector3;
  /** 前方向 */
  forward?: THREE.Vector3;
  /** 上方向 */
  up?: THREE.Vector3;
  /** 速度 */
  velocity?: THREE.Vector3;
}

/**
 * 扩展Web Audio API的AudioListener接口
 */
interface ExtendedAudioListener {
  // 标准AudioListener方法
  setPosition(x: number, y: number, z: number): void;
  setOrientation(x: number, y: number, z: number, xUp: number, yUp: number, zUp: number): void;

  // 新的AudioListener属性 (WebAudio API Level 2)
  positionX?: AudioParam;
  positionY?: AudioParam;
  positionZ?: AudioParam;
  forwardX?: AudioParam;
  forwardY?: AudioParam;
  forwardZ?: AudioParam;
  upX?: AudioParam;
  upY?: AudioParam;
  upZ?: AudioParam;
  speedX?: AudioParam;
  speedY?: AudioParam;
  speedZ?: AudioParam;
}

/**
 * 音频监听器
 */
export class AudioListener {
  /** 音频上下文 */
  private context: AudioContext;

  /** 音频监听器节点 */
  private listener: ExtendedAudioListener;

  /** 位置 */
  private position: THREE.Vector3 = new THREE.Vector3();

  /** 前方向 */
  private forward: THREE.Vector3 = new THREE.Vector3(0, 0, -1);

  /** 上方向 */
  private up: THREE.Vector3 = new THREE.Vector3(0, 1, 0);

  /** 速度 */
  private velocity: THREE.Vector3 = new THREE.Vector3();

  /** 上一次位置 */
  private previousPosition: THREE.Vector3 = new THREE.Vector3();

  /** 上一次更新时间 */
  private previousTime: number = 0;

  /** 相机 */
  private camera: THREE.Camera | null = null;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /**
   * 创建音频监听器
   * @param context 音频上下文
   * @param options 音频监听器选项
   */
  constructor(context: AudioContext, options: AudioListenerOptions = {}) {
    this.context = context;
    this.listener = context.listener;

    // 设置初始位置和方向
    if (options.position) this.position.copy(options.position);
    if (options.forward) this.forward.copy(options.forward).normalize();
    if (options.up) this.up.copy(options.up).normalize();
    if (options.velocity) this.velocity.copy(options.velocity);

    this.previousPosition.copy(this.position);
    this.previousTime = context.currentTime;

    // 更新监听器
    this.updateListener();
  }

  /**
   * 更新监听器
   */
  private updateListener(): void {
    // 设置位置
    if (this.listener.positionX && this.listener.positionY && this.listener.positionZ) {
      this.listener.positionX.value = this.position.x;
      this.listener.positionY.value = this.position.y;
      this.listener.positionZ.value = this.position.z;
    } else {
      this.listener.setPosition(this.position.x, this.position.y, this.position.z);
    }

    // 设置方向
    if (this.listener.forwardX && this.listener.forwardY && this.listener.forwardZ &&
        this.listener.upX && this.listener.upY && this.listener.upZ) {
      this.listener.forwardX.value = this.forward.x;
      this.listener.forwardY.value = this.forward.y;
      this.listener.forwardZ.value = this.forward.z;
      this.listener.upX.value = this.up.x;
      this.listener.upY.value = this.up.y;
      this.listener.upZ.value = this.up.z;
    } else {
      this.listener.setOrientation(
        this.forward.x, this.forward.y, this.forward.z,
        this.up.x, this.up.y, this.up.z
      );
    }

    // 设置速度
    if (this.listener.speedX && this.listener.speedY && this.listener.speedZ) {
      this.listener.speedX.value = this.velocity.x;
      this.listener.speedY.value = this.velocity.y;
      this.listener.speedZ.value = this.velocity.z;
    }
  }

  /**
   * 更新监听器
   */
  public update(): void {
    // 如果有相机，则从相机更新位置和方向
    if (this.camera) {
      this.updateFromCamera();
    }

    // 计算速度
    const currentTime = this.context.currentTime;
    const deltaTime = currentTime - this.previousTime;

    if (deltaTime > 0) {
      this.velocity.copy(this.position).sub(this.previousPosition).divideScalar(deltaTime);
      this.previousPosition.copy(this.position);
      this.previousTime = currentTime;
    }

    // 更新监听器
    this.updateListener();
  }

  /**
   * 从相机更新位置和方向
   */
  private updateFromCamera(): void {
    if (!this.camera) return;

    // 获取相机位置
    this.position.setFromMatrixPosition(this.camera.matrixWorld);

    // 获取相机方向
    this.forward.set(0, 0, -1).applyQuaternion(this.camera.quaternion);
    this.up.set(0, 1, 0).applyQuaternion(this.camera.quaternion);
  }

  /**
   * 设置位置
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   */
  public setPosition(x: number, y: number, z: number): void {
    this.position.set(x, y, z);
  }

  /**
   * 设置方向
   * @param forwardX 前方向X
   * @param forwardY 前方向Y
   * @param forwardZ 前方向Z
   * @param upX 上方向X
   * @param upY 上方向Y
   * @param upZ 上方向Z
   */
  public setOrientation(forwardX: number, forwardY: number, forwardZ: number, upX: number, upY: number, upZ: number): void {
    this.forward.set(forwardX, forwardY, forwardZ).normalize();
    this.up.set(upX, upY, upZ).normalize();
  }

  /**
   * 设置速度
   * @param x X速度
   * @param y Y速度
   * @param z Z速度
   */
  public setVelocity(x: number, y: number, z: number): void {
    this.velocity.set(x, y, z);
  }

  /**
   * 设置相机
   * @param camera 相机
   */
  public setCamera(camera: THREE.Camera): void {
    this.camera = camera;
    this.updateFromCamera();
  }

  /**
   * 获取位置
   * @returns 位置
   */
  public getPosition(): THREE.Vector3 {
    return this.position.clone();
  }

  /**
   * 获取前方向
   * @returns 前方向
   */
  public getForward(): THREE.Vector3 {
    return this.forward.clone();
  }

  /**
   * 获取上方向
   * @returns 上方向
   */
  public getUp(): THREE.Vector3 {
    return this.up.clone();
  }

  /**
   * 获取速度
   * @returns 速度
   */
  public getVelocity(): THREE.Vector3 {
    return this.velocity.clone();
  }

  /**
   * 获取相机
   * @returns 相机
   */
  public getCamera(): THREE.Camera | null {
    return this.camera;
  }

  /**
   * 获取音频上下文
   * @returns 音频上下文
   */
  public getContext(): AudioContext {
    return this.context;
  }

  /**
   * 获取原生监听器
   * @returns 原生监听器
   */
  public getNativeListener(): ExtendedAudioListener {
    return this.listener;
  }

  /**
   * 销毁监听器
   */
  public dispose(): void {
    if (this.destroyed) return;

    this.camera = null;
    this.destroyed = true;
  }
}
