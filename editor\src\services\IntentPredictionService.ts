/**
 * 意图预测服务
 * 用于预测用户编辑意图，实现基于意图的编辑区域预测
 */
import { EventEmitter } from '../utils/EventEmitter';
import { store } from '../store';
import { collaborationService, Operation, OperationType } from './CollaborationService';
import { conflictPreventionService, EditingZoneType } from './ConflictPreventionService';

/**
 * 用户行为类型枚举
 */
export enum UserBehaviorType {
  SELECTION = 'selection',
  CURSOR_MOVE = 'cursor_move',
  CAMERA_MOVE = 'camera_move',
  ENTITY_HOVER = 'entity_hover',
  COMPONENT_HOVER = 'component_hover',
  PROPERTY_HOVER = 'property_hover',
  PANEL_FOCUS = 'panel_focus',
  MENU_OPEN = 'menu_open',
  SEARCH = 'search',
  KEYBOARD_SHORTCUT = 'keyboard_shortcut'}

/**
 * 用户行为接口
 */
export interface UserBehavior {
  type: UserBehaviorType;
  timestamp: number;
  data: any;
}

/**
 * 编辑意图类型枚举
 */
export enum EditIntentType {
  ENTITY_TRANSFORM = 'entity_transform',
  ENTITY_PROPERTY = 'entity_property',
  COMPONENT_ADD = 'component_add',
  COMPONENT_REMOVE = 'component_remove',
  COMPONENT_PROPERTY = 'component_property',
  ENTITY_ADD = 'entity_add',
  ENTITY_REMOVE = 'entity_remove',
  ENTITY_DUPLICATE = 'entity_duplicate',
  ENTITY_PARENT = 'entity_parent',
  RESOURCE_EDIT = 'resource_edit',
  SCENE_SETTINGS = 'scene_settings',
  UNKNOWN = 'unknown'}

/**
 * 编辑意图接口
 */
export interface EditIntent {
  type: EditIntentType;
  confidence: number;
  entityIds?: string[];
  componentIds?: string[];
  propertyPaths?: string[][];
  resourceIds?: string[];
  predictedAt: number;
  expiresAt: number;
}

/**
 * 意图预测配置接口
 */
export interface IntentPredictionConfig {
  enabled: boolean;
  historySize: number;
  predictionThreshold: number;
  intentExpirationTime: number;
  autoCreateEditingZones: boolean;
  predictEntityTransform: boolean;
  predictEntityProperty: boolean;
  predictComponentAdd: boolean;
  predictComponentRemove: boolean;
  predictComponentProperty: boolean;
  predictEntityAdd: boolean;
  predictEntityRemove: boolean;
  predictEntityDuplicate: boolean;
  predictEntityParent: boolean;
  predictResourceEdit: boolean;
  predictSceneSettings: boolean;
}

/**
 * 意图预测服务类
 */
class IntentPredictionService extends EventEmitter {
  private config: IntentPredictionConfig;
  private enabled: boolean = false;
  private behaviorHistory: UserBehavior[] = [];
  private currentIntent: EditIntent | null = null;
  private intentHistory: EditIntent[] = [];
  private selectedEntityIds: string[] = [];
  // @ts-ignore - 保留用于将来的功能扩展
  private _hoveredEntityId: string | null = null;
  private hoveredComponentId: string | null = null;
  private hoveredPropertyPath: string[] | null = null;
  private activePanelId: string | null = null;
  // @ts-ignore - 保留用于将来的功能扩展
  private _lastCursorPosition: { x: number, y: number } | null = null;
  // @ts-ignore - 保留用于将来的功能扩展
  private _lastCameraPosition: { x: number, y: number, z: number } | null = null;
  // @ts-ignore - 保留用于将来的功能扩展
  private _lastCameraTarget: { x: number, y: number, z: number } | null = null;

  constructor() {
    super();

    // 默认配置
    this.config = {
      enabled: false,
      historySize: 20,
      predictionThreshold: 0.6,
      intentExpirationTime: 5000, // 5秒
      autoCreateEditingZones: true,
      predictEntityTransform: true,
      predictEntityProperty: true,
      predictComponentAdd: true,
      predictComponentRemove: true,
      predictComponentProperty: true,
      predictEntityAdd: true,
      predictEntityRemove: true,
      predictEntityDuplicate: true,
      predictEntityParent: true,
      predictResourceEdit: true,
      predictSceneSettings: true};

    // 监听Redux状态变化
    store.subscribe(this.handleStoreChange.bind(this));

    // 监听协作服务事件
    collaborationService.on('operation', this.handleOperation.bind(this));
  }

  /**
   * 设置配置
   * @param config 配置
   */
  public setConfig(config: Partial<IntentPredictionConfig>): void {
    this.config = { ...this.config, ...config };

    // 如果启用状态改变
    if (config.enabled !== undefined && config.enabled !== this.enabled) {
      if (config.enabled) {
        this.enable();
      } else {
        this.disable();
      }
    }
  }

  /**
   * 获取配置
   * @returns 配置
   */
  public getConfig(): IntentPredictionConfig {
    return { ...this.config };
  }

  /**
   * 启用服务
   */
  public enable(): void {
    if (this.enabled) return;

    this.enabled = true;
    this.config.enabled = true;

    console.log('意图预测服务已启用');
  }

  /**
   * 禁用服务
   */
  public disable(): void {
    if (!this.enabled) return;

    this.enabled = false;
    this.config.enabled = false;

    // 清除当前意图
    this.currentIntent = null;

    console.log('意图预测服务已禁用');
  }

  /**
   * 记录用户行为
   * @param type 行为类型
   * @param data 行为数据
   */
  public recordBehavior(type: UserBehaviorType, data: any): void {
    if (!this.enabled) return;

    // 创建行为对象
    const behavior: UserBehavior = {
      type,
      timestamp: Date.now(),
      data};

    // 添加到历史
    this.behaviorHistory.push(behavior);

    // 限制历史大小
    if (this.behaviorHistory.length > this.config.historySize) {
      this.behaviorHistory.shift();
    }

    // 预测意图
    this.predictIntent();
  }

  /**
   * 获取当前意图
   * @returns 当前意图
   */
  public getCurrentIntent(): EditIntent | null {
    // 检查意图是否过期
    if (this.currentIntent && this.currentIntent.expiresAt < Date.now()) {
      this.currentIntent = null;
    }

    return this.currentIntent;
  }

  /**
   * 获取意图历史
   * @returns 意图历史
   */
  public getIntentHistory(): EditIntent[] {
    return [...this.intentHistory];
  }

  /**
   * 清除历史
   */
  public clearHistory(): void {
    this.behaviorHistory = [];
    this.intentHistory = [];
    this.currentIntent = null;
  }

  /**
   * 处理Redux状态变化
   * @private
   */
  private handleStoreChange(): void {
    if (!this.enabled) return;

    const state = store.getState();

    // 检查选中实体变化 - 使用 editor.selectedObjects 和 collaboration.selectedEntityIds
    const editorSelectedObjects = state.editor?.selectedObjects || [];
    const collaborationSelectedEntityIds = state.collaboration?.selectedEntityIds || [];

    // 从编辑器选中对象中提取实体ID，如果对象有id属性的话
    const editorSelectedEntityIds = editorSelectedObjects
      .filter(obj => obj && obj.id)
      .map(obj => obj.id);

    // 合并编辑器选中的实体ID和协作状态中的选中实体ID
    const newSelectedEntityIds = [
      ...editorSelectedEntityIds,
      ...collaborationSelectedEntityIds
    ].filter((id, index, arr) => arr.indexOf(id) === index); // 去重

    if (!this.areArraysEqual(this.selectedEntityIds, newSelectedEntityIds)) {
      this.selectedEntityIds = [...newSelectedEntityIds];
      this.recordBehavior(UserBehaviorType.SELECTION, { entityIds: this.selectedEntityIds });
    }

    // 检查活动面板变化 - 从面板列表中找到可见的面板
    const visiblePanels = state.ui?.panels?.filter(panel => panel.isVisible) || [];
    const newActivePanelId = visiblePanels.length > 0 ? visiblePanels[0].id : null;

    if (this.activePanelId !== newActivePanelId) {
      this.activePanelId = newActivePanelId;
      this.recordBehavior(UserBehaviorType.PANEL_FOCUS, { panelId: this.activePanelId });
    }
  }

  /**
   * 处理操作
   * @param operation 操作
   * @private
   */
  private handleOperation(operation: Operation): void {
    if (!this.enabled) return;

    // 根据操作类型记录行为
    switch (operation.type) {
      case OperationType.CURSOR_MOVE:
        this._lastCursorPosition = operation.data;
        this.recordBehavior(UserBehaviorType.CURSOR_MOVE, operation.data);
        break;

      case OperationType.CAMERA_MOVE:
        this._lastCameraPosition = operation.data.position;
        this._lastCameraTarget = operation.data.target;
        this.recordBehavior(UserBehaviorType.CAMERA_MOVE, operation.data);
        break;

      case OperationType.ENTITY_HOVER:
        this._hoveredEntityId = operation.data.entityId;
        this.recordBehavior(UserBehaviorType.ENTITY_HOVER, operation.data);
        break;

      case OperationType.COMPONENT_HOVER:
        this.hoveredComponentId = operation.data.componentId;
        this.recordBehavior(UserBehaviorType.COMPONENT_HOVER, operation.data);
        break;

      case OperationType.PROPERTY_HOVER:
        this.hoveredPropertyPath = operation.data.propertyPath;
        this.recordBehavior(UserBehaviorType.PROPERTY_HOVER, operation.data);
        break;
    }
  }

  /**
   * 预测意图
   * @private
   */
  private predictIntent(): void {
    if (this.behaviorHistory.length < 2) return;

    // 获取最近的行为
    const recentBehaviors = this.behaviorHistory.slice(-5);

    // 预测各种意图类型
    const predictions: { type: EditIntentType, confidence: number, data: any }[] = [];

    // 预测实体变换意图
    if (this.config.predictEntityTransform) {
      const transformIntent = this.predictEntityTransformIntent(recentBehaviors);
      if (transformIntent) predictions.push(transformIntent);
    }

    // 预测实体属性编辑意图
    if (this.config.predictEntityProperty) {
      const propertyIntent = this.predictEntityPropertyIntent(recentBehaviors);
      if (propertyIntent) predictions.push(propertyIntent);
    }

    // 预测组件添加意图
    if (this.config.predictComponentAdd) {
      const addComponentIntent = this.predictComponentAddIntent(recentBehaviors);
      if (addComponentIntent) predictions.push(addComponentIntent);
    }

    // 预测组件移除意图
    if (this.config.predictComponentRemove) {
      const removeComponentIntent = this.predictComponentRemoveIntent(recentBehaviors);
      if (removeComponentIntent) predictions.push(removeComponentIntent);
    }

    // 预测组件属性编辑意图
    if (this.config.predictComponentProperty) {
      const componentPropertyIntent = this.predictComponentPropertyIntent(recentBehaviors);
      if (componentPropertyIntent) predictions.push(componentPropertyIntent);
    }

    // 如果没有预测到任何意图，返回
    if (predictions.length === 0) return;

    // 选择置信度最高的意图
    predictions.sort((a, b) => b.confidence - a.confidence);
    const bestPrediction = predictions[0];

    // 如果置信度低于阈值，返回
    if (bestPrediction.confidence < this.config.predictionThreshold) return;

    // 创建意图对象
    const intent: EditIntent = {
      type: bestPrediction.type,
      confidence: bestPrediction.confidence,
      predictedAt: Date.now(),
      expiresAt: Date.now() + this.config.intentExpirationTime,
      ...bestPrediction.data};

    // 更新当前意图
    this.currentIntent = intent;

    // 添加到意图历史
    this.intentHistory.push(intent);
    if (this.intentHistory.length > this.config.historySize) {
      this.intentHistory.shift();
    }

    // 发出意图预测事件
    this.emit('intentPredicted', intent);

    // 如果启用了自动创建编辑区域，创建编辑区域
    if (this.config.autoCreateEditingZones) {
      this.createEditingZoneFromIntent(intent);
    }
  }

  /**
   * 从意图创建编辑区域
   * @param intent 意图
   * @private
   */
  private createEditingZoneFromIntent(intent: EditIntent): void {
    if (!conflictPreventionService) return;

    switch (intent.type) {
      case EditIntentType.ENTITY_TRANSFORM:
      case EditIntentType.ENTITY_PROPERTY:
      case EditIntentType.ENTITY_ADD:
      case EditIntentType.ENTITY_REMOVE:
      case EditIntentType.ENTITY_DUPLICATE:
      case EditIntentType.ENTITY_PARENT:
        if (intent.entityIds && intent.entityIds.length > 0) {
          intent.entityIds.forEach(entityId => {
            conflictPreventionService.broadcastEditingZone(EditingZoneType.ENTITY, {
              entityId
            });
          });
        }
        break;

      case EditIntentType.COMPONENT_ADD:
      case EditIntentType.COMPONENT_REMOVE:
      case EditIntentType.COMPONENT_PROPERTY:
        if (intent.entityIds && intent.entityIds.length > 0 && intent.componentIds && intent.componentIds.length > 0) {
          intent.entityIds.forEach(entityId => {
            intent.componentIds!.forEach(componentId => {
              conflictPreventionService.broadcastEditingZone(EditingZoneType.COMPONENT, {
                entityId,
                componentId
              });
            });
          });
        }
        break;

      case EditIntentType.RESOURCE_EDIT:
        if (intent.resourceIds && intent.resourceIds.length > 0) {
          intent.resourceIds.forEach(resourceId => {
            conflictPreventionService.broadcastEditingZone(EditingZoneType.RESOURCE, {
              resourceId
            });
          });
        }
        break;

      case EditIntentType.SCENE_SETTINGS:
        conflictPreventionService.broadcastEditingZone(EditingZoneType.SCENE, {});
        break;
    }
  }

  /**
   * 预测实体变换意图
   * @param behaviors 行为历史
   * @returns 意图预测
   * @private
   */
  private predictEntityTransformIntent(behaviors: UserBehavior[]): { type: EditIntentType, confidence: number, data: any } | null {
    // 实现实体变换意图预测逻辑
    // 分析行为模式，如选择实体后的鼠标移动、相机移动等
    if (behaviors.length === 0) return null;

    // 这里可以添加具体的预测逻辑
    // 例如：检查是否有选择实体后的鼠标移动行为
    const hasSelection = behaviors.some(b => b.type === UserBehaviorType.SELECTION);
    const hasCursorMove = behaviors.some(b => b.type === UserBehaviorType.CURSOR_MOVE);

    if (hasSelection && hasCursorMove) {
      return {
        type: EditIntentType.ENTITY_TRANSFORM,
        confidence: 0.7,
        data: { entityIds: this.selectedEntityIds }
      };
    }

    return null;
  }

  /**
   * 预测实体属性编辑意图
   * @param behaviors 行为历史
   * @returns 意图预测
   * @private
   */
  private predictEntityPropertyIntent(behaviors: UserBehavior[]): { type: EditIntentType, confidence: number, data: any } | null {
    // 实现实体属性编辑意图预测逻辑
    if (behaviors.length === 0) return null;

    // 检查是否有选择实体后的属性悬停行为
    const hasSelection = behaviors.some(b => b.type === UserBehaviorType.SELECTION);
    const hasPropertyHover = behaviors.some(b => b.type === UserBehaviorType.PROPERTY_HOVER);

    if (hasSelection && hasPropertyHover) {
      return {
        type: EditIntentType.ENTITY_PROPERTY,
        confidence: 0.8,
        data: {
          entityIds: this.selectedEntityIds,
          propertyPaths: this.hoveredPropertyPath ? [this.hoveredPropertyPath] : []
        }
      };
    }

    return null;
  }

  /**
   * 预测组件添加意图
   * @param behaviors 行为历史
   * @returns 意图预测
   * @private
   */
  private predictComponentAddIntent(behaviors: UserBehavior[]): { type: EditIntentType, confidence: number, data: any } | null {
    // 实现组件添加意图预测逻辑
    if (behaviors.length === 0) return null;

    // 检查是否有选择实体后的面板聚焦行为
    const hasSelection = behaviors.some(b => b.type === UserBehaviorType.SELECTION);
    const hasPanelFocus = behaviors.some(b => b.type === UserBehaviorType.PANEL_FOCUS);

    if (hasSelection && hasPanelFocus) {
      return {
        type: EditIntentType.COMPONENT_ADD,
        confidence: 0.6,
        data: { entityIds: this.selectedEntityIds }
      };
    }

    return null;
  }

  /**
   * 预测组件移除意图
   * @param behaviors 行为历史
   * @returns 意图预测
   * @private
   */
  private predictComponentRemoveIntent(behaviors: UserBehavior[]): { type: EditIntentType, confidence: number, data: any } | null {
    // 实现组件移除意图预测逻辑
    if (behaviors.length === 0) return null;

    // 检查是否有组件悬停行为
    const hasComponentHover = behaviors.some(b => b.type === UserBehaviorType.COMPONENT_HOVER);

    if (hasComponentHover) {
      return {
        type: EditIntentType.COMPONENT_REMOVE,
        confidence: 0.5,
        data: {
          entityIds: this.selectedEntityIds,
          componentIds: this.hoveredComponentId ? [this.hoveredComponentId] : []
        }
      };
    }

    return null;
  }

  /**
   * 预测组件属性编辑意图
   * @param behaviors 行为历史
   * @returns 意图预测
   * @private
   */
  private predictComponentPropertyIntent(behaviors: UserBehavior[]): { type: EditIntentType, confidence: number, data: any } | null {
    // 实现组件属性编辑意图预测逻辑
    if (behaviors.length === 0) return null;

    // 检查是否有组件悬停和属性悬停行为
    const hasComponentHover = behaviors.some(b => b.type === UserBehaviorType.COMPONENT_HOVER);
    const hasPropertyHover = behaviors.some(b => b.type === UserBehaviorType.PROPERTY_HOVER);

    if (hasComponentHover && hasPropertyHover) {
      return {
        type: EditIntentType.COMPONENT_PROPERTY,
        confidence: 0.8,
        data: {
          entityIds: this.selectedEntityIds,
          componentIds: this.hoveredComponentId ? [this.hoveredComponentId] : [],
          propertyPaths: this.hoveredPropertyPath ? [this.hoveredPropertyPath] : []
        }
      };
    }

    return null;
  }

  /**
   * 比较两个数组是否相等
   * @param arr1 数组1
   * @param arr2 数组2
   * @returns 是否相等
   * @private
   */
  private areArraysEqual(arr1: any[], arr2: any[]): boolean {
    if (arr1.length !== arr2.length) return false;
    return arr1.every((item, index) => item === arr2[index]);
  }
}

// 创建单例实例
export const intentPredictionService = new IntentPredictionService();
