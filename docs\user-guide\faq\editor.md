# 编辑器常见问题

本文档收集了关于DL（Digital Learning）引擎编辑器的常见问题和解答，帮助您快速解决在使用过程中遇到的问题。

## 基本问题

### 编辑器支持哪些操作系统？

DL（Digital Learning）引擎编辑器支持以下操作系统：
- Windows 10/11 (64位)
- macOS 10.15+
- Ubuntu 20.04+ (部分功能可能受限)

### 编辑器的系统要求是什么？

**最低配置**：
- 处理器：Intel Core i5-6600 或 AMD Ryzen 5 1600
- 内存：8GB RAM
- 显卡：支持WebGL 2.0的显卡，2GB显存
- 存储：5GB可用空间
- 网络：宽带互联网连接

**推荐配置**：
- 处理器：Intel Core i7-9700 或 AMD Ryzen 7 3700X
- 内存：16GB RAM
- 显卡：NVIDIA GTX 1660 或 AMD RX 5600 XT，6GB显存
- 存储：10GB SSD可用空间
- 网络：高速宽带互联网连接

### 如何更新编辑器？

1. 启动编辑器
2. 点击顶部菜单栏的"帮助 > 检查更新"
3. 如果有可用更新，按照提示完成更新过程

或者，您可以访问官方网站下载最新版本。

### 编辑器支持哪些文件格式？

DL（Digital Learning）引擎编辑器支持以下文件格式：

**3D模型**：
- glTF/GLB (.gltf, .glb)
- FBX (.fbx)
- OBJ (.obj)
- STL (.stl)
- COLLADA (.dae)

**图像**：
- PNG (.png)
- JPEG (.jpg, .jpeg)
- WebP (.webp)
- HDR (.hdr)
- EXR (.exr)

**音频**：
- MP3 (.mp3)
- WAV (.wav)
- OGG (.ogg)

**视频**：
- MP4 (.mp4)
- WebM (.webm)

## 界面和操作

### 如何重置编辑器布局？

1. 点击顶部菜单栏的"视图 > 重置布局"
2. 编辑器布局将恢复为默认设置

### 如何自定义快捷键？

1. 点击顶部菜单栏的"编辑 > 首选项"
2. 在首选项对话框中，选择"快捷键"选项卡
3. 找到要修改的命令，双击快捷键列
4. 按下新的快捷键组合
5. 点击"应用"或"确定"保存更改

### 为什么我无法选择某些对象？

可能的原因包括：

1. 对象被锁定 - 检查层次面板中对象是否有锁定图标
2. 对象被隐藏 - 检查层次面板中对象是否有隐藏图标
3. 对象在当前视图中不可见 - 调整视图或使用层次面板选择
4. 对象太小或被其他对象遮挡 - 使用层次面板选择

### 如何同时操作多个对象？

1. 按住Ctrl键（Windows）或Command键（Mac）的同时点击多个对象
2. 或在场景视图中按住鼠标左键拖动，创建选择框
3. 选中多个对象后，可以同时移动、旋转或缩放它们

### 如何精确设置对象的位置？

1. 选择对象
2. 在属性面板的"变换"部分，直接输入位置、旋转或缩放的数值
3. 按Enter键确认

### 如何撤销操作？

- 按下Ctrl+Z（Windows）或Command+Z（Mac）撤销上一步操作
- 按下Ctrl+Y（Windows）或Command+Shift+Z（Mac）重做操作

### 如何复制对象的属性？

1. 选择源对象
2. 右键点击并选择"复制组件"，然后选择要复制的组件
3. 选择目标对象
4. 右键点击并选择"粘贴组件值"

## 项目管理

### 如何创建项目备份？

1. 点击顶部菜单栏的"文件 > 导出 > 项目备份"
2. 选择备份保存位置
3. 点击"保存"按钮

### 如何恢复项目备份？

1. 点击顶部菜单栏的"文件 > 导入 > 项目备份"
2. 选择备份文件
3. 点击"打开"按钮
4. 按照提示完成恢复过程

### 项目文件保存在哪里？

默认情况下，项目文件保存在以下位置：

- Windows: `C:\Users\<USER>\Documents\IR Engine\Projects\`
- macOS: `/Users/<USER>/Documents/IR Engine/Projects/`
- Linux: `/home/<USER>/Documents/IR Engine/Projects/`

您可以在"编辑 > 首选项 > 常规"中修改默认项目位置。

### 如何管理大型项目的性能？

1. 使用层级细节(LOD)系统减少远处对象的复杂度
2. 启用遮挡剔除，避免渲染被遮挡的对象
3. 优化纹理大小和格式
4. 使用实例化渲染相似对象
5. 将大型场景分割为多个子场景
6. 使用性能分析工具找出瓶颈

## 协作编辑

### 如何启用协作编辑？

1. 点击顶部菜单栏的"文件 > 协作 > 创建协作会话"
2. 设置会话名称和权限
3. 点击"创建"按钮
4. 系统会生成一个会话ID，您可以将此ID分享给其他用户

### 如何加入协作会话？

1. 点击顶部菜单栏的"文件 > 协作 > 加入协作会话"
2. 输入会话ID
3. 点击"加入"按钮

### 如何解决编辑冲突？

当多人同时编辑同一对象时，可能会发生冲突。解决冲突的方法：

1. 在冲突对话框中，查看冲突详情
2. 选择保留您的更改、接受他人的更改，或手动合并
3. 点击"应用"按钮

### 如何管理协作权限？

1. 打开协作面板
2. 切换到"用户"标签页
3. 找到目标用户
4. 点击用户旁边的设置图标
5. 选择"更改权限"
6. 设置新的权限级别
7. 点击"确认"按钮

## 性能和故障排除

### 编辑器运行缓慢怎么办？

1. 检查系统资源使用情况（CPU、内存、GPU）
2. 关闭不必要的面板和功能
3. 降低场景复杂度（临时隐藏复杂对象）
4. 减少场景中的灯光数量
5. 在首选项中降低渲染质量设置
6. 更新显卡驱动程序
7. 重启编辑器

### 编辑器崩溃后如何恢复工作？

DL（Digital Learning）引擎编辑器有自动保存功能，默认每5分钟保存一次。恢复步骤：

1. 重新启动编辑器
2. 如果出现恢复对话框，选择"恢复"
3. 如果没有出现对话框，点击"文件 > 打开最近 > 自动保存"

### 如何报告问题？

1. 点击顶部菜单栏的"帮助 > 报告问题"
2. 填写问题描述表单
3. 附上相关截图和日志文件
4. 点击"提交"按钮

或者，您可以在官方论坛或GitHub仓库提交问题。

### 如何查看日志文件？

日志文件位于以下位置：

- Windows: `C:\Users\<USER>\AppData\Local\IR Engine\Logs\`
- macOS: `/Users/<USER>/Library/Logs/IR Engine/`
- Linux: `/home/<USER>/.config/IR Engine/Logs/`

您也可以通过编辑器中的控制台面板查看日志。

## 其他问题

### 如何获取更多帮助？

如果您的问题在此FAQ中未得到解答，可以通过以下方式获取帮助：

1. 查阅[用户手册](../README.md)
2. 访问[官方论坛](https://dl-engine.example.com/forum)
3. 加入[官方社区](https://dl-engine.example.com/community)
4. 联系[技术支持](mailto:<EMAIL>)

### 如何提供反馈和建议？

我们欢迎用户提供反馈和建议，您可以通过以下方式联系我们：

1. 点击顶部菜单栏的"帮助 > 提供反馈"
2. 在官方论坛发帖
3. 发送邮件至[<EMAIL>](mailto:<EMAIL>)

### 如何了解最新更新和功能？

1. 关注[官方网站](https://dl-engine.example.com)
2. 订阅[电子邮件通讯](https://dl-engine.example.com/newsletter)
3. 关注社交媒体账号
4. 查看编辑器启动页面的"新闻"部分
