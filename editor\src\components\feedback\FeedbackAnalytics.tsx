/**
 * 反馈分析组件
 * 用于显示和分析反馈数据
 */
import React, { useState, useEffect } from 'react';
import { Card, Tabs, Table, Statistic, Row, Col, Spin, Empty, Alert, Tag, Space, Button, Select} from 'antd';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer, Cell } from 'recharts';
import { UserOutlined, BugOutlined, RocketOutlined, ToolOutlined, StarOutlined, ClockCircleOutlined, FilterOutlined, ReloadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { FeedbackService, FeedbackData, FeedbackStats } from '../../services/FeedbackService';
import './FeedbackAnalytics.less';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';

// 图表颜色
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#FF6B6B', '#6B66FF'];

export interface FeedbackAnalyticsProps {
  /** 反馈类型 */
  type?: string;
  /** 反馈子类型 */
  subType?: string;
  /** 是否自动刷新 */
  autoRefresh?: boolean;
  /** 刷新间隔（毫秒） */
  refreshInterval?: number;
}

/**
 * 反馈分析组件
 */
const FeedbackAnalytics: React.FC<FeedbackAnalyticsProps> = ({
  type,
  subType,
  autoRefresh = false,
  refreshInterval = 60000
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<FeedbackStats | null>(null);
  const [feedbackList, setFeedbackList] = useState<FeedbackData[]>([]);
  const [filters, setFilters] = useState<any>({
    type: type || undefined,
    subType: subType || undefined,
    dateRange: undefined,
    feedbackType: undefined
  });

  // 加载反馈数据
  const loadFeedbackData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 准备过滤条件
      const queryFilters: any = { ...filters };
      
      // 处理日期范围
      if (queryFilters.dateRange && queryFilters.dateRange.length === 2) {
        queryFilters.startDate = queryFilters.dateRange[0].toISOString();
        queryFilters.endDate = queryFilters.dateRange[1].toISOString();
        delete queryFilters.dateRange;
      }

      // 获取反馈列表
      const list = await FeedbackService.getFeedbackList(queryFilters);
      setFeedbackList(list);

      // 获取反馈统计
      const statistics = await FeedbackService.getFeedbackStats(queryFilters);
      setStats(statistics);
    } catch (error) {
      console.error('加载反馈数据失败:', error);
      setError(t('feedback.analytics.loadError'));
    } finally {
      setLoading(false);
    }
  };

  // 处理过滤器变更
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 重置过滤器
  const resetFilters = () => {
    setFilters({
      type: type || undefined,
      subType: subType || undefined,
      dateRange: undefined,
      feedbackType: undefined
    });
  };

  // 初始加载和自动刷新
  useEffect(() => {
    loadFeedbackData();

    // 设置自动刷新
    let intervalId: NodeJS.Timeout | null = null;
    if (autoRefresh) {
      intervalId = setInterval(loadFeedbackData, refreshInterval);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [filters, autoRefresh, refreshInterval]);

  // 表格列定义
  const columns = [
    {
      title: t('feedback.analytics.table.title'),
      dataIndex: 'title',
      key: 'title',
      ellipsis: true},
    {
      title: t('feedback.analytics.table.type'),
      dataIndex: 'feedbackType',
      key: 'feedbackType',
      render: (text: string) => {
        const colorMap: Record<string, string> = {
          bug: 'red',
          feature: 'blue',
          improvement: 'green',
          performance: 'orange',
          usability: 'purple',
          other: 'default'
        };
        return <Tag color={colorMap[text] || 'default'}>{text}</Tag>;
      }},
    {
      title: t('feedback.analytics.table.satisfaction'),
      dataIndex: 'satisfaction',
      key: 'satisfaction',
      render: (rating: number) => rating ? '⭐'.repeat(rating) : '-'},
    {
      title: t('feedback.analytics.table.date'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString()}
  ];

  // 准备饼图数据
  const preparePieData = (data: Record<string, number>) => {
    return Object.entries(data).map(([name, value]) => ({ name, value }));
  };

  // 渲染过滤器
  const renderFilters = () => (
    <div className="feedback-filters">
      <Space wrap>
        <Select
          placeholder={t('feedback.analytics.filter.type')}
          style={{ width: 150 }}
          allowClear
          value={filters.type}
          onChange={(value) => handleFilterChange('type', value)}
        >
          <Option value="animation">{t('feedback.type.animation')}</Option>
          <Option value="physics">{t('feedback.type.physics')}</Option>
          <Option value="rendering">{t('feedback.type.rendering')}</Option>
          <Option value="editor">{t('feedback.type.editor')}</Option>
          <Option value="general">{t('feedback.type.general')}</Option>
        </Select>

        {filters.type === 'animation' && (
          <Select
            placeholder={t('feedback.analytics.filter.subType')}
            style={{ width: 150 }}
            allowClear
            value={filters.subType}
            onChange={(value) => handleFilterChange('subType', value)}
          >
            <Option value="blend">{t('feedback.animation.subType.blend')}</Option>
            <Option value="stateMachine">{t('feedback.animation.subType.stateMachine')}</Option>
            <Option value="timeline">{t('feedback.animation.subType.timeline')}</Option>
            <Option value="retargeting">{t('feedback.animation.subType.retargeting')}</Option>
          </Select>
        )}

        <Select
          placeholder={t('feedback.analytics.filter.feedbackType')}
          style={{ width: 150 }}
          allowClear
          value={filters.feedbackType}
          onChange={(value) => handleFilterChange('feedbackType', value)}
        >
          <Option value="bug">{t('feedback.type.bug')}</Option>
          <Option value="feature">{t('feedback.type.feature')}</Option>
          <Option value="improvement">{t('feedback.type.improvement')}</Option>
          <Option value="performance">{t('feedback.type.performance')}</Option>
          <Option value="usability">{t('feedback.type.usability')}</Option>
          <Option value="other">{t('feedback.type.other')}</Option>
        </Select>

        <RangePicker
          placeholder={[
            t('feedback.analytics.filter.startDate'),
            t('feedback.analytics.filter.endDate')
          ]}
          value={filters.dateRange}
          onChange={(dates) => handleFilterChange('dateRange', dates)}
        />

        <Button
          icon={<ReloadOutlined />}
          onClick={loadFeedbackData}
        >
          {t('feedback.analytics.refresh')}
        </Button>

        <Button
          icon={<FilterOutlined />}
          onClick={resetFilters}
        >
          {t('feedback.analytics.resetFilters')}
        </Button>
      </Space>
    </div>
  );

  // 渲染统计卡片
  const renderStatCards = () => {
    if (!stats) return null;

    return (
      <Row gutter={[16, 16]} className="stat-cards">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t('feedback.analytics.stats.total')}
              value={stats.total}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t('feedback.analytics.stats.bugs')}
              value={stats.byFeedbackType.bug || 0}
              prefix={<BugOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t('feedback.analytics.stats.features')}
              value={stats.byFeedbackType.feature || 0}
              prefix={<RocketOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t('feedback.analytics.stats.satisfaction')}
              value={stats.averageSatisfaction.toFixed(1)}
              prefix={<StarOutlined />}
              suffix="/ 5"
              precision={1}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染图表
  const renderCharts = () => {
    if (!stats) return null;

    return (
      <Row gutter={[16, 16]} className="charts">
        <Col xs={24} md={12}>
          <Card title={t('feedback.analytics.charts.byType')}>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={preparePieData(stats.byFeedbackType)}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {preparePieData(stats.byFeedbackType).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Legend />
                <RechartsTooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title={t('feedback.analytics.charts.bySubType')}>
            {Object.keys(stats.bySubType).length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={preparePieData(stats.bySubType)}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Bar dataKey="value" fill="#8884d8">
                    {preparePieData(stats.bySubType).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <Empty description={t('feedback.analytics.noSubTypeData')} />
            )}
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <div className="loading-container">
          <Spin size="large" />
          <p>{t('feedback.analytics.loading')}</p>
        </div>
      );
    }

    if (error) {
      return (
        <Alert
          message={t('feedback.analytics.error')}
          description={error}
          type="error"
          showIcon
        />
      );
    }

    if (!stats || feedbackList.length === 0) {
      return (
        <Empty
          description={t('feedback.analytics.noData')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <Tabs defaultActiveKey="overview">
        <TabPane
          tab={
            <span>
              <StarOutlined />
              {t('feedback.analytics.tabs.overview')}
            </span>
          }
          key="overview"
        >
          {renderStatCards()}
          {renderCharts()}
        </TabPane>
        <TabPane
          tab={
            <span>
              <ClockCircleOutlined />
              {t('feedback.analytics.tabs.recent')}
            </span>
          }
          key="recent"
        >
          <Table
            dataSource={feedbackList}
            columns={columns}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        </TabPane>
      </Tabs>
    );
  };

  return (
    <div className="feedback-analytics">
      <div className="feedback-analytics-header">
        <h2>{t('feedback.analytics.title')}</h2>
        {renderFilters()}
      </div>
      <div className="feedback-analytics-content">
        {renderContent()}
      </div>
    </div>
  );
};

export default FeedbackAnalytics;
