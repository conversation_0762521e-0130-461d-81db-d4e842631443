<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="500" xmlns="http://www.w3.org/2000/svg">
  <style>
    .panel {
      fill: #f5f5f5;
      stroke: #ddd;
      stroke-width: 1;
    }
    .header {
      fill: #e0e0e0;
      stroke: #ddd;
      stroke-width: 1;
    }
    .title {
      font-family: 'Arial', sans-serif;
      font-size: 16px;
      font-weight: bold;
    }
    .label {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
    }
    .code {
      font-family: 'Consolas', monospace;
      font-size: 14px;
    }
    .keyword {
      fill: #0000ff;
    }
    .string {
      fill: #a31515;
    }
    .comment {
      fill: #008000;
    }
    .type {
      fill: #267f99;
    }
    .function {
      fill: #795e26;
    }
    .line-number {
      font-family: 'Consolas', monospace;
      font-size: 14px;
      fill: #888;
    }
    .line-highlight {
      fill: #f8f8d0;
    }
    .gutter {
      fill: #f0f0f0;
      stroke: #ddd;
      stroke-width: 1;
    }
    .toolbar {
      fill: #e8e8e8;
      stroke: #ddd;
      stroke-width: 1;
    }
    .button {
      fill: #f0f0f0;
      stroke: #ccc;
      stroke-width: 1;
      rx: 4;
      ry: 4;
    }
    .button-text {
      font-family: 'Arial', sans-serif;
      font-size: 12px;
      fill: #333;
      text-anchor: middle;
      dominant-baseline: middle;
    }
    .tab {
      fill: #e0e0e0;
      stroke: #ccc;
      stroke-width: 1;
      rx: 4 4 0 0;
      ry: 4 4 0 0;
    }
    .tab-active {
      fill: #f5f5f5;
      stroke: #ccc;
      stroke-width: 1;
      rx: 4 4 0 0;
      ry: 4 4 0 0;
    }
    .tab-text {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
      fill: #333;
      text-anchor: middle;
      dominant-baseline: middle;
    }
  </style>
  
  <!-- Editor Panel -->
  <rect x="50" y="50" width="700" height="400" class="panel" rx="5" ry="5" />
  
  <!-- Tabs -->
  <rect x="50" y="50" width="700" height="30" class="header" rx="5 5 0 0" ry="5 5 0 0" />
  <rect x="60" y="50" width="150" height="30" class="tab-active" />
  <text x="135" y="65" class="tab-text">PlayerController.ts</text>
  <rect x="220" y="50" width="150" height="30" class="tab" />
  <text x="295" y="65" class="tab-text">EnemyAI.ts</text>
  <rect x="380" y="50" width="150" height="30" class="tab" />
  <text x="455" y="65" class="tab-text">GameManager.ts</text>
  
  <!-- Toolbar -->
  <rect x="50" y="80" width="700" height="30" class="toolbar" />
  <rect x="60" y="85" width="80" height="20" class="button" />
  <text x="100" y="95" class="button-text">保存</text>
  <rect x="150" y="85" width="80" height="20" class="button" />
  <text x="190" y="95" class="button-text">运行</text>
  <rect x="240" y="85" width="80" height="20" class="button" />
  <text x="280" y="95" class="button-text">调试</text>
  <rect x="330" y="85" width="80" height="20" class="button" />
  <text x="370" y="95" class="button-text">格式化</text>
  
  <!-- Line Numbers -->
  <rect x="50" y="110" width="30" height="340" class="gutter" />
  <text x="65" y="130" class="line-number">1</text>
  <text x="65" y="150" class="line-number">2</text>
  <text x="65" y="170" class="line-number">3</text>
  <text x="65" y="190" class="line-number">4</text>
  <text x="65" y="210" class="line-number">5</text>
  <text x="65" y="230" class="line-number">6</text>
  <text x="65" y="250" class="line-number">7</text>
  <text x="65" y="270" class="line-number">8</text>
  <text x="65" y="290" class="line-number">9</text>
  <text x="65" y="310" class="line-number">10</text>
  <text x="65" y="330" class="line-number">11</text>
  <text x="65" y="350" class="line-number">12</text>
  <text x="65" y="370" class="line-number">13</text>
  <text x="65" y="390" class="line-number">14</text>
  <text x="65" y="410" class="line-number">15</text>
  <text x="65" y="430" class="line-number">16</text>
  
  <!-- Code Editor -->
  <rect x="80" y="210" width="670" height="20" class="line-highlight" />
  
  <!-- Code Content -->
  <text x="90" y="130" class="code"><tspan class="keyword">import</tspan> { Script, Vector3, Input } <tspan class="keyword">from</tspan> <tspan class="string">'dl-engine'</tspan>;</text>
  <text x="90" y="150" class="code"></text>
  <text x="90" y="170" class="code"><tspan class="comment">/**</tspan></text>
  <text x="90" y="190" class="code"><tspan class="comment"> * 玩家控制器脚本，处理玩家输入和移动</tspan></text>
  <text x="90" y="210" class="code"><tspan class="comment"> */</tspan></text>
  <text x="90" y="230" class="code"><tspan class="keyword">export</tspan> <tspan class="keyword">class</tspan> <tspan class="type">PlayerController</tspan> <tspan class="keyword">extends</tspan> <tspan class="type">Script</tspan> {</text>
  <text x="90" y="250" class="code">    <tspan class="keyword">public</tspan> moveSpeed: <tspan class="type">number</tspan> = 5;</text>
  <text x="90" y="270" class="code">    <tspan class="keyword">public</tspan> rotateSpeed: <tspan class="type">number</tspan> = 60;</text>
  <text x="90" y="290" class="code"></text>
  <text x="90" y="310" class="code">    <tspan class="function">onUpdate</tspan>(deltaTime: <tspan class="type">number</tspan>): <tspan class="type">void</tspan> {</text>
  <text x="90" y="330" class="code">        <tspan class="keyword">const</tspan> horizontal = Input.<tspan class="function">getAxis</tspan>(<tspan class="string">'Horizontal'</tspan>);</text>
  <text x="90" y="350" class="code">        <tspan class="keyword">const</tspan> vertical = Input.<tspan class="function">getAxis</tspan>(<tspan class="string">'Vertical'</tspan>);</text>
  <text x="90" y="370" class="code">        </text>
  <text x="90" y="390" class="code">        <tspan class="keyword">const</tspan> moveDirection = <tspan class="keyword">new</tspan> <tspan class="type">Vector3</tspan>(horizontal, 0, vertical);</text>
  <text x="90" y="410" class="code">        <tspan class="keyword">this</tspan>.entity.transform.<tspan class="function">translate</tspan>(moveDirection.<tspan class="function">normalize</tspan>().<tspan class="function">multiplyScalar</tspan>(<tspan class="keyword">this</tspan>.moveSpeed * deltaTime));</text>
  <text x="90" y="430" class="code">    }</text>
  
  <!-- Cursor -->
  <rect x="90" y="209" width="2" height="20" fill="#000" />
</svg>
