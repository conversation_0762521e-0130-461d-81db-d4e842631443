/**
 * 递归合并服务测试文件
 * 用于验证修复后的递归合并服务是否正常工作
 */
import { recursiveMergeService, MergeStrategy } from './RecursiveMergeService';

// 测试递归合并服务
function testRecursiveMergeService() {
  console.log('开始测试递归合并服务...');

  try {
    // 测试基本对象合并
    const local1 = {
      name: 'Local Object',
      value: 100,
      settings: {
        enabled: true,
        theme: 'dark'
      }
    };

    const remote1 = {
      name: 'Remote Object',
      value: 200,
      settings: {
        enabled: false,
        language: 'zh-CN'
      }
    };

    console.log('=== 测试深度合并策略 ===');
    const deepMergeResult = recursiveMergeService.mergeObjects(local1, remote1, {
      strategy: MergeStrategy.DEEP_MERGE
    });

    console.log('✅ 深度合并结果:', JSON.stringify(deepMergeResult.merged, null, 2));
    console.log('✅ 冲突数量:', deepMergeResult.conflicts.length);
    console.log('✅ 合并成功:', deepMergeResult.success);

    // 测试优先本地策略
    console.log('\n=== 测试优先本地策略 ===');
    const localPreferResult = recursiveMergeService.mergeObjects(local1, remote1, {
      strategy: MergeStrategy.PREFER_LOCAL
    });

    console.log('✅ 优先本地结果:', JSON.stringify(localPreferResult.merged, null, 2));
    console.log('✅ 冲突数量:', localPreferResult.conflicts.length);

    // 测试优先远程策略
    console.log('\n=== 测试优先远程策略 ===');
    const remotePreferResult = recursiveMergeService.mergeObjects(local1, remote1, {
      strategy: MergeStrategy.PREFER_REMOTE
    });

    console.log('✅ 优先远程结果:', JSON.stringify(remotePreferResult.merged, null, 2));
    console.log('✅ 冲突数量:', remotePreferResult.conflicts.length);

    // 测试自定义合并函数
    console.log('\n=== 测试自定义合并策略 ===');
    const customMergeResult = recursiveMergeService.mergeObjects(local1, remote1, {
      strategy: MergeStrategy.CUSTOM,
      customMergeFn: (localValue, remoteValue, path) => {
        // 对于数值类型，取平均值
        if (typeof localValue === 'number' && typeof remoteValue === 'number') {
          return (localValue + remoteValue) / 2;
        }
        // 对于字符串，连接起来
        if (typeof localValue === 'string' && typeof remoteValue === 'string') {
          return `${localValue} & ${remoteValue}`;
        }
        // 其他情况优先使用远程值
        return remoteValue;
      }
    });

    console.log('✅ 自定义合并结果:', JSON.stringify(customMergeResult.merged, null, 2));
    console.log('✅ 冲突数量:', customMergeResult.conflicts.length);

    // 测试数组合并
    console.log('\n=== 测试数组合并 ===');
    const localArray = {
      items: [1, 2, 3],
      tags: ['local', 'test']
    };

    const remoteArray = {
      items: [4, 5, 6],
      tags: ['remote', 'test']
    };

    const arrayMergeResult = recursiveMergeService.mergeObjects(localArray, remoteArray, {
      strategy: MergeStrategy.DEEP_MERGE
    });

    console.log('✅ 数组合并结果:', JSON.stringify(arrayMergeResult.merged, null, 2));
    console.log('✅ 数组合并冲突:', arrayMergeResult.conflicts.length);

    // 测试合并预览
    console.log('\n=== 测试合并预览 ===');
    const preview = recursiveMergeService.previewMerge(local1, remote1, {
      strategy: MergeStrategy.DEEP_MERGE
    });

    console.log('✅ 可以合并:', preview.canMerge);
    console.log('✅ 冲突数量:', preview.conflictCount);
    console.log('✅ 冲突路径:', preview.conflictPaths);

    // 测试合并能力检查
    console.log('\n=== 测试合并能力检查 ===');
    const canMergeObjects = recursiveMergeService.canMerge(local1, remote1);
    const canMergeNumbers = recursiveMergeService.canMerge(123, 456);
    const canMergeStrings = recursiveMergeService.canMerge('hello', 'world');
    const canMergeDifferentTypes = recursiveMergeService.canMerge('hello', 123);

    console.log('✅ 对象可以合并:', canMergeObjects);
    console.log('✅ 数字可以合并:', canMergeNumbers);
    console.log('✅ 字符串可以合并:', canMergeStrings);
    console.log('✅ 不同类型可以合并:', canMergeDifferentTypes);

    // 测试冲突解决
    console.log('\n=== 测试冲突解决 ===');
    const conflictResult = recursiveMergeService.mergeObjects(local1, remote1, {
      strategy: MergeStrategy.PREFER_LOCAL
    });

    if (conflictResult.conflicts.length > 0) {
      const resolvedConflicts = recursiveMergeService.resolveConflictAtPath(
        conflictResult.conflicts,
        ['name'],
        'Resolved Name'
      );

      const finalResult = recursiveMergeService.applyConflictResolutions(
        conflictResult.merged,
        resolvedConflicts
      );

      console.log('✅ 冲突解决后结果:', JSON.stringify(finalResult, null, 2));
    }

    // 测试合并统计
    console.log('\n=== 测试合并统计 ===');
    const statistics = recursiveMergeService.getMergeStatistics(deepMergeResult);
    console.log('✅ 合并统计:', {
      总冲突数: statistics.totalConflicts,
      按策略分组: statistics.conflictsByStrategy,
      平均冲突深度: statistics.averageConflictDepth.toFixed(2)
    });

    console.log('\n🎉 递归合并服务测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 导出测试函数
export { testRecursiveMergeService };

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testRecursiveMergeService();
}
