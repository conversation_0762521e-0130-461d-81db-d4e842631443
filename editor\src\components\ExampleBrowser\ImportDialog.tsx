/**
 * 导入对话框组件
 * 用于设置示例项目导入的选项
 */
import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Button, Checkbox, Radio, Tooltip, Alert } from 'antd';
import { QuestionCircleOutlined, FolderOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Example } from '../../types/example';
import { fetchWorkspaces } from '../../services/workspaceService';
import './ImportDialog.less';

const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { Group } = Radio;

interface ImportDialogProps {
  visible: boolean;
  example: Example | null;
  onCancel: () => void;
  onConfirm: (projectName: string, location: string) => void;
}

/**
 * 导入对话框组件
 */
const ImportDialog: React.FC<ImportDialogProps> = ({
  visible,
  example,
  onCancel,
  onConfirm}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [workspaces, setWorkspaces] = useState<{ id: string; name: string; path: string }[]>([]);
  const [selectedWorkspace, setSelectedWorkspace] = useState<string>('');
  const [importOption, setImportOption] = useState<string>('copy');
  const [includeAssets, setIncludeAssets] = useState<boolean>(true);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  /**
   * 加载工作空间列表
   */
  useEffect(() => {
    const loadWorkspaces = async () => {
      try {
        const data = await fetchWorkspaces();
        setWorkspaces(data);
        if (data.length > 0) {
          setSelectedWorkspace(data[0].id);
          form.setFieldsValue({ workspace: data[0].id });
        }
      } catch (error) {
        console.error('加载工作空间失败:', error);
      }
    };

    if (visible) {
      loadWorkspaces();
    }
  }, [visible, form]);

  /**
   * 设置表单初始值
   */
  useEffect(() => {
    if (visible && example) {
      form.setFieldsValue({
        projectName: example.title,
        workspace: selectedWorkspace,
        importOption: importOption,
        includeAssets: includeAssets});
    }
  }, [visible, example, form, selectedWorkspace, importOption, includeAssets]);

  /**
   * 处理工作空间变更
   * @param value 工作空间ID
   */
  const handleWorkspaceChange = (value: string) => {
    setSelectedWorkspace(value);
  };

  /**
   * 处理导入选项变更
   * @param e 事件对象
   */
  const handleImportOptionChange = (e: any) => {
    setImportOption(e.target.value);
  };

  /**
   * 处理包含资源变更
   * @param e 事件对象
   */
  const handleIncludeAssetsChange = (e: any) => {
    setIncludeAssets(e.target.checked);
  };

  /**
   * 处理确认
   */
  const handleOk = async () => {
    try {
      setConfirmLoading(true);
      const values = await form.validateFields();
      onConfirm(values.projectName, values.workspace);
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setConfirmLoading(false);
    }
  };

  /**
   * 处理取消
   */
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  /**
   * 处理浏览文件夹
   */
  const handleBrowseFolder = () => {
    // TODO: 实现浏览文件夹功能
    console.log('浏览文件夹');
  };

  return (
    <Modal
      title={t('importDialog.title')}
      visible={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          {t('importDialog.cancel')}
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={confirmLoading}
          onClick={handleOk}
        >
          {t('importDialog.import')}
        </Button>,
      ]}
      className="import-dialog"
    >
      {example && (
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            projectName: example.title,
            workspace: selectedWorkspace,
            importOption: importOption,
            includeAssets: includeAssets}}
        >
          <Alert
            message={t('importDialog.notice')}
            description={t('importDialog.noticeDescription')}
            type="info"
            showIcon
            className="import-alert"
          />

          <Form.Item
            name="projectName"
            label={t('importDialog.projectName')}
            rules={[
              {
                required: true,
                message: t('importDialog.projectNameRequired')},
            ]}
          >
            <Input placeholder={t('importDialog.projectNamePlaceholder')} />
          </Form.Item>

          <Form.Item
            name="workspace"
            label={
              <span>
                {t('importDialog.workspace')}{' '}
                <Tooltip title={t('importDialog.workspaceTooltip')}>
                  <QuestionCircleOutlined />
                </Tooltip>
              </span>
            }
            rules={[
              {
                required: true,
                message: t('importDialog.workspaceRequired')},
            ]}
          >
            <Select
              placeholder={t('importDialog.selectWorkspace')}
              onChange={handleWorkspaceChange}
              dropdownRender={menu => (
                <div>
                  {menu}
                  <div className="workspace-dropdown-footer">
                    <Button
                      type="link"
                      icon={<FolderOutlined />}
                      onClick={handleBrowseFolder}
                    >
                      {t('importDialog.browseFolder')}
                    </Button>
                  </div>
                </div>
              )}
            >
              {workspaces.map(workspace => (
                <Option key={workspace.id} value={workspace.id}>
                  {workspace.name} ({workspace.path})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="importOption"
            label={t('importDialog.importOption')}
          >
            <Group onChange={handleImportOptionChange} value={importOption}>
              <Radio value="copy">
                {t('importDialog.copyOption')}
                <div className="option-description">
                  {t('importDialog.copyDescription')}
                </div>
              </Radio>
              <Radio value="reference">
                {t('importDialog.referenceOption')}
                <div className="option-description">
                  {t('importDialog.referenceDescription')}
                </div>
              </Radio>
            </Group>
          </Form.Item>

          <Form.Item
            name="includeAssets"
            valuePropName="checked"
          >
            <Checkbox onChange={handleIncludeAssetsChange} checked={includeAssets}>
              {t('importDialog.includeAssets')}
              <div className="option-description">
                {t('importDialog.includeAssetsDescription')}
              </div>
            </Checkbox>
          </Form.Item>
        </Form>
      )}
    </Modal>
  );
};

export default ImportDialog;
