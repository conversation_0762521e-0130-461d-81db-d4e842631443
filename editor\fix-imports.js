/**
 * 批量修复导入错误的脚本
 */
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 需要修复的导入模式
const importFixes = [
  // 引擎导入修复
  {
    pattern: /import\s+{[^}]*}\s+from\s+['"][^'"]*engine\/src\/[^'"]*['"];?/g,
    replacement: '// 移除引擎直接导入'
  },
  {
    pattern: /import\s+\*\s+as\s+\w+\s+from\s+['"][^'"]*engine\/src\/[^'"]*['"];?/g,
    replacement: '// 移除引擎直接导入'
  },
  {
    pattern: /import\s+\w+\s+from\s+['"][^'"]*engine\/src\/[^'"]*['"];?/g,
    replacement: '// 移除引擎直接导入'
  },
  
  // 实体 slice 导入修复
  {
    pattern: /import\s+{[^}]*updateEntity[^}]*}\s+from\s+['"][^'"]*entities\/entitiesSlice['"];?/g,
    replacement: '// 移除实体 slice 导入'
  },
  
  // rootReducer 导入修复
  {
    pattern: /from\s+['"][^'"]*store\/rootReducer['"];?/g,
    replacement: "from '../store';"
  },
  
  // 常见的错误导入路径修复
  {
    pattern: /from\s+['"]\.\.\/\.\.\/\.\.\/engine\/src\/[^'"]*['"];?/g,
    replacement: "// 移除引擎导入"
  }
];

// 需要添加的本地类型定义
const typeDefinitions = {
  'BlendCurveType': `
// 定义本地的混合曲线类型枚举
enum BlendCurveType {
  LINEAR = 'linear',
  SMOOTH = 'smooth',
  EASE_IN = 'ease_in',
  EASE_OUT = 'ease_out',
  EASE_IN_OUT = 'ease_in_out',
  CUSTOM = 'custom'
}`,
  
  'AnimationBlendMode': `
// 定义本地的动画混合模式枚举
enum AnimationBlendMode {
  OVERRIDE = 'override',
  ADDITIVE = 'additive',
  MULTIPLY = 'multiply',
  SCREEN = 'screen',
  OVERLAY = 'overlay'
}`,

  'PhysicsBodyType': `
// 定义本地的物理体类型枚举
enum PhysicsBodyType {
  STATIC = 'static',
  DYNAMIC = 'dynamic',
  KINEMATIC = 'kinematic'
}`
};

// 递归获取所有 TypeScript/JavaScript 文件
function getAllFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 跳过 node_modules 和其他不需要的目录
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          traverse(fullPath);
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// 修复单个文件
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 应用导入修复
    for (const fix of importFixes) {
      const newContent = content.replace(fix.pattern, fix.replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    // 检查是否需要添加类型定义
    for (const [typeName, definition] of Object.entries(typeDefinitions)) {
      // 如果文件中使用了这个类型但没有定义，则添加定义
      const typeRegex = new RegExp(`\\b${typeName}\\b`, 'g');
      const definitionRegex = new RegExp(`enum\\s+${typeName}`, 'g');
      
      if (typeRegex.test(content) && !definitionRegex.test(content)) {
        // 在导入语句后添加类型定义
        const importEndIndex = content.lastIndexOf('import ');
        if (importEndIndex !== -1) {
          const nextLineIndex = content.indexOf('\n', importEndIndex);
          if (nextLineIndex !== -1) {
            content = content.slice(0, nextLineIndex + 1) + 
                     definition + '\n' + 
                     content.slice(nextLineIndex + 1);
            modified = true;
          }
        }
      }
    }
    
    // 如果文件被修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`修复: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`修复文件失败 ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src 目录不存在');
    return;
  }
  
  console.log('开始修复导入错误...');
  
  const files = getAllFiles(srcDir);
  let fixedCount = 0;
  
  for (const file of files) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`修复完成! 共修复 ${fixedCount} 个文件`);
}

// 运行脚本
main();

export { fixFile, getAllFiles };
