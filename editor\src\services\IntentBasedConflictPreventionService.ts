/**
 * 基于意图的冲突预测服务
 * 实现基于用户编辑意图的冲突预测系统，减少冲突发生
 */
import { EventEmitter } from '../utils/EventEmitter';
import { message } from 'antd';
import { store } from '../store';
import {
  addPredictedConflict,
  removePredictedConflict,
  clearPredictedConflicts
} from '../store/collaboration/conflictPredictionSlice';
import {
  Operation,
  OperationType,
  collaborationService
} from './CollaborationService';
import { ConflictType } from './ConflictResolutionService';
import { conflictPreventionService, EditingZone, EditingZoneType } from './ConflictPreventionService';

/**
 * 编辑意图类型枚举
 */
export enum EditingIntentType {
  TRANSFORM = 'transform',           // 变换（位置、旋转、缩放）
  PROPERTY_CHANGE = 'property_change', // 属性修改
  COMPONENT_EDIT = 'component_edit',   // 组件编辑
  ENTITY_CREATION = 'entity_creation', // 实体创建
  ENTITY_DELETION = 'entity_deletion', // 实体删除
  SCENE_STRUCTURE = 'scene_structure', // 场景结构修改
  RESOURCE_EDIT = 'resource_edit',     // 资源编辑
  UNKNOWN = 'unknown'                 // 未知意图
}

/**
 * 编辑意图接口
 */
export interface EditingIntent {
  id: string;                        // 意图ID
  userId: string;                    // 用户ID
  userName: string;                  // 用户名
  type: EditingIntentType;           // 意图类型
  entityId?: string;                 // 实体ID
  componentId?: string;              // 组件ID
  propertyPath?: string[];           // 属性路径
  resourceId?: string;               // 资源ID
  startTime: number;                 // 开始时间
  lastUpdateTime: number;            // 最后更新时间
  predictedEndTime?: number;         // 预测结束时间
  confidence: number;                // 置信度
  relatedZones: string[];            // 相关编辑区域
  relatedOperations: string[];       // 相关操作
  editingPattern?: string;           // 编辑模式
  editingFrequency?: number;         // 编辑频率
  editingDuration?: number;          // 编辑持续时间
  previousIntents?: string[];        // 之前的意图
  predictedNextAction?: EditingIntentType; // 预测的下一个动作
}

/**
 * 预测冲突接口
 */
export interface PredictedConflict {
  id: string;                        // 冲突ID
  type: ConflictType;                // 冲突类型
  userA: {                           // 用户A
    id: string;
    name: string;
    intent: EditingIntent;
  };
  userB: {                           // 用户B
    id: string;
    name: string;
    intent: EditingIntent;
  };
  entityId?: string;                 // 实体ID
  componentId?: string;              // 组件ID
  propertyPath?: string[];           // 属性路径
  resourceId?: string;               // 资源ID
  probability: number;               // 冲突概率
  createdAt: number;                 // 创建时间
  expiresAt: number;                 // 过期时间
  resolved: boolean;                 // 是否已解决
  resolvedAt?: number;               // 解决时间
}

/**
 * 基于意图的冲突预测服务类
 */
class IntentBasedConflictPreventionService extends EventEmitter {
  private enabled: boolean = false;
  private currentUserId: string = '';
  private intents: Map<string, EditingIntent> = new Map();
  private predictedConflicts: Map<string, PredictedConflict> = new Map();
  private operationBuffer: Operation[] = [];
  private maxOperationBufferSize: number = 100;
  private intentExpirationTime: number = 30000; // 30秒后过期
  private conflictPredictionThreshold: number = 0.7; // 冲突预测阈值
  private cleanupInterval: number | null = null;
  private intentDetectionInterval: number | null = null;
  private patternAnalysisInterval: number | null = null;

  // 意图检测配置
  private intentDetectionConfig = {
    minOperationsForIntent: 2,       // 检测意图的最小操作数
    maxTimeBetweenOperations: 5000,  // 操作之间的最大时间间隔
    intentConfidenceThreshold: 0.6,  // 意图置信度阈值
    intentDetectionInterval: 2000,   // 意图检测间隔
    intentExpirationTime: 30000,     // 意图过期时间
    conflictPredictionInterval: 3000, // 冲突预测间隔
    conflictExpirationTime: 60000,   // 冲突过期时间
    notificationThreshold: 0.8,      // 通知阈值
    patternRecognitionEnabled: true, // 是否启用模式识别
    userHistoryLength: 20,           // 用户历史记录长度
    predictiveAnalysisEnabled: true, // 是否启用预测分析
    adaptiveThresholdEnabled: true,  // 是否启用自适应阈值
    minEditingFrequency: 0.1,        // 最小编辑频率
    maxEditingFrequency: 10,         // 最大编辑频率
    behaviorAnalysisWeight: 0.3      // 行为分析权重
  };

  // 用户编辑历史
  private userEditingHistory: Map<string, {
    intents: string[],
    patterns: Map<string, number>,
    averageEditingDuration: number,
    averageEditingFrequency: number,
    lastEditTime: number
  }> = new Map();

  constructor() {
    super();
  }

  /**
   * 获取用户名
   * @private
   */
  private getUserName(userId: string): string {
    // 尝试从协作服务获取用户信息
    const user = collaborationService.getUser(userId);
    return user?.name || userId;
  }

  /**
   * 初始化服务
   * @param userId 当前用户ID
   * @param _userName 当前用户名（暂未使用）
   */
  public initialize(userId: string, _userName: string): void {
    this.currentUserId = userId;
    // userName 参数暂时不存储，可以通过 getUserName 方法获取

    // 监听协作服务事件
    collaborationService.on('operation', this.handleOperation.bind(this));
    collaborationService.on('userLeft', this.handleUserLeft.bind(this));

    // 监听编辑区域事件
    conflictPreventionService.on('editingZoneCreated', this.handleEditingZoneCreated.bind(this));
    conflictPreventionService.on('editingZoneUpdated', this.handleEditingZoneUpdated.bind(this));
    conflictPreventionService.on('editingZoneRemoved', this.handleEditingZoneRemoved.bind(this));

    // 初始化当前用户的编辑历史
    this.initializeUserEditingHistory(userId);

    // 启动定时器
    this.startCleanupTimer();
    this.startIntentDetectionTimer();
    this.startPatternAnalysisTimer();
  }

  /**
   * 初始化用户编辑历史
   * @private
   */
  private initializeUserEditingHistory(userId: string): void {
    if (!this.userEditingHistory.has(userId)) {
      this.userEditingHistory.set(userId, {
        intents: [],
        patterns: new Map<string, number>(),
        averageEditingDuration: 0,
        averageEditingFrequency: 0,
        lastEditTime: Date.now()
      });
    }
  }

  /**
   * 设置是否启用冲突预测
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;

    if (!enabled) {
      this.clearAllIntents();
      this.clearAllPredictedConflicts();
      this.stopTimers();
    } else {
      this.startCleanupTimer();
      this.startIntentDetectionTimer();
    }
  }

  /**
   * 设置意图过期时间
   * @param timeMs 过期时间（毫秒）
   */
  public setIntentExpirationTime(timeMs: number): void {
    this.intentExpirationTime = timeMs;
    this.intentDetectionConfig.intentExpirationTime = timeMs;
  }

  /**
   * 设置冲突预测阈值
   * @param threshold 阈值（0-1）
   */
  public setConflictPredictionThreshold(threshold: number): void {
    this.conflictPredictionThreshold = Math.max(0, Math.min(1, threshold));
  }

  /**
   * 获取所有编辑意图
   * @returns 编辑意图数组
   */
  public getAllIntents(): EditingIntent[] {
    return Array.from(this.intents.values());
  }

  /**
   * 获取所有预测冲突
   * @returns 预测冲突数组
   */
  public getAllPredictedConflicts(): PredictedConflict[] {
    return Array.from(this.predictedConflicts.values());
  }

  /**
   * 获取用户的编辑意图
   * @param userId 用户ID
   * @returns 编辑意图数组
   */
  public getUserIntents(userId: string): EditingIntent[] {
    return Array.from(this.intents.values()).filter(intent => intent.userId === userId);
  }

  /**
   * 获取实体的编辑意图
   * @param entityId 实体ID
   * @returns 编辑意图数组
   */
  public getEntityIntents(entityId: string): EditingIntent[] {
    return Array.from(this.intents.values()).filter(intent => intent.entityId === entityId);
  }

  /**
   * 清除所有编辑意图
   */
  public clearAllIntents(): void {
    this.intents.clear();
  }

  /**
   * 清除所有预测冲突
   */
  public clearAllPredictedConflicts(): void {
    this.predictedConflicts.clear();
    store.dispatch(clearPredictedConflicts());
  }

  /**
   * 手动添加编辑意图
   * @param intent 编辑意图
   */
  public addIntent(intent: Omit<EditingIntent, 'id' | 'startTime' | 'lastUpdateTime' | 'confidence' | 'relatedZones' | 'relatedOperations'>): void {
    if (!this.enabled) return;

    const id = this.generateIntentId(intent.userId, intent.type, {
      entityId: intent.entityId,
      componentId: intent.componentId,
      propertyPath: intent.propertyPath,
      resourceId: intent.resourceId
    });

    const newIntent: EditingIntent = {
      id,
      ...intent,
      startTime: Date.now(),
      lastUpdateTime: Date.now(),
      confidence: 1.0,
      relatedZones: [],
      relatedOperations: []
    };

    this.intents.set(id, newIntent);
    this.emit('intentCreated', newIntent);

    // 预测冲突
    this.predictConflicts(newIntent);
  }

  /**
   * 处理操作
   * @private
   */
  private handleOperation(operation: Operation): void {
    if (!this.enabled) return;

    // 添加到操作缓冲区
    this.operationBuffer.push(operation);

    // 限制缓冲区大小
    if (this.operationBuffer.length > this.maxOperationBufferSize) {
      this.operationBuffer = this.operationBuffer.slice(-this.maxOperationBufferSize);
    }

    // 尝试检测意图
    this.detectIntentFromOperation(operation);
  }

  /**
   * 处理用户离开
   * @private
   */
  private handleUserLeft(userId: string): void {
    // 移除该用户的所有意图
    const intentsToRemove: string[] = [];

    this.intents.forEach((intent, id) => {
      if (intent.userId === userId) {
        intentsToRemove.push(id);
      }
    });

    intentsToRemove.forEach(id => {
      this.intents.delete(id);
      this.emit('intentRemoved', id);
    });

    // 移除相关的预测冲突
    const conflictsToRemove: string[] = [];

    this.predictedConflicts.forEach((conflict, id) => {
      if (conflict.userA.id === userId || conflict.userB.id === userId) {
        conflictsToRemove.push(id);
      }
    });

    conflictsToRemove.forEach(id => {
      this.predictedConflicts.delete(id);
      store.dispatch(removePredictedConflict(id));
      this.emit('predictedConflictRemoved', id);
    });
  }

  /**
   * 处理编辑区域创建
   * @private
   */
  private handleEditingZoneCreated(zone: EditingZone): void {
    // 尝试从编辑区域检测意图
    this.detectIntentFromEditingZone(zone);
  }

  /**
   * 处理编辑区域更新
   * @private
   */
  private handleEditingZoneUpdated(zone: EditingZone): void {
    // 更新相关意图
    this.updateIntentFromEditingZone(zone);
  }

  /**
   * 处理编辑区域移除
   * @private
   */
  private handleEditingZoneRemoved(zoneId: string): void {
    // 更新相关意图
    this.removeZoneFromIntents(zoneId);
  }

  /**
   * 分析用户编辑模式
   * @private
   */
  private analyzeUserEditingPatterns(): void {
    if (!this.intentDetectionConfig.patternRecognitionEnabled) {
      return;
    }

    // 遍历所有意图，按用户分组
    const userIntents = new Map<string, EditingIntent[]>();

    this.intents.forEach(intent => {
      if (!userIntents.has(intent.userId)) {
        userIntents.set(intent.userId, []);
      }
      userIntents.get(intent.userId)!.push(intent);
    });

    // 分析每个用户的编辑模式
    userIntents.forEach((intents, userId) => {
      // 初始化用户历史（如果不存在）
      this.initializeUserEditingHistory(userId);
      const userHistory = this.userEditingHistory.get(userId)!;

      // 按时间排序意图
      intents.sort((a, b) => a.startTime - b.startTime);

      // 分析编辑频率
      if (intents.length >= 2) {
        const timeSpans: number[] = [];
        for (let i = 1; i < intents.length; i++) {
          timeSpans.push(intents[i].startTime - intents[i-1].startTime);
        }

        // 计算平均编辑间隔（毫秒）
        const avgTimeSpan = timeSpans.reduce((sum, span) => sum + span, 0) / timeSpans.length;

        // 计算编辑频率（每分钟操作数）
        const editingFrequency = avgTimeSpan > 0 ? 60000 / avgTimeSpan : 0;

        // 更新用户历史
        userHistory.averageEditingFrequency = editingFrequency;
      }

      // 分析编辑持续时间
      const activeDurations: number[] = [];
      let currentStart = 0;
      let lastTime = 0;

      for (let i = 0; i < intents.length; i++) {
        const intent = intents[i];

        if (currentStart === 0) {
          currentStart = intent.startTime;
          lastTime = intent.lastUpdateTime;
        } else if (intent.startTime - lastTime > this.intentDetectionConfig.maxTimeBetweenOperations) {
          // 如果间隔过长，认为是新的编辑会话
          activeDurations.push(lastTime - currentStart);
          currentStart = intent.startTime;
        }

        lastTime = Math.max(lastTime, intent.lastUpdateTime);
      }

      // 添加最后一个会话
      if (currentStart > 0 && lastTime > currentStart) {
        activeDurations.push(lastTime - currentStart);
      }

      // 计算平均编辑持续时间
      if (activeDurations.length > 0) {
        const avgDuration = activeDurations.reduce((sum, duration) => sum + duration, 0) / activeDurations.length;
        userHistory.averageEditingDuration = avgDuration;
      }

      // 分析编辑模式
      this.detectEditingPatterns(userId, intents);

      // 更新意图的编辑模式和频率
      intents.forEach(intent => {
        const updatedIntent = { ...intent };
        updatedIntent.editingFrequency = userHistory.averageEditingFrequency;
        updatedIntent.editingDuration = userHistory.averageEditingDuration;

        // 设置编辑模式
        const pattern = this.getPredominantPattern(userId);
        if (pattern) {
          updatedIntent.editingPattern = pattern;
        }

        // 预测下一个动作
        updatedIntent.predictedNextAction = this.predictNextAction(userId, intent);

        // 更新意图
        this.intents.set(intent.id, updatedIntent);
      });

      // 更新用户历史
      this.userEditingHistory.set(userId, userHistory);
    });
  }

  /**
   * 检测用户编辑模式
   * @private
   */
  private detectEditingPatterns(userId: string, intents: EditingIntent[]): void {
    if (intents.length < 3) {
      return;
    }

    const userHistory = this.userEditingHistory.get(userId);
    if (!userHistory) return;

    // 分析意图序列，寻找模式
    const patterns = new Map<string, number>();

    // 分析2-gram模式
    for (let i = 0; i < intents.length - 1; i++) {
      const pattern = `${intents[i].type}>${intents[i+1].type}`;
      const count = patterns.get(pattern) || 0;
      patterns.set(pattern, count + 1);
    }

    // 分析3-gram模式（如果有足够的意图）
    if (intents.length >= 3) {
      for (let i = 0; i < intents.length - 2; i++) {
        const pattern = `${intents[i].type}>${intents[i+1].type}>${intents[i+2].type}`;
        const count = patterns.get(pattern) || 0;
        patterns.set(pattern, count + 1);
      }
    }

    // 更新用户历史中的模式
    userHistory.patterns = patterns;

    // 更新用户历史中的意图序列
    userHistory.intents = intents.slice(-this.intentDetectionConfig.userHistoryLength).map(intent => intent.id);
  }

  /**
   * 获取用户的主要编辑模式
   * @private
   */
  private getPredominantPattern(userId: string): string | undefined {
    const userHistory = this.userEditingHistory.get(userId);
    if (!userHistory || userHistory.patterns.size === 0) {
      return undefined;
    }

    let predominantPattern: string | undefined = undefined;
    let maxCount = 0;

    userHistory.patterns.forEach((count, pattern) => {
      if (count > maxCount) {
        maxCount = count;
        predominantPattern = pattern;
      }
    });

    return predominantPattern;
  }

  /**
   * 预测用户的下一个动作
   * @private
   */
  private predictNextAction(userId: string, currentIntent: EditingIntent): EditingIntentType | undefined {
    const userHistory = this.userEditingHistory.get(userId);
    if (!userHistory || userHistory.patterns.size === 0) {
      return undefined;
    }

    // 查找以当前意图类型开头的模式
    const matchingPatterns: [string, number][] = [];

    userHistory.patterns.forEach((count, pattern) => {
      if (pattern.startsWith(`${currentIntent.type}>`)) {
        matchingPatterns.push([pattern, count]);
      }
    });

    if (matchingPatterns.length === 0) {
      return undefined;
    }

    // 按计数排序
    matchingPatterns.sort((a, b) => b[1] - a[1]);

    // 获取最可能的下一个动作
    const topPattern = matchingPatterns[0][0];
    const parts = topPattern.split('>');

    // 如果是2-gram模式，返回第二部分
    if (parts.length === 2) {
      return parts[1] as EditingIntentType;
    }

    // 如果是3-gram模式，返回第二部分（因为我们只关心下一个动作）
    if (parts.length === 3) {
      return parts[1] as EditingIntentType;
    }

    return undefined;
  }

  /**
   * 从操作检测意图
   * @private
   */
  private detectIntentFromOperation(operation: Operation): void {
    const { type, data, userId } = operation;

    // 获取用户名，如果没有则使用用户ID
    const userName = this.getUserName(userId);

    // 忽略自己的操作
    if (userId === this.currentUserId) return;

    // 初始化用户历史（如果不存在）
    this.initializeUserEditingHistory(userId);
    const userHistory = this.userEditingHistory.get(userId)!;

    // 更新用户最后编辑时间
    userHistory.lastEditTime = Date.now();

    // 根据操作类型推断意图类型
    let intentType: EditingIntentType = EditingIntentType.UNKNOWN;
    let entityId: string | undefined;
    let componentId: string | undefined;
    let propertyPath: string[] | undefined;
    let resourceId: string | undefined;

    switch (type) {
      case OperationType.ENTITY_CREATE:
        intentType = EditingIntentType.ENTITY_CREATION;
        entityId = data?.entityId;
        break;

      case OperationType.ENTITY_DELETE:
        intentType = EditingIntentType.ENTITY_DELETION;
        entityId = data?.entityId;
        break;

      case OperationType.ENTITY_UPDATE:
        // 检查是否是变换操作
        if (data?.transform) {
          intentType = EditingIntentType.TRANSFORM;
        } else {
          intentType = EditingIntentType.PROPERTY_CHANGE;
        }
        entityId = data?.entityId;
        break;

      case OperationType.COMPONENT_ADD:
      case OperationType.COMPONENT_UPDATE:
      case OperationType.COMPONENT_REMOVE:
        intentType = EditingIntentType.COMPONENT_EDIT;
        entityId = data?.entityId;
        componentId = data?.componentId;
        break;

      case OperationType.PROPERTY_UPDATE:
        intentType = EditingIntentType.PROPERTY_CHANGE;
        entityId = data?.entityId;
        componentId = data?.componentId;
        propertyPath = data?.path;
        break;

      case OperationType.SCENE_UPDATE:
        intentType = EditingIntentType.SCENE_STRUCTURE;
        break;

      default:
        return; // 无法推断意图
    }

    // 创建或更新意图
    const intentId = this.generateIntentId(userId, intentType, {
      entityId,
      componentId,
      propertyPath,
      resourceId
    });

    const existingIntent = this.intents.get(intentId);

    if (existingIntent) {
      // 更新现有意图
      existingIntent.lastUpdateTime = Date.now();
      existingIntent.confidence = Math.min(1.0, existingIntent.confidence + 0.1);

      if (!existingIntent.relatedOperations.includes(operation.id)) {
        existingIntent.relatedOperations.push(operation.id);
      }

      // 更新编辑持续时间
      existingIntent.editingDuration = existingIntent.lastUpdateTime - existingIntent.startTime;

      // 获取编辑模式和频率
      if (this.intentDetectionConfig.patternRecognitionEnabled) {
        existingIntent.editingFrequency = userHistory.averageEditingFrequency;
        existingIntent.editingPattern = this.getPredominantPattern(userId);
        existingIntent.predictedNextAction = this.predictNextAction(userId, existingIntent);
      }

      this.intents.set(intentId, existingIntent);
      this.emit('intentUpdated', existingIntent);

      // 预测冲突
      this.predictConflicts(existingIntent);
    } else {
      // 创建新意图
      const newIntent: EditingIntent = {
        id: intentId,
        userId,
        userName: userName || userId,
        type: intentType,
        entityId,
        componentId,
        propertyPath,
        resourceId,
        startTime: Date.now(),
        lastUpdateTime: Date.now(),
        confidence: 0.6, // 初始置信度
        relatedZones: [],
        relatedOperations: [operation.id],
        editingFrequency: userHistory.averageEditingFrequency,
        editingPattern: this.getPredominantPattern(userId),
        editingDuration: 0,
        previousIntents: userHistory.intents.slice(-3) // 保存最近的3个意图
      };

      this.intents.set(intentId, newIntent);
      this.emit('intentCreated', newIntent);

      // 预测冲突
      this.predictConflicts(newIntent);
    }
  }

  /**
   * 从编辑区域检测意图
   * @private
   */
  private detectIntentFromEditingZone(zone: EditingZone): void {
    const { userId, userName, type, entityId, componentId, propertyPath, resourceId } = zone;

    // 忽略自己的编辑区域
    if (userId === this.currentUserId) return;

    // 初始化用户历史（如果不存在）
    this.initializeUserEditingHistory(userId);
    const userHistory = this.userEditingHistory.get(userId)!;

    // 更新用户最后编辑时间
    userHistory.lastEditTime = Date.now();

    // 根据编辑区域类型推断意图类型
    let intentType: EditingIntentType = EditingIntentType.UNKNOWN;

    switch (type) {
      case EditingZoneType.ENTITY:
        intentType = EditingIntentType.PROPERTY_CHANGE;
        break;

      case EditingZoneType.COMPONENT:
        intentType = EditingIntentType.COMPONENT_EDIT;
        break;

      case EditingZoneType.PROPERTY:
        intentType = EditingIntentType.PROPERTY_CHANGE;
        break;

      case EditingZoneType.RESOURCE:
        intentType = EditingIntentType.RESOURCE_EDIT;
        break;

      case EditingZoneType.SCENE:
        intentType = EditingIntentType.SCENE_STRUCTURE;
        break;

      default:
        return; // 无法推断意图
    }

    // 创建或更新意图
    const intentId = this.generateIntentId(userId, intentType, {
      entityId,
      componentId,
      propertyPath,
      resourceId
    });

    const existingIntent = this.intents.get(intentId);

    if (existingIntent) {
      // 更新现有意图
      existingIntent.lastUpdateTime = Date.now();

      if (!existingIntent.relatedZones.includes(zone.id)) {
        existingIntent.relatedZones.push(zone.id);
      }

      // 更新编辑持续时间
      existingIntent.editingDuration = existingIntent.lastUpdateTime - existingIntent.startTime;

      // 获取编辑模式和频率
      if (this.intentDetectionConfig.patternRecognitionEnabled) {
        existingIntent.editingFrequency = userHistory.averageEditingFrequency;
        existingIntent.editingPattern = this.getPredominantPattern(userId);
        existingIntent.predictedNextAction = this.predictNextAction(userId, existingIntent);
      }

      this.intents.set(intentId, existingIntent);
      this.emit('intentUpdated', existingIntent);

      // 预测冲突
      this.predictConflicts(existingIntent);
    } else {
      // 创建新意图
      const newIntent: EditingIntent = {
        id: intentId,
        userId,
        userName: userName || userId,
        type: intentType,
        entityId,
        componentId,
        propertyPath,
        resourceId,
        startTime: Date.now(),
        lastUpdateTime: Date.now(),
        confidence: 0.7, // 初始置信度
        relatedZones: [zone.id],
        relatedOperations: [],
        editingFrequency: userHistory.averageEditingFrequency,
        editingPattern: this.getPredominantPattern(userId),
        editingDuration: 0,
        previousIntents: userHistory.intents.slice(-3) // 保存最近的3个意图
      };

      this.intents.set(intentId, newIntent);
      this.emit('intentCreated', newIntent);

      // 预测冲突
      this.predictConflicts(newIntent);
    }
  }

  /**
   * 更新编辑区域的意图
   * @private
   */
  private updateIntentFromEditingZone(zone: EditingZone): void {
    // 查找与该编辑区域相关的所有意图
    this.intents.forEach((intent, id) => {
      if (intent.userId === zone.userId && intent.relatedZones.includes(zone.id)) {
        // 更新意图
        intent.lastUpdateTime = Date.now();
        this.intents.set(id, intent);
        this.emit('intentUpdated', intent);
      }
    });
  }

  /**
   * 从意图中移除编辑区域
   * @private
   */
  private removeZoneFromIntents(zoneId: string): void {
    // 查找与该编辑区域相关的所有意图
    this.intents.forEach((intent, id) => {
      if (intent.relatedZones.includes(zoneId)) {
        // 移除编辑区域
        intent.relatedZones = intent.relatedZones.filter(id => id !== zoneId);

        // 如果没有相关区域和操作，移除意图
        if (intent.relatedZones.length === 0 && intent.relatedOperations.length === 0) {
          this.intents.delete(id);
          this.emit('intentRemoved', id);
        } else {
          this.intents.set(id, intent);
          this.emit('intentUpdated', intent);
        }
      }
    });
  }

  /**
   * 预测冲突
   * @private
   */
  private predictConflicts(intent: EditingIntent): void {
    // 忽略置信度低的意图
    if (intent.confidence < this.intentDetectionConfig.intentConfidenceThreshold) {
      return;
    }

    // 查找可能冲突的其他意图
    this.intents.forEach((otherIntent) => {
      // 忽略自己的意图
      if (otherIntent.id === intent.id || otherIntent.userId === intent.userId) {
        return;
      }

      // 检查是否可能冲突
      const conflictProbability = this.calculateConflictProbability(intent, otherIntent);

      // 如果冲突概率超过阈值，创建预测冲突
      if (conflictProbability >= this.conflictPredictionThreshold) {
        this.createPredictedConflict(intent, otherIntent, conflictProbability);
      }
    });
  }

  /**
   * 计算冲突概率
   * @private
   */
  private calculateConflictProbability(intentA: EditingIntent, intentB: EditingIntent): number {
    // 基础概率
    let probability = 0;

    // 如果两个意图操作相同的实体
    if (intentA.entityId && intentB.entityId && intentA.entityId === intentB.entityId) {
      probability += 0.5;

      // 如果两个意图操作相同的组件
      if (intentA.componentId && intentB.componentId && intentA.componentId === intentB.componentId) {
        probability += 0.2;

        // 如果两个意图操作相同的属性
        if (intentA.propertyPath && intentB.propertyPath &&
            JSON.stringify(intentA.propertyPath) === JSON.stringify(intentB.propertyPath)) {
          probability += 0.3;
        }
      }
    }

    // 如果两个意图操作相同的资源
    if (intentA.resourceId && intentB.resourceId && intentA.resourceId === intentB.resourceId) {
      probability += 0.7;
    }

    // 根据意图类型调整概率
    if (intentA.type === intentB.type) {
      switch (intentA.type) {
        case EditingIntentType.TRANSFORM:
          probability += 0.2;
          break;

        case EditingIntentType.PROPERTY_CHANGE:
          probability += 0.3;
          break;

        case EditingIntentType.COMPONENT_EDIT:
          probability += 0.3;
          break;

        case EditingIntentType.ENTITY_CREATION:
        case EditingIntentType.ENTITY_DELETION:
          probability += 0.1;
          break;

        case EditingIntentType.SCENE_STRUCTURE:
          probability += 0.4;
          break;

        case EditingIntentType.RESOURCE_EDIT:
          probability += 0.5;
          break;
      }
    }

    // 根据意图的置信度调整概率
    probability *= (intentA.confidence + intentB.confidence) / 2;

    // 根据编辑模式和频率调整概率
    if (this.intentDetectionConfig.patternRecognitionEnabled) {
      probability = this.adjustProbabilityByPattern(probability, intentA, intentB);
    }

    // 根据用户历史行为调整概率
    if (this.intentDetectionConfig.predictiveAnalysisEnabled) {
      probability = this.adjustProbabilityByUserHistory(probability, intentA, intentB);
    }

    // 根据编辑持续时间调整概率
    if (intentA.editingDuration && intentB.editingDuration) {
      // 如果两个用户都在长时间编辑，增加冲突概率
      const durationFactor = Math.min(intentA.editingDuration, intentB.editingDuration) / 60000; // 转换为分钟
      probability += Math.min(0.2, durationFactor * 0.05); // 最多增加0.2
    }

    // 根据预测的下一个动作调整概率
    if (intentA.predictedNextAction && intentB.predictedNextAction) {
      if (this.areActionsConflicting(intentA.predictedNextAction, intentB.predictedNextAction)) {
        probability += 0.15;
      }
    }

    // 确保概率在0-1之间
    return Math.max(0, Math.min(1, probability));
  }

  /**
   * 根据编辑模式调整冲突概率
   * @private
   */
  private adjustProbabilityByPattern(
    baseProbability: number,
    intentA: EditingIntent,
    intentB: EditingIntent
  ): number {
    let adjustedProbability = baseProbability;

    // 如果两个用户有相似的编辑模式，增加冲突概率
    if (intentA.editingPattern && intentB.editingPattern &&
        intentA.editingPattern === intentB.editingPattern) {
      adjustedProbability += 0.1;
    }

    // 根据编辑频率调整
    if (intentA.editingFrequency && intentB.editingFrequency) {
      // 如果两个用户都有高频率编辑，增加冲突概率
      if (intentA.editingFrequency > this.intentDetectionConfig.maxEditingFrequency * 0.7 &&
          intentB.editingFrequency > this.intentDetectionConfig.maxEditingFrequency * 0.7) {
        adjustedProbability += 0.15;
      }
    }

    return adjustedProbability;
  }

  /**
   * 根据用户历史行为调整冲突概率
   * @private
   */
  private adjustProbabilityByUserHistory(
    baseProbability: number,
    intentA: EditingIntent,
    intentB: EditingIntent
  ): number {
    let adjustedProbability = baseProbability;

    // 获取用户历史
    const historyA = this.userEditingHistory.get(intentA.userId);
    const historyB = this.userEditingHistory.get(intentB.userId);

    if (!historyA || !historyB) {
      return adjustedProbability;
    }

    // 检查用户是否有冲突历史
    const hasConflictHistory = this.checkConflictHistory(intentA.userId, intentB.userId);
    if (hasConflictHistory) {
      adjustedProbability += 0.1;
    }

    // 根据用户的平均编辑持续时间调整
    const durationFactorA = historyA.averageEditingDuration / 60000; // 转换为分钟
    const durationFactorB = historyB.averageEditingDuration / 60000;

    // 如果两个用户都倾向于长时间编辑，增加冲突概率
    if (durationFactorA > 5 && durationFactorB > 5) {
      adjustedProbability += 0.05;
    }

    return adjustedProbability;
  }

  /**
   * 检查两个用户是否有冲突历史
   * @private
   */
  private checkConflictHistory(userIdA: string, userIdB: string): boolean {
    // 这里应该查询冲突历史记录
    // 简化实现，实际应该从数据库或其他存储中查询

    // 检查最近的预测冲突
    let hasConflict = false;
    this.predictedConflicts.forEach(conflict => {
      if ((conflict.userA.id === userIdA && conflict.userB.id === userIdB) ||
          (conflict.userA.id === userIdB && conflict.userB.id === userIdA)) {
        hasConflict = true;
      }
    });

    return hasConflict;
  }

  /**
   * 判断两个动作是否冲突
   * @private
   */
  private areActionsConflicting(actionA: EditingIntentType, actionB: EditingIntentType): boolean {
    // 定义冲突的动作对
    const conflictingPairs: [EditingIntentType, EditingIntentType][] = [
      [EditingIntentType.ENTITY_DELETION, EditingIntentType.PROPERTY_CHANGE],
      [EditingIntentType.ENTITY_DELETION, EditingIntentType.COMPONENT_EDIT],
      [EditingIntentType.ENTITY_DELETION, EditingIntentType.TRANSFORM],
      [EditingIntentType.SCENE_STRUCTURE, EditingIntentType.SCENE_STRUCTURE],
      [EditingIntentType.RESOURCE_EDIT, EditingIntentType.RESOURCE_EDIT]
    ];

    return conflictingPairs.some(pair =>
      (pair[0] === actionA && pair[1] === actionB) ||
      (pair[0] === actionB && pair[1] === actionA)
    );
  }

  /**
   * 创建预测冲突
   * @private
   */
  private createPredictedConflict(intentA: EditingIntent, intentB: EditingIntent, probability: number): void {
    // 确保用户A的ID小于用户B的ID，以避免重复
    let userA, userB;
    if (intentA.userId < intentB.userId) {
      userA = { id: intentA.userId, name: intentA.userName, intent: intentA };
      userB = { id: intentB.userId, name: intentB.userName, intent: intentB };
    } else {
      userA = { id: intentB.userId, name: intentB.userName, intent: intentB };
      userB = { id: intentA.userId, name: intentA.userName, intent: intentA };
    }

    // 生成冲突ID
    const conflictId = this.generateConflictId(userA.id, userB.id, intentA.type, intentB.type);

    // 确定冲突类型
    let conflictType: ConflictType = ConflictType.PROPERTY_CONFLICT;

    if (intentA.type === EditingIntentType.ENTITY_DELETION || intentB.type === EditingIntentType.ENTITY_DELETION) {
      conflictType = ConflictType.DELETION_CONFLICT;
    } else if (intentA.type === EditingIntentType.TRANSFORM || intentB.type === EditingIntentType.TRANSFORM) {
      conflictType = ConflictType.ENTITY_CONFLICT;
    } else if (intentA.type === EditingIntentType.COMPONENT_EDIT || intentB.type === EditingIntentType.COMPONENT_EDIT) {
      conflictType = ConflictType.COMPONENT_CONFLICT;
    } else if (intentA.type === EditingIntentType.SCENE_STRUCTURE || intentB.type === EditingIntentType.SCENE_STRUCTURE) {
      conflictType = ConflictType.SCENE_CONFLICT;
    }

    // 检查是否已存在相同的预测冲突
    const existingConflict = this.predictedConflicts.get(conflictId);

    if (existingConflict) {
      // 更新现有冲突
      existingConflict.probability = probability;
      existingConflict.expiresAt = Date.now() + this.intentDetectionConfig.conflictExpirationTime;

      this.predictedConflicts.set(conflictId, existingConflict);

      // 如果概率超过通知阈值且尚未解决，通知用户
      if (probability >= this.intentDetectionConfig.notificationThreshold && !existingConflict.resolved) {
        this.notifyPredictedConflict(existingConflict);
      }
    } else {
      // 创建新的预测冲突
      const newConflict: PredictedConflict = {
        id: conflictId,
        type: conflictType,
        userA,
        userB,
        entityId: intentA.entityId || intentB.entityId,
        componentId: intentA.componentId || intentB.componentId,
        propertyPath: intentA.propertyPath || intentB.propertyPath,
        resourceId: intentA.resourceId || intentB.resourceId,
        probability,
        createdAt: Date.now(),
        expiresAt: Date.now() + this.intentDetectionConfig.conflictExpirationTime,
        resolved: false
      };

      this.predictedConflicts.set(conflictId, newConflict);
      store.dispatch(addPredictedConflict(newConflict));
      this.emit('predictedConflictCreated', newConflict);

      // 如果概率超过通知阈值，通知用户
      if (probability >= this.intentDetectionConfig.notificationThreshold) {
        this.notifyPredictedConflict(newConflict);
      }
    }
  }

  /**
   * 通知预测冲突
   * @private
   */
  private notifyPredictedConflict(conflict: PredictedConflict): void {
    // 只通知当前用户相关的冲突
    if (conflict.userA.id !== this.currentUserId && conflict.userB.id !== this.currentUserId) {
      return;
    }

    // 获取对方用户
    const otherUser = conflict.userA.id === this.currentUserId ? conflict.userB : conflict.userA;

    // 显示通知
    message.warning(`检测到可能的编辑冲突：您和 ${otherUser.name} 正在编辑相同的内容`);

    // 发出通知事件
    this.emit('conflictNotification', conflict);
  }

  /**
   * 启动清理定时器
   * @private
   */
  private startCleanupTimer(): void {
    if (this.cleanupInterval !== null) {
      this.stopCleanupTimer();
    }

    this.cleanupInterval = window.setInterval(() => {
      this.cleanupExpiredIntents();
      this.cleanupExpiredConflicts();
    }, 10000); // 每10秒检查一次
  }

  /**
   * 停止清理定时器
   * @private
   */
  private stopCleanupTimer(): void {
    if (this.cleanupInterval !== null) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * 启动意图检测定时器
   * @private
   */
  private startIntentDetectionTimer(): void {
    if (this.intentDetectionInterval !== null) {
      this.stopIntentDetectionTimer();
    }

    this.intentDetectionInterval = window.setInterval(() => {
      this.detectIntentsFromOperationBuffer();
    }, this.intentDetectionConfig.intentDetectionInterval);
  }

  /**
   * 停止意图检测定时器
   * @private
   */
  private stopIntentDetectionTimer(): void {
    if (this.intentDetectionInterval !== null) {
      clearInterval(this.intentDetectionInterval);
      this.intentDetectionInterval = null;
    }
  }

  /**
   * 启动模式分析定时器
   * @private
   */
  private startPatternAnalysisTimer(): void {
    if (this.patternAnalysisInterval !== null) {
      this.stopPatternAnalysisTimer();
    }

    this.patternAnalysisInterval = window.setInterval(() => {
      this.analyzeUserEditingPatterns();
    }, 30000); // 每30秒分析一次
  }

  /**
   * 停止模式分析定时器
   * @private
   */
  private stopPatternAnalysisTimer(): void {
    if (this.patternAnalysisInterval !== null) {
      clearInterval(this.patternAnalysisInterval);
      this.patternAnalysisInterval = null;
    }
  }

  /**
   * 停止所有定时器
   * @private
   */
  private stopTimers(): void {
    this.stopCleanupTimer();
    this.stopIntentDetectionTimer();
    this.stopPatternAnalysisTimer();
  }

  /**
   * 清理过期的意图
   * @private
   */
  private cleanupExpiredIntents(): void {
    const now = Date.now();
    const expiredIntents: string[] = [];

    this.intents.forEach((intent, id) => {
      if (now - intent.lastUpdateTime > this.intentExpirationTime) {
        expiredIntents.push(id);
      }
    });

    expiredIntents.forEach(id => {
      this.intents.delete(id);
      this.emit('intentExpired', id);
    });
  }

  /**
   * 清理过期的冲突
   * @private
   */
  private cleanupExpiredConflicts(): void {
    const now = Date.now();
    const expiredConflicts: string[] = [];

    this.predictedConflicts.forEach((conflict, id) => {
      if (conflict.expiresAt < now) {
        expiredConflicts.push(id);
      }
    });

    expiredConflicts.forEach(id => {
      this.predictedConflicts.delete(id);
      store.dispatch(removePredictedConflict(id));
      this.emit('predictedConflictExpired', id);
    });
  }

  /**
   * 从操作缓冲区检测意图
   * @private
   */
  private detectIntentsFromOperationBuffer(): void {
    // 按用户和实体分组操作
    const operationGroups = this.groupOperationsByUserAndEntity();

    // 分析每个组的操作序列
    for (const [, operations] of operationGroups.entries()) {
      // 如果操作数量足够
      if (operations.length >= this.intentDetectionConfig.minOperationsForIntent) {
        this.analyzeOperationSequence(operations);
      }
    }
  }

  /**
   * 按用户和实体分组操作
   * @private
   */
  private groupOperationsByUserAndEntity(): Map<string, Operation[]> {
    const groups = new Map<string, Operation[]>();

    for (const operation of this.operationBuffer) {
      // 忽略自己的操作
      if (operation.userId === this.currentUserId) continue;

      const entityId = operation.data?.entityId || 'global';
      const groupKey = `${operation.userId}:${entityId}`;

      if (!groups.has(groupKey)) {
        groups.set(groupKey, []);
      }

      groups.get(groupKey)!.push(operation);
    }

    return groups;
  }

  /**
   * 分析操作序列
   * @private
   */
  private analyzeOperationSequence(operations: Operation[]): void {
    // 按时间排序
    operations.sort((a, b) => a.timestamp - b.timestamp);

    // 检查操作之间的时间间隔
    let validSequence = true;
    for (let i = 1; i < operations.length; i++) {
      if (operations[i].timestamp - operations[i-1].timestamp > this.intentDetectionConfig.maxTimeBetweenOperations) {
        validSequence = false;
        break;
      }
    }

    if (!validSequence) return;

    // 分析操作类型分布
    const typeCount = new Map<OperationType, number>();
    for (const operation of operations) {
      const count = typeCount.get(operation.type) || 0;
      typeCount.set(operation.type, count + 1);
    }

    // 确定主要操作类型
    let mainType: OperationType | null = null;
    let maxCount = 0;

    typeCount.forEach((count, type) => {
      if (count > maxCount) {
        maxCount = count;
        mainType = type;
      }
    });

    if (!mainType) return;

    // 根据主要操作类型推断意图
    const latestOperation = operations[operations.length - 1];
    this.detectIntentFromOperation(latestOperation);
  }

  /**
   * 生成意图ID
   * @private
   */
  private generateIntentId(
    userId: string,
    type: EditingIntentType,
    data: {
      entityId?: string;
      componentId?: string;
      propertyPath?: string[];
      resourceId?: string;
    }
  ): string {
    let id = `${userId}:${type}`;

    if (data.entityId) id += `:${data.entityId}`;
    if (data.componentId) id += `:${data.componentId}`;
    if (data.propertyPath) id += `:${data.propertyPath.join('.')}`;
    if (data.resourceId) id += `:${data.resourceId}`;

    return id;
  }

  /**
   * 生成冲突ID
   * @private
   */
  private generateConflictId(
    userIdA: string,
    userIdB: string,
    intentTypeA: EditingIntentType,
    intentTypeB: EditingIntentType
  ): string {
    return `${userIdA}:${userIdB}:${intentTypeA}:${intentTypeB}:${Date.now()}`;
  }
}

// 创建单例实例
export const intentBasedConflictPreventionService = new IntentBasedConflictPreventionService();
