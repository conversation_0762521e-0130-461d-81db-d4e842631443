/**
 * 水体预设
 * 提供各种类型的水体预设配置
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyType, WaterBodyConfig } from './WaterBodyComponent';

/**
 * 水体预设类型
 */
export enum WaterPresetType {
  /** 湖泊 */
  LAKE = 'lake',
  /** 河流 */
  RIVER = 'river',
  /** 海洋 */
  OCEAN = 'ocean',
  /** 游泳池 */
  POOL = 'pool',
  /** 温泉 */
  HOT_SPRING = 'hot_spring',
  /** 地下湖泊 */
  UNDERGROUND_LAKE = 'underground_lake',
  /** 地下河流 */
  UNDERGROUND_RIVER = 'underground_river',
  /** 瀑布 */
  WATERFALL = 'waterfall',
  /** 浅滩 */
  SHALLOW = 'shallow',
  /** 沼泽 */
  SWAMP = 'swamp',
  /** 冰湖 */
  ICE_LAKE = 'ice_lake',
  /** 熔岩 */
  LAVA = 'lava'
}

/**
 * 水体预设配置
 */
export interface WaterPresetConfig {
  /** 水体类型 */
  type: WaterPresetType;
  /** 水体尺寸 */
  size?: {
    width: number;
    height: number;
    depth: number;
  };
  /** 水体位置 */
  position?: THREE.Vector3;
  /** 水体旋转 */
  rotation?: THREE.Euler;
  /** 水体颜色 */
  color?: THREE.Color;
  /** 水体透明度 */
  opacity?: number;
  /** 水体反射率 */
  reflectivity?: number;
  /** 水体折射率 */
  refractivity?: number;
  /** 水体波动参数 */
  waveParams?: {
    amplitude: number;
    frequency: number;
    speed: number;
    direction: THREE.Vector2;
  };
  /** 水体流动参数 */
  flowParams?: {
    direction: THREE.Vector3;
    speed: number;
  };
  /** 水体物理参数 */
  physicsParams?: {
    density: number;
    viscosity: number;
    surfaceTension?: number;
  };
}

/**
 * 水体预设类
 */
export class WaterPresets {
  /**
   * 获取预设配置
   * @param type 预设类型
   * @returns 预设配置
   */
  public static getPresetConfig(type: WaterPresetType): WaterPresetConfig {
    switch (type) {
      case WaterPresetType.LAKE:
        return {
          type: 'lake' as WaterPresetType,
          size: { width: 20, height: 2, depth: 20 },
          color: new THREE.Color(0x0077be),
          opacity: 0.8,
          reflectivity: 0.5,
          refractivity: 0.7,
          waveParams: {
            amplitude: 0.1,
            frequency: 0.5,
            speed: 0.3,
            direction: new THREE.Vector2(1, 1)
          },
          flowParams: {
            direction: new THREE.Vector3(0.1, 0, 0.1),
            speed: 0.1
          },
          physicsParams: {
            density: 1000,
            viscosity: 1.0
          }
        };

      case WaterPresetType.RIVER:
        return {
          type: 'river' as WaterPresetType,
          size: { width: 5, height: 1, depth: 50 },
          color: new THREE.Color(0x0088cc),
          opacity: 0.7,
          reflectivity: 0.4,
          refractivity: 0.8,
          waveParams: {
            amplitude: 0.05,
            frequency: 0.8,
            speed: 0.5,
            direction: new THREE.Vector2(1, 0)
          },
          flowParams: {
            direction: new THREE.Vector3(1, 0, 0),
            speed: 0.5
          },
          physicsParams: {
            density: 1000,
            viscosity: 0.8
          }
        };

      case WaterPresetType.OCEAN:
        return {
          type: 'ocean' as WaterPresetType,
          size: { width: 100, height: 5, depth: 100 },
          color: new THREE.Color(0x006994),
          opacity: 0.8,
          reflectivity: 0.6,
          refractivity: 0.6,
          waveParams: {
            amplitude: 0.3,
            frequency: 0.3,
            speed: 0.2,
            direction: new THREE.Vector2(1, 1)
          },
          flowParams: {
            direction: new THREE.Vector3(0.5, 0, 0.5),
            speed: 0.3
          },
          physicsParams: {
            density: 1030,
            viscosity: 1.2
          }
        };

      case WaterPresetType.POOL:
        return {
          type: 'pool' as WaterPresetType,
          size: { width: 10, height: 2, depth: 5 },
          color: new THREE.Color(0x00bbff),
          opacity: 0.7,
          reflectivity: 0.3,
          refractivity: 0.9,
          waveParams: {
            amplitude: 0.02,
            frequency: 1.0,
            speed: 0.2,
            direction: new THREE.Vector2(1, 1)
          },
          flowParams: {
            direction: new THREE.Vector3(0, 0, 0),
            speed: 0
          },
          physicsParams: {
            density: 1000,
            viscosity: 0.5
          }
        };

      default:
        return {
          type: 'lake' as WaterPresetType,
          size: { width: 10, height: 1, depth: 10 },
          color: new THREE.Color(0x0077be),
          opacity: 0.8,
          reflectivity: 0.5,
          refractivity: 0.7,
          waveParams: {
            amplitude: 0.1,
            frequency: 0.5,
            speed: 0.3,
            direction: new THREE.Vector2(1, 1)
          },
          flowParams: {
            direction: new THREE.Vector3(0.1, 0, 0.1),
            speed: 0.1
          },
          physicsParams: {
            density: 1000,
            viscosity: 1.0
          }
        };
    }
  }

  /**
   * 创建水体预设
   * @param entity 实体
   * @param config 预设配置
   * @returns 水体组件
   */
  public static createPreset(entity: any, config: WaterPresetConfig): WaterBodyComponent {
    // 获取预设配置
    const presetConfig = this.getPresetConfig(config.type);

    // 转换为 WaterBodyConfig 格式
    const waterBodyConfig: WaterBodyConfig = {
      type: this.convertToWaterBodyType(config.type),
      size: config.size || presetConfig.size,
      density: config.physicsParams?.density || presetConfig.physicsParams?.density,
      viscosity: config.physicsParams?.viscosity || presetConfig.physicsParams?.viscosity,
      flowDirection: config.flowParams?.direction ? {
        x: config.flowParams.direction.x,
        y: config.flowParams.direction.y,
        z: config.flowParams.direction.z
      } : presetConfig.flowParams ? {
        x: presetConfig.flowParams.direction.x,
        y: presetConfig.flowParams.direction.y,
        z: presetConfig.flowParams.direction.z
      } : undefined,
      flowSpeed: config.flowParams?.speed || presetConfig.flowParams?.speed,
      waveParams: config.waveParams ? {
        amplitude: config.waveParams.amplitude,
        frequency: config.waveParams.frequency,
        speed: config.waveParams.speed,
        direction: { x: config.waveParams.direction.x, z: config.waveParams.direction.y }
      } : presetConfig.waveParams ? {
        amplitude: presetConfig.waveParams.amplitude,
        frequency: presetConfig.waveParams.frequency,
        speed: presetConfig.waveParams.speed,
        direction: { x: presetConfig.waveParams.direction.x, z: presetConfig.waveParams.direction.y }
      } : undefined,
      enableWaves: true,
      enableFlow: true,
      enableBuoyancy: true,
      enableDrag: true
    };

    // 创建水体组件
    const waterBody = new WaterBodyComponent(entity, waterBodyConfig);

    return waterBody;
  }

  /**
   * 转换预设类型到水体类型
   * @param presetType 预设类型
   * @returns 水体类型
   */
  private static convertToWaterBodyType(presetType: WaterPresetType): WaterBodyType {
    switch (presetType) {
      case WaterPresetType.LAKE:
        return WaterBodyType.LAKE;
      case WaterPresetType.RIVER:
        return WaterBodyType.RIVER;
      case WaterPresetType.OCEAN:
        return WaterBodyType.OCEAN;
      case WaterPresetType.HOT_SPRING:
        return WaterBodyType.HOT_SPRING;
      case WaterPresetType.UNDERGROUND_LAKE:
        return WaterBodyType.UNDERGROUND_LAKE;
      case WaterPresetType.UNDERGROUND_RIVER:
        return WaterBodyType.UNDERGROUND_RIVER;
      case WaterPresetType.WATERFALL:
        return WaterBodyType.WATERFALL;
      default:
        return WaterBodyType.LAKE;
    }
  }

  /**
   * 获取所有预设类型
   * @returns 预设类型数组
   */
  public static getAllPresetTypes(): WaterPresetType[] {
    return Object.values(WaterPresetType);
  }

  /**
   * 获取预设显示名称
   * @param type 预设类型
   * @returns 显示名称
   */
  public static getPresetDisplayName(type: WaterPresetType): string {
    const names: Record<WaterPresetType, string> = {
      [WaterPresetType.LAKE]: '湖泊',
      [WaterPresetType.RIVER]: '河流',
      [WaterPresetType.OCEAN]: '海洋',
      [WaterPresetType.POOL]: '游泳池',
      [WaterPresetType.HOT_SPRING]: '温泉',
      [WaterPresetType.UNDERGROUND_LAKE]: '地下湖泊',
      [WaterPresetType.UNDERGROUND_RIVER]: '地下河流',
      [WaterPresetType.WATERFALL]: '瀑布',
      [WaterPresetType.SHALLOW]: '浅滩',
      [WaterPresetType.SWAMP]: '沼泽',
      [WaterPresetType.ICE_LAKE]: '冰湖',
      [WaterPresetType.LAVA]: '熔岩'
    };
    return names[type] || type;
  }
}