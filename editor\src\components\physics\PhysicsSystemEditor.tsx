/**
 * 物理系统编辑器组件
 * 用于配置物理系统
 */
import React, { useState, useEffect } from 'react';
import { Form, InputNumber, Collapse, Divider, Card, Row, Col } from 'antd';
import { InfoCircleOutlined, RocketOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { updatePhysicsSystemSettings } from '../../store/physics/physicsSlice';
import { Vector3Input } from '../common/Vector3Input';
import PhysicsDebuggerEditor from './PhysicsDebuggerEditor';

const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { Panel } = Collapse;

/**
 * 物理系统编辑器组件
 */
const PhysicsSystemEditor: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 获取物理系统设置
  const systemSettings = useSelector((state: RootState) => state.physics.systemSettings);
  
  // 表单状态
  const [form] = Form.useForm();
  
  // 初始化表单
  useEffect(() => {
    if (systemSettings) {
      form.setFieldsValue({
        gravity: systemSettings.gravity || { x: 0, y: -9.81, z: 0 },
        updateFrequency: systemSettings.updateFrequency || 60,
        allowSleep: systemSettings.allowSleep || true,
        enableCCD: systemSettings.enableCCD || false,
        iterations: systemSettings.iterations || 10,
        maxSubSteps: systemSettings.ccdOptions?.maxSubSteps || 5,
        minSubStepTime: systemSettings.ccdOptions?.minSubStepTime || (1 / 240),
        velocityThreshold: systemSettings.ccdOptions?.velocityThreshold || 5,
        enableForAll: systemSettings.ccdOptions?.enableForAll || false
      });
    }
  }, [systemSettings, form]);
  
  // 处理表单变更
  const handleValuesChange = (changedValues: any, allValues: any) => {
    // 构建CCD选项
    const ccdOptions = {
      maxSubSteps: allValues.maxSubSteps,
      minSubStepTime: allValues.minSubStepTime,
      velocityThreshold: allValues.velocityThreshold,
      enableForAll: allValues.enableForAll
    };
    
    // 更新物理系统设置
    dispatch(updatePhysicsSystemSettings({
      ...systemSettings,
      ...changedValues,
      ccdOptions
    }));
  };
  
  return (
    <div className="component-editor physics-system-editor">
      <Card title={t('editor.physics.physicsSystem')} extra={<RocketOutlined />}>
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
        >
          <Form.Item 
            name="gravity" 
            label={t('editor.physics.gravity')}
            tooltip={t('editor.physics.gravityTooltip')}
          >
            <Vector3Input />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item 
                name="updateFrequency" 
                label={t('editor.physics.updateFrequency')}
                tooltip={t('editor.physics.updateFrequencyTooltip')}
                rules={[{ type: 'number', min: 1 }]}
              >
                <InputNumber min={1} step={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item 
                name="iterations" 
                label={t('editor.physics.iterations')}
                tooltip={t('editor.physics.iterationsTooltip')}
                rules={[{ type: 'number', min: 1 }]}
              >
                <InputNumber min={1} step={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item 
                name="allowSleep" 
                label={t('editor.physics.allowSleep')}
                tooltip={t('editor.physics.allowSleepTooltip')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item 
                name="enableCCD" 
                label={t('editor.physics.enableCCD')}
                tooltip={t('editor.physics.enableCCDTooltip')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Collapse defaultActiveKey={['ccdSettings']}>
            <Panel header={t('editor.physics.ccdSettings')} key="ccdSettings">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item 
                    name="maxSubSteps" 
                    label={t('editor.physics.maxSubSteps')}
                    tooltip={t('editor.physics.maxSubStepsTooltip')}
                    rules={[{ type: 'number', min: 1 }]}
                  >
                    <InputNumber min={1} step={1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                
                <Col span={12}>
                  <Form.Item 
                    name="minSubStepTime" 
                    label={t('editor.physics.minSubStepTime')}
                    tooltip={t('editor.physics.minSubStepTimeTooltip')}
                    rules={[{ type: 'number', min: 0.001 }]}
                  >
                    <InputNumber min={0.001} step={0.001} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item 
                    name="velocityThreshold" 
                    label={t('editor.physics.velocityThreshold')}
                    tooltip={t('editor.physics.velocityThresholdTooltip')}
                    rules={[{ type: 'number', min: 0.1 }]}
                  >
                    <InputNumber min={0.1} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                
                <Col span={12}>
                  <Form.Item 
                    name="enableForAll" 
                    label={t('editor.physics.enableForAll')}
                    tooltip={t('editor.physics.enableForAllTooltip')}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </Panel>
          </Collapse>
        </Form>
      </Card>
      
      <Divider />
      
      <PhysicsDebuggerEditor />
    </div>
  );
};

export default PhysicsSystemEditor;
