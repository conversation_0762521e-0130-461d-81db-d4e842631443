/**
 * UI元素编辑器
 */
import React from 'react';
import GenericEditor from '../common/GenericEditor';

/**
 * UI元素编辑器属性
 */
interface UIElementEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * UI元素编辑器组件
 */
const UIElementEditor: React.FC<UIElementEditorProps> = ({ data, onChange }) => {
  return <GenericEditor title="UI元素" data={data} onChange={onChange} />;
};

export default UIElementEditor;
