# 脚本参考文档

本文档提供了DL（Digital Learning）引擎编辑器中脚本编程的详细参考信息，包括脚本API、生命周期函数、常用工具类和使用示例。

## 目录

- [脚本系统概述](#脚本系统概述)
- [脚本生命周期](#脚本生命周期)
- [脚本API](#脚本api)
- [实体和组件操作](#实体和组件操作)
- [输入处理](#输入处理)
- [物理交互](#物理交互)
- [动画控制](#动画控制)
- [UI交互](#ui交互)
- [网络通信](#网络通信)
- [工具类](#工具类)
- [调试和性能](#调试和性能)
- [最佳实践](#最佳实践)

## 脚本系统概述

DL（Digital Learning）引擎支持两种脚本编写方式：

1. **JavaScript/TypeScript脚本**：使用JavaScript或TypeScript编写脚本，通过脚本组件附加到实体上。
2. **视觉脚本**：使用可视化节点编辑器创建脚本逻辑，无需编写代码。

本文档主要介绍JavaScript/TypeScript脚本编程，视觉脚本请参考[视觉脚本文档](../features/visual-scripting.md)。

## 脚本生命周期

脚本组件遵循以下生命周期：

| 生命周期函数 | 调用时机 | 描述 |
|------------|---------|------|
| constructor | 创建脚本实例时 | 初始化脚本实例，不应在此访问其他组件 |
| onInit | 脚本初始化时 | 在所有组件初始化后调用，可以安全地访问其他组件 |
| onStart | 首次启用脚本时 | 在第一帧更新前调用，适合进行一次性设置 |
| onUpdate | 每帧更新时 | 每帧调用一次，用于实现持续的行为 |
| onLateUpdate | 所有更新完成后 | 在所有onUpdate调用后调用，适合处理依赖于其他更新的逻辑 |
| onFixedUpdate | 固定时间步长更新时 | 以固定时间间隔调用，适合物理相关逻辑 |
| onDestroy | 脚本销毁时 | 在脚本被销毁前调用，用于清理资源 |
| onEnable | 脚本启用时 | 在脚本被启用时调用 |
| onDisable | 脚本禁用时 | 在脚本被禁用时调用 |
| onCollisionEnter | 碰撞开始时 | 当实体开始与另一个实体碰撞时调用 |
| onCollisionStay | 碰撞持续时 | 当实体持续与另一个实体碰撞时调用 |
| onCollisionExit | 碰撞结束时 | 当实体结束与另一个实体的碰撞时调用 |
| onTriggerEnter | 触发器进入时 | 当实体进入触发器时调用 |
| onTriggerStay | 触发器停留时 | 当实体停留在触发器内时调用 |
| onTriggerExit | 触发器退出时 | 当实体退出触发器时调用 |

### 示例：基本脚本结构

```typescript
import { Script, Entity, Vector3 } from 'dl-engine';

export class RotateScript extends Script {
    // 公开属性，可在编辑器中设置
    public rotationSpeed: number = 30;

    // 私有变量
    private initialPosition: Vector3;

    // 初始化
    onInit(): void {
        console.log('脚本初始化');
        this.initialPosition = this.entity.transform.position.clone();
    }

    // 开始
    onStart(): void {
        console.log('脚本开始');
    }

    // 每帧更新
    onUpdate(deltaTime: number): void {
        // 旋转实体
        this.entity.transform.rotate(new Vector3(0, this.rotationSpeed * deltaTime, 0));
    }

    // 销毁
    onDestroy(): void {
        console.log('脚本销毁');
    }
}
```

## 脚本API

### 脚本基类

所有脚本都继承自`Script`基类，该类提供了以下属性和方法：

| 属性/方法 | 类型 | 描述 |
|----------|------|------|
| entity | Entity | 脚本附加的实体 |
| transform | TransformComponent | 实体的变换组件 |
| enabled | boolean | 脚本是否启用 |
| world | World | 当前世界实例 |
| getComponent | <T>(componentType: ComponentType<T>) => T | 获取实体上的组件 |
| findEntity | (name: string) => Entity | 按名称查找实体 |
| findEntitiesByTag | (tag: string) => Entity[] | 按标签查找实体 |
| instantiate | (prefab: Prefab, position?: Vector3, rotation?: Quaternion) => Entity | 实例化预制体 |
| destroy | (entity: Entity) => void | 销毁实体 |
| invoke | (method: () => void, delay: number) => void | 延迟调用方法 |
| invokeRepeating | (method: () => void, delay: number, interval: number) => void | 重复调用方法 |
| cancelInvoke | (method?: () => void) => void | 取消延迟调用 |

### 示例：使用脚本API

```typescript
import { Script, Entity, Vector3, MeshRendererComponent } from 'dl-engine';

export class PlayerController extends Script {
    // 公开属性
    public moveSpeed: number = 5;
    public rotateSpeed: number = 60;
    public bulletPrefab: Prefab;

    // 私有变量
    private renderer: MeshRendererComponent;
    private fireTimer: number;

    onInit(): void {
        // 获取组件
        this.renderer = this.getComponent(MeshRendererComponent);

        // 设置定时器
        this.invokeRepeating(this.checkStatus, 1, 5);
    }

    onUpdate(deltaTime: number): void {
        // 移动实体
        const moveDirection = new Vector3(Input.getAxis('Horizontal'), 0, Input.getAxis('Vertical'));
        this.entity.transform.translate(moveDirection.normalize().multiplyScalar(this.moveSpeed * deltaTime));

        // 旋转实体
        if (Input.getKey('KeyQ')) {
            this.entity.transform.rotate(new Vector3(0, -this.rotateSpeed * deltaTime, 0));
        }
        if (Input.getKey('KeyE')) {
            this.entity.transform.rotate(new Vector3(0, this.rotateSpeed * deltaTime, 0));
        }

        // 发射子弹
        if (Input.getKeyDown('Space')) {
            this.fire();
        }
    }

    fire(): void {
        if (!this.bulletPrefab) return;

        // 实例化子弹
        const bulletPosition = this.entity.transform.position.clone().add(this.entity.transform.forward.multiplyScalar(2));
        const bullet = this.instantiate(this.bulletPrefab, bulletPosition, this.entity.transform.rotation);

        // 设置子弹速度
        const bulletScript = bullet.getComponent(BulletScript);
        if (bulletScript) {
            bulletScript.direction = this.entity.transform.forward;
        }

        // 3秒后销毁子弹
        this.invoke(() => this.destroy(bullet), 3);
    }

    checkStatus(): void {
        console.log('Player status check');
    }

    onDestroy(): void {
        // 取消所有定时器
        this.cancelInvoke();
    }
}
```

![脚本组件](../../assets/images/script-component.png)

## 实体和组件操作

DL（Digital Learning）引擎使用实体组件系统（ECS）作为其核心架构。本节介绍如何在脚本中操作实体和组件。

### 实体操作

实体是场景中的基本对象，可以附加各种组件来定义其行为和外观。

#### 创建实体

```typescript
// 创建一个新实体
const entity = world.createEntity('MyEntity');

// 创建一个带有父级的实体
const parentEntity = world.createEntity('ParentEntity');
const childEntity = world.createEntity('ChildEntity', parentEntity);

// 从预制体创建实体
const prefabEntity = world.instantiate('prefabs/character.prefab');

// 克隆现有实体
const clonedEntity = world.cloneEntity(sourceEntity);
```

#### 查找实体

```typescript
// 通过名称查找实体
const entity = world.findEntityByName('MyEntity');

// 通过标签查找实体
const entities = world.findEntitiesByTag('Enemy');

// 通过ID查找实体
const entity = world.getEntityById('550e8400-e29b-41d4-a716-446655440000');

// 查找子实体
const childEntity = parentEntity.findChild('ChildName');
const allChildren = parentEntity.getChildren();

// 使用查询查找实体
const query = world.createQuery().hasComponent(TransformComponent).hasComponent(MeshRendererComponent);
const entities = query.execute();
```

#### 实体层次结构

```typescript
// 设置父级
entity.setParent(parentEntity);

// 获取父级
const parent = entity.getParent();

// 获取子级
const children = entity.getChildren();

// 遍历子级
entity.forEachChild(child => {
  console.log(child.name);
});

// 分离实体（从父级移除）
entity.detachFromParent();

// 销毁所有子级
entity.destroyChildren();
```

#### 实体属性

```typescript
// 获取/设置名称
const name = entity.name;
entity.name = 'NewName';

// 获取/设置标签
const tag = entity.tag;
entity.tag = 'Player';

// 获取/设置层级
const layer = entity.layer;
entity.layer = 2;

// 获取/设置激活状态
const isActive = entity.active;
entity.active = false;

// 获取/设置静态状态
const isStatic = entity.isStatic;
entity.isStatic = true;

// 获取唯一ID
const id = entity.id;

// 获取场景
const scene = entity.scene;
```

#### 销毁实体

```typescript
// 销毁实体
world.destroyEntity(entity);

// 或者
entity.destroy();

// 延迟销毁
world.destroyEntityDelayed(entity, 2.0); // 2秒后销毁
```

### 组件操作

组件定义了实体的行为和属性。每个组件通常专注于一个特定功能。

#### 添加组件

```typescript
// 添加组件
const meshRenderer = entity.addComponent(MeshRendererComponent);

// 添加带有初始参数的组件
const rigidbody = entity.addComponent(RigidbodyComponent, {
  mass: 10,
  useGravity: true
});

// 添加自定义脚本组件
const playerController = entity.addComponent(PlayerController);
```

#### 获取组件

```typescript
// 获取组件
const transform = entity.getComponent(TransformComponent);

// 获取第一个找到的组件（从自身或父级）
const camera = entity.getComponentInParent(CameraComponent);

// 获取第一个找到的组件（从自身或子级）
const light = entity.getComponentInChildren(LightComponent);

// 获取所有指定类型的组件
const colliders = entity.getComponents(ColliderComponent);

// 获取所有指定类型的组件（包括子级）
const renderers = entity.getComponentsInChildren(RendererComponent);

// 检查是否有组件
const hasAudio = entity.hasComponent(AudioSourceComponent);
```

#### 移除组件

```typescript
// 移除组件
entity.removeComponent(ParticleSystemComponent);

// 移除特定组件实例
entity.removeComponent(specificComponent);

// 移除所有组件（除了Transform）
entity.removeAllComponents();
```

#### 组件事件

```typescript
// 监听组件添加事件
entity.onComponentAdded.add((component) => {
  console.log(`组件已添加: ${component.constructor.name}`);
});

// 监听组件移除事件
entity.onComponentRemoved.add((component) => {
  console.log(`组件已移除: ${component.constructor.name}`);
});

// 监听组件启用事件
entity.onComponentEnabled.add((component) => {
  console.log(`组件已启用: ${component.constructor.name}`);
});

// 监听组件禁用事件
entity.onComponentDisabled.add((component) => {
  console.log(`组件已禁用: ${component.constructor.name}`);
});
```

### 预制体操作

预制体是可重用的实体模板，可以在场景中多次实例化。

```typescript
// 加载预制体
const prefab = AssetManager.getPrefab('prefabs/enemy.prefab');

// 实例化预制体
const instance = world.instantiate(prefab, new Vector3(0, 0, 0), Quaternion.identity);

// 修改实例
instance.name = 'Enemy1';
const health = instance.getComponent(HealthComponent);
health.maxHealth = 200;

// 创建预制体
const newPrefab = PrefabUtility.createPrefab(entity, 'prefabs/newPrefab.prefab');

// 应用预制体修改
PrefabUtility.applyPrefab(instance);

// 断开预制体连接
PrefabUtility.disconnectPrefabInstance(instance);
```

### 实体组件系统最佳实践

- **组件职责单一**：每个组件应该只负责一个功能，避免创建"万能"组件。
- **组件通信**：组件之间应该通过事件系统或引用其他组件来通信，而不是直接修改其他组件的内部状态。
- **延迟获取组件**：在`onInit`或`onStart`中获取组件引用，而不是在构造函数中。
- **缓存组件引用**：频繁使用的组件引用应该缓存，而不是每次都调用`getComponent`。
- **检查组件存在性**：在使用组件前总是检查它是否存在，避免空引用错误。
- **避免在Update中查找组件**：在`onUpdate`等频繁调用的方法中避免使用`getComponent`，这会影响性能。

## 输入处理

DL（Digital Learning）引擎提供了全面的输入系统，支持键盘、鼠标、触摸和游戏手柄等多种输入设备。

### 键盘输入

```typescript
// 检查按键是否按下（当前帧）
if (Input.getKeyDown('KeyW') || Input.getKeyDown('ArrowUp')) {
  console.log('W或上箭头键被按下');
}

// 检查按键是否持续按下
if (Input.getKey('KeyW') || Input.getKey('ArrowUp')) {
  // 持续向前移动
  this.entity.transform.translate(Vector3.forward.multiplyScalar(this.moveSpeed * deltaTime));
}

// 检查按键是否释放（当前帧）
if (Input.getKeyUp('KeyW') || Input.getKeyUp('ArrowUp')) {
  console.log('W或上箭头键被释放');
}

// 检查修饰键
if (Input.getKey('ShiftLeft')) {
  // 加速移动
  this.moveSpeed *= 2;
}

// 获取任意按下的按键
const pressedKeys = Input.getPressedKeys();
```

### 鼠标输入

```typescript
// 获取鼠标位置
const mousePosition = Input.getMousePosition(); // 返回Vector2

// 获取鼠标在世界中的射线
const mouseRay = Camera.main.screenPointToRay(mousePosition);

// 检查鼠标按钮是否按下（当前帧）
if (Input.getMouseButtonDown(0)) { // 0=左键, 1=右键, 2=中键
  console.log('鼠标左键被按下');
}

// 检查鼠标按钮是否持续按下
if (Input.getMouseButton(0)) {
  // 拖拽物体
}

// 检查鼠标按钮是否释放（当前帧）
if (Input.getMouseButtonUp(0)) {
  console.log('鼠标左键被释放');
}

// 获取鼠标滚轮增量
const scrollDelta = Input.getMouseScrollDelta();
if (scrollDelta.y > 0) {
  // 向上滚动，放大
  Camera.main.fieldOfView -= 5;
} else if (scrollDelta.y < 0) {
  // 向下滚动，缩小
  Camera.main.fieldOfView += 5;
}

// 锁定/解锁鼠标光标
Input.lockCursor();
Input.unlockCursor();
```

### 触摸输入

```typescript
// 检查是否有触摸输入
if (Input.touchCount > 0) {
  // 获取第一个触摸点
  const touch = Input.getTouch(0);

  // 检查触摸阶段
  switch (touch.phase) {
    case TouchPhase.Began:
      console.log('触摸开始');
      break;
    case TouchPhase.Moved:
      // 处理拖拽
      this.handleDrag(touch.position, touch.deltaPosition);
      break;
    case TouchPhase.Ended:
      console.log('触摸结束');
      break;
  }

  // 多点触摸（捏合缩放）
  if (Input.touchCount >= 2) {
    const touch1 = Input.getTouch(0);
    const touch2 = Input.getTouch(1);

    // 计算当前两点距离
    const currentDistance = Vector2.distance(touch1.position, touch2.position);

    // 计算上一帧两点距离
    const previousDistance = Vector2.distance(
      touch1.position.subtract(touch1.deltaPosition),
      touch2.position.subtract(touch2.deltaPosition)
    );

    // 计算缩放因子
    const pinchFactor = currentDistance / previousDistance;

    // 应用缩放
    Camera.main.fieldOfView /= pinchFactor;
  }
}
```

### 游戏手柄输入

```typescript
// 检查手柄是否连接
if (Input.getGamepadConnected(0)) { // 0是第一个手柄
  // 获取左摇杆输入
  const leftStick = Input.getGamepadStick(0, GamepadStick.Left);

  // 移动角色
  const moveDirection = new Vector3(leftStick.x, 0, leftStick.y);
  this.entity.transform.translate(moveDirection.multiplyScalar(this.moveSpeed * deltaTime));

  // 获取右摇杆输入
  const rightStick = Input.getGamepadStick(0, GamepadStick.Right);

  // 旋转摄像机
  this.cameraRotation.x += rightStick.x * this.rotateSpeed * deltaTime;
  this.cameraRotation.y += rightStick.y * this.rotateSpeed * deltaTime;

  // 检查按钮是否按下
  if (Input.getGamepadButtonDown(0, GamepadButton.A)) {
    this.jump();
  }

  // 检查按钮是否持续按下
  if (Input.getGamepadButton(0, GamepadButton.RightTrigger)) {
    // 根据扳机键的压力值调整射击力度
    const triggerValue = Input.getGamepadButtonValue(0, GamepadButton.RightTrigger);
    this.shootWithPower(triggerValue); // 0.0到1.0之间
  }

  // 检查按钮是否释放
  if (Input.getGamepadButtonUp(0, GamepadButton.B)) {
    this.cancelAction();
  }
}
```

### 输入映射

DL（Digital Learning）引擎支持输入映射，允许将物理输入映射到逻辑操作，便于跨平台和自定义控制。

```typescript
// 定义输入映射（通常在项目设置中配置）
InputMapping.define('Horizontal', [
  { device: 'Keyboard', positive: 'KeyD', negative: 'KeyA' },
  { device: 'Keyboard', positive: 'ArrowRight', negative: 'ArrowLeft' },
  { device: 'Gamepad', axis: 'LeftStickX' }
]);

InputMapping.define('Vertical', [
  { device: 'Keyboard', positive: 'KeyW', negative: 'KeyS' },
  { device: 'Keyboard', positive: 'ArrowUp', negative: 'ArrowDown' },
  { device: 'Gamepad', axis: 'LeftStickY' }
]);

InputMapping.define('Jump', [
  { device: 'Keyboard', key: 'Space' },
  { device: 'Gamepad', button: 'A' }
]);

// 使用映射的输入
onUpdate(deltaTime: number): void {
  // 获取映射的轴值（-1.0到1.0之间）
  const horizontal = Input.getAxis('Horizontal');
  const vertical = Input.getAxis('Vertical');

  // 创建移动向量
  const moveDirection = new Vector3(horizontal, 0, vertical);

  // 移动角色
  this.entity.transform.translate(moveDirection.normalize().multiplyScalar(this.moveSpeed * deltaTime));

  // 检查映射的按钮
  if (Input.getButtonDown('Jump')) {
    this.jump();
  }
}
```

### 输入事件

除了轮询输入状态外，DL（Digital Learning）引擎还提供了基于事件的输入处理方式。

```typescript
// 在脚本初始化时注册事件
onInit(): void {
  // 键盘事件
  Input.onKeyDown.add(this.handleKeyDown.bind(this));
  Input.onKeyUp.add(this.handleKeyUp.bind(this));

  // 鼠标事件
  Input.onMouseDown.add(this.handleMouseDown.bind(this));
  Input.onMouseUp.add(this.handleMouseUp.bind(this));
  Input.onMouseMove.add(this.handleMouseMove.bind(this));

  // 触摸事件
  Input.onTouchBegan.add(this.handleTouchBegan.bind(this));
  Input.onTouchMoved.add(this.handleTouchMoved.bind(this));
  Input.onTouchEnded.add(this.handleTouchEnded.bind(this));

  // 游戏手柄事件
  Input.onGamepadConnected.add(this.handleGamepadConnected.bind(this));
  Input.onGamepadDisconnected.add(this.handleGamepadDisconnected.bind(this));
  Input.onGamepadButtonDown.add(this.handleGamepadButtonDown.bind(this));
}

// 在脚本销毁时取消注册事件
onDestroy(): void {
  Input.onKeyDown.remove(this.handleKeyDown.bind(this));
  // 取消注册其他事件...
}

// 事件处理方法
private handleKeyDown(key: string): void {
  console.log(`按键按下: ${key}`);
}

private handleMouseDown(button: number, position: Vector2): void {
  console.log(`鼠标按钮 ${button} 在位置 (${position.x}, ${position.y}) 按下`);

  // 执行射线检测
  const ray = Camera.main.screenPointToRay(position);
  const hit = Physics.raycast(ray);

  if (hit) {
    console.log(`点击了物体: ${hit.entity.name}`);
    this.selectEntity(hit.entity);
  }
}
```

### 输入系统最佳实践

- **使用输入映射**：尽量使用输入映射而不是硬编码的输入检测，这样可以轻松支持不同的输入设备和自定义控制。
- **分离输入逻辑**：将输入处理逻辑与游戏逻辑分离，使代码更清晰和可维护。
- **考虑多平台**：设计输入系统时考虑不同平台的特性和限制，确保良好的跨平台体验。
- **提供反馈**：对用户输入提供及时的视觉或音频反馈，增强交互体验。
- **优化性能**：避免在每一帧检查所有可能的输入，只检查当前上下文相关的输入。
