/**
 * 调试面板组件
 * 集成各种调试工具
 */
import React, { useState } from 'react';
import { Tabs } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  DashboardOutlined,
  AreaChartOutlined,
  Bar<PERSON><PERSON>Outlined,
  <PERSON>boltOutlined,
  BulbOutlined,
  RocketOutlined,
  DesktopOutlined,
  AppstoreOutlined} from '@ant-design/icons';
import PerformancePanel from './PerformancePanel';
import PerformanceComparisonPanel from './PerformanceComparisonPanel';
import PerformanceTestAutomationPanel from './PerformanceTestAutomationPanel';
import MemoryAnalysisPanel from './MemoryAnalysisPanel';
import RenderingAnalysisPanel from './RenderingAnalysisPanel';
import SceneOptimizationPanel from './SceneOptimizationPanel';
import PerformanceOptimizationPanel from './PerformanceOptimizationPanel';
import ResourceOptimizationPanel from './ResourceOptimizationPanel';
import UIOptimizationPanel from './UIOptimizationPanel';
import './DebugPanel.less';

const { TabPane } = Tabs;

interface DebugPanelProps {
  className?: string;
}

/**
 * 调试面板组件
 */
const DebugPanel: React.FC<DebugPanelProps> = ({ className }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('performance');

  return (
    <div className={`debug-panel ${className || ''}`}>
      <Tabs activeKey={activeTab} onChange={setActiveTab} type="card">
        <TabPane
          tab={
            <span>
              <DashboardOutlined />
              {t('debug.tabs.performance')}
            </span>
          }
          key="performance"
        >
          <PerformancePanel />
        </TabPane>
        <TabPane
          tab={
            <span>
              <LineChartOutlined />
              {t('debug.tabs.performanceComparison')}
            </span>
          }
          key="performanceComparison"
        >
          <PerformanceComparisonPanel />
        </TabPane>
        <TabPane
          tab={
            <span>
              <ThunderboltOutlined />
              {t('debug.tabs.performanceTest')}
            </span>
          }
          key="performanceTest"
        >
          <PerformanceTestAutomationPanel />
        </TabPane>
        <TabPane
          tab={
            <span>
              <AreaChartOutlined />
              {t('debug.tabs.memory')}
            </span>
          }
          key="memory"
        >
          <MemoryAnalysisPanel />
        </TabPane>
        <TabPane
          tab={
            <span>
              <BarChartOutlined />
              {t('debug.tabs.rendering')}
            </span>
          }
          key="rendering"
        >
          <RenderingAnalysisPanel />
        </TabPane>
        <TabPane
          tab={
            <span>
              <BulbOutlined />
              {t('debug.tabs.optimization')}
            </span>
          }
          key="optimization"
        >
          <SceneOptimizationPanel />
        </TabPane>
        <TabPane
          tab={
            <span>
              <RocketOutlined />
              {t('debug.tabs.performanceOptimization')}
            </span>
          }
          key="performanceOptimization"
        >
          <PerformanceOptimizationPanel />
        </TabPane>
        <TabPane
          tab={
            <span>
              <AppstoreOutlined />
              {t('debug.tabs.resourceOptimization')}
            </span>
          }
          key="resourceOptimization"
        >
          <ResourceOptimizationPanel />
        </TabPane>
        <TabPane
          tab={
            <span>
              <DesktopOutlined />
              {t('debug.tabs.uiOptimization')}
            </span>
          }
          key="uiOptimization"
        >
          <UIOptimizationPanel />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default DebugPanel;
