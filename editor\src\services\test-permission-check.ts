/**
 * 权限检查服务测试文件
 * 用于验证修复后的服务是否正常工作
 */
import { permissionCheckService } from './PermissionCheckService';
import { Permission } from './PermissionService';

// 测试权限检查服务
function testPermissionCheckService() {
  console.log('开始测试权限检查服务...');
  
  try {
    // 测试服务实例
    console.log('✓ 成功获取权限检查服务实例');
    
    // 测试配置方法
    permissionCheckService.setEnabled(true);
    permissionCheckService.setShowErrorMessages(false); // 测试时不显示错误消息
    permissionCheckService.setLogFailedChecks(false); // 测试时不记录日志
    console.log('✓ 成功配置权限检查服务');
    
    // 测试权限检查方法（这些会返回false，因为没有登录用户）
    const canEdit = permissionCheckService.canEditScene();
    const canCreate = permissionCheckService.canCreateEntity();
    const canUpdate = permissionCheckService.canUpdateEntity();
    const canDelete = permissionCheckService.canDeleteEntity();
    
    console.log('✓ 权限检查方法调用成功');
    console.log(`  - 可以编辑场景: ${canEdit}`);
    console.log(`  - 可以创建实体: ${canCreate}`);
    console.log(`  - 可以更新实体: ${canUpdate}`);
    console.log(`  - 可以删除实体: ${canDelete}`);
    
    // 测试组件权限
    const canAddComponent = permissionCheckService.canAddComponent();
    const canUpdateComponent = permissionCheckService.canUpdateComponent();
    const canRemoveComponent = permissionCheckService.canRemoveComponent();
    
    console.log('✓ 组件权限检查方法调用成功');
    console.log(`  - 可以添加组件: ${canAddComponent}`);
    console.log(`  - 可以更新组件: ${canUpdateComponent}`);
    console.log(`  - 可以移除组件: ${canRemoveComponent}`);
    
    // 测试资源权限
    const canUploadAsset = permissionCheckService.canUploadAsset();
    const canDeleteAsset = permissionCheckService.canDeleteAsset();
    
    console.log('✓ 资源权限检查方法调用成功');
    console.log(`  - 可以上传资源: ${canUploadAsset}`);
    console.log(`  - 可以删除资源: ${canDeleteAsset}`);
    
    // 测试场景权限
    const canSaveScene = permissionCheckService.canSaveScene();
    const canExportScene = permissionCheckService.canExportScene();
    const canImportScene = permissionCheckService.canImportScene();
    
    console.log('✓ 场景权限检查方法调用成功');
    console.log(`  - 可以保存场景: ${canSaveScene}`);
    console.log(`  - 可以导出场景: ${canExportScene}`);
    console.log(`  - 可以导入场景: ${canImportScene}`);
    
    // 测试管理权限
    const canManageUsers = permissionCheckService.canManageUsers();
    const canAssignRoles = permissionCheckService.canAssignRoles();
    const canManagePermissions = permissionCheckService.canManagePermissions();
    const canManageProject = permissionCheckService.canManageProject();
    const canDeleteProject = permissionCheckService.canDeleteProject();
    
    console.log('✓ 管理权限检查方法调用成功');
    console.log(`  - 可以管理用户: ${canManageUsers}`);
    console.log(`  - 可以分配角色: ${canAssignRoles}`);
    console.log(`  - 可以管理权限: ${canManagePermissions}`);
    console.log(`  - 可以管理项目: ${canManageProject}`);
    console.log(`  - 可以删除项目: ${canDeleteProject}`);
    
    // 测试直接权限检查
    const hasEditPermission = permissionCheckService.checkPermission(Permission.EDIT_SCENE, false);
    console.log('✓ 直接权限检查方法调用成功');
    console.log(`  - 有编辑场景权限: ${hasEditPermission}`);
    
    // 测试用户权限检查
    const userHasPermission = permissionCheckService.checkUserPermission('test-user', Permission.VIEW_SCENE);
    console.log('✓ 用户权限检查方法调用成功');
    console.log(`  - 测试用户有查看权限: ${userHasPermission}`);
    
    // 测试禁用权限检查
    permissionCheckService.setEnabled(false);
    const canEditWhenDisabled = permissionCheckService.canEditScene();
    console.log('✓ 禁用权限检查测试成功');
    console.log(`  - 禁用时可以编辑: ${canEditWhenDisabled}`); // 应该返回true
    
    // 恢复设置
    permissionCheckService.setEnabled(true);
    permissionCheckService.setShowErrorMessages(true);
    permissionCheckService.setLogFailedChecks(true);
    
    console.log('✅ 所有测试通过！权限检查服务修复成功！');
    
  } catch (error) {
    console.error('✗ 测试过程中发生错误:', error);
  }
}

// 导出测试函数
export { testPermissionCheckService };

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).testPermissionCheckService = testPermissionCheckService;
  console.log('测试函数已添加到 window.testPermissionCheckService');
  console.log('可以在浏览器控制台中运行: testPermissionCheckService()');
}
