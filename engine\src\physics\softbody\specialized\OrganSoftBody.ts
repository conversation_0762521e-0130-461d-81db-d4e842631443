/**
 * 器官软体组件 - 用于模拟心脏等器官的物理行为
 */
import * as THREE from 'three';
import { SoftBodyComponent, SoftBodyComponentOptions } from '../SoftBodyComponent';

/**
 * 器官类型枚举
 */
export enum OrganType {
  /** 心脏 */
  HEART = 'heart',
  /** 肺部 */
  LUNG = 'lung',
  /** 肝脏 */
  LIVER = 'liver',
  /** 肾脏 */
  KIDNEY = 'kidney',
  /** 胃部 */
  STOMACH = 'stomach',
  /** 肠道 */
  INTESTINE = 'intestine'
}

/**
 * 血管连接类型
 */
export enum VesselConnectionType {
  /** 动脉连接 */
  ARTERIAL = 'arterial',
  /** 静脉连接 */
  VENOUS = 'venous',
  /** 毛细血管连接 */
  CAPILLARY = 'capillary'
}

/**
 * 血管连接接口
 */
export interface VesselConnection {
  /** 连接ID */
  id: string;
  /** 连接类型 */
  type: VesselConnectionType;
  /** 连接位置 */
  position: THREE.Vector3;
  /** 连接方向 */
  direction: THREE.Vector3;
  /** 血管直径 */
  diameter: number;
  /** 流量 */
  flowRate: number;
  /** 压力 */
  pressure: number;
}

/**
 * 器官软体组件选项
 */
export interface OrganSoftBodyOptions extends SoftBodyComponentOptions {
  /** 器官类型 */
  organType: OrganType;
  /** 组织弹性参数 */
  tissueElasticity?: number;
  /** 组织密度 */
  tissueDensity?: number;
  /** 血管连接点 */
  vesselConnections?: VesselConnection[];
}

export class OrganSoftBody extends SoftBodyComponent {
  /** 器官类型 */
  private organType: OrganType;
  /** 血管连接点 */
  private vesselConnections: VesselConnection[] = [];
  /** 组织弹性参数 */
  private tissueElasticity: number;
  /** 组织密度 */
  private tissueDensity: number;

  /**
   * 构造函数
   * @param options 器官软体组件选项
   */
  constructor(options: OrganSoftBodyOptions) {
    super(options);

    this.organType = options.organType;
    this.tissueElasticity = options.tissueElasticity || 1.0;
    this.tissueDensity = options.tissueDensity || 1.0;
    this.vesselConnections = options.vesselConnections || [];
  }
  
  /**
   * 获取器官类型
   * @returns 器官类型
   */
  public getOrganType(): OrganType {
    return this.organType;
  }

  /**
   * 获取血管连接点
   * @returns 血管连接点数组
   */
  public getVesselConnections(): VesselConnection[] {
    return this.vesselConnections;
  }

  /**
   * 添加血管连接点
   * @param connection 血管连接
   */
  public addVesselConnection(connection: VesselConnection): void {
    this.vesselConnections.push(connection);
  }

  /**
   * 获取组织弹性参数
   * @returns 组织弹性参数
   */
  public getTissueElasticity(): number {
    return this.tissueElasticity;
  }

  /**
   * 设置组织弹性参数
   * @param elasticity 弹性参数
   */
  public setTissueElasticity(elasticity: number): void {
    this.tissueElasticity = elasticity;
  }

  /**
   * 获取组织密度
   * @returns 组织密度
   */
  public getTissueDensity(): number {
    return this.tissueDensity;
  }

  /**
   * 设置组织密度
   * @param density 组织密度
   */
  public setTissueDensity(density: number): void {
    this.tissueDensity = density;
  }

  /**
   * 创建心脏模型
   * 使用四腔结构和主要血管连接点
   */
  public createHeart(): void {
    // 设置器官类型为心脏
    this.organType = OrganType.HEART;

    // 创建心脏四腔结构
    this.createHeartChambers();

    // 创建主要血管连接点
    this.createVesselConnections();

    // 设置心脏特定的物理参数
    this.setHeartPhysicsParameters();
  }

  /**
   * 创建心脏四腔结构
   * 创建左心房、右心房、左心室、右心室
   */
  private createHeartChambers(): void {
    // 这里应该创建心脏的四腔结构
    // 左心房 (Left Atrium)
    // 右心房 (Right Atrium)
    // 左心室 (Left Ventricle)
    // 右心室 (Right Ventricle)

    console.log('创建心脏四腔结构');
    // TODO: 实现具体的心脏腔室创建逻辑
  }

  /**
   * 创建血管连接点
   * 创建主要的血管连接，如主动脉、肺动脉等
   */
  private createVesselConnections(): void {
    // 清空现有连接
    this.vesselConnections = [];

    // 主动脉连接 (Aorta)
    this.vesselConnections.push({
      id: 'aorta',
      type: VesselConnectionType.ARTERIAL,
      position: new THREE.Vector3(0, 1, 0),
      direction: new THREE.Vector3(0, 1, 0),
      diameter: 25, // mm
      flowRate: 5000, // ml/min
      pressure: 120 // mmHg
    });

    // 肺动脉连接 (Pulmonary Artery)
    this.vesselConnections.push({
      id: 'pulmonary_artery',
      type: VesselConnectionType.ARTERIAL,
      position: new THREE.Vector3(-0.5, 0.8, 0),
      direction: new THREE.Vector3(-1, 1, 0).normalize(),
      diameter: 22, // mm
      flowRate: 5000, // ml/min
      pressure: 25 // mmHg
    });

    // 上腔静脉连接 (Superior Vena Cava)
    this.vesselConnections.push({
      id: 'superior_vena_cava',
      type: VesselConnectionType.VENOUS,
      position: new THREE.Vector3(0.5, 0.8, 0),
      direction: new THREE.Vector3(0, -1, 0),
      diameter: 20, // mm
      flowRate: 2500, // ml/min
      pressure: 5 // mmHg
    });

    // 下腔静脉连接 (Inferior Vena Cava)
    this.vesselConnections.push({
      id: 'inferior_vena_cava',
      type: VesselConnectionType.VENOUS,
      position: new THREE.Vector3(0.5, -0.8, 0),
      direction: new THREE.Vector3(0, 1, 0),
      diameter: 25, // mm
      flowRate: 2500, // ml/min
      pressure: 5 // mmHg
    });

    console.log(`创建了 ${this.vesselConnections.length} 个血管连接点`);
  }

  /**
   * 设置心脏特定的物理参数
   */
  private setHeartPhysicsParameters(): void {
    // 设置心脏特有的物理参数
    this.setTissueElasticity(0.8); // 心肌弹性
    this.setTissueDensity(1.06); // 心肌密度 (g/cm³)

    console.log('设置心脏物理参数完成');
  }

  /**
   * 模拟心脏搏动
   * @param rate 心率(次/分钟)
   * @param strength 收缩强度 (0-1)
   */
  public simulateHeartbeat(rate: number, strength: number): void {
    // 计算心跳周期 (秒)
    const period = 60 / rate;

    // 收缩期占心跳周期的约1/3
    const systoleDuration = period * 0.35;
    // 舒张期占心跳周期的约2/3
    // const diastoleDuration = period * 0.65;

    // 当前时间在心跳周期中的位置
    const currentTime = Date.now() / 1000;
    const cyclePosition = (currentTime % period) / period;

    let contractionFactor = 0;

    if (cyclePosition < systoleDuration / period) {
      // 收缩期 - 心肌收缩
      const systoleProgress = cyclePosition / (systoleDuration / period);
      contractionFactor = Math.sin(systoleProgress * Math.PI) * strength;
    } else {
      // 舒张期 - 心肌放松
      contractionFactor = 0;
    }

    // 应用收缩效果到软体物理
    this.applyContraction(contractionFactor);

    console.log(`心跳模拟: 心率=${rate}bpm, 强度=${strength}, 收缩因子=${contractionFactor.toFixed(3)}`);
  }

  /**
   * 应用收缩效果
   * @param factor 收缩因子 (0-1)
   */
  private applyContraction(factor: number): void {
    // 这里应该实际修改软体的物理属性来模拟心肌收缩
    // 例如调整粒子间的约束长度、刚度等

    // TODO: 实现具体的收缩物理效果
    // 可以通过修改软体约束的休息长度来实现收缩效果

    if (factor > 0.1) {
      console.log(`应用心肌收缩，收缩因子: ${factor.toFixed(3)}`);
    }
  }
}