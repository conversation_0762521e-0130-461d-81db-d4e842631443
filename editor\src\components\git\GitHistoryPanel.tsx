/**
 * Git历史面板组件
 * 显示Git提交历史
 */
import React, { useState, useEffect } from 'react';
import { Tag, Button, Tooltip, Space, Modal, message, Timeline } from 'antd';
import {
  HistoryOutlined,
  UserOutlined,
  ClockCircleOutlined,
  TagOutlined,
  BranchesOutlined,
  RollbackOutlined,
  DiffOutlined,
  CopyOutlined,
  SearchOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { gitService, GitCommit } from '../../services/GitService';
import './GitHistoryPanel.less';

const { Search } = Input;

/**
 * Git历史面板组件
 */
const GitHistoryPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { commitHistory, isLoading } = useSelector((state: RootState) => state.git);
  
  const [searchText, setSearchText] = useState<string>('');
  const [selectedCommit, setSelectedCommit] = useState<GitCommit | null>(null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState<boolean>(false);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filteredCommits, setFilteredCommits] = useState<GitCommit[]>([]);

  // 初始化
  useEffect(() => {
    setFilteredCommits(commitHistory);
  }, [commitHistory]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
    
    if (!value.trim()) {
      setFilteredCommits(commitHistory);
      return;
    }
    
    const filtered = commitHistory.filter(commit => 
      commit.message.toLowerCase().includes(value.toLowerCase()) ||
      commit.author.toLowerCase().includes(value.toLowerCase()) ||
      commit.hash.includes(value) ||
      commit.shortHash.includes(value)
    );
    
    setFilteredCommits(filtered);
  };

  // 处理排序
  const handleSort = () => {
    const newOrder = sortOrder === 'desc' ? 'asc' : 'desc';
    setSortOrder(newOrder);
    
    const sorted = [...filteredCommits].sort((a, b) => {
      const dateA = new Date(a.date).getTime();
      const dateB = new Date(b.date).getTime();
      return newOrder === 'desc' ? dateB - dateA : dateA - dateB;
    });
    
    setFilteredCommits(sorted);
  };

  // 处理查看提交详情
  const handleViewCommitDetail = (commit: GitCommit) => {
    setSelectedCommit(commit);
    setIsDetailModalVisible(true);
  };

  // 处理关闭详情对话框
  const handleCloseDetailModal = () => {
    setIsDetailModalVisible(false);
    setSelectedCommit(null);
  };

  // 处理复制提交哈希
  const handleCopyHash = (hash: string) => {
    navigator.clipboard.writeText(hash);
    message.success(t('git.hashCopied'));
  };

  // 处理查看差异
  const handleViewDiff = (commit: GitCommit) => {
    // 这里应该调用gitService的viewCommitDiff方法
    message.info(`${t('git.viewingCommitDiff')}: ${commit.shortHash}`);
  };

  // 处理回滚到提交
  const handleRollback = (commit: GitCommit) => {
    Modal.confirm({
      title: t('git.rollbackConfirmTitle'),
      content: t('git.rollbackConfirmContent', { hash: commit.shortHash }),
      okText: t('git.rollback'),
      cancelText: t('common.cancel'),
      onOk: () => {
        // 这里应该调用gitService的rollbackToCommit方法
        message.success(t('git.rollbackSuccess', { hash: commit.shortHash }));
      }});
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // 渲染提交列表
  const renderCommitList = () => {
    if (filteredCommits.length === 0) {
      return (
        <div className="git-empty-list">
          <p>{searchText ? t('git.noCommitsFound') : t('git.noCommits')}</p>
        </div>
      );
    }

    return (
      <Timeline mode="left">
        {filteredCommits.map((commit) => (
          <Timeline.Item
            key={commit.hash}
            dot={<HistoryOutlined />}
          >
            <div className="git-commit-item" onClick={() => handleViewCommitDetail(commit)}>
              <div className="git-commit-header">
                <div className="git-commit-title">
                  <span className="git-commit-message">{commit.message}</span>
                </div>
                <div className="git-commit-meta">
                  <Tag color="blue">{commit.shortHash}</Tag>
                  <span className="git-commit-author">
                    <UserOutlined /> {commit.author}
                  </span>
                  <span className="git-commit-date">
                    <ClockCircleOutlined /> {formatDate(commit.date)}
                  </span>
                </div>
              </div>
              <div className="git-commit-actions">
                <Space>
                  <Tooltip title={t('git.viewDiff')}>
                    <Button
                      icon={<DiffOutlined />}
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewDiff(commit);
                      }}
                    />
                  </Tooltip>
                  <Tooltip title={t('git.copyHash')}>
                    <Button
                      icon={<CopyOutlined />}
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCopyHash(commit.hash);
                      }}
                    />
                  </Tooltip>
                  <Tooltip title={t('git.rollback')}>
                    <Button
                      icon={<RollbackOutlined />}
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRollback(commit);
                      }}
                    />
                  </Tooltip>
                </Space>
              </div>
            </div>
          </Timeline.Item>
        ))}
      </Timeline>
    );
  };

  // 渲染提交详情对话框
  const renderCommitDetailModal = () => {
    if (!selectedCommit) return null;

    return (
      <Modal
        title={t('git.commitDetail')}
        open={isDetailModalVisible}
        onCancel={handleCloseDetailModal}
        footer={[
          <Button key="close" onClick={handleCloseDetailModal}>
            {t('common.close')}
          </Button>,
          <Button
            key="diff"
            icon={<DiffOutlined />}
            onClick={() => handleViewDiff(selectedCommit)}
          >
            {t('git.viewDiff')}
          </Button>,
          <Button
            key="rollback"
            icon={<RollbackOutlined />}
            onClick={() => {
              handleCloseDetailModal();
              handleRollback(selectedCommit);
            }}
          >
            {t('git.rollback')}
          </Button>,
        ]}
        width={700}
      >
        <div className="git-commit-detail">
          <div className="git-commit-detail-item">
            <div className="git-commit-detail-label">{t('git.message')}:</div>
            <div className="git-commit-detail-value">{selectedCommit.message}</div>
          </div>
          <div className="git-commit-detail-item">
            <div className="git-commit-detail-label">{t('git.author')}:</div>
            <div className="git-commit-detail-value">
              {selectedCommit.author} ({selectedCommit.email})
            </div>
          </div>
          <div className="git-commit-detail-item">
            <div className="git-commit-detail-label">{t('git.date')}:</div>
            <div className="git-commit-detail-value">{formatDate(selectedCommit.date)}</div>
          </div>
          <div className="git-commit-detail-item">
            <div className="git-commit-detail-label">{t('git.hash')}:</div>
            <div className="git-commit-detail-value">
              {selectedCommit.hash}
              <Button
                icon={<CopyOutlined />}
                size="small"
                onClick={() => handleCopyHash(selectedCommit.hash)}
                style={{ marginLeft: 8 }}
              />
            </div>
          </div>
        </div>
      </Modal>
    );
  };

  return (
    <div className="git-history-panel">
      {/* 搜索和过滤 */}
      <div className="git-history-toolbar">
        <Search
          placeholder={t('git.searchCommits')}
          onSearch={handleSearch}
          onChange={(e) => setSearchText(e.target.value)}
          value={searchText}
          style={{ width: 300 }}
          allowClear
        />
        <Tooltip title={sortOrder === 'desc' ? t('git.sortAscending') : t('git.sortDescending')}>
          <Button
            icon={sortOrder === 'desc' ? <SortAscendingOutlined /> : <SortDescendingOutlined />}
            onClick={handleSort}
          />
        </Tooltip>
      </div>

      {/* 提交历史列表 */}
      <div className="git-history-list">
        {isLoading ? (
          <div className="git-loading">
            <p>{t('git.loadingCommits')}</p>
          </div>
        ) : (
          renderCommitList()
        )}
      </div>

      {/* 提交详情对话框 */}
      {renderCommitDetailModal()}
    </div>
  );
};

export default GitHistoryPanel;
