/**
 * 环境感知面板
 * 
 * 该面板显示和编辑环境感知组件的属性，包括环境类型、天气、光照等。
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
   
  Select, 
  Button, 
  Slider, 
  SpaceNumber, 
  Tooltip, 
  Tag, 
  Row, 
  Col,
  Tabs,
  message
} from 'antd';
import { 
  EnvironmentOutlined, 
  CloudOutlined, 
  BulbOutlined, 
  CompassOutlined, 
  SoundOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  ReloadOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
// 移除引擎直接导入

const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { TabPane } = Tabs;

/**
 * 环境感知面板属性接口
 */
interface EnvironmentAwarenessPanelProps {
  entityId?: string;
  onSave?: (data: Partial<EnvironmentAwarenessData>) => void;
  onRefresh?: () => void;
  environmentData?: EnvironmentAwarenessData;
}

/**
 * 环境感知面板组件
 */
const EnvironmentAwarenessPanel: React.FC<EnvironmentAwarenessPanelProps> = ({
  entityId,
  onSave,
  onRefresh,
  environmentData
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState<string>('basic');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [localEnvironmentData, setLocalEnvironmentData] = useState<EnvironmentAwarenessData | null>(null);

  // 初始化环境数据
  useEffect(() => {
    if (environmentData) {
      setLocalEnvironmentData(environmentData);
      form.setFieldsValue({
        environmentType: environmentData.environmentType,
        weatherType: environmentData.weatherType,
        terrainType: environmentData.terrainType,
        lightIntensity: environmentData.lightIntensity,
        temperature: environmentData.temperature,
        humidity: environmentData.humidity,
        windSpeed: environmentData.windSpeed,
        noiseLevel: environmentData.noiseLevel,
        airQuality: environmentData.airQuality,
        waterLevel: environmentData.waterLevel,
        visibility: environmentData.visibility,
        timeOfDay: environmentData.timeOfDay
      });
    }
  }, [environmentData, form]);

  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
      message.success(t('environment.dataRefreshed'));
    }
  };

  /**
   * 处理编辑
   */
  const handleEdit = () => {
    setIsEditing(true);
  };

  /**
   * 处理取消
   */
  const handleCancel = () => {
    setIsEditing(false);
    if (environmentData) {
      form.setFieldsValue({
        environmentType: environmentData.environmentType,
        weatherType: environmentData.weatherType,
        terrainType: environmentData.terrainType,
        lightIntensity: environmentData.lightIntensity,
        temperature: environmentData.temperature,
        humidity: environmentData.humidity,
        windSpeed: environmentData.windSpeed,
        noiseLevel: environmentData.noiseLevel,
        airQuality: environmentData.airQuality,
        waterLevel: environmentData.waterLevel,
        visibility: environmentData.visibility,
        timeOfDay: environmentData.timeOfDay
      });
    }
  };

  /**
   * 处理保存
   */
  const handleSave = () => {
    form.validateFields().then(values => {
      if (onSave) {
        onSave(values);
        setIsEditing(false);
        message.success(t('environment.dataSaved'));
      }
    });
  };

  /**
   * 渲染基本信息标签页
   */
  const renderBasicTab = () => {
    return (
      <div className="basic-tab">
        <Form
          form={form}
          layout="vertical"
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="environmentType"
                label={t('environment.environmentType')}
                rules={[{ required: true, message: t('environment.environmentTypeRequired') }]}
              >
                <Select>
                  <Option value={EnvironmentType.INDOOR}>{t('environment.environmentTypes.indoor')}</Option>
                  <Option value={EnvironmentType.OUTDOOR}>{t('environment.environmentTypes.outdoor')}</Option>
                  <Option value={EnvironmentType.UNDERWATER}>{t('environment.environmentTypes.underwater')}</Option>
                  <Option value={EnvironmentType.SPACE}>{t('environment.environmentTypes.space')}</Option>
                  <Option value={EnvironmentType.CAVE}>{t('environment.environmentTypes.cave')}</Option>
                  <Option value={EnvironmentType.FOREST}>{t('environment.environmentTypes.forest')}</Option>
                  <Option value={EnvironmentType.DESERT}>{t('environment.environmentTypes.desert')}</Option>
                  <Option value={EnvironmentType.SNOW}>{t('environment.environmentTypes.snow')}</Option>
                  <Option value={EnvironmentType.URBAN}>{t('environment.environmentTypes.urban')}</Option>
                  <Option value={EnvironmentType.CUSTOM}>{t('environment.environmentTypes.custom')}</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="weatherType"
                label={t('environment.weatherType')}
                rules={[{ required: true, message: t('environment.weatherTypeRequired') }]}
              >
                <Select>
                  <Option value={WeatherType.CLEAR}>{t('environment.weatherTypes.clear')}</Option>
                  <Option value={WeatherType.CLOUDY}>{t('environment.weatherTypes.cloudy')}</Option>
                  <Option value={WeatherType.RAINY}>{t('environment.weatherTypes.rainy')}</Option>
                  <Option value={WeatherType.STORMY}>{t('environment.weatherTypes.stormy')}</Option>
                  <Option value={WeatherType.SNOWY}>{t('environment.weatherTypes.snowy')}</Option>
                  <Option value={WeatherType.FOGGY}>{t('environment.weatherTypes.foggy')}</Option>
                  <Option value={WeatherType.CUSTOM}>{t('environment.weatherTypes.custom')}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="terrainType"
                label={t('environment.terrainType')}
                rules={[{ required: true, message: t('environment.terrainTypeRequired') }]}
              >
                <Select>
                  <Option value={TerrainType.FLAT}>{t('environment.terrainTypes.flat')}</Option>
                  <Option value={TerrainType.HILLS}>{t('environment.terrainTypes.hills')}</Option>
                  <Option value={TerrainType.MOUNTAINS}>{t('environment.terrainTypes.mountains')}</Option>
                  <Option value={TerrainType.WATER}>{t('environment.terrainTypes.water')}</Option>
                  <Option value={TerrainType.URBAN}>{t('environment.terrainTypes.urban')}</Option>
                  <Option value={TerrainType.CUSTOM}>{t('environment.terrainTypes.custom')}</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="timeOfDay"
                label={t('environment.timeOfDay')}
                rules={[{ required: true, message: t('environment.timeOfDayRequired') }]}
              >
                <Slider
                  min={0}
                  max={24}
                  step={0.5}
                  marks={{
                    0: '0:00',
                    6: '6:00',
                    12: '12:00',
                    18: '18:00',
                    24: '24:00'
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };

  /**
   * 渲染环境条件标签页
   */
  const renderConditionsTab = () => {
    return (
      <div className="conditions-tab">
        <Form
          form={form}
          layout="vertical"
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="temperature"
                label={t('environment.temperature')}
                rules={[{ required: true, message: t('environment.temperatureRequired') }]}
              >
                <Slider
                  min={-50}
                  max={50}
                  marks={{
                    '-50': '-50°C',
                    '0': '0°C',
                    '25': '25°C',
                    '50': '50°C'
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="humidity"
                label={t('environment.humidity')}
                rules={[{ required: true, message: t('environment.humidityRequired') }]}
              >
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  marks={{
                    0: '0%',
                    0.5: '50%',
                    1: '100%'
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="windSpeed"
                label={t('environment.windSpeed')}
                rules={[{ required: true, message: t('environment.windSpeedRequired') }]}
              >
                <Slider
                  min={0}
                  max={30}
                  marks={{
                    0: '0 m/s',
                    10: '10 m/s',
                    20: '20 m/s',
                    30: '30 m/s'
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="visibility"
                label={t('environment.visibility')}
                rules={[{ required: true, message: t('environment.visibilityRequired') }]}
              >
                <Slider
                  min={0}
                  max={2000}
                  step={10}
                  marks={{
                    0: '0m',
                    500: '500m',
                    1000: '1km',
                    2000: '2km'
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };

  /**
   * 渲染光照标签页
   */
  const renderLightingTab = () => {
    return (
      <div className="lighting-tab">
        <Form
          form={form}
          layout="vertical"
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="lightIntensity"
                label={t('environment.lightIntensity')}
                rules={[{ required: true, message: t('environment.lightIntensityRequired') }]}
              >
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  marks={{
                    0: t('environment.dark'),
                    0.5: t('environment.medium'),
                    1: t('environment.bright')
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };

  /**
   * 渲染其他标签页
   */
  const renderOthersTab = () => {
    return (
      <div className="others-tab">
        <Form
          form={form}
          layout="vertical"
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="noiseLevel"
                label={t('environment.noiseLevel')}
                rules={[{ required: true, message: t('environment.noiseLevelRequired') }]}
              >
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  marks={{
                    0: t('environment.quiet'),
                    0.5: t('environment.moderate'),
                    1: t('environment.loud')
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="airQuality"
                label={t('environment.airQuality')}
                rules={[{ required: true, message: t('environment.airQualityRequired') }]}
              >
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  marks={{
                    0: t('environment.poor'),
                    0.5: t('environment.moderate'),
                    1: t('environment.excellent')
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="waterLevel"
                label={t('environment.waterLevel')}
                rules={[{ required: true, message: t('environment.waterLevelRequired') }]}
              >
                <Slider
                  min={0}
                  max={10}
                  step={0.1}
                  marks={{
                    0: '0m',
                    2.5: '2.5m',
                    5: '5m',
                    7.5: '7.5m',
                    10: '10m'
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };

  /**
   * 渲染环境数据概览
   */
  const renderEnvironmentDataOverview = () => {
    if (!localEnvironmentData) return null;

    return (
      <div className="environment-data-overview">
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card size="small" title={t('environment.environmentType')}>
              <Tag color="blue">{localEnvironmentData.environmentType}</Tag>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title={t('environment.weatherType')}>
              <Tag color="green">{localEnvironmentData.weatherType}</Tag>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title={t('environment.terrainType')}>
              <Tag color="orange">{localEnvironmentData.terrainType}</Tag>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={6}>
            <Card size="small" title={t('environment.temperature')}>
              {localEnvironmentData.temperature.toFixed(1)}°C
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title={t('environment.humidity')}>
              {(localEnvironmentData.humidity * 100).toFixed(0)}%
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title={t('environment.windSpeed')}>
              {localEnvironmentData.windSpeed.toFixed(1)} m/s
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title={t('environment.timeOfDay')}>
              {Math.floor(localEnvironmentData.timeOfDay)}:{(localEnvironmentData.timeOfDay % 1 * 60).toFixed(0).padStart(2, '0')}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={8}>
            <Card size="small" title={t('environment.lightIntensity')}>
              {(localEnvironmentData.lightIntensity * 100).toFixed(0)}%
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title={t('environment.visibility')}>
              {localEnvironmentData.visibility.toFixed(0)} m
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" title={t('environment.waterLevel')}>
              {localEnvironmentData.waterLevel.toFixed(1)} m
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <div className="environment-awareness-panel">
      <Card
        title={
          <Space>
            <EnvironmentOutlined />
            <span>{t('environment.awarenessPanel')}</span>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title={t('environment.refresh')}>
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
            {isEditing ? (
              <>
                <Button onClick={handleCancel}>{t('environment.cancel')}</Button>
                <Button type="primary" onClick={handleSave}>{t('environment.save')}</Button>
              </>
            ) : (
              <Button type="primary" icon={<SettingOutlined />} onClick={handleEdit}>
                {t('environment.edit')}
              </Button>
            )}
          </Space>
        }
      >
        {localEnvironmentData ? (
          <>
            {!isEditing && renderEnvironmentDataOverview()}
            
            {isEditing && (
              <Tabs activeKey={activeTab} onChange={setActiveTab}>
                <TabPane
                  tab={
                    <span>
                      <EnvironmentOutlined />
                      {t('environment.basic')}
                    </span>
                  }
                  key="basic"
                >
                  {renderBasicTab()}
                </TabPane>
                <TabPane
                  tab={
                    <span>
                      <CloudOutlined />
                      {t('environment.conditions')}
                    </span>
                  }
                  key="conditions"
                >
                  {renderConditionsTab()}
                </TabPane>
                <TabPane
                  tab={
                    <span>
                      <BulbOutlined />
                      {t('environment.lighting')}
                    </span>
                  }
                  key="lighting"
                >
                  {renderLightingTab()}
                </TabPane>
                <TabPane
                  tab={
                    <span>
                      <SoundOutlined />
                      {t('environment.others')}
                    </span>
                  }
                  key="others"
                >
                  {renderOthersTab()}
                </TabPane>
              </Tabs>
            )}
          </>
        ) : (
          <div className="no-data">
            <p>{t('environment.noData')}</p>
            <Button type="primary" onClick={handleRefresh}>
              {t('environment.refresh')}
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
};

export default EnvironmentAwarenessPanel;
