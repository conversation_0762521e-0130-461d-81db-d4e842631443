/**
 * 场景树组件测试
 */
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import { SceneTree } from './SceneTree';

// 创建模拟的Redux store
const mockStore = configureStore([]);

// 模拟场景数据
const mockSceneData = {
  id: 'scene1',
  name: '测试场景',
  entities: [
    {
      id: 'entity1',
      name: '实体1',
      type: 'mesh',
      children: [
        {
          id: 'entity3',
          name: '实体3',
          type: 'mesh',
          children: []},
      ]},
    {
      id: 'entity2',
      name: '实体2',
      type: 'light',
      children: []},
  ]};

// 模拟选中实体的动作
const mockSelectEntity = jest.fn() as any;

// 模拟展开/折叠节点的动作
const mockToggleNode = jest.fn() as any;

describe('SceneTree 组件', () => {
  let store;

  beforeEach(() => {
    store = mockStore({
      scene: {
        currentScene: mockSceneData,
        selectedEntityId: null,
        expandedNodes: {}}});

    // 模拟Redux动作
    store.dispatch = jest.fn() as any;

    render(
      <Provider store={store}>
        <SceneTree
          onSelectEntity={mockSelectEntity}
          onToggleNode={mockToggleNode}
        />
      </Provider>
    );
  });

  it('应该渲染场景名称', () => {
    expect(screen.getByText('测试场景')).toBeInTheDocument();
  });

  it('应该渲染所有顶级实体', () => {
    expect(screen.getByText('实体1')).toBeInTheDocument();
    expect(screen.getByText('实体2')).toBeInTheDocument();
  });

  it('点击实体时应该调用选择实体函数', () => {
    fireEvent.click(screen.getByText('实体1'));
    expect(mockSelectEntity).toHaveBeenCalledWith('entity1');
  });

  it('点击展开图标时应该调用展开/折叠节点函数', () => {
    // 找到展开图标并点击
    const expandIcons = screen.getAllByTestId('expand-icon');
    fireEvent.click(expandIcons[0]);
    expect(mockToggleNode).toHaveBeenCalledWith('entity1');
  });

  it('展开节点后应该显示子实体', () => {
    // 模拟节点已展开的状态
    store = mockStore({
      scene: {
        currentScene: mockSceneData,
        selectedEntityId: null,
        expandedNodes: { entity1: true }}});

    // 重新渲染组件
    store.dispatch = jest.fn() as any;
    render(
      <Provider store={store}>
        <SceneTree
          onSelectEntity={mockSelectEntity}
          onToggleNode={mockToggleNode}
        />
      </Provider>
    );

    // 检查子实体是否显示
    expect(screen.getByText('实体3')).toBeInTheDocument();
  });

  it('应该根据实体类型显示不同的图标', () => {
    // 检查网格实体图标
    const meshIcons = screen.getAllByTestId('mesh-icon');
    expect(meshIcons.length).toBe(1); // 只有顶级实体1是网格

    // 检查光源实体图标
    const lightIcons = screen.getAllByTestId('light-icon');
    expect(lightIcons.length).toBe(1);
  });

  it('选中的实体应该有不同的样式', () => {
    // 模拟实体已选中的状态
    store = mockStore({
      scene: {
        currentScene: mockSceneData,
        selectedEntityId: 'entity2',
        expandedNodes: {}}});

    // 重新渲染组件
    store.dispatch = jest.fn() as any;
    render(
      <Provider store={store}>
        <SceneTree
          onSelectEntity={mockSelectEntity}
          onToggleNode={mockToggleNode}
        />
      </Provider>
    );

    // 检查选中的实体是否有选中样式
    const selectedEntity = screen.getByTestId('entity-item-entity2');
    expect(selectedEntity).toHaveClass('selected');
  });
});
