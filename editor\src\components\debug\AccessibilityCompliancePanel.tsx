/**
 * 辅助功能合规性测试面板
 * 用于测试编辑器的WCAG 2.1 AA标准合规性
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Divider, Space, Typography, Row, Col, Checkbox, Progress, Table, Tabs, Select, Alert, Tag} from 'antd';
import { useTranslation } from 'react-i18next';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  FileTextOutlined,
  DownloadOutlined,
  BugOutlined,
  EyeOutlined,
  SettingOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import AccessibilityComplianceTester, { TestConfig, TestReport, TestResult, TestResultType, AccessibilityComplianceTesterEventType } from '../../services/AccessibilityComplianceTester';
import AccessibilityComplianceReporter, { ReportFormat, ReportOptions } from '../../services/AccessibilityComplianceReporter';
import './AccessibilityCompliancePanel.less';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';

/**
 * 辅助功能合规性测试面板属性
 */
interface AccessibilityCompliancePanelProps {
  /** 是否显示标题 */
  showTitle?: boolean;
}

/**
 * 辅助功能合规性测试面板
 */
const AccessibilityCompliancePanel: React.FC<AccessibilityCompliancePanelProps> = ({
  showTitle = true
}) => {
  const { t } = useTranslation();
  
  // 测试状态
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [testReport, setTestReport] = useState<TestReport | null>(null);
  const [activeTab, setActiveTab] = useState<string>('config');
  const [selectedResult, setSelectedResult] = useState<TestResult | null>(null);
  
  // 测试配置
  const [testConfig, setTestConfig] = useState<TestConfig>({
    testColorContrast: true,
    testKeyboardAccessibility: true,
    testScreenReaderSupport: true,
    testTextScaling: true,
    testReducedMotion: true,
    testImageAltText: true,
    testFormLabels: true,
    testHeadingStructure: true,
    testLanguageSettings: true,
    testErrorIdentification: true,
    testFocusOrder: true,
    testFocusVisibility: true,
    testPageTitle: true,
    testLinkPurpose: true,
    testContentHoverFocus: true,
    testInputModalities: true,
    testStatusMessages: true
  });
  
  // 报告选项
  const [reportFormat, setReportFormat] = useState<ReportFormat>(ReportFormat.HTML);
  const [reportOptions, setReportOptions] = useState<ReportOptions>({
    includeDetails: true,
    includeFixSuggestions: true,
    includeElements: false,
    includeCharts: true,
    includeWcagInfo: true,
    includeComplianceScore: true,
    includeTimestamp: true,
    includeSummary: true
  });
  
  // 初始化
  useEffect(() => {
    // 监听测试进度
    const handleTestProgress = (data: any) => {
      const { completedTests, totalTests } = data;
      const progressValue = Math.round((completedTests / totalTests) * 100);
      setProgress(progressValue);
    };
    
    // 监听测试完成
    const handleTestCompleted = (report: TestReport) => {
      setTestReport(report);
      setIsRunning(false);
      setProgress(100);
      setActiveTab('results');
    };
    
    // 添加事件监听
    AccessibilityComplianceTester.on(AccessibilityComplianceTesterEventType.TEST_PROGRESS, handleTestProgress);
    AccessibilityComplianceTester.on(AccessibilityComplianceTesterEventType.TEST_COMPLETED, handleTestCompleted);
    
    // 清理事件监听
    return () => {
      AccessibilityComplianceTester.off(AccessibilityComplianceTesterEventType.TEST_PROGRESS, handleTestProgress);
      AccessibilityComplianceTester.off(AccessibilityComplianceTesterEventType.TEST_COMPLETED, handleTestCompleted);
    };
  }, []);
  
  // 运行测试
  const runTests = async () => {
    setIsRunning(true);
    setProgress(0);
    setTestReport(null);
    setSelectedResult(null);
    
    // 配置测试工具
    AccessibilityComplianceTester.configure(testConfig);
    
    try {
      // 运行测试
      await AccessibilityComplianceTester.runAllTests();
    } catch (error) {
      console.error('运行测试失败:', error);
      setIsRunning(false);
    }
  };
  
  // 生成报告
  const generateReport = () => {
    if (!testReport) return;
    
    try {
      // 生成报告
      const report = AccessibilityComplianceReporter.generateReport(testReport, {
        format: reportFormat,
        title: '辅助功能合规性测试报告',
        ...reportOptions
      });
      
      // 下载报告
      const blob = new Blob([report], { type: getContentType(reportFormat) });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `accessibility-compliance-report.${getFileExtension(reportFormat)}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('生成报告失败:', error);
    }
  };
  
  // 获取内容类型
  const getContentType = (format: ReportFormat): string => {
    switch (format) {
      case ReportFormat.HTML:
        return 'text/html';
      case ReportFormat.JSON:
        return 'application/json';
      case ReportFormat.CSV:
        return 'text/csv';
      case ReportFormat.MARKDOWN:
        return 'text/markdown';
      default:
        return 'text/plain';
    }
  };
  
  // 获取文件扩展名
  const getFileExtension = (format: ReportFormat): string => {
    switch (format) {
      case ReportFormat.HTML:
        return 'html';
      case ReportFormat.JSON:
        return 'json';
      case ReportFormat.CSV:
        return 'csv';
      case ReportFormat.MARKDOWN:
        return 'md';
      default:
        return 'txt';
    }
  };
  
  // 处理配置变更
  const handleConfigChange = (key: keyof TestConfig, value: boolean) => {
    setTestConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };
  
  // 处理报告选项变更
  const handleReportOptionChange = (key: keyof ReportOptions, value: boolean) => {
    setReportOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };
  
  // 获取结果标签
  const getResultTag = (resultType: TestResultType) => {
    switch (resultType) {
      case TestResultType.PASS:
        return <Tag color="success" icon={<CheckCircleOutlined />}>{t('accessibility.pass')}</Tag>;
      case TestResultType.FAIL:
        return <Tag color="error" icon={<CloseCircleOutlined />}>{t('accessibility.fail')}</Tag>;
      case TestResultType.WARNING:
        return <Tag color="warning" icon={<WarningOutlined />}>{t('accessibility.warning')}</Tag>;
      case TestResultType.NOT_APPLICABLE:
        return <Tag color="default" icon={<InfoCircleOutlined />}>{t('accessibility.notApplicable')}</Tag>;
      default:
        return null;
    }
  };
  
  // 渲染测试配置
  const renderTestConfig = () => {
    return (
      <Card>
        <Title level={5}>{t('accessibility.testConfiguration')}</Title>
        <Paragraph>{t('accessibility.testConfigurationDescription')}</Paragraph>
        
        <Row gutter={[16, 8]}>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testColorContrast}
              onChange={e => handleConfigChange('testColorContrast', e.target.checked)}
            >
              {t('accessibility.testColorContrast')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testKeyboardAccessibility}
              onChange={e => handleConfigChange('testKeyboardAccessibility', e.target.checked)}
            >
              {t('accessibility.testKeyboardAccessibility')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testScreenReaderSupport}
              onChange={e => handleConfigChange('testScreenReaderSupport', e.target.checked)}
            >
              {t('accessibility.testScreenReaderSupport')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testTextScaling}
              onChange={e => handleConfigChange('testTextScaling', e.target.checked)}
            >
              {t('accessibility.testTextScaling')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testReducedMotion}
              onChange={e => handleConfigChange('testReducedMotion', e.target.checked)}
            >
              {t('accessibility.testReducedMotion')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testImageAltText}
              onChange={e => handleConfigChange('testImageAltText', e.target.checked)}
            >
              {t('accessibility.testImageAltText')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testFormLabels}
              onChange={e => handleConfigChange('testFormLabels', e.target.checked)}
            >
              {t('accessibility.testFormLabels')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testHeadingStructure}
              onChange={e => handleConfigChange('testHeadingStructure', e.target.checked)}
            >
              {t('accessibility.testHeadingStructure')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testLanguageSettings}
              onChange={e => handleConfigChange('testLanguageSettings', e.target.checked)}
            >
              {t('accessibility.testLanguageSettings')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testErrorIdentification}
              onChange={e => handleConfigChange('testErrorIdentification', e.target.checked)}
            >
              {t('accessibility.testErrorIdentification')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testFocusOrder}
              onChange={e => handleConfigChange('testFocusOrder', e.target.checked)}
            >
              {t('accessibility.testFocusOrder')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testFocusVisibility}
              onChange={e => handleConfigChange('testFocusVisibility', e.target.checked)}
            >
              {t('accessibility.testFocusVisibility')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testPageTitle}
              onChange={e => handleConfigChange('testPageTitle', e.target.checked)}
            >
              {t('accessibility.testPageTitle')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testLinkPurpose}
              onChange={e => handleConfigChange('testLinkPurpose', e.target.checked)}
            >
              {t('accessibility.testLinkPurpose')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testContentHoverFocus}
              onChange={e => handleConfigChange('testContentHoverFocus', e.target.checked)}
            >
              {t('accessibility.testContentHoverFocus')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testInputModalities}
              onChange={e => handleConfigChange('testInputModalities', e.target.checked)}
            >
              {t('accessibility.testInputModalities')}
            </Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox
              checked={testConfig.testStatusMessages}
              onChange={e => handleConfigChange('testStatusMessages', e.target.checked)}
            >
              {t('accessibility.testStatusMessages')}
            </Checkbox>
          </Col>
        </Row>
        
        <Divider />
        
        <Space>
          <Button
            type="primary"
            icon={<BugOutlined />}
            onClick={runTests}
            loading={isRunning}
            disabled={isRunning}
          >
            {isRunning ? t('accessibility.testingInProgress') : t('accessibility.runTests')}
          </Button>
          
          <Button
            icon={<ReloadOutlined />}
            onClick={() => setTestConfig({
              testColorContrast: true,
              testKeyboardAccessibility: true,
              testScreenReaderSupport: true,
              testTextScaling: true,
              testReducedMotion: true,
              testImageAltText: true,
              testFormLabels: true,
              testHeadingStructure: true,
              testLanguageSettings: true,
              testErrorIdentification: true,
              testFocusOrder: true,
              testFocusVisibility: true,
              testPageTitle: true,
              testLinkPurpose: true,
              testContentHoverFocus: true,
              testInputModalities: true,
              testStatusMessages: true
            })}
          >
            {t('accessibility.resetConfig')}
          </Button>
        </Space>
        
        {isRunning && (
          <div className="progress-container">
            <Divider />
            <Progress percent={progress} status="active" />
            <div className="progress-text">{t('accessibility.testingInProgress')}: {progress}%</div>
          </div>
        )}
      </Card>
    );
  };
  
  // 渲染测试结果
  const renderTestResults = () => {
    if (!testReport) {
      return (
        <Card>
          <Alert
            message={t('accessibility.noTestResults')}
            description={t('accessibility.runTestsFirst')}
            type="info"
            showIcon
          />
        </Card>
      );
    }
    
    return (
      <Card>
        <div className="results-summary">
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Card className="summary-card pass">
                <div className="summary-number">{testReport.passCount}</div>
                <div className="summary-label">{t('accessibility.pass')}</div>
              </Card>
            </Col>
            <Col span={6}>
              <Card className="summary-card fail">
                <div className="summary-number">{testReport.failCount}</div>
                <div className="summary-label">{t('accessibility.fail')}</div>
              </Card>
            </Col>
            <Col span={6}>
              <Card className="summary-card warning">
                <div className="summary-number">{testReport.warningCount}</div>
                <div className="summary-label">{t('accessibility.warning')}</div>
              </Card>
            </Col>
            <Col span={6}>
              <Card className="summary-card not-applicable">
                <div className="summary-number">{testReport.notApplicableCount}</div>
                <div className="summary-label">{t('accessibility.notApplicable')}</div>
              </Card>
            </Col>
          </Row>
          
          <div className="compliance-score">
            <Title level={4}>{t('accessibility.complianceScore')}</Title>
            <Progress
              type="circle"
              percent={testReport.complianceScore}
              format={percent => `${percent}%`}
              status={testReport.complianceScore >= 90 ? 'success' : (testReport.complianceScore >= 70 ? 'normal' : 'exception')}
              width={120}
            />
          </div>
        </div>
        
        <Divider />
        
        <Table
          dataSource={testReport.results.map((result, index) => ({ ...result, key: index }))}
          columns={[
            {
              title: t('accessibility.testName'),
              dataIndex: 'name',
              key: 'name',
              render: (text, record) => (
                <Button
                  type="link"
                  onClick={() => setSelectedResult(record)}
                >
                  {text}
                </Button>
              )
            },
            {
              title: t('accessibility.result'),
              dataIndex: 'resultType',
              key: 'resultType',
              render: resultType => getResultTag(resultType)
            },
            {
              title: t('accessibility.wcagStandard'),
              dataIndex: 'wcagStandard',
              key: 'wcagStandard'
            },
            {
              title: t('accessibility.description'),
              dataIndex: 'description',
              key: 'description',
              ellipsis: true
            }
          ]}
          pagination={{ pageSize: 10 }}
        />
        
        {selectedResult && (
          <div className="result-details">
            <Divider>{t('accessibility.testDetails')}</Divider>
            <Card>
              <Title level={5}>{selectedResult.name}</Title>
              <div className="result-info">
                <div>
                  <strong>{t('accessibility.result')}:</strong> {getResultTag(selectedResult.resultType)}
                </div>
                <div>
                  <strong>{t('accessibility.wcagStandard')}:</strong> {selectedResult.wcagStandard}
                </div>
                <div>
                  <strong>{t('accessibility.description')}:</strong> {selectedResult.description}
                </div>
                {selectedResult.fixSuggestion && (
                  <div className="fix-suggestion">
                    <strong>{t('accessibility.fixSuggestion')}:</strong> {selectedResult.fixSuggestion}
                  </div>
                )}
              </div>
            </Card>
          </div>
        )}
      </Card>
    );
  };
  
  // 渲染报告选项
  const renderReportOptions = () => {
    return (
      <Card>
        <Title level={5}>{t('accessibility.reportOptions')}</Title>
        <Paragraph>{t('accessibility.reportOptionsDescription')}</Paragraph>
        
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <div className="option-label">{t('accessibility.reportFormat')}</div>
            <Select
              style={{ width: '100%' }}
              value={reportFormat}
              onChange={value => setReportFormat(value)}
            >
              <Option value={ReportFormat.HTML}>HTML</Option>
              <Option value={ReportFormat.JSON}>JSON</Option>
              <Option value={ReportFormat.CSV}>CSV</Option>
              <Option value={ReportFormat.MARKDOWN}>Markdown</Option>
            </Select>
          </Col>
          
          <Col span={24}>
            <Divider orientation="left">{t('accessibility.includeInReport')}</Divider>
            <Row gutter={[16, 8]}>
              <Col span={12}>
                <Checkbox
                  checked={reportOptions.includeDetails}
                  onChange={e => handleReportOptionChange('includeDetails', e.target.checked)}
                >
                  {t('accessibility.includeDetails')}
                </Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox
                  checked={reportOptions.includeFixSuggestions}
                  onChange={e => handleReportOptionChange('includeFixSuggestions', e.target.checked)}
                >
                  {t('accessibility.includeFixSuggestions')}
                </Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox
                  checked={reportOptions.includeElements}
                  onChange={e => handleReportOptionChange('includeElements', e.target.checked)}
                >
                  {t('accessibility.includeElements')}
                </Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox
                  checked={reportOptions.includeCharts}
                  onChange={e => handleReportOptionChange('includeCharts', e.target.checked)}
                >
                  {t('accessibility.includeCharts')}
                </Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox
                  checked={reportOptions.includeWcagInfo}
                  onChange={e => handleReportOptionChange('includeWcagInfo', e.target.checked)}
                >
                  {t('accessibility.includeWcagInfo')}
                </Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox
                  checked={reportOptions.includeComplianceScore}
                  onChange={e => handleReportOptionChange('includeComplianceScore', e.target.checked)}
                >
                  {t('accessibility.includeComplianceScore')}
                </Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox
                  checked={reportOptions.includeTimestamp}
                  onChange={e => handleReportOptionChange('includeTimestamp', e.target.checked)}
                >
                  {t('accessibility.includeTimestamp')}
                </Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox
                  checked={reportOptions.includeSummary}
                  onChange={e => handleReportOptionChange('includeSummary', e.target.checked)}
                >
                  {t('accessibility.includeSummary')}
                </Checkbox>
              </Col>
            </Row>
          </Col>
        </Row>
        
        <Divider />
        
        <Button
          type="primary"
          icon={<DownloadOutlined />}
          onClick={generateReport}
          disabled={!testReport}
        >
          {t('accessibility.generateReport')}
        </Button>
      </Card>
    );
  };
  
  return (
    <div className="accessibility-compliance-panel">
      {showTitle && <Title level={4}>{t('accessibility.title')}</Title>}
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={<span><SettingOutlined />{t('accessibility.configTab')}</span>}
          key="config"
        >
          {renderTestConfig()}
        </TabPane>
        <TabPane
          tab={<span><BugOutlined />{t('accessibility.resultsTab')}</span>}
          key="results"
        >
          {renderTestResults()}
        </TabPane>
        <TabPane
          tab={<span><FileTextOutlined />{t('accessibility.reportTab')}</span>}
          key="report"
        >
          {renderReportOptions()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default AccessibilityCompliancePanel;
