/**
 * 动画遮罩编辑器组件
 * 用于编辑动画遮罩
 */
import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Select, Switch, Button, Table, Slider, InputNumber, Tabs, Card, Space, Tooltip, message, Collapse, Radio, Divider, Tree, Input as AntInput, Row, Col, Statistic } from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  ReloadOutlined,
  ClearOutlined,
  SwapOutlined,
  CheckOutlined,
  PartitionOutlined,
  AimOutlined,
  FieldTimeOutlined,
  DotChartOutlined,
  SlidersFilled,
  DownloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import * as THREE from 'three';
import './AnimationEditor.less';

const { Option } = Select;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Search } = AntInput;

// 定义本地类型以替代引擎类型
enum MaskType {
  INCLUDE = 'include',
  EXCLUDE = 'exclude',
  HIERARCHY = 'hierarchy',
  INVERSE_HIERARCHY = 'inverseHierarchy',
  GROUP = 'group',
  BLEND = 'blend',
  TRANSITION = 'transition'
}

enum MaskWeightType {
  BINARY = 'binary',
  WEIGHTED = 'weighted',
  SMOOTH = 'smooth',
  DISTANCE = 'distance',
  GRADIENT = 'gradient',
  DYNAMIC = 'dynamic'
}

enum BoneGroupType {
  UPPER_BODY = 'upperBody',
  LOWER_BODY = 'lowerBody',
  LEFT_ARM = 'leftArm',
  RIGHT_ARM = 'rightArm',
  LEFT_LEG = 'leftLeg',
  RIGHT_LEG = 'rightLeg',
  HEAD = 'head',
  SPINE = 'spine'
}

enum DynamicMaskType {
  DISTANCE = 'distance',
  DIRECTION = 'direction',
  VELOCITY = 'velocity',
  PARAMETER = 'parameter',
  TIME = 'time'
}

// 简化的动画遮罩接口
interface AnimationMask {
  getName(): string;
  getType(): MaskType;
  getWeightType(): MaskWeightType;
  getBones(): string[];
  getBoneWeight(bone: string): number;
  setBoneWeight(bone: string, weight: number): void;
}

// 简化的增强动画遮罩接口
interface AnimationMaskEnhanced extends AnimationMask {
  getDynamicType(): DynamicMaskType | undefined;
  isWeightInterpolationEnabled(): boolean;
  getWeightInterpolationSpeed(): number;
}

// 配置接口
interface AnimationMaskConfig {
  name: string;
  type: MaskType;
  weightType: MaskWeightType;
  bones: string[];
}

interface EnhancedMaskConfig extends AnimationMaskConfig {
  dynamicType?: DynamicMaskType;
  dynamicParams?: any;
  enableHierarchyCache?: boolean;
  enableWeightInterpolation?: boolean;
  weightInterpolationSpeed?: number;
}

/**
 * 遮罩编辑器属性
 */
interface MaskEditorProps {
  /** 遮罩对象 */
  mask: AnimationMask;
  /** 更新回调 */
  onUpdate: (mask: AnimationMask) => void;
  /** 骨骼对象（可选，用于层级和预览） */
  skeleton?: THREE.Skeleton;
  /** 是否启用高级功能 */
  enableAdvanced?: boolean;
}

/**
 * 骨骼数据
 */
interface BoneData {
  name: string;
  weight: number;
  /** 父骨骼名称（用于树状视图） */
  parent?: string;
  /** 子骨骼列表（用于树状视图） */
  children?: BoneData[];
  /** 骨骼深度（用于层级） */
  depth?: number;
  /** 骨骼在层级中的路径 */
  path?: string[];
}

/**
 * 预设数据
 */
interface PresetData {
  /** 预设名称 */
  name: string;
  /** 预设类型 */
  type: BoneGroupType | string;
  /** 预设描述 */
  description: string;
  /** 骨骼列表 */
  bones?: string[];
  /** 是否是内置预设 */
  builtin?: boolean;
}

/**
 * 动态参数数据
 */
interface DynamicParamsData {
  /** 目标点（用于距离和方向） */
  target?: THREE.Vector3;
  /** 最大距离（用于距离） */
  maxDistance?: number;
  /** 最小距离（用于距离） */
  minDistance?: number;
  /** 方向向量（用于方向） */
  direction?: THREE.Vector3;
  /** 最大角度（用于方向） */
  maxAngle?: number;
  /** 速度阈值（用于速度） */
  velocityThreshold?: number;
  /** 参数名称（用于参数） */
  paramName?: string;
  /** 参数范围（用于参数） */
  paramRange?: [number, number];
  /** 衰减类型 */
  falloffType?: string;
}

/**
 * 遮罩编辑器组件
 */
export const MaskEditor: React.FC<MaskEditorProps> = ({ mask, onUpdate, skeleton, enableAdvanced = false }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);

  // 本地状态
  const [maskType, setMaskType] = useState<MaskType>(MaskType.INCLUDE);
  const [weightType, setWeightType] = useState<MaskWeightType>(MaskWeightType.BINARY);
  const [bones, setBones] = useState<BoneData[]>([]);
  const [newBoneName, setNewBoneName] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('basic');
  const [viewMode, setViewMode] = useState<'flat' | 'tree'>('flat');
  const [searchText, setSearchText] = useState<string>('');
  const [_boneHierarchy, setBoneHierarchy] = useState<Map<string, string[]>>(new Map());
  const [rootBone, setRootBone] = useState<string>('');
  const [maxDepth, setMaxDepth] = useState<number>(10);
  const [includeChildren, setIncludeChildren] = useState<boolean>(true);
  const [dynamicType, setDynamicType] = useState<DynamicMaskType | undefined>(undefined);
  const [dynamicParams, setDynamicParams] = useState<DynamicParamsData>({});
  const [enableWeightInterpolation, setEnableWeightInterpolation] = useState<boolean>(false);
  const [weightInterpolationSpeed, setWeightInterpolationSpeed] = useState<number>(5.0);
  const [showWeights, setShowWeights] = useState<boolean>(true);
  const [colorMode, setColorMode] = useState<'heatmap' | 'gradient' | 'solid'>('heatmap');
  const [isEnhanced, setIsEnhanced] = useState<boolean>(false);
  const [customPresets, setCustomPresets] = useState<PresetData[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);
  const [newPresetName, setNewPresetName] = useState<string>('');

  // 可用骨骼列表
  const [availableBones, setAvailableBones] = useState<string[]>([
    'root', 'spine', 'spine1', 'spine2', 'neck', 'head',
    'leftShoulder', 'leftArm', 'leftForeArm', 'leftHand',
    'rightShoulder', 'rightArm', 'rightForeArm', 'rightHand',
    'leftUpLeg', 'leftLeg', 'leftFoot', 'leftToe',
    'rightUpLeg', 'rightLeg', 'rightFoot', 'rightToe'
  ]);

  // 初始化表单
  useEffect(() => {
    if (mask) {
      setMaskType(mask.getType());
      setWeightType(mask.getWeightType());

      // 检查是否是增强版遮罩
      const isEnhancedMask = 'getDynamicType' in mask;
      setIsEnhanced(isEnhancedMask);

      if (isEnhancedMask) {
        const enhancedMask = mask as AnimationMaskEnhanced;
        setDynamicType(enhancedMask.getDynamicType());
        setEnableWeightInterpolation(enhancedMask.isWeightInterpolationEnabled());
        setWeightInterpolationSpeed(enhancedMask.getWeightInterpolationSpeed());
      }

      // 获取骨骼列表
      const boneList: BoneData[] = [];
      const maskBones = mask.getBones();

      maskBones.forEach(bone => {
        boneList.push({
          name: bone,
          weight: mask.getBoneWeight(bone)
        });
      });

      setBones(boneList);

      // 设置表单值
      form.setFieldsValue({
        name: mask.getName(),
        type: mask.getType(),
        weightType: mask.getWeightType()
      });
    }
  }, [mask, form]);

  // 初始化骨骼层级
  useEffect(() => {
    if (skeleton) {
      // 构建骨骼层级
      const hierarchy = new Map<string, string[]>();
      const boneNames: string[] = [];

      // 获取所有骨骼名称
      skeleton.bones.forEach(bone => {
        boneNames.push(bone.name);

        // 初始化子骨骼列表
        hierarchy.set(bone.name, []);
      });

      // 构建父子关系
      skeleton.bones.forEach(bone => {
        if (bone.parent && bone.parent.type === 'Bone') {
          const parentName = bone.parent.name;
          const children = hierarchy.get(parentName) || [];
          children.push(bone.name);
          hierarchy.set(parentName, children);
        }
      });

      // 找到根骨骼
      let root = '';
      for (const bone of skeleton.bones) {
        if (!bone.parent || bone.parent.type !== 'Bone') {
          root = bone.name;
          break;
        }
      }

      // 更新状态
      setBoneHierarchy(hierarchy);
      setRootBone(root);
      setAvailableBones(boneNames);
    }
  }, [skeleton]);

  // 处理表单变更
  const handleFormChange = (changedValues: any) => {
    const values = form.getFieldsValue();

    if (changedValues.type !== undefined) {
      setMaskType(changedValues.type);
    }

    if (changedValues.weightType !== undefined) {
      setWeightType(changedValues.weightType);
    }

    // 更新遮罩
    updateMask(values);
  };

  // 添加骨骼
  const handleAddBone = () => {
    if (!newBoneName) {
      message.warning(t('editor.animation.mask.enterBoneName'));
      return;
    }

    // 检查是否已存在
    if (bones.some(bone => bone.name === newBoneName)) {
      message.warning(t('editor.animation.mask.boneExists'));
      return;
    }

    // 添加骨骼
    const newBones = [...bones, { name: newBoneName, weight: 1.0 }];
    setBones(newBones);
    setNewBoneName('');

    // 更新遮罩
    const values = form.getFieldsValue();
    updateMask(values, newBones);
  };

  // 删除骨骼
  const handleDeleteBone = (boneName: string) => {
    const newBones = bones.filter(bone => bone.name !== boneName);
    setBones(newBones);

    // 更新遮罩
    const values = form.getFieldsValue();
    updateMask(values, newBones);
  };

  // 更新骨骼权重
  const handleWeightChange = (boneName: string, weight: number) => {
    const newBones = bones.map(bone => {
      if (bone.name === boneName) {
        return { ...bone, weight };
      }
      return bone;
    });

    setBones(newBones);

    // 更新遮罩
    const values = form.getFieldsValue();
    updateMask(values, newBones);
  };

  // 应用预设遮罩
  const handleApplyPreset = (preset: BoneGroupType) => {
    // 根据预设类型创建骨骼列表
    const presetBones = getPresetBones(preset);
    const newBones: BoneData[] = [];

    presetBones.forEach((bone: string) => {
      newBones.push({
        name: bone,
        weight: 1.0
      });
    });

    // 更新状态
    setBones(newBones);

    // 更新遮罩
    const values = form.getFieldsValue();
    updateMask(values, newBones);

    message.success(t('editor.animation.mask.presetApplied'));
  };

  // 获取预设骨骼列表
  const getPresetBones = (preset: BoneGroupType): string[] => {
    switch (preset) {
      case BoneGroupType.UPPER_BODY:
        return ['spine', 'spine1', 'spine2', 'neck', 'head', 'leftShoulder', 'leftArm', 'leftForeArm', 'leftHand', 'rightShoulder', 'rightArm', 'rightForeArm', 'rightHand'];
      case BoneGroupType.LOWER_BODY:
        return ['leftUpLeg', 'leftLeg', 'leftFoot', 'leftToe', 'rightUpLeg', 'rightLeg', 'rightFoot', 'rightToe'];
      case BoneGroupType.LEFT_ARM:
        return ['leftShoulder', 'leftArm', 'leftForeArm', 'leftHand'];
      case BoneGroupType.RIGHT_ARM:
        return ['rightShoulder', 'rightArm', 'rightForeArm', 'rightHand'];
      case BoneGroupType.LEFT_LEG:
        return ['leftUpLeg', 'leftLeg', 'leftFoot', 'leftToe'];
      case BoneGroupType.RIGHT_LEG:
        return ['rightUpLeg', 'rightLeg', 'rightFoot', 'rightToe'];
      case BoneGroupType.HEAD:
        return ['neck', 'head'];
      case BoneGroupType.SPINE:
        return ['spine', 'spine1', 'spine2'];
      default:
        return [];
    }
  };

  // 构建骨骼树
  const buildBoneTree = (bones: BoneData[]): BoneData[] => {
    if (!skeleton || viewMode !== 'tree') return bones;

    // 创建骨骼映射
    const boneMap = new Map<string, BoneData>();
    bones.forEach(bone => {
      boneMap.set(bone.name, { ...bone, children: [] });
    });

    // 构建树结构
    const rootNodes: BoneData[] = [];

    // 遍历所有骨骼
    skeleton.bones.forEach(bone => {
      const boneName = bone.name;
      const boneData = boneMap.get(boneName);

      if (boneData) {
        // 如果有父骨骼且父骨骼在映射中
        if (bone.parent && bone.parent.type === 'Bone') {
          const parentName = bone.parent.name;
          const parentData = boneMap.get(parentName);

          if (parentData) {
            if (!parentData.children) {
              parentData.children = [];
            }
            parentData.children.push(boneData);
            boneData.parent = parentName;
          } else {
            rootNodes.push(boneData);
          }
        } else {
          rootNodes.push(boneData);
        }
      }
    });

    return rootNodes;
  };

  // 过滤骨骼列表
  const filterBones = (bones: BoneData[]): BoneData[] => {
    if (!searchText) return bones;

    return bones.filter(bone =>
      bone.name.toLowerCase().includes(searchText.toLowerCase())
    );
  };

  // 获取骨骼层级路径
  const getBonePath = (boneName: string): string[] => {
    if (!skeleton) return [boneName];

    const path: string[] = [boneName];
    let currentBone = skeleton.bones.find(b => b.name === boneName);

    while (currentBone && currentBone.parent && currentBone.parent.type === 'Bone') {
      path.unshift(currentBone.parent.name);
      currentBone = skeleton.bones.find(b => b.name === currentBone!.parent!.name);
    }

    return path;
  };

  // 更新遮罩
  const updateMask = (values: any, newBones?: BoneData[]) => {
    const boneList = newBones || bones;

    // 创建一个模拟的遮罩对象
    const updatedMask: AnimationMask = {
      getName: () => values.name || '',
      getType: () => values.type || MaskType.INCLUDE,
      getWeightType: () => values.weightType || MaskWeightType.BINARY,
      getBones: () => boneList.map(bone => bone.name),
      getBoneWeight: (boneName: string) => {
        const bone = boneList.find(b => b.name === boneName);
        return bone ? bone.weight : 0;
      },
      setBoneWeight: (boneName: string, weight: number) => {
        const bone = boneList.find(b => b.name === boneName);
        if (bone) {
          bone.weight = weight;
        }
      }
    };

    // 调用更新回调
    onUpdate(updatedMask);
  };

  // 添加自定义预设
  const handleAddCustomPreset = () => {
    if (!newPresetName) {
      message.warning(t('editor.animation.mask.enterPresetName'));
      return;
    }

    // 检查是否已存在
    if (customPresets.some(preset => preset.name === newPresetName)) {
      message.warning(t('editor.animation.mask.presetExists'));
      return;
    }

    // 创建新预设
    const newPreset: PresetData = {
      name: newPresetName,
      type: 'custom',
      description: t('editor.animation.mask.customPresetDesc'),
      bones: bones.map(bone => bone.name)
    };

    // 更新预设列表
    setCustomPresets([...customPresets, newPreset]);
    setNewPresetName('');

    message.success(t('editor.animation.mask.presetSaved'));
  };

  // 删除自定义预设
  const handleDeleteCustomPreset = (presetName: string) => {
    const newPresets = customPresets.filter(preset => preset.name !== presetName);
    setCustomPresets(newPresets);

    if (selectedPreset === presetName) {
      setSelectedPreset(null);
    }

    message.success(t('editor.animation.mask.presetDeleted'));
  };

  // 清空骨骼列表
  const handleClearBones = () => {
    setBones([]);
    updateMask(form.getFieldsValue(), []);
  };

  // 全选骨骼
  const handleSelectAllBones = () => {
    const allBones: BoneData[] = availableBones.map(bone => ({
      name: bone,
      weight: 1.0
    }));

    setBones(allBones);
    updateMask(form.getFieldsValue(), allBones);
  };

  // 反选骨骼
  const handleInvertSelection = () => {
    const selectedBoneNames = new Set(bones.map(bone => bone.name));
    const invertedBones: BoneData[] = availableBones
      .filter(bone => !selectedBoneNames.has(bone))
      .map(bone => ({
        name: bone,
        weight: 1.0
      }));

    setBones(invertedBones);
    updateMask(form.getFieldsValue(), invertedBones);
  };

  // 切换视图模式
  const handleViewModeChange = (mode: 'flat' | 'tree') => {
    setViewMode(mode);
  };

  // 搜索骨骼
  const handleSearchBones = (value: string) => {
    setSearchText(value);
  };

  // 导出遮罩
  const handleExportMask = () => {
    try {
      const maskData = {
        name: mask.getName(),
        type: mask.getType(),
        weightType: mask.getWeightType(),
        bones: bones.map(bone => ({
          name: bone.name,
          weight: bone.weight
        }))
      };

      const dataStr = JSON.stringify(maskData, null, 2);
      const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;

      const exportName = `${mask.getName() || 'mask'}_${new Date().getTime()}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportName);
      linkElement.click();

      message.success(t('editor.animation.mask.exportSuccess'));
    } catch (error) {
      console.error('导出遮罩失败:', error);
      message.error(t('editor.animation.mask.exportError'));
    }
  };

  // 渲染骨骼树
  const renderBoneTree = () => {
    const treeData = buildBoneTree(bones).map(bone => ({
      title: (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
          <span>{bone.name}</span>
          {weightType === MaskWeightType.SMOOTH ? (
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={bone.weight}
              onChange={(value) => handleWeightChange(bone.name, value)}
              style={{ width: 100 }}
            />
          ) : (
            <Switch
              checked={bone.weight > 0.5}
              onChange={(checked) => handleWeightChange(bone.name, checked ? 1.0 : 0.0)}
            />
          )}
        </div>
      ),
      key: bone.name,
      children: bone.children?.map(child => ({
        title: (
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
            <span>{child.name}</span>
            {weightType === MaskWeightType.SMOOTH ? (
              <Slider
                min={0}
                max={1}
                step={0.01}
                value={child.weight}
                onChange={(value) => handleWeightChange(child.name, value)}
                style={{ width: 100 }}
              />
            ) : (
              <Switch
                checked={child.weight > 0.5}
                onChange={(checked) => handleWeightChange(child.name, checked ? 1.0 : 0.0)}
              />
            )}
          </div>
        ),
        key: child.name,
        children: child.children?.map(grandChild => ({
          // 递归处理更深层级
          // 简化实现，仅支持三层
          title: grandChild.name,
          key: grandChild.name
        }))
      }))
    }));

    return (
      <Tree
        treeData={treeData}
        defaultExpandAll
        showLine
        blockNode
      />
    );
  };

  // 表格列定义
  const columns = [
    {
      title: t('editor.animation.mask.boneName'),
      dataIndex: 'name',
      key: 'name',
      width: '40%',
      render: (name: string, _record: BoneData) => {
        // 如果有骨骼对象，显示完整路径
        if (skeleton && maskType === MaskType.HIERARCHY) {
          const path = getBonePath(name);
          return (
            <Tooltip title={path.join(' > ')}>
              <span>{name}</span>
            </Tooltip>
          );
        }
        return name;
      }
    },
    {
      title: t('editor.animation.mask.weight'),
      dataIndex: 'weight',
      key: 'weight',
      width: '40%',
      render: (weight: number, record: BoneData) => (
        weightType === MaskWeightType.SMOOTH ? (
          <Slider
            min={0}
            max={1}
            step={0.01}
            value={weight}
            onChange={(value) => handleWeightChange(record.name, value)}
          />
        ) : (
          <Switch
            checked={weight > 0.5}
            onChange={(checked) => handleWeightChange(record.name, checked ? 1.0 : 0.0)}
          />
        )
      )
    },
    {
      title: t('editor.animation.mask.actions'),
      key: 'actions',
      width: '20%',
      render: (_: any, record: BoneData) => (
        <Button
          icon={<DeleteOutlined />}
          size="small"
          danger
          onClick={() => handleDeleteBone(record.name)}
        />
      )
    }
  ];

  // 渲染预览
  const renderPreview = () => {
    if (!previewCanvasRef.current || !skeleton) return null;

    // 这里应该实现一个简单的骨骼预览
    // 由于实现复杂，这里只提供一个简单的占位符
    return (
      <div className="preview-container">
        <canvas
          ref={previewCanvasRef}
          width={300}
          height={300}
          style={{ background: '#1e1e1e', borderRadius: '4px' }}
        />
        <div className="preview-controls">
          <Space>
            <Button icon={<ReloadOutlined />} onClick={() => {}} size="small">
              {t('editor.animation.mask.resetView')}
            </Button>
            <Switch
              checkedChildren={<EyeOutlined />}
              unCheckedChildren={<EyeInvisibleOutlined />}
              checked={showWeights}
              onChange={setShowWeights}
            />
          </Space>
        </div>
      </div>
    );
  };

  // 渲染动态设置
  const renderDynamicSettings = () => {
    if (!dynamicType) return null;

    switch (dynamicType) {
      case DynamicMaskType.DISTANCE:
        return (
          <div className="dynamic-settings">
            <Form.Item label={t('editor.animation.mask.maxDistance')}>
              <InputNumber
                min={0}
                step={0.1}
                value={dynamicParams.maxDistance || 10.0}
                onChange={(value) => setDynamicParams({ ...dynamicParams, maxDistance: value as number })}
              />
            </Form.Item>
            <Form.Item label={t('editor.animation.mask.minDistance')}>
              <InputNumber
                min={0}
                step={0.1}
                value={dynamicParams.minDistance || 0.0}
                onChange={(value) => setDynamicParams({ ...dynamicParams, minDistance: value as number })}
              />
            </Form.Item>
            <Form.Item label={t('editor.animation.mask.falloffType')}>
              <Select
                value={dynamicParams.falloffType || 'linear'}
                onChange={(value) => setDynamicParams({ ...dynamicParams, falloffType: value })}
              >
                <Option value="linear">{t('editor.animation.mask.linear')}</Option>
                <Option value="quadratic">{t('editor.animation.mask.quadratic')}</Option>
                <Option value="exponential">{t('editor.animation.mask.exponential')}</Option>
              </Select>
            </Form.Item>
          </div>
        );

      case DynamicMaskType.DIRECTION:
        return (
          <div className="dynamic-settings">
            <Form.Item label={t('editor.animation.mask.maxAngle')}>
              <InputNumber
                min={0}
                max={180}
                step={1}
                value={dynamicParams.maxAngle ? (dynamicParams.maxAngle * 180 / Math.PI) : 90}
                onChange={(value) => setDynamicParams({
                  ...dynamicParams,
                  maxAngle: (value as number) * Math.PI / 180
                })}
              />
            </Form.Item>
          </div>
        );

      case DynamicMaskType.PARAMETER:
        return (
          <div className="dynamic-settings">
            <Form.Item label={t('editor.animation.mask.paramName')}>
              <Input
                value={dynamicParams.paramName || ''}
                onChange={(e) => setDynamicParams({ ...dynamicParams, paramName: e.target.value })}
              />
            </Form.Item>
            <Form.Item label={t('editor.animation.mask.paramRange')}>
              <Row gutter={8}>
                <Col span={12}>
                  <InputNumber
                    min={0}
                    step={0.1}
                    value={dynamicParams.paramRange?.[0] || 0}
                    onChange={(value) => setDynamicParams({
                      ...dynamicParams,
                      paramRange: [value as number, dynamicParams.paramRange?.[1] || 1]
                    })}
                  />
                </Col>
                <Col span={12}>
                  <InputNumber
                    min={0}
                    step={0.1}
                    value={dynamicParams.paramRange?.[1] || 1}
                    onChange={(value) => setDynamicParams({
                      ...dynamicParams,
                      paramRange: [dynamicParams.paramRange?.[0] || 0, value as number]
                    })}
                  />
                </Col>
              </Row>
            </Form.Item>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="mask-editor">
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormChange}
        initialValues={{
          name: mask?.getName() || '',
          type: mask?.getType() || MaskType.INCLUDE,
          weightType: mask?.getWeightType() || MaskWeightType.BINARY
        }}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t('editor.animation.mask.basic')} key="basic">
            <Form.Item name="name" label={t('editor.animation.mask.name')}>
              <Input />
            </Form.Item>

            <Form.Item name="type" label={t('editor.animation.mask.type')}>
              <Select>
                <Option value={MaskType.INCLUDE}>{t('editor.animation.mask.include')}</Option>
                <Option value={MaskType.EXCLUDE}>{t('editor.animation.mask.exclude')}</Option>
                <Option value={MaskType.HIERARCHY}>{t('editor.animation.mask.hierarchy')}</Option>
                <Option value={MaskType.INVERSE_HIERARCHY}>{t('editor.animation.mask.inverseHierarchy')}</Option>
                <Option value={MaskType.GROUP}>{t('editor.animation.mask.group')}</Option>
                <Option value={MaskType.BLEND}>{t('editor.animation.mask.blend')}</Option>
                {enableAdvanced && (
                  <Option value={MaskType.TRANSITION}>{t('editor.animation.mask.transition')}</Option>
                )}
              </Select>
            </Form.Item>

            <Form.Item name="weightType" label={t('editor.animation.mask.weightType')}>
              <Select>
                <Option value={MaskWeightType.BINARY}>{t('editor.animation.mask.binary')}</Option>
                <Option value={MaskWeightType.SMOOTH}>{t('editor.animation.mask.smooth')}</Option>
                {enableAdvanced && (
                  <>
                    <Option value={MaskWeightType.DISTANCE}>{t('editor.animation.mask.distance')}</Option>
                    <Option value={MaskWeightType.GRADIENT}>{t('editor.animation.mask.gradient')}</Option>
                    <Option value={MaskWeightType.DYNAMIC}>{t('editor.animation.mask.dynamic')}</Option>
                  </>
                )}
              </Select>
            </Form.Item>

            {enableAdvanced && (
              <Form.Item>
                <Switch
                  checkedChildren={t('common.enabled')}
                  unCheckedChildren={t('common.disabled')}
                  checked={isEnhanced}
                  onChange={setIsEnhanced}
                />
                <span style={{ marginLeft: '8px' }}>
                  {t('editor.animation.mask.enhancedMode')}
                </span>
              </Form.Item>
            )}
          </TabPane>

          <TabPane tab={t('editor.animation.mask.bones')} key="bones">
            <div className="bone-tools">
              <Space style={{ marginBottom: '16px' }}>
                <Radio.Group
                  value={viewMode}
                  onChange={(e) => handleViewModeChange(e.target.value)}
                  buttonStyle="solid"
                  size="small"
                >
                  <Radio.Button value="flat">{t('editor.animation.mask.flatList')}</Radio.Button>
                  <Radio.Button value="tree" disabled={!skeleton}>{t('editor.animation.mask.treeView')}</Radio.Button>
                </Radio.Group>

                <Search
                  placeholder={t('editor.animation.mask.searchBones') || '搜索骨骼'}
                  onSearch={handleSearchBones}
                  style={{ width: 200 }}
                  size="small"
                />
              </Space>

              <Space style={{ marginBottom: '16px' }}>
                <Button
                  icon={<ClearOutlined />}
                  onClick={handleClearBones}
                  size="small"
                >
                  {t('editor.animation.mask.clearBones')}
                </Button>
                <Button
                  icon={<CheckOutlined />}
                  onClick={handleSelectAllBones}
                  size="small"
                >
                  {t('editor.animation.mask.selectAll')}
                </Button>
                <Button
                  icon={<SwapOutlined />}
                  onClick={handleInvertSelection}
                  size="small"
                >
                  {t('editor.animation.mask.invertSelection')}
                </Button>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExportMask}
                  size="small"
                >
                  {t('editor.animation.mask.exportMask')}
                </Button>
              </Space>

              <div className="add-bone-form">
                <Space>
                  <Select
                    style={{ width: 200 }}
                    value={newBoneName}
                    onChange={setNewBoneName}
                    showSearch
                    placeholder={t('editor.animation.mask.selectBone')}
                    filterOption={(input, option) =>
                      (option?.children as unknown as string).toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {availableBones.map(bone => (
                      <Option key={bone} value={bone}>{bone}</Option>
                    ))}
                  </Select>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAddBone}
                  >
                    {t('editor.animation.mask.addBone')}
                  </Button>
                </Space>
              </div>
            </div>

            <div className="bone-list">
              {viewMode === 'tree' ? (
                renderBoneTree()
              ) : (
                <Table
                  dataSource={filterBones(bones)}
                  columns={columns}
                  rowKey="name"
                  pagination={false}
                  size="small"
                  scroll={{ y: 300 }}
                />
              )}
            </div>

            <div className="bone-stats" style={{ marginTop: '16px' }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title={t('editor.animation.mask.boneCount')}
                    value={bones.length}
                    suffix={`/ ${availableBones.length}`}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title={t('editor.animation.mask.selectedBones')}
                    value={bones.filter(b => b.weight > 0.5).length}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title={t('editor.animation.mask.weightSum')}
                    value={bones.reduce((sum, b) => sum + b.weight, 0).toFixed(2)}
                    precision={2}
                  />
                </Col>
              </Row>
            </div>
          </TabPane>

          <TabPane tab={t('editor.animation.mask.presets')} key="presets">
            <div className="mask-presets">
              <Card
                title={t('editor.animation.mask.upperBody')}
                size="small"
                extra={
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => handleApplyPreset(BoneGroupType.UPPER_BODY)}
                  >
                    {t('editor.animation.mask.apply')}
                  </Button>
                }
              >
                <p>{t('editor.animation.mask.upperBodyDesc')}</p>
              </Card>

              <Card
                title={t('editor.animation.mask.lowerBody')}
                size="small"
                extra={
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => handleApplyPreset(BoneGroupType.LOWER_BODY)}
                  >
                    {t('editor.animation.mask.apply')}
                  </Button>
                }
              >
                <p>{t('editor.animation.mask.lowerBodyDesc')}</p>
              </Card>

              <Card
                title={t('editor.animation.mask.leftArm')}
                size="small"
                extra={
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => handleApplyPreset(BoneGroupType.LEFT_ARM)}
                  >
                    {t('editor.animation.mask.apply')}
                  </Button>
                }
              >
                <p>{t('editor.animation.mask.leftArmDesc')}</p>
              </Card>

              <Card
                title={t('editor.animation.mask.rightArm')}
                size="small"
                extra={
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => handleApplyPreset(BoneGroupType.RIGHT_ARM)}
                  >
                    {t('editor.animation.mask.apply')}
                  </Button>
                }
              >
                <p>{t('editor.animation.mask.rightArmDesc')}</p>
              </Card>

              {enableAdvanced && (
                <>
                  <Card
                    title={t('editor.animation.mask.leftLeg')}
                    size="small"
                    extra={
                      <Button
                        type="primary"
                        size="small"
                        onClick={() => handleApplyPreset(BoneGroupType.LEFT_LEG)}
                      >
                        {t('editor.animation.mask.apply')}
                      </Button>
                    }
                  >
                    <p>{t('editor.animation.mask.leftLegDesc')}</p>
                  </Card>

                  <Card
                    title={t('editor.animation.mask.rightLeg')}
                    size="small"
                    extra={
                      <Button
                        type="primary"
                        size="small"
                        onClick={() => handleApplyPreset(BoneGroupType.RIGHT_LEG)}
                      >
                        {t('editor.animation.mask.apply')}
                      </Button>
                    }
                  >
                    <p>{t('editor.animation.mask.rightLegDesc')}</p>
                  </Card>

                  <Card
                    title={t('editor.animation.mask.head')}
                    size="small"
                    extra={
                      <Button
                        type="primary"
                        size="small"
                        onClick={() => handleApplyPreset(BoneGroupType.HEAD)}
                      >
                        {t('editor.animation.mask.apply')}
                      </Button>
                    }
                  >
                    <p>{t('editor.animation.mask.headDesc')}</p>
                  </Card>
                </>
              )}

              <Divider>{t('editor.animation.mask.customPresets')}</Divider>

              <div className="custom-presets">
                <Space style={{ marginBottom: '16px' }}>
                  <Input
                    placeholder={t('editor.animation.mask.presetName') || '预设名称'}
                    value={newPresetName}
                    onChange={(e) => setNewPresetName(e.target.value)}
                    style={{ width: 200 }}
                  />
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleAddCustomPreset}
                  >
                    {t('editor.animation.mask.saveAsPreset')}
                  </Button>
                </Space>

                {customPresets.map(preset => (
                  <Card
                    key={preset.name}
                    title={preset.name}
                    size="small"
                    extra={
                      <Space>
                        <Button
                          type="primary"
                          size="small"
                          onClick={() => {
                            // 应用自定义预设
                            const newBones: BoneData[] = preset.bones?.map(bone => ({
                              name: bone,
                              weight: 1.0
                            })) || [];

                            setBones(newBones);
                            updateMask(form.getFieldsValue(), newBones);
                            message.success(t('editor.animation.mask.presetApplied'));
                          }}
                        >
                          {t('editor.animation.mask.apply')}
                        </Button>
                        <Button
                          type="text"
                          size="small"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeleteCustomPreset(preset.name)}
                        />
                      </Space>
                    }
                  >
                    <p>{preset.description}</p>
                  </Card>
                ))}
              </div>
            </div>
          </TabPane>

          {enableAdvanced && (
            <>
              <TabPane tab={t('editor.animation.mask.advanced')} key="advanced">
                <Collapse defaultActiveKey={['hierarchy', 'dynamic']}>
                  <Panel header={t('editor.animation.mask.hierarchySettings')} key="hierarchy">
                    <Form.Item label={t('editor.animation.mask.rootBone')}>
                      <Select
                        value={rootBone}
                        onChange={setRootBone}
                        disabled={!skeleton}
                      >
                        {availableBones.map(bone => (
                          <Option key={bone} value={bone}>{bone}</Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Form.Item label={t('editor.animation.mask.includeChildren')}>
                      <Switch
                        checked={includeChildren}
                        onChange={setIncludeChildren}
                      />
                    </Form.Item>

                    <Form.Item label={t('editor.animation.mask.maxDepth')}>
                      <InputNumber
                        min={1}
                        max={100}
                        value={maxDepth}
                        onChange={(value) => setMaxDepth(value as number)}
                      />
                    </Form.Item>
                  </Panel>

                  <Panel header={t('editor.animation.mask.dynamicSettings')} key="dynamic">
                    <Form.Item label={t('editor.animation.mask.dynamicType')}>
                      <Select
                        value={dynamicType}
                        onChange={setDynamicType}
                        allowClear
                      >
                        <Option value={DynamicMaskType.DISTANCE}>
                          <Space>
                            <AimOutlined />
                            {t('editor.animation.mask.distance')}
                          </Space>
                        </Option>
                        <Option value={DynamicMaskType.DIRECTION}>
                          <Space>
                            <PartitionOutlined />
                            {t('editor.animation.mask.direction')}
                          </Space>
                        </Option>
                        <Option value={DynamicMaskType.VELOCITY}>
                          <Space>
                            <DotChartOutlined />
                            {t('editor.animation.mask.velocity')}
                          </Space>
                        </Option>
                        <Option value={DynamicMaskType.TIME}>
                          <Space>
                            <FieldTimeOutlined />
                            {t('editor.animation.mask.time')}
                          </Space>
                        </Option>
                        <Option value={DynamicMaskType.PARAMETER}>
                          <Space>
                            <SlidersFilled />
                            {t('editor.animation.mask.parameter')}
                          </Space>
                        </Option>
                      </Select>
                    </Form.Item>

                    {renderDynamicSettings()}

                    <Form.Item label={t('editor.animation.mask.enableWeightInterpolation')}>
                      <Switch
                        checked={enableWeightInterpolation}
                        onChange={setEnableWeightInterpolation}
                      />
                    </Form.Item>

                    {enableWeightInterpolation && (
                      <Form.Item label={t('editor.animation.mask.weightInterpolationSpeed')}>
                        <Slider
                          min={0.1}
                          max={10}
                          step={0.1}
                          value={weightInterpolationSpeed}
                          onChange={(value) => setWeightInterpolationSpeed(value)}
                        />
                      </Form.Item>
                    )}
                  </Panel>
                </Collapse>
              </TabPane>

              <TabPane tab={t('editor.animation.mask.preview')} key="preview">
                {renderPreview()}

                <div className="preview-settings" style={{ marginTop: '16px' }}>
                  <Form.Item label={t('editor.animation.mask.showWeights')}>
                    <Switch
                      checked={showWeights}
                      onChange={setShowWeights}
                    />
                  </Form.Item>

                  <Form.Item label={t('editor.animation.mask.colorMode')}>
                    <Radio.Group
                      value={colorMode}
                      onChange={(e) => setColorMode(e.target.value)}
                    >
                      <Radio value="heatmap">{t('editor.animation.mask.heatmap')}</Radio>
                      <Radio value="gradient">{t('editor.animation.mask.gradient')}</Radio>
                      <Radio value="solid">{t('editor.animation.mask.solid')}</Radio>
                    </Radio.Group>
                  </Form.Item>

                  <Button
                    type="primary"
                    onClick={() => {
                      message.success(t('editor.animation.mask.applyToModelSuccess'));
                    }}
                  >
                    {t('editor.animation.mask.applyToModel')}
                  </Button>
                </div>
              </TabPane>
            </>
          )}
        </Tabs>
      </Form>
    </div>
  );
};
