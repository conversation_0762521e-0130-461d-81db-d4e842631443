# MY引擎 Kubernetes 部署指南

本文档提供了在Kubernetes集群上部署MY引擎的详细指南。MY引擎是一个基于微服务架构的3D引擎和编辑器平台，支持大规模并发用户访问。

## 目录

- [系统要求](#系统要求)
- [部署架构](#部署架构)
- [前置准备](#前置准备)
- [部署步骤](#部署步骤)
  - [创建命名空间](#创建命名空间)
  - [配置ConfigMap和Secret](#配置configmap和secret)
  - [部署数据库和Redis](#部署数据库和redis)
  - [部署服务注册中心](#部署服务注册中心)
  - [部署微服务](#部署微服务)
  - [部署API网关](#部署api网关)
  - [部署前端应用](#部署前端应用)
- [服务暴露和访问](#服务暴露和访问)
- [监控和日志](#监控和日志)
- [扩展和高可用性](#扩展和高可用性)
- [故障排除](#故障排除)

## 系统要求

- Kubernetes集群 v1.20+
- Helm v3.0+
- kubectl 命令行工具
- 容器仓库（如Docker Hub、阿里云容器服务等）
- 持久化存储支持（如NFS、云存储等）
- Ingress控制器（如Nginx Ingress、Traefik等）

## 部署架构

MY引擎在Kubernetes中的部署架构如下：

```
                                    ┌─────────────┐
                                    │   Ingress   │
                                    └──────┬──────┘
                                           │
                                    ┌──────┴──────┐
                                    │ API Gateway │
                                    └──────┬──────┘
                                           │
                 ┌───────────┬─────────────┼─────────────┬───────────┐
                 │           │             │             │           │
        ┌────────┴────────┐  │  ┌──────────┴─────────┐   │  ┌────────┴────────┐
        │  User Service   │  │  │   Project Service  │   │  │  Asset Service  │
        └────────┬────────┘  │  └──────────┬─────────┘   │  └────────┬────────┘
                 │           │             │             │           │
                 │           │             │             │           │
        ┌────────┴────────┐  │  ┌──────────┴─────────┐   │  ┌────────┴────────┐
        │  User Database  │  │  │  Project Database  │   │  │  Asset Database │
        └─────────────────┘  │  └────────────────────┘   │  └─────────────────┘
                             │                           │
                     ┌───────┴────────┐          ┌───────┴────────┐
                     │Service Registry│          │ Render Service │
                     └────────┬───────┘          └────────┬───────┘
                              │                           │
                     ┌────────┴───────┐          ┌───────┴────────┐
                     │Registry Database│         │     Redis      │
                     └────────────────┘          └────────────────┘
```

## 前置准备

1. 确保您已经构建并推送了所有微服务的Docker镜像到容器仓库
2. 准备好Kubernetes集群并配置好kubectl访问权限
3. 安装Helm（如果计划使用Helm Chart部署）
4. 准备好持久化存储解决方案
5. 安装Ingress控制器

## 部署步骤

### 创建命名空间

首先，为MY引擎创建一个专用的命名空间：

```bash
kubectl create namespace my-engine
```

### 部署MinIO对象存储

MY引擎使用MinIO作为对象存储服务，用于存储3D模型、纹理、音频和渲染输出等资源文件。

```yaml
# minio-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: minio
  namespace: my-engine
spec:
  selector:
    matchLabels:
      app: minio
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: minio
    spec:
      containers:
      - name: minio
        image: minio/minio:RELEASE.2023-06-29T05-12-28Z
        args:
        - server
        - /data
        - --console-address
        - ":9001"
        env:
        - name: MINIO_ROOT_USER
          valueFrom:
            secretKeyRef:
              name: my-engine-secrets
              key: MINIO_ROOT_USER
        - name: MINIO_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: my-engine-secrets
              key: MINIO_ROOT_PASSWORD
        ports:
        - containerPort: 9000
          name: api
        - containerPort: 9001
          name: console
        volumeMounts:
        - name: minio-data
          mountPath: /data
        readinessProbe:
          httpGet:
            path: /minio/health/ready
            port: 9000
          initialDelaySeconds: 20
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /minio/health/live
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 20
      volumes:
      - name: minio-data
        persistentVolumeClaim:
          claimName: minio-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: minio
  namespace: my-engine
spec:
  ports:
  - port: 9000
    targetPort: 9000
    name: api
  - port: 9001
    targetPort: 9001
    name: console
  selector:
    app: minio
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: minio-pvc
  namespace: my-engine
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
```

创建初始化MinIO的Job，用于创建必要的存储桶：

```yaml
# minio-init-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: minio-init
  namespace: my-engine
spec:
  template:
    spec:
      containers:
      - name: mc
        image: minio/mc:RELEASE.2023-06-28T21-54-17Z
        command: ["/bin/sh", "-c"]
        args:
        - |
          mc alias set myminio http://minio:9000 "$MINIO_ROOT_USER" "$MINIO_ROOT_PASSWORD" &&
          mc mb --ignore-existing myminio/models &&
          mc mb --ignore-existing myminio/textures &&
          mc mb --ignore-existing myminio/audio &&
          mc mb --ignore-existing myminio/renders &&
          mc policy set download myminio/renders
        env:
        - name: MINIO_ROOT_USER
          valueFrom:
            secretKeyRef:
              name: my-engine-secrets
              key: MINIO_ROOT_USER
        - name: MINIO_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: my-engine-secrets
              key: MINIO_ROOT_PASSWORD
      restartPolicy: OnFailure
  backoffLimit: 3
```

应用MinIO部署：

```bash
kubectl apply -f minio-deployment.yaml
kubectl apply -f minio-init-job.yaml
```

### 配置ConfigMap和Secret

创建ConfigMap存储配置信息：

```yaml
# my-engine-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: dl-engine-config
  namespace: dl-engine
data:
  NODE_ENV: "production"
  SERVICE_REGISTRY_HOST: "service-registry"
  SERVICE_REGISTRY_PORT: "3010"
  USER_SERVICE_HOST: "user-service"
  USER_SERVICE_PORT: "3001"
  PROJECT_SERVICE_HOST: "project-service"
  PROJECT_SERVICE_PORT: "3002"
  ASSET_SERVICE_HOST: "asset-service"
  ASSET_SERVICE_PORT: "3003"
  RENDER_SERVICE_HOST: "render-service"
  RENDER_SERVICE_PORT: "3004"
  REDIS_HOST: "redis"
  REDIS_PORT: "6379"
  DB_HOST: "mysql"
  DB_PORT: "3306"
  MINIO_HOST: "minio"
  MINIO_PORT: "9000"
  MINIO_USE_SSL: "false"
  MINIO_BUCKET_MODELS: "models"
  MINIO_BUCKET_TEXTURES: "textures"
  MINIO_BUCKET_AUDIO: "audio"
  MINIO_BUCKET_RENDERS: "renders"
```

创建Secret存储敏感信息：

```yaml
# dl-engine-secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: dl-engine-secrets
  namespace: dl-engine
type: Opaque
data:
  MYSQL_ROOT_PASSWORD: <base64编码的密码>
  MYSQL_PASSWORD: <base64编码的密码>
  JWT_SECRET: <base64编码的JWT密钥>
  MINIO_ROOT_USER: <base64编码的MinIO用户名>
  MINIO_ROOT_PASSWORD: <base64编码的MinIO密码>
  MINIO_ACCESS_KEY: <base64编码的MinIO访问密钥>
  MINIO_SECRET_KEY: <base64编码的MinIO秘密密钥>
```

应用配置：

```bash
kubectl apply -f dl-engine-config.yaml
kubectl apply -f dl-engine-secrets.yaml
```

### 部署数据库和Redis

部署MySQL数据库：

```yaml
# mysql-deployment.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mysql
  namespace: dl-engine
spec:
  serviceName: mysql
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        ports:
        - containerPort: 3306
          name: mysql
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: MYSQL_DATABASE
          value: ir_engine
        volumeMounts:
        - name: mysql-data
          mountPath: /var/lib/mysql
        - name: init-scripts
          mountPath: /docker-entrypoint-initdb.d
      volumes:
      - name: init-scripts
        configMap:
          name: mysql-init-scripts
  volumeClaimTemplates:
  - metadata:
      name: mysql-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: mysql
  namespace: dl-engine
spec:
  ports:
  - port: 3306
    targetPort: 3306
  selector:
    app: mysql
  clusterIP: None
```

部署Redis：

```yaml
# redis-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: dl-engine
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7.0-alpine
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: dl-engine
spec:
  ports:
  - port: 6379
    targetPort: 6379
  selector:
    app: redis
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: dl-engine
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
```

应用部署：

```bash
kubectl apply -f mysql-deployment.yaml
kubectl apply -f redis-deployment.yaml
```

### 部署服务注册中心

```yaml
# service-registry-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: service-registry
  namespace: dl-engine
spec:
  replicas: 1
  selector:
    matchLabels:
      app: service-registry
  template:
    metadata:
      labels:
        app: service-registry
    spec:
      containers:
      - name: service-registry
        image: <您的容器仓库>/dl-engine-service-registry:latest
        ports:
        - containerPort: 3010
          name: tcp
        - containerPort: 4010
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_PORT
        - name: DB_USERNAME
          value: root
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: DB_DATABASE
          value: ir_engine_registry
        - name: SERVICE_REGISTRY_HOST
          value: service-registry
        - name: SERVICE_REGISTRY_PORT
          value: "3010"
        - name: SERVICE_REGISTRY_HTTP_PORT
          value: "4010"
        readinessProbe:
          httpGet:
            path: /health
            port: 4010
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 4010
          initialDelaySeconds: 30
          periodSeconds: 20
---
apiVersion: v1
kind: Service
metadata:
  name: service-registry
  namespace: dl-engine
spec:
  ports:
  - port: 3010
    targetPort: 3010
    name: tcp
  - port: 4010
    targetPort: 4010
    name: http
  selector:
    app: service-registry
```

应用部署：

```bash
kubectl apply -f service-registry-deployment.yaml
```

### 部署微服务

部署用户服务：

```yaml
# user-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: <您的容器仓库>/dl-engine-user-service:latest
        ports:
        - containerPort: 3001
          name: tcp
        - containerPort: 4001
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_PORT
        - name: DB_USERNAME
          value: root
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: DB_DATABASE
          value: ir_engine_users
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: JWT_SECRET
        - name: JWT_EXPIRES_IN
          value: "1d"
        - name: USER_SERVICE_HOST
          value: user-service
        - name: USER_SERVICE_PORT
          value: "3001"
        - name: USER_SERVICE_HTTP_PORT
          value: "4001"
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        readinessProbe:
          httpGet:
            path: /health
            port: 4001
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 4001
          initialDelaySeconds: 30
          periodSeconds: 20
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: dl-engine
spec:
  ports:
  - port: 3001
    targetPort: 3001
    name: tcp
  - port: 4001
    targetPort: 4001
    name: http
  selector:
    app: user-service
```

部署项目服务：

```yaml
# project-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: project-service
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: project-service
  template:
    metadata:
      labels:
        app: project-service
    spec:
      containers:
      - name: project-service
        image: <您的容器仓库>/dl-engine-project-service:latest
        ports:
        - containerPort: 3002
          name: tcp
        - containerPort: 4002
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_PORT
        - name: DB_USERNAME
          value: root
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: DB_DATABASE
          value: ir_engine_projects
        - name: PROJECT_SERVICE_HOST
          value: project-service
        - name: PROJECT_SERVICE_PORT
          value: "3002"
        - name: PROJECT_SERVICE_HTTP_PORT
          value: "4002"
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        - name: USER_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: USER_SERVICE_HOST
        - name: USER_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: USER_SERVICE_PORT
        readinessProbe:
          httpGet:
            path: /health
            port: 4002
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 4002
          initialDelaySeconds: 30
          periodSeconds: 20
---
apiVersion: v1
kind: Service
metadata:
  name: project-service
  namespace: dl-engine
spec:
  ports:
  - port: 3002
    targetPort: 3002
    name: tcp
  - port: 4002
    targetPort: 4002
    name: http
  selector:
    app: project-service
```

部署资产服务：

```yaml
# asset-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: asset-service
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: asset-service
  template:
    metadata:
      labels:
        app: asset-service
    spec:
      containers:
      - name: asset-service
        image: <您的容器仓库>/dl-engine-asset-service:latest
        ports:
        - containerPort: 3003
          name: tcp
        - containerPort: 4003
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: DB_PORT
        - name: DB_USERNAME
          value: root
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: DB_DATABASE
          value: ir_engine_assets
        - name: ASSET_SERVICE_HOST
          value: asset-service
        - name: ASSET_SERVICE_PORT
          value: "3003"
        - name: ASSET_SERVICE_HTTP_PORT
          value: "4003"
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: JWT_SECRET
        - name: MAX_FILE_SIZE
          value: "104857600"
        - name: MINIO_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_HOST
        - name: MINIO_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_PORT
        - name: MINIO_USE_SSL
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_USE_SSL
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_SECRET_KEY
        - name: MINIO_BUCKET_MODELS
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_BUCKET_MODELS
        - name: MINIO_BUCKET_TEXTURES
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_BUCKET_TEXTURES
        - name: MINIO_BUCKET_AUDIO
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_BUCKET_AUDIO
        readinessProbe:
          httpGet:
            path: /health
            port: 4003
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 4003
          initialDelaySeconds: 30
          periodSeconds: 20
      # 不再需要本地存储卷，因为使用MinIO存储资产
---
apiVersion: v1
kind: Service
metadata:
  name: asset-service
  namespace: dl-engine
spec:
  ports:
  - port: 3003
    targetPort: 3003
    name: tcp
  - port: 4003
    targetPort: 4003
    name: http
  selector:
    app: asset-service

```

部署渲染服务：

```yaml
# render-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: render-service
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: render-service
  template:
    metadata:
      labels:
        app: render-service
    spec:
      containers:
      - name: render-service
        image: <您的容器仓库>/dl-engine-render-service:latest
        ports:
        - containerPort: 3004
          name: tcp
        - containerPort: 4004
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: RENDER_SERVICE_HOST
          value: render-service
        - name: RENDER_SERVICE_PORT
          value: "3004"
        - name: RENDER_SERVICE_HTTP_PORT
          value: "4004"
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: REDIS_PORT
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: JWT_SECRET
        - name: MINIO_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_HOST
        - name: MINIO_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_PORT
        - name: MINIO_USE_SSL
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_USE_SSL
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MINIO_SECRET_KEY
        - name: MINIO_BUCKET_RENDERS
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: MINIO_BUCKET_RENDERS
        readinessProbe:
          httpGet:
            path: /health
            port: 4004
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 4004
          initialDelaySeconds: 30
          periodSeconds: 20
      # 不再需要本地存储卷，因为使用MinIO存储渲染输出
---
apiVersion: v1
kind: Service
metadata:
  name: render-service
  namespace: dl-engine
spec:
  ports:
  - port: 3004
    targetPort: 3004
    name: tcp
  - port: 4004
    targetPort: 4004
    name: http
  selector:
    app: render-service

```

应用部署：

```bash
kubectl apply -f user-service-deployment.yaml
kubectl apply -f project-service-deployment.yaml
kubectl apply -f asset-service-deployment.yaml
kubectl apply -f render-service-deployment.yaml
```

### 部署API网关

```yaml
# api-gateway-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: <您的容器仓库>/dl-engine-api-gateway:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: NODE_ENV
        - name: PORT
          value: "3000"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: JWT_SECRET
        - name: JWT_EXPIRES_IN
          value: "1d"
        - name: USER_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: USER_SERVICE_HOST
        - name: USER_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: USER_SERVICE_PORT
        - name: PROJECT_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: PROJECT_SERVICE_HOST
        - name: PROJECT_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: PROJECT_SERVICE_PORT
        - name: ASSET_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: ASSET_SERVICE_HOST
        - name: ASSET_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: ASSET_SERVICE_PORT
        - name: RENDER_SERVICE_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: RENDER_SERVICE_HOST
        - name: RENDER_SERVICE_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: RENDER_SERVICE_PORT
        - name: SERVICE_REGISTRY_HOST
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_HOST
        - name: SERVICE_REGISTRY_PORT
          valueFrom:
            configMapKeyRef:
              name: dl-engine-config
              key: SERVICE_REGISTRY_PORT
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 15
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 20
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  namespace: dl-engine
spec:
  ports:
  - port: 3000
    targetPort: 3000
  selector:
    app: api-gateway
```

### 部署前端应用

```yaml
# editor-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: editor
  namespace: dl-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: editor
  template:
    metadata:
      labels:
        app: editor
    spec:
      containers:
      - name: editor
        image: <您的容器仓库>/dl-engine-editor:latest
        ports:
        - containerPort: 80
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 20
          periodSeconds: 15
---
apiVersion: v1
kind: Service
metadata:
  name: editor
  namespace: dl-engine
spec:
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: editor
```

应用部署：

```bash
kubectl apply -f api-gateway-deployment.yaml
kubectl apply -f editor-deployment.yaml
```

## 服务暴露和访问

使用Ingress暴露服务：

```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dl-engine-ingress
  namespace: dl-engine
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
spec:
  rules:
  - host: dl-engine.example.com  # 替换为您的域名
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 3000
      - path: /minio
        pathType: Prefix
        backend:
          service:
            name: minio
            port:
              number: 9000
      - path: /minio-console
        pathType: Prefix
        backend:
          service:
            name: minio
            port:
              number: 9001
      - path: /
        pathType: Prefix
        backend:
          service:
            name: editor
            port:
              number: 80
```

应用Ingress配置：

```bash
kubectl apply -f ingress.yaml
```

## 监控和日志

### 部署Prometheus和Grafana监控

推荐使用Prometheus Operator和Grafana来监控Kubernetes集群和DL（Digital Learning）引擎服务。

```bash
# 安装Prometheus Operator
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
helm install prometheus prometheus-community/kube-prometheus-stack --namespace monitoring --create-namespace
```

### 配置日志收集

推荐使用EFK(Elasticsearch, Fluentd, Kibana)或ELK(Elasticsearch, Logstash, Kibana)堆栈收集和分析日志。

```bash
# 安装EFK堆栈
helm repo add elastic https://helm.elastic.co
helm repo update
helm install elasticsearch elastic/elasticsearch --namespace logging --create-namespace
helm install kibana elastic/kibana --namespace logging
helm install fluentd fluent/fluentd --namespace logging
```

## 扩展和高可用性

### 水平自动扩展

为微服务配置HPA(Horizontal Pod Autoscaler)：

```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: dl-engine
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
  namespace: dl-engine
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

应用HPA配置：

```bash
kubectl apply -f hpa.yaml
```

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查环境变量配置是否正确
   - 检查数据库连接是否正常
   - 查看服务日志：`kubectl logs -f deployment/service-name -n dl-engine`

2. **服务注册失败**
   - 确保服务注册中心正常运行
   - 检查网络连接和DNS解析
   - 查看服务注册中心日志

3. **数据库连接问题**
   - 确保数据库Pod正常运行
   - 检查数据库凭据是否正确
   - 检查数据库初始化脚本是否执行

4. **存储问题**
   - 确保PV和PVC正确配置
   - 检查存储类是否支持所需的访问模式
   - 检查存储容量是否足够

5. **MinIO对象存储问题**
   - 确保MinIO服务正常运行：`kubectl get pods -n dl-engine | grep minio`
   - 检查MinIO存储桶是否已创建：`kubectl exec -it <minio-pod-name> -n dl-engine -- mc ls myminio`
   - 检查MinIO凭据是否正确配置
   - 验证微服务是否能正确连接MinIO：`kubectl logs -f <service-pod-name> -n dl-engine | grep minio`

### 查看日志

```bash
# 查看特定服务的日志
kubectl logs -f deployment/api-gateway -n dl-engine

# 查看特定Pod的日志
kubectl logs -f pod-name -n dl-engine

# 查看前一个容器实例的日志
kubectl logs -f pod-name -n dl-engine --previous
```

### 进入容器调试

```bash
# 进入容器执行命令
kubectl exec -it pod-name -n dl-engine -- /bin/sh

# 查看容器环境变量
kubectl exec pod-name -n dl-engine -- env
```
