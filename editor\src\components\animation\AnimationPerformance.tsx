/**
 * 动画性能监控组件
 */
import React, { useState, useEffect } from 'react';
import { Card, Progress, Statistic, Row, Col, Alert, Switch, Typography } from 'antd';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const { Text } = Typography;

/**
 * 性能数据接口
 */
interface PerformanceData {
  timestamp: string;
  fps: number;
  memoryUsage: number;
  cpuUsage: number;
  renderTime: number;
  updateTime: number;
  blendTime: number;
}

/**
 * 动画性能监控属性
 */
interface AnimationPerformanceProps {
  /** 是否启用监控 */
  enabled?: boolean;
  /** 性能数据更新回调 */
  onPerformanceUpdate?: (data: PerformanceData) => void;
}

/**
 * 动画性能监控组件
 */
export const AnimationPerformance: React.FC<AnimationPerformanceProps> = ({
  enabled = true,
  onPerformanceUpdate
}) => {
  // 状态
  const [isMonitoring, setIsMonitoring] = useState(enabled);
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
  const [currentData, setCurrentData] = useState<PerformanceData>({
    timestamp: new Date().toISOString(),
    fps: 60,
    memoryUsage: 45,
    cpuUsage: 25,
    renderTime: 8,
    updateTime: 4,
    blendTime: 2
  });

  // 性能监控
  useEffect(() => {
    if (!isMonitoring) return;

    const interval = setInterval(() => {
      // 模拟性能数据（实际应用中应该从真实的性能监控API获取）
      const newData: PerformanceData = {
        timestamp: new Date().toISOString(),
        fps: 55 + Math.random() * 10,
        memoryUsage: 40 + Math.random() * 20,
        cpuUsage: 20 + Math.random() * 30,
        renderTime: 5 + Math.random() * 10,
        updateTime: 2 + Math.random() * 6,
        blendTime: 1 + Math.random() * 4
      };

      setCurrentData(newData);
      setPerformanceData(prev => {
        const updated = [...prev, newData];
        // 只保留最近50个数据点
        return updated.slice(-50);
      });

      if (onPerformanceUpdate) {
        onPerformanceUpdate(newData);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isMonitoring, onPerformanceUpdate]);

  // 获取性能状态
  const getPerformanceStatus = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'success';
    if (value <= thresholds.warning) return 'normal';
    return 'exception';
  };

  // 获取FPS状态
  const getFpsStatus = (fps: number) => {
    if (fps >= 55) return 'success';
    if (fps >= 30) return 'warning';
    return 'exception';
  };

  return (
    <Card 
      title="动画性能监控" 
      size="small"
      extra={
        <Switch
          checked={isMonitoring}
          onChange={setIsMonitoring}
          checkedChildren="监控中"
          unCheckedChildren="已停止"
        />
      }
    >
      {/* 性能警告 */}
      {currentData.fps < 30 && (
        <Alert
          message="性能警告"
          description="帧率过低，可能影响动画流畅度"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {currentData.memoryUsage > 80 && (
        <Alert
          message="内存警告"
          description="内存使用率过高，建议优化动画资源"
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 实时性能指标 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Statistic
            title="帧率 (FPS)"
            value={currentData.fps}
            precision={1}
            valueStyle={{ 
              color: getFpsStatus(currentData.fps) === 'success' ? '#3f8600' : 
                     getFpsStatus(currentData.fps) === 'warning' ? '#cf1322' : '#cf1322'
            }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="渲染时间 (ms)"
            value={currentData.renderTime}
            precision={1}
            valueStyle={{ 
              color: currentData.renderTime < 16 ? '#3f8600' : '#cf1322'
            }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="混合时间 (ms)"
            value={currentData.blendTime}
            precision={1}
            valueStyle={{ 
              color: currentData.blendTime < 5 ? '#3f8600' : '#cf1322'
            }}
          />
        </Col>
      </Row>

      {/* 资源使用率 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Text>内存使用率</Text>
          <Progress
            percent={currentData.memoryUsage}
            status={getPerformanceStatus(currentData.memoryUsage, { good: 50, warning: 80 })}
            size="small"
          />
        </Col>
        <Col span={12}>
          <Text>CPU使用率</Text>
          <Progress
            percent={currentData.cpuUsage}
            status={getPerformanceStatus(currentData.cpuUsage, { good: 30, warning: 70 })}
            size="small"
          />
        </Col>
      </Row>

      {/* 性能图表 */}
      {performanceData.length > 0 && (
        <div style={{ height: 200, marginTop: 16 }}>
          <Text strong>性能趋势</Text>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={performanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={(value) => new Date(value).toLocaleTimeString()}
              />
              <YAxis />
              <Tooltip 
                labelFormatter={(value) => new Date(value).toLocaleTimeString()}
                formatter={(value: number, name: string) => [
                  `${value.toFixed(1)}${name === 'fps' ? '' : name.includes('Time') ? 'ms' : '%'}`,
                  name === 'fps' ? 'FPS' :
                  name === 'memoryUsage' ? '内存使用率' :
                  name === 'cpuUsage' ? 'CPU使用率' :
                  name === 'renderTime' ? '渲染时间' :
                  name === 'updateTime' ? '更新时间' :
                  name === 'blendTime' ? '混合时间' : name
                ]}
              />
              <Line 
                type="monotone" 
                dataKey="fps" 
                stroke="#1890ff" 
                strokeWidth={2}
                dot={false}
              />
              <Line 
                type="monotone" 
                dataKey="renderTime" 
                stroke="#52c41a" 
                strokeWidth={2}
                dot={false}
              />
              <Line 
                type="monotone" 
                dataKey="blendTime" 
                stroke="#faad14" 
                strokeWidth={2}
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      )}

      {/* 性能建议 */}
      <div style={{ marginTop: 16 }}>
        <Text strong>性能建议:</Text>
        <ul style={{ marginTop: 8, paddingLeft: 20 }}>
          {currentData.fps < 30 && (
            <li><Text type="warning">降低动画复杂度或减少同时播放的动画数量</Text></li>
          )}
          {currentData.memoryUsage > 80 && (
            <li><Text type="danger">优化动画资源，减少内存占用</Text></li>
          )}
          {currentData.renderTime > 16 && (
            <li><Text type="warning">优化渲染管线，减少绘制调用</Text></li>
          )}
          {currentData.blendTime > 5 && (
            <li><Text type="warning">减少混合层数量或优化混合算法</Text></li>
          )}
          {currentData.fps >= 55 && currentData.memoryUsage < 50 && (
            <li><Text type="success">性能表现良好</Text></li>
          )}
        </ul>
      </div>
    </Card>
  );
};

export default AnimationPerformance;
