/**
 * 场景图层面板
 * 用于管理场景中的图层
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  List,
  Button,
  Input,
  Space,
  Dropdown,
  Menu,
  Modal,
  Form,
  ColorPicker,
  Tag,
  message
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  MoreOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  EditOutlined,
  SearchOutlined,
  TagOutlined,
  FolderOutlined,
  FolderOpenOutlined,
  CaretRightOutlined,
  CaretDownOutlined
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '../../store';
import EngineService from '../../services/EngineService';
import './LayersPanel.less';

// 图层数据接口
interface LayerData {
  id: string;
  name: string;
  visible: boolean;
  locked: boolean;
  order: number;
  color: string;
  tags: string[];
  entityCount: number;
  type: string;
  parentId: string | null;
  children: LayerData[];
  expanded: boolean;
  level: number;
}

const LayersPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // 状态
  const [layers, setLayers] = useState<LayerData[]>([]);
  const [selectedLayerId, setSelectedLayerId] = useState<string | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isCreateGroupModalVisible, setIsCreateGroupModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [groupForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 加载图层数据
  useEffect(() => {
    loadLayers();
  }, []);

  // 加载图层
  const loadLayers = () => {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) {
        setLayers([]);
        return;
      }

      const layerManager = scene.getLayerManager();
      if (!layerManager) {
        setLayers([]);
        return;
      }

      const sceneLayers = layerManager.getLayers();

      // 转换为扁平数据结构
      const flatLayerData: Record<string, LayerData> = {};

      // 首先创建所有图层的数据
      sceneLayers.forEach(layer => {
        flatLayerData[layer.id] = {
          id: layer.id,
          name: layer.name,
          visible: layer.isVisible(),
          locked: layer.isLocked(),
          order: layer.getOrder(),
          color: layer.getColor().getHexString(),
          tags: layer.getTags(),
          entityCount: layer.getEntityCount(),
          type: layer.getType(),
          parentId: layer.getParentId(),
          children: [],
          expanded: layer.isExpanded(),
          level: 0 // 初始化为0，后面会计算
        };
      });

      // 构建层级关系
      Object.values(flatLayerData).forEach(layer => {
        if (layer.parentId && flatLayerData[layer.parentId]) {
          flatLayerData[layer.parentId].children.push(layer);
        }
      });

      // 计算层级深度
      const calculateLevel = (layerId: string, level: number) => {
        flatLayerData[layerId].level = level;

        flatLayerData[layerId].children.forEach(child => {
          calculateLevel(child.id, level + 1);
        });
      };

      // 从根图层开始计算层级
      const rootLayers = Object.values(flatLayerData).filter(layer => !layer.parentId);
      rootLayers.forEach(layer => calculateLevel(layer.id, 0));

      // 按顺序排序
      const sortLayers = (layers: LayerData[]) => {
        layers.sort((a, b) => a.order - b.order);

        layers.forEach(layer => {
          sortLayers(layer.children);
        });
      };

      sortLayers(rootLayers);

      // 将层级结构扁平化为列表，用于显示
      const flattenLayers = (layers: LayerData[], result: LayerData[] = []) => {
        layers.forEach(layer => {
          result.push(layer);

          if (layer.expanded && layer.children.length > 0) {
            flattenLayers(layer.children, result);
          }
        });

        return result;
      };

      const flattenedLayers = flattenLayers(rootLayers);

      setLayers(flattenedLayers);
    } catch (error) {
      console.error('加载图层失败:', error);
      message.error('加载图层失败');
    }
  };

  // 过滤图层
  const filteredLayers = layers.filter(layer =>
    layer.name.toLowerCase().includes(searchValue.toLowerCase()) ||
    layer.tags.some(tag => tag.toLowerCase().includes(searchValue.toLowerCase()))
  );

  // 创建图层
  const handleCreateLayer = () => {
    form.validateFields().then(values => {
      try {
        const scene = EngineService.getActiveScene();
        if (!scene) {
          message.error('没有活动场景');
          return;
        }

        const layerManager = scene.getLayerManager();
        if (!layerManager) {
          message.error('场景没有图层管理器');
          return;
        }

        // 创建图层
        layerManager.createLayer({
          id: `layer_${Date.now()}`,
          name: values.name,
          visible: true,
          locked: false,
          order: layers.length,
          tags: values.tags || [],
          color: values.color || '#ffffff',
          parentId: values.parentId || null
        });

        // 重新加载图层
        loadLayers();

        // 关闭对话框
        setIsCreateModalVisible(false);
        form.resetFields();

        message.success(`图层 "${values.name}" 已创建`);
      } catch (error) {
        console.error('创建图层失败:', error);
        message.error('创建图层失败');
      }
    });
  };

  // 创建图层组
  const handleCreateLayerGroup = () => {
    groupForm.validateFields().then(values => {
      try {
        const scene = EngineService.getActiveScene();
        if (!scene) {
          message.error('没有活动场景');
          return;
        }

        const layerManager = scene.getLayerManager();
        if (!layerManager) {
          message.error('场景没有图层管理器');
          return;
        }

        // 创建图层组
        layerManager.createLayerGroup(values.name, values.parentId || null);

        // 重新加载图层
        loadLayers();

        // 关闭对话框
        setIsCreateGroupModalVisible(false);
        groupForm.resetFields();

        message.success(`图层组 "${values.name}" 已创建`);
      } catch (error) {
        console.error('创建图层组失败:', error);
        message.error('创建图层组失败');
      }
    });
  };

  // 编辑图层
  const handleEditLayer = () => {
    if (!selectedLayerId) return;

    editForm.validateFields().then(values => {
      try {
        const scene = EngineService.getActiveScene();
        if (!scene) {
          message.error('没有活动场景');
          return;
        }

        const layerManager = scene.getLayerManager();
        if (!layerManager) {
          message.error('场景没有图层管理器');
          return;
        }

        const layer = layerManager.getLayer(selectedLayerId);
        if (!layer) {
          message.error('找不到选中的图层');
          return;
        }

        // 更新图层属性
        layer.name = values.name;

        // 更新标签
        const currentTags = layer.getTags();
        for (const tag of currentTags) {
          layer.removeTag(tag);
        }

        for (const tag of values.tags || []) {
          layer.addTag(tag);
        }

        // 更新颜色
        layer.setColor(new THREE.Color(values.color));

        // 重新加载图层
        loadLayers();

        // 关闭对话框
        setIsEditModalVisible(false);
        editForm.resetFields();

        message.success(`图层 "${values.name}" 已更新`);
      } catch (error) {
        console.error('编辑图层失败:', error);
        message.error('编辑图层失败');
      }
    });
  };

  // 删除图层
  const handleDeleteLayer = (layerId: string) => {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) {
        message.error('没有活动场景');
        return;
      }

      const layerManager = scene.getLayerManager();
      if (!layerManager) {
        message.error('场景没有图层管理器');
        return;
      }

      // 获取图层名称
      const layer = layerManager.getLayer(layerId);
      const layerName = layer ? layer.name : layerId;

      // 确认删除
      Modal.confirm({
        title: t('editor.layers.confirmDelete'),
        content: t('editor.layers.confirmDeleteContent', { name: layerName }),
        okText: t('common.yes'),
        cancelText: t('common.no'),
        onOk: () => {
          // 删除图层
          const result = layerManager.removeLayer(layerId);

          if (result) {
            // 重新加载图层
            loadLayers();

            // 如果删除的是当前选中的图层，清除选择
            if (selectedLayerId === layerId) {
              setSelectedLayerId(null);
            }

            message.success(`图层 "${layerName}" 已删除`);
          } else {
            message.error(`无法删除图层 "${layerName}"`);
          }
        }
      });
    } catch (error) {
      console.error('删除图层失败:', error);
      message.error('删除图层失败');
    }
  };

  // 切换图层可见性
  const handleToggleVisibility = (layerId: string, visible: boolean) => {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) return;

      const layerManager = scene.getLayerManager();
      if (!layerManager) return;

      const layer = layerManager.getLayer(layerId);
      if (!layer) return;

      // 设置可见性
      layer.setVisible(!visible);

      // 更新状态
      setLayers(prevLayers =>
        prevLayers.map(l =>
          l.id === layerId ? { ...l, visible: !visible } : l
        )
      );
    } catch (error) {
      console.error('切换图层可见性失败:', error);
    }
  };

  // 切换图层锁定状态
  const handleToggleLock = (layerId: string, locked: boolean) => {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) return;

      const layerManager = scene.getLayerManager();
      if (!layerManager) return;

      const layer = layerManager.getLayer(layerId);
      if (!layer) return;

      // 设置锁定状态
      layer.setLocked(!locked);

      // 更新状态
      setLayers(prevLayers =>
        prevLayers.map(l =>
          l.id === layerId ? { ...l, locked: !locked } : l
        )
      );
    } catch (error) {
      console.error('切换图层锁定状态失败:', error);
    }
  };

  // 移动图层顺序
  const handleMoveLayer = (layerId: string, direction: 'up' | 'down') => {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) return;

      const layerManager = scene.getLayerManager();
      if (!layerManager) return;

      // 找到当前图层和目标图层
      const currentIndex = layers.findIndex(l => l.id === layerId);
      if (currentIndex === -1) return;

      const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

      // 检查边界
      if (targetIndex < 0 || targetIndex >= layers.length) return;

      const currentLayer = layerManager.getLayer(layerId);
      const targetLayer = layerManager.getLayer(layers[targetIndex].id);

      if (!currentLayer || !targetLayer) return;

      // 交换顺序
      const currentOrder = currentLayer.getOrder();
      const targetOrder = targetLayer.getOrder();

      currentLayer.setOrder(targetOrder);
      targetLayer.setOrder(currentOrder);

      // 重新加载图层
      loadLayers();
    } catch (error) {
      console.error('移动图层失败:', error);
    }
  };

  // 打开编辑对话框
  const handleOpenEditModal = (layerId: string) => {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) return;

      const layerManager = scene.getLayerManager();
      if (!layerManager) return;

      const layer = layerManager.getLayer(layerId);
      if (!layer) return;

      // 设置表单初始值
      editForm.setFieldsValue({
        name: layer.name,
        tags: layer.getTags(),
        color: layer.getColor().getHexString(),
        parentId: layer.getParentId()
      });

      setSelectedLayerId(layerId);
      setIsEditModalVisible(true);
    } catch (error) {
      console.error('打开编辑对话框失败:', error);
    }
  };

  // 切换图层展开状态
  const handleToggleExpanded = (layerId: string) => {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) return;

      const layerManager = scene.getLayerManager();
      if (!layerManager) return;

      const layer = layerManager.getLayer(layerId);
      if (!layer) return;

      // 切换展开状态
      layer.setExpanded(!layer.isExpanded());

      // 重新加载图层
      loadLayers();
    } catch (error) {
      console.error('切换图层展开状态失败:', error);
    }
  };

  return (
    <div className="layers-panel">
      <div className="panel-header">
        <h3>{t('editor.panels.layers')}</h3>
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            {t('editor.layers.add')}
          </Button>
          <Button
            size="small"
            icon={<FolderOutlined />}
            onClick={() => setIsCreateGroupModalVisible(true)}
          >
            {t('editor.layers.addGroup')}
          </Button>
        </Space>
      </div>

      <Input
        placeholder={t('editor.layers.search')}
        prefix={<SearchOutlined />}
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        className="search-input"
      />

      <List
        className="layers-list"
        dataSource={filteredLayers}
        renderItem={(layer) => (
          <List.Item
            key={layer.id}
            className={`layer-item ${selectedLayerId === layer.id ? 'selected' : ''} ${layer.type === 'group' ? 'layer-group' : ''}`}
            onClick={() => setSelectedLayerId(layer.id)}
            style={{ paddingLeft: `${layer.level * 16 + 8}px` }}
          >
            {layer.type === 'group' && (
              <Button
                type="text"
                size="small"
                className="expand-button"
                icon={layer.expanded ? <CaretDownOutlined /> : <CaretRightOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleExpanded(layer.id);
                }}
              />
            )}
            {layer.type === 'group' ? (
              <div className="layer-icon">
                {layer.expanded ? <FolderOpenOutlined /> : <FolderOutlined />}
              </div>
            ) : (
              <div className="layer-color" style={{ backgroundColor: `#${layer.color}` }} />
            )}
            <div className="layer-info">
              <div className="layer-name">{layer.name}</div>
              <div className="layer-meta">
                {layer.tags.map(tag => (
                  <Tag key={tag} className="layer-tag">{tag}</Tag>
                ))}
                <span className="entity-count">
                  {layer.type === 'group'
                    ? `${layer.children.length} 个子图层`
                    : `${layer.entityCount} 个实体`}
                </span>
              </div>
            </div>
            <div className="layer-actions">
              <Button
                type="text"
                size="small"
                icon={layer.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleVisibility(layer.id, layer.visible);
                }}
              />
              <Button
                type="text"
                size="small"
                icon={layer.locked ? <LockOutlined /> : <UnlockOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleLock(layer.id, layer.locked);
                }}
              />
              <Dropdown
                overlay={
                  <Menu>
                    <Menu.Item
                      key="edit"
                      icon={<EditOutlined />}
                      onClick={() => handleOpenEditModal(layer.id)}
                    >
                      {t('editor.layers.edit')}
                    </Menu.Item>
                    {layer.type === 'group' && (
                      <Menu.Item
                        key="addLayer"
                        icon={<PlusOutlined />}
                        onClick={() => {
                          setSelectedLayerId(layer.id);
                          form.setFieldsValue({ parentId: layer.id });
                          setIsCreateModalVisible(true);
                        }}
                      >
                        {t('editor.layers.addLayerToGroup')}
                      </Menu.Item>
                    )}
                    {layer.type === 'group' && (
                      <Menu.Item
                        key="addGroup"
                        icon={<FolderOutlined />}
                        onClick={() => {
                          setSelectedLayerId(layer.id);
                          groupForm.setFieldsValue({ parentId: layer.id });
                          setIsCreateGroupModalVisible(true);
                        }}
                      >
                        {t('editor.layers.addGroupToGroup')}
                      </Menu.Item>
                    )}
                    <Menu.Item
                      key="moveUp"
                      icon={<ArrowUpOutlined />}
                      disabled={layers.indexOf(layer) === 0}
                      onClick={() => handleMoveLayer(layer.id, 'up')}
                    >
                      {t('editor.layers.moveUp')}
                    </Menu.Item>
                    <Menu.Item
                      key="moveDown"
                      icon={<ArrowDownOutlined />}
                      disabled={layers.indexOf(layer) === layers.length - 1}
                      onClick={() => handleMoveLayer(layer.id, 'down')}
                    >
                      {t('editor.layers.moveDown')}
                    </Menu.Item>
                    <Menu.Divider />
                    <Menu.Item
                      key="delete"
                      icon={<DeleteOutlined />}
                      danger
                      disabled={layer.id === 'default' || layer.id === 'root'} // 不允许删除默认图层和根图层组
                      onClick={() => handleDeleteLayer(layer.id)}
                    >
                      {t('editor.layers.delete')}
                    </Menu.Item>
                  </Menu>
                }
                trigger={['click']}
              >
                <Button
                  type="text"
                  size="small"
                  icon={<MoreOutlined />}
                  onClick={(e) => e.stopPropagation()}
                />
              </Dropdown>
            </div>
          </List.Item>
        )}
      />

      {/* 创建图层对话框 */}
      <Modal
        title={t('editor.layers.createLayer')}
        open={isCreateModalVisible}
        onOk={handleCreateLayer}
        onCancel={() => setIsCreateModalVisible(false)}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label={t('editor.layers.name')}
            rules={[{ required: true, message: t('editor.layers.nameRequired') }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="parentId"
            label={t('editor.layers.parent')}
          >
            <Select
              allowClear
              placeholder={t('editor.layers.selectParent')}
              style={{ width: '100%' }}
            >
              {layers
                .filter(layer => layer.type === 'group')
                .map(layer => (
                  <Select.Option key={layer.id} value={layer.id}>
                    {'　'.repeat(layer.level)}{layer.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="tags"
            label={t('editor.layers.tags')}
          >
            <Select mode="tags" style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="color"
            label={t('editor.layers.color')}
          >
            <ColorPicker />
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建图层组对话框 */}
      <Modal
        title={t('editor.layers.createGroup')}
        open={isCreateGroupModalVisible}
        onOk={handleCreateLayerGroup}
        onCancel={() => setIsCreateGroupModalVisible(false)}
      >
        <Form form={groupForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('editor.layers.groupName')}
            rules={[{ required: true, message: t('editor.layers.groupNameRequired') }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="parentId"
            label={t('editor.layers.parent')}
          >
            <Select
              allowClear
              placeholder={t('editor.layers.selectParent')}
              style={{ width: '100%' }}
            >
              {layers
                .filter(layer => layer.type === 'group')
                .map(layer => (
                  <Select.Option key={layer.id} value={layer.id}>
                    {'　'.repeat(layer.level)}{layer.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑图层对话框 */}
      <Modal
        title={t('editor.layers.editLayer')}
        open={isEditModalVisible}
        onOk={handleEditLayer}
        onCancel={() => setIsEditModalVisible(false)}
      >
        <Form form={editForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('editor.layers.name')}
            rules={[{ required: true, message: t('editor.layers.nameRequired') }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="parentId"
            label={t('editor.layers.parent')}
          >
            <Select
              allowClear
              placeholder={t('editor.layers.selectParent')}
              style={{ width: '100%' }}
            >
              {layers
                .filter(layer => layer.type === 'group' && layer.id !== selectedLayerId)
                .map(layer => (
                  <Select.Option key={layer.id} value={layer.id}>
                    {'　'.repeat(layer.level)}{layer.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="tags"
            label={t('editor.layers.tags')}
          >
            <Select mode="tags" style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="color"
            label={t('editor.layers.color')}
          >
            <ColorPicker />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default LayersPanel;
