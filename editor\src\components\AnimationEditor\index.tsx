/**
 * 动画编辑器组件
 */
import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, InputNumber, Select, Button, Slider, Tooltip } from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepBackwardOutlined,
  StepForwardOutlined,
  SaveOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
// 注意：这里假设 Engine 是一个本地模块，实际使用时需要确保它已正确安装
// 临时使用模拟的 Engine 类
class MockEngine {
  constructor(options: any) {
    console.log('创建引擎实例', options);
  }

  createScene() {
    console.log('创建场景');
    return {};
  }

  createCamera(options: any) {
    console.log('创建相机', options);
    return {};
  }

  createLight(options: any) {
    console.log('创建光源', options);
    return {};
  }

  createGeometry(options: any) {
    console.log('创建几何体', options);
    return {};
  }

  createMaterial(options: any) {
    console.log('创建材质', options);
    return {};
  }

  createMesh(options: any) {
    console.log('创建网格', options);
    return {};
  }

  getScene() {
    return {
      removeAllEntities: () => {},
      getEntities: () => [{
        setPosition: () => {},
        setRotation: () => {},
        setScale: () => {},
        setVisible: () => {}
      }]
    };
  }

  start() {
    console.log('启动引擎');
  }

  dispose() {
    console.log('销毁引擎');
  }
}
import './AnimationEditor.less';

// 只保留需要使用的组件
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';

// 动画类型选项
const animationTypeOptions = [
  { value: 'position', label: '位置' },
  { value: 'rotation', label: '旋转' },
  { value: 'scale', label: '缩放' },
  { value: 'visibility', label: '可见性' },
  { value: 'color', label: '颜色' },
  { value: 'intensity', label: '强度' },
];

// 插值类型选项
const interpolationTypeOptions = [
  { value: 'linear', label: '线性' },
  { value: 'step', label: '阶梯' },
  { value: 'cubicBezier', label: '贝塞尔' },
  { value: 'catmullRom', label: 'Catmull-Rom' },
];

interface AnimationEditorProps {
  animationId?: string;
  entityId?: string;
  onSave?: (animation: any) => void;
  onCancel?: () => void;
}

const AnimationEditor: React.FC<AnimationEditorProps> = ({ animationId, onSave, onCancel }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const engineRef = useRef<any>(null);

  // 动画状态
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [duration, setDuration] = useState<number>(5);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [selectedKeyframe, setSelectedKeyframe] = useState<any>(null);
  const [tracks, setTracks] = useState<any[]>([]);
  const [selectedTrackIndex, setSelectedTrackIndex] = useState<number>(-1);

  // 从Redux获取动画数据
  // 注释掉未使用的代码，实际项目中可以取消注释并实现
  /*
  const animation = useSelector((state: RootState) => {
    if (!animationId) return null;
    // 这里应该从Redux状态中获取动画数据
    return state.animations[animationId];
  });
  */

  // 初始化引擎和预览
  useEffect(() => {
    if (!previewCanvasRef.current) return;

    // 创建引擎实例
    engineRef.current = new MockEngine({
      canvas: previewCanvasRef.current,
      width: previewCanvasRef.current.clientWidth,
      height: previewCanvasRef.current.clientHeight});

    // 设置预览场景
    setupPreviewScene();

    // 如果有动画ID，加载动画
    if (animationId) {
      loadAnimation(animationId);
    }

    // 清理函数
    return () => {
      if (engineRef.current) {
        engineRef.current.dispose();
      }
    };
  }, [animationId]);

  // 动画播放循环
  useEffect(() => {
    let animationFrame: number;
    let lastTime = 0;

    const animate = (time: number) => {
      if (!isPlaying) return;

      const deltaTime = time - lastTime;
      lastTime = time;

      // 更新当前时间
      setCurrentTime(prevTime => {
        const newTime = prevTime + deltaTime / 1000;
        return newTime > duration ? 0 : newTime;
      });

      animationFrame = requestAnimationFrame(animate);
    };

    if (isPlaying) {
      lastTime = performance.now();
      animationFrame = requestAnimationFrame(animate);
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying, duration]);

  // 设置预览场景
  const setupPreviewScene = () => {
    if (!engineRef.current) return;

    // 创建场景、相机和灯光
    // 直接调用方法而不保存返回值，因为我们不需要引用这些对象
    engineRef.current.createScene();
    engineRef.current.createCamera({
      type: 'perspective',
      position: [0, 0, 5],
      lookAt: [0, 0, 0]});

    // 添加环境光和方向光
    engineRef.current.createLight({
      type: 'ambient',
      intensity: 0.5});

    engineRef.current.createLight({
      type: 'directional',
      intensity: 0.8,
      position: [1, 1, 1],
      castShadow: true});

    // 创建预览对象
    createPreviewObject();

    // 启动引擎
    engineRef.current.start();
  };

  // 创建预览对象
  const createPreviewObject = () => {
    if (!engineRef.current) return;

    // 移除现有的预览对象
    engineRef.current.getScene().removeAllEntities();

    // 创建立方体
    const geometry = engineRef.current.createGeometry({
      type: 'box',
      width: 1,
      height: 1,
      depth: 1});

    // 创建材质
    const material = engineRef.current.createMaterial({
      type: 'standard',
      color: '#3498db'});

    // 创建网格
    engineRef.current.createMesh({
      geometry,
      material});
  };

  // 加载动画
  const loadAnimation = (id: string) => {
    // 这里应该从API或Redux加载动画数据
    // 由于我们没有实际的API，这里只是一个示例
    const dummyAnimation = {
      id,
      name: '示例动画',
      duration: 5,
      tracks: [
        {
          name: '位置动画',
          type: 'position',
          interpolation: 'linear',
          keyframes: [
            { time: 0, value: [0, 0, 0] },
            { time: 2.5, value: [2, 1, 0] },
            { time: 5, value: [0, 0, 0] },
          ]},
        {
          name: '旋转动画',
          type: 'rotation',
          interpolation: 'linear',
          keyframes: [
            { time: 0, value: [0, 0, 0] },
            { time: 2.5, value: [0, 3.14, 0] },
            { time: 5, value: [0, 6.28, 0] },
          ]},
      ]};

    // 设置表单值
    form.setFieldsValue({
      name: dummyAnimation.name,
      duration: dummyAnimation.duration});

    setDuration(dummyAnimation.duration);
    setTracks(dummyAnimation.tracks);
  };

  // 更新预览
  const updatePreview = () => {
    if (!engineRef.current) return;

    // 获取预览对象
    const mesh = engineRef.current.getScene().getEntities()[0];
    if (!mesh) return;

    // 应用当前时间的动画状态
    tracks.forEach(track => {
      if (track.keyframes.length < 2) return;

      // 找到当前时间所在的关键帧
      let startFrame = track.keyframes[0];
      let endFrame = track.keyframes[1];
      let t = 0;

      for (let i = 0; i < track.keyframes.length - 1; i++) {
        if (currentTime >= track.keyframes[i].time && currentTime <= track.keyframes[i + 1].time) {
          startFrame = track.keyframes[i];
          endFrame = track.keyframes[i + 1];
          t = (currentTime - startFrame.time) / (endFrame.time - startFrame.time);
          break;
        }
      }

      // 插值计算当前值
      let currentValue;
      switch (track.type) {
        case 'position':
        case 'rotation':
        case 'scale':
          currentValue = [
            startFrame.value[0] + (endFrame.value[0] - startFrame.value[0]) * t,
            startFrame.value[1] + (endFrame.value[1] - startFrame.value[1]) * t,
            startFrame.value[2] + (endFrame.value[2] - startFrame.value[2]) * t,
          ];
          break;
        case 'visibility':
          currentValue = t >= 0.5 ? endFrame.value : startFrame.value;
          break;
        default:
          currentValue = startFrame.value;
      }

      // 应用值到对象
      switch (track.type) {
        case 'position':
          mesh.setPosition(currentValue);
          break;
        case 'rotation':
          mesh.setRotation(currentValue);
          break;
        case 'scale':
          mesh.setScale(currentValue);
          break;
        case 'visibility':
          mesh.setVisible(currentValue);
          break;
      }
    });
  };

  // 每次时间或轨道变化时更新预览
  useEffect(() => {
    updatePreview();
  }, [currentTime, tracks]);

  // 处理播放/暂停
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  // 处理跳转到开始
  const handleJumpToStart = () => {
    setCurrentTime(0);
  };

  // 处理跳转到结束
  const handleJumpToEnd = () => {
    setCurrentTime(duration);
  };

  // 处理添加轨道
  const handleAddTrack = () => {
    const newTrack = {
      name: `轨道 ${tracks.length + 1}`,
      type: 'position',
      interpolation: 'linear',
      keyframes: [
        { time: 0, value: [0, 0, 0] },
        { time: duration, value: [0, 0, 0] },
      ]};

    setTracks([...tracks, newTrack]);
    setSelectedTrackIndex(tracks.length);
  };

  // 处理删除轨道
  const handleDeleteTrack = (index: number) => {
    const newTracks = [...tracks];
    newTracks.splice(index, 1);
    setTracks(newTracks);

    if (selectedTrackIndex === index) {
      setSelectedTrackIndex(-1);
    } else if (selectedTrackIndex > index) {
      setSelectedTrackIndex(selectedTrackIndex - 1);
    }
  };

  // 处理添加关键帧
  const handleAddKeyframe = (trackIndex: number) => {
    const track = tracks[trackIndex];
    if (!track) return;

    // 创建新关键帧
    let defaultValue;
    switch (track.type) {
      case 'position':
      case 'rotation':
      case 'scale':
        defaultValue = [0, 0, 0];
        break;
      case 'visibility':
        defaultValue = true;
        break;
      case 'color':
        defaultValue = '#ffffff';
        break;
      case 'intensity':
        defaultValue = 1;
        break;
      default:
        defaultValue = 0;
    }

    const newKeyframe = {
      time: currentTime,
      value: defaultValue};

    // 插入到正确的位置
    const newKeyframes = [...track.keyframes];
    let inserted = false;

    for (let i = 0; i < newKeyframes.length; i++) {
      if (newKeyframes[i].time > currentTime) {
        newKeyframes.splice(i, 0, newKeyframe);
        inserted = true;
        break;
      } else if (newKeyframes[i].time === currentTime) {
        // 如果已经存在相同时间的关键帧，则更新它
        newKeyframes[i] = newKeyframe;
        inserted = true;
        break;
      }
    }

    if (!inserted) {
      newKeyframes.push(newKeyframe);
    }

    // 更新轨道
    const newTracks = [...tracks];
    newTracks[trackIndex] = {
      ...track,
      keyframes: newKeyframes};

    setTracks(newTracks);
    setSelectedKeyframe({ trackIndex, keyframeIndex: newKeyframes.indexOf(newKeyframe) });
  };

  // 处理删除关键帧
  const handleDeleteKeyframe = (trackIndex: number, keyframeIndex: number) => {
    const track = tracks[trackIndex];
    if (!track || track.keyframes.length <= 2) {
      // 至少保留两个关键帧
      return;
    }

    const newKeyframes = [...track.keyframes];
    newKeyframes.splice(keyframeIndex, 1);

    const newTracks = [...tracks];
    newTracks[trackIndex] = {
      ...track,
      keyframes: newKeyframes};

    setTracks(newTracks);

    if (selectedKeyframe && selectedKeyframe.trackIndex === trackIndex && selectedKeyframe.keyframeIndex === keyframeIndex) {
      setSelectedKeyframe(null);
    }
  };

  // 处理保存
  const handleSave = () => {
    form.validateFields().then(values => {
      if (onSave) {
        onSave({
          id: animationId,
          ...values,
          tracks});
      }
    });
  };

  // 渲染时间轴
  const renderTimeline = () => {
    const timelineWidth = timelineRef.current?.clientWidth || 600;
    const pixelsPerSecond = timelineWidth / duration;

    return (
      <div className="timeline-container" ref={timelineRef}>
        <div className="timeline-header">
          {/* 时间刻度 */}
          <div className="timeline-ruler">
            {Array.from({ length: Math.ceil(duration) + 1 }).map((_, i) => (
              <div key={i} className="timeline-tick" style={{ left: `${i * pixelsPerSecond}px` }}>
                <span>{i}s</span>
              </div>
            ))}
          </div>

          {/* 当前时间指示器 */}
          <div
            className="timeline-cursor"
            style={{ left: `${currentTime * pixelsPerSecond}px` }}
          />
        </div>

        {/* 轨道 */}
        <div className="timeline-tracks">
          {tracks.map((track, trackIndex) => (
            <div
              key={trackIndex}
              className={`timeline-track ${selectedTrackIndex === trackIndex ? 'selected' : ''}`}
              onClick={() => setSelectedTrackIndex(trackIndex)}
            >
              <div className="track-header">
                <span>{track.name}</span>
                <div className="track-actions">
                  <Button
                    type="text"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddKeyframe(trackIndex);
                    }}
                  />
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteTrack(trackIndex);
                    }}
                  />
                </div>
              </div>

              <div className="track-content">
                {/* 关键帧 */}
                {track.keyframes.map((keyframe: any, keyframeIndex: number) => (
                  <Tooltip
                    key={keyframeIndex}
                    title={`${keyframe.time.toFixed(2)}s: ${Array.isArray(keyframe.value) ? keyframe.value.map((v: number) => v.toFixed(2)).join(', ') : keyframe.value}`}
                  >
                    <div
                      className={`keyframe ${selectedKeyframe && selectedKeyframe.trackIndex === trackIndex && selectedKeyframe.keyframeIndex === keyframeIndex ? 'selected' : ''}`}
                      style={{ left: `${keyframe.time * pixelsPerSecond}px` }}
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedKeyframe({ trackIndex, keyframeIndex });
                        setCurrentTime(keyframe.time);
                      }}
                      onDoubleClick={(e) => {
                        e.stopPropagation();
                        handleDeleteKeyframe(trackIndex, keyframeIndex);
                      }}
                    />
                  </Tooltip>
                ))}

                {/* 轨道连线 */}
                <svg className="track-lines" width="100%" height="100%">
                  {track.keyframes.slice(0, -1).map((keyframe: any, i: number) => {
                    const nextKeyframe = track.keyframes[i + 1];
                    return (
                      <line
                        key={i}
                        x1={keyframe.time * pixelsPerSecond}
                        y1="50%"
                        x2={nextKeyframe.time * pixelsPerSecond}
                        y2="50%"
                        stroke="#1890ff"
                        strokeWidth="2"
                      />
                    );
                  })}
                </svg>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="animation-editor">
      <div className="animation-editor-header">
        <h2>{animationId ? t('editor.animation.edit') : t('editor.animation.create')}</h2>
        <div className="animation-editor-actions">
          <Button onClick={onCancel}>{t('editor.cancel')}</Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
            {t('editor.save')}
          </Button>
        </div>
      </div>

      <div className="animation-editor-content">
        <div className="animation-editor-preview">
          <canvas ref={previewCanvasRef} className="preview-canvas" />

          <div className="preview-controls">
            <Button icon={<StepBackwardOutlined />} onClick={handleJumpToStart} />
            <Button
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={handlePlayPause}
            />
            <Button icon={<StepForwardOutlined />} onClick={handleJumpToEnd} />
            <span className="time-display">
              {currentTime.toFixed(2)}s / {duration.toFixed(2)}s
            </span>
          </div>
        </div>

        <div className="animation-editor-form">
          <Form
            form={form}
            layout="vertical"
          >
            <Form.Item name="name" label={t('editor.animation.name')} rules={[{ required: true }]}>
              <Input />
            </Form.Item>

            <Form.Item name="duration" label={t('editor.animation.duration')} rules={[{ required: true }]}>
              <InputNumber
                min={0.1}
                step={0.1}
                style={{ width: '100%' }}
                onChange={(value) => setDuration(value || 5)}
              />
            </Form.Item>
          </Form>

          <div className="animation-editor-timeline">
            <div className="timeline-toolbar">
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAddTrack}>
                {t('editor.animation.addTrack')}
              </Button>
              <Slider
                value={currentTime}
                min={0}
                max={duration}
                step={0.01}
                style={{ flex: 1, margin: '0 16px' }}
                onChange={setCurrentTime}
              />
            </div>

            {renderTimeline()}
          </div>

          {selectedTrackIndex !== -1 && (
            <div className="track-properties">
              <h3>{t('editor.animation.trackProperties')}</h3>
              <Form
                layout="vertical"
                initialValues={tracks[selectedTrackIndex]}
                onValuesChange={(_changedValues, allValues) => {
                  const newTracks = [...tracks];
                  newTracks[selectedTrackIndex] = {
                    ...newTracks[selectedTrackIndex],
                    ...allValues};
                  setTracks(newTracks);
                }}
              >
                <Form.Item name="name" label={t('editor.animation.trackName')}>
                  <Input />
                </Form.Item>

                <Form.Item name="type" label={t('editor.animation.trackType')}>
                  <Select>
                    {animationTypeOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item name="interpolation" label={t('editor.animation.interpolation')}>
                  <Select>
                    {interpolationTypeOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Form>
            </div>
          )}

          {selectedKeyframe && (
            <div className="keyframe-properties">
              <h3>{t('editor.animation.keyframeProperties')}</h3>
              <Form
                layout="vertical"
                initialValues={tracks[selectedKeyframe.trackIndex]?.keyframes[selectedKeyframe.keyframeIndex]}
                onValuesChange={(_changedValues, allValues) => {
                  const newTracks = [...tracks];
                  const track = newTracks[selectedKeyframe.trackIndex];
                  const keyframes = [...track.keyframes];
                  keyframes[selectedKeyframe.keyframeIndex] = {
                    ...keyframes[selectedKeyframe.keyframeIndex],
                    ...allValues};
                  newTracks[selectedKeyframe.trackIndex] = {
                    ...track,
                    keyframes};
                  setTracks(newTracks);
                }}
              >
                <Form.Item name="time" label={t('editor.animation.time')}>
                  <InputNumber
                    min={0}
                    max={duration}
                    step={0.01}
                    style={{ width: '100%' }}
                  />
                </Form.Item>

                {tracks[selectedKeyframe.trackIndex]?.type === 'position' ||
                 tracks[selectedKeyframe.trackIndex]?.type === 'rotation' ||
                 tracks[selectedKeyframe.trackIndex]?.type === 'scale' ? (
                  <>
                    <Form.Item label={t('editor.animation.value')}>
                      <div style={{ display: 'flex', flexDirection: 'row' }}>
                        <Form.Item name={['value', 0]} noStyle>
                          <InputNumber addonBefore="X" style={{ width: '100%', marginRight: '4px' }} />
                        </Form.Item>
                        <Form.Item name={['value', 1]} noStyle>
                          <InputNumber addonBefore="Y" style={{ width: '100%', marginRight: '4px' }} />
                        </Form.Item>
                        <Form.Item name={['value', 2]} noStyle>
                          <InputNumber addonBefore="Z" style={{ width: '100%' }} />
                        </Form.Item>
                      </div>
                    </Form.Item>
                  </>
                ) : (
                  <Form.Item name="value" label={t('editor.animation.value')}>
                    {tracks[selectedKeyframe.trackIndex]?.type === 'visibility' ? (
                      <Select>
                        <Option value={true}>{t('editor.yes')}</Option>
                        <Option value={false}>{t('editor.no')}</Option>
                      </Select>
                    ) : tracks[selectedKeyframe.trackIndex]?.type === 'color' ? (
                      <Input type="color" />
                    ) : (
                      <InputNumber style={{ width: '100%' }} />
                    )}
                  </Form.Item>
                )}
              </Form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnimationEditor;
