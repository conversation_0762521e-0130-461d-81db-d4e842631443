/**
 * Markdown查看器组件
 * 用于渲染Markdown内容
 */
import React, { useEffect, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import rehypeHighlight from 'rehype-highlight';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Image, Table } from 'antd';
import './MarkdownViewer.less';

interface MarkdownViewerProps {
  content: string;
  baseUrl?: string;
}

const MarkdownViewer: React.FC<MarkdownViewerProps> = ({ content, baseUrl = '' }) => {
  const [processedContent, setProcessedContent] = useState(content);

  // 处理图片路径
  useEffect(() => {
    if (!content) return;

    // 替换相对路径的图片链接为绝对路径
    let processed = content;
    if (baseUrl) {
      const imgRegex = /!\[(.*?)\]\((\.\.\/.*?)\)/g;
      processed = processed.replace(imgRegex, (match, alt, path) => {
        const absolutePath = path.replace(/^\.\.\//, baseUrl);
        return `![${alt}](${absolutePath})`;
      });
    }

    setProcessedContent(processed);
  }, [content, baseUrl]);

  // 自定义组件渲染
  const components = {
    // 代码块渲染
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || '');
      return !inline && match ? (
        <SyntaxHighlighter
          style={vscDarkPlus}
          language={match[1]}
          PreTag="div"
          {...props}
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      ) : (
        <code className={className} {...props}>
          {children}
        </code>
      );
    },
    
    // 图片渲染
    img({ node, alt, src, ...props }: any) {
      return (
        <Image
          alt={alt}
          src={src}
          preview={{ mask: '点击查看大图' }}
          {...props}
        />
      );
    },
    
    // 表格渲染
    table({ node, children, ...props }: any) {
      return (
        <Table
          bordered
          pagination={false}
          size="small"
          {...props}
          components={{
            body: {
              cell: ({ children }: any) => <td>{children}</td>,
              row: ({ children }: any) => <tr>{children}</tr>}}}
        >
          {children}
        </Table>
      );
    },
    
    // 链接渲染
    a({ node, children, href, ...props }: any) {
      // 处理内部链接
      if (href && href.startsWith('./') && baseUrl) {
        href = href.replace(/^\.\//, baseUrl);
      }
      
      // 处理外部链接
      const isExternal = href && (href.startsWith('http://') || href.startsWith('https://'));
      
      return (
        <a
          href={href}
          target={isExternal ? '_blank' : undefined}
          rel={isExternal ? 'noopener noreferrer' : undefined}
          {...props}
        >
          {children}
        </a>
      );
    }
  };

  return (
    <div className="markdown-viewer">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw, rehypeHighlight]}
        components={components}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownViewer;
