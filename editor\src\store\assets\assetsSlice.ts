/**
 * 资产状态切片
 */
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import assetService from '../../services/AssetService';

// 定义资产类型
export enum AssetType {
  MODEL = 'model',
  TEXTURE = 'texture',
  MATERIAL = 'material',
  AUDIO = 'audio',
  SCRIPT = 'script',
  PREFAB = 'prefab',
  SCENE = 'scene',
  ANIMATION = 'animation',
  PARTICLE = 'particle',
  OTHER = 'other'}

// 定义资产接口
export interface Asset {
  id: string;
  name: string;
  type: AssetType;
  url: string;
  thumbnail?: string;
  metadata?: any;
  projectId: string;
  folderId?: string;
  createdAt: string;
  updatedAt: string;
}

// 定义资产状态
interface AssetState {
  assets: Asset[];
  selectedAssetId: string | null;
  loading: boolean;
  error: string | null;
  currentProjectId: string | null;
  currentFolderId: string | null;
}

// 初始状态
const initialState: AssetState = {
  assets: [],
  selectedAssetId: null,
  loading: false,
  error: null,
  currentProjectId: null,
  currentFolderId: null};

// 异步操作：获取所有资产
export const fetchAssets = createAsyncThunk(
  'assets/fetchAssets',
  async ({ projectId, folderId }: { projectId: string; folderId?: string }, { rejectWithValue }) => {
    try {
      const response = await assetService.fetchAssets(projectId, folderId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：获取单个资产
export const fetchAsset = createAsyncThunk(
  'assets/fetchAsset',
  async (assetId: string, { rejectWithValue }) => {
    try {
      // AssetService 没有单独获取资产的方法，我们从已有的资产列表中查找
      const assets = assetService.getAssets();
      const asset = assets.find(a => a.id === assetId);
      if (!asset) {
        throw new Error('资产不存在');
      }
      return asset;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：上传资产
export const uploadAsset = createAsyncThunk(
  'assets/uploadAsset',
  async ({ file, type, folderId }: { file: File; type: AssetType; folderId?: string }, { rejectWithValue }) => {
    try {
      const response = await assetService.uploadAsset(file, type, folderId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：删除资产
export const removeAsset = createAsyncThunk(
  'assets/removeAsset',
  async (assetId: string, { rejectWithValue }) => {
    try {
      await assetService.deleteAsset(assetId);
      return assetId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 创建资产切片
const assetsSlice = createSlice({
  name: 'assets',
  initialState,
  reducers: {
    // 选择资产
    selectAsset: (state, action: PayloadAction<string | null>) => {
      state.selectedAssetId = action.payload;
    },

    // 设置当前项目
    setCurrentProject: (state, action: PayloadAction<string | null>) => {
      state.currentProjectId = action.payload;
      // 切换项目时清空资产列表
      if (action.payload !== state.currentProjectId) {
        state.assets = [];
        state.selectedAssetId = null;
      }
    },

    // 设置当前文件夹
    setCurrentFolder: (state, action: PayloadAction<string | null>) => {
      state.currentFolderId = action.payload;
    },

    // 添加资产（本地操作，不发送请求）
    addAsset: (state, action: PayloadAction<Asset>) => {
      state.assets.push(action.payload);
    },

    // 更新资产（本地操作，不发送请求）
    updateAsset: (state, action: PayloadAction<{ id: string; changes: Partial<Asset> }>) => {
      const { id, changes } = action.payload;
      const assetIndex = state.assets.findIndex(asset => asset.id === id);

      if (assetIndex !== -1) {
        state.assets[assetIndex] = {
          ...state.assets[assetIndex],
          ...changes,
          metadata: changes.metadata
            ? { ...state.assets[assetIndex].metadata, ...changes.metadata }
            : state.assets[assetIndex].metadata};
      }
    },

    // 清除错误
    clearError: (state) => {
      state.error = null;
    }},
  extraReducers: (builder) => {
    builder
      // 获取所有资产
      .addCase(fetchAssets.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAssets.fulfilled, (state, action) => {
        state.loading = false;
        state.assets = action.payload;
      })
      .addCase(fetchAssets.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 获取单个资产
      .addCase(fetchAsset.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAsset.fulfilled, (state, action) => {
        state.loading = false;
        const assetIndex = state.assets.findIndex(asset => asset.id === action.payload.id);
        
        if (assetIndex !== -1) {
          state.assets[assetIndex] = action.payload;
        } else {
          state.assets.push(action.payload);
        }
      })
      .addCase(fetchAsset.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 上传资产
      .addCase(uploadAsset.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(uploadAsset.fulfilled, (state, action) => {
        state.loading = false;
        state.assets.push(action.payload);
      })
      .addCase(uploadAsset.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 删除资产
      .addCase(removeAsset.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeAsset.fulfilled, (state, action) => {
        state.loading = false;
        state.assets = state.assets.filter(asset => asset.id !== action.payload);
        
        if (state.selectedAssetId === action.payload) {
          state.selectedAssetId = null;
        }
      })
      .addCase(removeAsset.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }});

export const {
  selectAsset,
  setCurrentProject,
  setCurrentFolder,
  addAsset,
  updateAsset,
  clearError} = assetsSlice.actions;

export default assetsSlice.reducer;
