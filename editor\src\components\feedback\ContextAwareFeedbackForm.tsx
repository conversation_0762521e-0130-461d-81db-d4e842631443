/**
 * 上下文感知反馈表单组件
 * 根据当前操作上下文自动收集相关信息的反馈表单
 */
import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Rate, Radio, Checkbox, Upload, Modal, message, Space, Collapse, Tag } from 'antd';
import { 
  UploadOutlined, 
  SendOutlined, 
  CloseOutlined, 
  InfoCircleOutlined, 
  BugOutlined, 
  BulbOutlined, 
  StarOutlined, 
  CommentOutlined,
  EnvironmentOutlined,
  HistoryOutlined,
  ToolOutlined,
  FileImageOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { FeedbackService } from '../../services/FeedbackService';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { useEditorContext } from '../../contexts/EditorContext';
import { captureScreenshot } from '../../utils/screenshot';
import './FeedbackForm.less';

const { TextArea } = Input;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { Panel } = Collapse;
const { Group: RadioGroup } = Radio;

export interface ContextAwareFeedbackFormProps {
  /** 反馈类型 */
  type: 'animation' | 'physics' | 'rendering' | 'editor' | 'general';
  /** 反馈子类型 */
  subType?: string;
  /** 相关实体ID */
  entityId?: string;
  /** 相关资源ID */
  resourceId?: string;
  /** 关闭回调 */
  onClose?: () => void;
  /** 提交成功回调 */
  onSuccess?: () => void;
  /** 自动捕获上下文 */
  autoCaptureContext?: boolean;
}

/**
 * 上下文感知反馈表单组件
 */
const ContextAwareFeedbackForm: React.FC<ContextAwareFeedbackFormProps> = ({
  type,
  subType,
  entityId,
  resourceId,
  onClose,
  onSuccess,
  autoCaptureContext = true
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [feedbackType, setFeedbackType] = useState<string>('bug');
  const [autoScreenshot, setAutoScreenshot] = useState<boolean>(true);
  const [contextData, setContextData] = useState<any>(null);
  const [screenshotPreview, setScreenshotPreview] = useState<string | null>(null);
  
  // 从Redux获取当前编辑器状态
  const editorState = useSelector((state: RootState) => state.editor);
  const uiState = useSelector((state: RootState) => state.ui);
  const { getCurrentContext } = useEditorContext();
  
  // 反馈类型选项
  const feedbackTypeOptions = [
    { label: t('feedback.type.bug'), value: 'bug', icon: <BugOutlined /> },
    { label: t('feedback.type.feature'), value: 'feature', icon: <BulbOutlined /> },
    { label: t('feedback.type.improvement'), value: 'improvement', icon: <ToolOutlined /> },
    { label: t('feedback.type.performance'), value: 'performance', icon: <HistoryOutlined /> },
    { label: t('feedback.type.usability'), value: 'usability', icon: <StarOutlined /> },
    { label: t('feedback.type.other'), value: 'other', icon: <CommentOutlined /> }
  ];

  // 初始化时捕获上下文
  useEffect(() => {
    if (autoCaptureContext) {
      captureContext();
    }
    
    // 如果启用了自动截图，则捕获截图
    if (autoScreenshot) {
      captureScreenshotForFeedback();
    }
  }, []);

  // 捕获上下文
  const captureContext = () => {
    try {
      // 获取当前编辑器上下文
      const context = getCurrentContext();
      
      // 获取当前选中的实体
      const selectedEntity = editorState.selectedEntity;
      
      // 获取当前打开的面板
      const activePanels = uiState.activePanels;
      
      // 获取当前工具
      const activeTool = editorState.activeTool;
      
      // 获取当前操作历史
      const recentOperations = editorState.operationHistory?.slice(-5) || [];
      
      // 组合上下文数据
      const contextualData = {
        timestamp: new Date().toISOString(),
        location: window.location.href,
        selectedEntity,
        activePanels,
        activeTool,
        recentOperations,
        ...context
      };
      
      setContextData(contextualData);
      
      // 预填充表单
      if (selectedEntity) {
        form.setFieldsValue({
          entityId: selectedEntity.id,
          entityName: selectedEntity.name
        });
      }
    } catch (error) {
      console.error('捕获上下文失败:', error);
    }
  };

  // 捕获截图
  const captureScreenshotForFeedback = async () => {
    try {
      const screenshot = await captureScreenshot();
      setScreenshotPreview(screenshot);
      
      // 将截图添加到表单
      form.setFieldsValue({
        autoScreenshot: screenshot
      });
    } catch (error) {
      console.error('捕获截图失败:', error);
      message.error(t('feedback.screenshotError'));
    }
  };

  // 处理反馈类型变化
  const handleFeedbackTypeChange = (e: any) => {
    setFeedbackType(e.target.value);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);
      
      // 准备反馈数据
      const feedbackData = {
        ...values,
        type,
        subType,
        entityId: values.entityId || entityId,
        resourceId: values.resourceId || resourceId,
        timestamp: new Date().toISOString(),
        browser: navigator.userAgent,
        screenSize: `${window.innerWidth}x${window.innerHeight}`,
        contextData: contextData,
        feedbackType: values.feedbackType || feedbackType
      };
      
      // 如果有自动截图，添加到反馈数据
      if (autoScreenshot && screenshotPreview) {
        feedbackData.screenshots = [screenshotPreview];
      }
      
      // 提交反馈
      await FeedbackService.submitFeedback(feedbackData);
      
      // 重置表单
      form.resetFields();
      
      // 显示成功提示
      setShowSuccessModal(true);
      
      // 调用成功回调
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('提交反馈失败:', error);
      message.error(t('feedback.submitError'));
    } finally {
      setSubmitting(false);
    }
  };

  // 关闭成功提示
  const handleCloseSuccessModal = () => {
    setShowSuccessModal(false);
    if (onClose) {
      onClose();
    }
  };

  // 渲染上下文数据预览
  const renderContextPreview = () => {
    if (!contextData) return null;
    
    return (
      <Collapse ghost className="context-preview-collapse">
        <Panel 
          header={
            <Space>
              <EnvironmentOutlined />
              <span>{t('feedback.contextData')}</span>
              <Tag color="blue">{t('feedback.automatic')}</Tag>
            </Space>
          } 
          key="context"
        >
          <div className="context-preview">
            {contextData.selectedEntity && (
              <div className="context-item">
                <strong>{t('feedback.selectedEntity')}:</strong> {contextData.selectedEntity.name} (ID: {contextData.selectedEntity.id})
              </div>
            )}
            
            {contextData.activeTool && (
              <div className="context-item">
                <strong>{t('feedback.activeTool')}:</strong> {contextData.activeTool}
              </div>
            )}
            
            {contextData.activePanels && contextData.activePanels.length > 0 && (
              <div className="context-item">
                <strong>{t('feedback.activePanels')}:</strong> {contextData.activePanels.join(', ')}
              </div>
            )}
            
            {contextData.recentOperations && contextData.recentOperations.length > 0 && (
              <div className="context-item">
                <strong>{t('feedback.recentOperations')}:</strong>
                <ul>
                  {contextData.recentOperations.map((op: any, index: number) => (
                    <li key={index}>{op.type} - {new Date(op.timestamp).toLocaleTimeString()}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </Panel>
      </Collapse>
    );
  };

  // 渲染截图预览
  const renderScreenshotPreview = () => {
    if (!screenshotPreview) return null;
    
    return (
      <div className="screenshot-preview">
        <div className="screenshot-preview-header">
          <Space>
            <FileImageOutlined />
            <span>{t('feedback.screenshotPreview')}</span>
            <Tag color="blue">{t('feedback.automatic')}</Tag>
          </Space>
        </div>
        <div className="screenshot-preview-image">
          <img src={screenshotPreview} alt="Screenshot Preview" />
        </div>
      </div>
    );
  };

  return (
    <div className="feedback-form">
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          feedbackType: 'bug',
          includeContext: true,
          allowContact: false
        }}
      >
        {/* 反馈类型 */}
        <Form.Item
          name="feedbackType"
          label={t('feedback.form.feedbackType')}
        >
          <RadioGroup onChange={handleFeedbackTypeChange} value={feedbackType}>
            <Space direction="vertical">
              {feedbackTypeOptions.map(option => (
                <Radio key={option.value} value={option.value}>
                  <Space>
                    {option.icon}
                    <span>{option.label}</span>
                  </Space>
                </Radio>
              ))}
            </Space>
          </RadioGroup>
        </Form.Item>

        {/* 标题 */}
        <Form.Item
          name="title"
          label={t('feedback.form.title')}
          rules={[{ required: true, message: t('feedback.form.required') }]}
        >
          <Input placeholder={t('feedback.form.titlePlaceholder')} />
        </Form.Item>

        {/* 描述 */}
        <Form.Item
          name="description"
          label={t('feedback.form.description')}
          rules={[{ required: true, message: t('feedback.form.required') }]}
        >
          <TextArea
            placeholder={t('feedback.form.descriptionPlaceholder')}
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </Form.Item>

        {/* 重现步骤 - 仅在反馈类型为bug时显示 */}
        {feedbackType === 'bug' && (
          <Form.Item
            name="reproductionSteps"
            label={t('feedback.form.reproductionSteps')}
          >
            <TextArea
              placeholder={t('feedback.form.reproductionStepsPlaceholder')}
              autoSize={{ minRows: 3, maxRows: 6 }}
            />
          </Form.Item>
        )}

        {/* 上下文数据预览 */}
        {renderContextPreview()}

        {/* 截图预览 */}
        {autoScreenshot && renderScreenshotPreview()}

        {/* 满意度评分 */}
        <Form.Item
          name="satisfaction"
          label={t('feedback.form.satisfaction')}
        >
          <Rate allowHalf />
        </Form.Item>

        {/* 改进建议 */}
        <Form.Item
          name="suggestions"
          label={t('feedback.form.suggestions')}
        >
          <TextArea
            placeholder={t('feedback.form.suggestionsPlaceholder')}
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </Form.Item>

        {/* 截图上传 - 允许用户上传额外的截图 */}
        <Form.Item
          name="additionalScreenshots"
          label={t('feedback.form.additionalScreenshots')}
        >
          <Upload
            listType="picture"
            maxCount={3}
            beforeUpload={() => false} // 阻止自动上传
          >
            <Button icon={<UploadOutlined />}>{t('feedback.form.uploadScreenshot')}</Button>
          </Upload>
        </Form.Item>

        {/* 联系许可 */}
        <Form.Item
          name="allowContact"
          valuePropName="checked"
        >
          <Checkbox>{t('feedback.form.allowContact')}</Checkbox>
        </Form.Item>

        {/* 提交按钮 */}
        <Form.Item>
          <Button
            type="primary"
            onClick={handleSubmit}
            loading={submitting}
            icon={<SendOutlined />}
          >
            {t('feedback.form.submit')}
          </Button>
        </Form.Item>
      </Form>

      {/* 成功提示 */}
      <Modal
        title={t('feedback.success.title')}
        open={showSuccessModal}
        onCancel={handleCloseSuccessModal}
        footer={[
          <Button key="close" onClick={handleCloseSuccessModal}>
            {t('feedback.success.close')}
          </Button>
        ]}
      >
        <p>{t('feedback.success.message')}</p>
      </Modal>
    </div>
  );
};

export default ContextAwareFeedbackForm;
