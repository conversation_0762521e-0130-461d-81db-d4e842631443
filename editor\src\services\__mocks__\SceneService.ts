/**
 * SceneService 模拟实现
 * 用于测试环境
 */

const mockSceneService = {
  loadScene: jest.fn<any, any>().mockResolvedValue({
    id: 'test-scene',
    entities: []}),
  saveScene: jest.fn<any, any>().mockResolvedValue(true),
  createScene: jest.fn<any, any>().mockResolvedValue({
    id: 'new-scene',
    entities: []}),
  deleteScene: jest.fn<any, any>().mockResolvedValue(true)};

export default mockSceneService;
