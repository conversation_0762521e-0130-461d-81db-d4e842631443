/**
 * 视频教程播放器组件
 * 用于播放视频教程并显示章节信息
 */
import React, { useState, useEffect, useRef } from 'react';
import { Button, Slider, Tooltip, List, Tag, Typography, Divider } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  FullscreenOutlined, 
  SettingOutlined,
  SoundOutlined,
  ExpandOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import VideoTutorialService, { VideoTutorial, VideoTutorialChapter } from '../../services/VideoTutorialService';
import './VideoTutorialPlayer.less';

const { Title, Text, Paragraph } = Typography;

interface VideoTutorialPlayerProps {
  tutorialId: string;
  onClose?: () => void;
}

const VideoTutorialPlayer: React.FC<VideoTutorialPlayerProps> = ({ tutorialId, onClose }) => {
  const { t } = useTranslation();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [tutorial, setTutorial] = useState<VideoTutorial | null>(null);
  const [playing, setPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [muted, setMuted] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [currentChapter, setCurrentChapter] = useState<VideoTutorialChapter | null>(null);
  const [showChapters, setShowChapters] = useState(true);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [showControls, setShowControls] = useState(true);
  const [controlsTimeout, setControlsTimeout] = useState<NodeJS.Timeout | null>(null);
  
  // 加载视频教程
  useEffect(() => {
    const tutorial = VideoTutorialService.getTutorialById(tutorialId);
    if (tutorial) {
      setTutorial(tutorial);
      
      // 恢复上次观看进度
      const savedProgress = VideoTutorialService.getTutorialProgress(tutorialId);
      if (savedProgress > 0) {
        setCurrentTime(savedProgress);
      }
    }
  }, [tutorialId]);
  
  // 更新当前章节
  useEffect(() => {
    if (!tutorial || !tutorial.chapters) return;
    
    const chapter = tutorial.chapters.find(
      chapter => currentTime >= chapter.startTime && currentTime < chapter.endTime
    );
    
    if (chapter) {
      setCurrentChapter(chapter);
    } else {
      setCurrentChapter(null);
    }
  }, [tutorial, currentTime]);
  
  // 自动隐藏控制栏
  useEffect(() => {
    if (playing) {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }
      
      const timeout = setTimeout(() => {
        setShowControls(false);
      }, 3000);
      
      setControlsTimeout(timeout);
    } else {
      setShowControls(true);
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
        setControlsTimeout(null);
      }
    }
    
    return () => {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }
    };
  }, [playing, controlsTimeout]);
  
  // 处理视频事件
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;
    
    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
      VideoTutorialService.updateProgress(tutorialId, video.currentTime);
    };
    
    const handleDurationChange = () => {
      setDuration(video.duration);
    };
    
    const handleEnded = () => {
      setPlaying(false);
      VideoTutorialService.markAsWatched(tutorialId);
    };
    
    const handlePlay = () => {
      setPlaying(true);
    };
    
    const handlePause = () => {
      setPlaying(false);
    };
    
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('durationchange', handleDurationChange);
    video.addEventListener('ended', handleEnded);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    
    // 设置初始时间
    if (currentTime > 0) {
      video.currentTime = currentTime;
    }
    
    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('durationchange', handleDurationChange);
      video.removeEventListener('ended', handleEnded);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
    };
  }, [tutorialId, currentTime]);
  
  // 播放/暂停
  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;
    
    if (playing) {
      video.pause();
      VideoTutorialService.pauseTutorial();
    } else {
      video.play();
      VideoTutorialService.resumeTutorial();
    }
  };
  
  // 调整音量
  const handleVolumeChange = (value: number) => {
    const video = videoRef.current;
    if (!video) return;
    
    setVolume(value);
    video.volume = value;
    setMuted(value === 0);
  };
  
  // 静音/取消静音
  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;
    
    const newMuted = !muted;
    setMuted(newMuted);
    video.muted = newMuted;
  };
  
  // 全屏
  const toggleFullscreen = () => {
    const container = document.querySelector('.video-tutorial-player');
    if (!container) return;
    
    if (!fullscreen) {
      if (container.requestFullscreen) {
        container.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    
    setFullscreen(!fullscreen);
  };
  
  // 跳转到指定时间
  const handleSeek = (value: number) => {
    const video = videoRef.current;
    if (!video) return;
    
    video.currentTime = value;
    setCurrentTime(value);
  };
  
  // 跳转到指定章节
  const handleChapterClick = (chapter: VideoTutorialChapter) => {
    const video = videoRef.current;
    if (!video) return;
    
    video.currentTime = chapter.startTime;
    setCurrentTime(chapter.startTime);
    
    if (!playing) {
      video.play();
      setPlaying(true);
    }
  };
  
  // 调整播放速度
  const handlePlaybackRateChange = (rate: number) => {
    const video = videoRef.current;
    if (!video) return;
    
    setPlaybackRate(rate);
    video.playbackRate = rate;
  };
  
  // 格式化时间
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };
  
  // 显示控制栏
  const handleMouseMove = () => {
    setShowControls(true);
    
    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }
    
    if (playing) {
      const timeout = setTimeout(() => {
        setShowControls(false);
      }, 3000);
      
      setControlsTimeout(timeout);
    }
  };
  
  if (!tutorial) {
    return (
      <div className="video-tutorial-player-loading">
        <Text>{t('videoTutorials.loading')}</Text>
      </div>
    );
  }
  
  return (
    <div 
      className={`video-tutorial-player ${fullscreen ? 'fullscreen' : ''}`}
      onMouseMove={handleMouseMove}
    >
      <div className="video-container">
        <video
          ref={videoRef}
          src={tutorial.videoUrl}
          poster={tutorial.thumbnailUrl}
          className="video-element"
          onClick={togglePlay}
        >
          {tutorial.subtitlesUrl && (
            <track 
              kind="subtitles" 
              src={tutorial.subtitlesUrl} 
              srcLang="zh-CN" 
              label="中文" 
              default 
            />
          )}
          {t('videoTutorials.browserNotSupported')}
        </video>
        
        {!playing && (
          <div className="video-overlay" onClick={togglePlay}>
            <PlayCircleOutlined className="play-button" />
          </div>
        )}
        
        {currentChapter && (
          <div className="chapter-indicator">
            <Text>{currentChapter.title}</Text>
          </div>
        )}
        
        <div className={`video-controls ${showControls ? 'visible' : ''}`}>
          <div className="progress-bar">
            <Slider
              value={currentTime}
              min={0}
              max={duration}
              step={0.1}
              onChange={handleSeek}
              tooltip={{ formatter: value => formatTime(value || 0) }}
            />
          </div>
          
          <div className="controls-row">
            <div className="left-controls">
              <Button 
                type="text" 
                icon={playing ? <PauseCircleOutlined /> : <PlayCircleOutlined />} 
                onClick={togglePlay}
              />
              
              <div className="volume-control">
                <Button 
                  type="text" 
                  icon={<SoundOutlined />} 
                  onClick={toggleMute}
                />
                <Slider
                  value={muted ? 0 : volume}
                  min={0}
                  max={1}
                  step={0.1}
                  onChange={handleVolumeChange}
                  style={{ width: 50 }}
                />
              </div>
              
              <div className="time-display">
                <Text>{formatTime(currentTime)} / {formatTime(duration)}</Text>
              </div>
            </div>
            
            <div className="right-controls">
              <Tooltip title={t('videoTutorials.playbackSpeed')}>
                <Button 
                  type="text" 
                  onClick={() => {
                    const rates = [0.5, 0.75, 1, 1.25, 1.5, 2];
                    const currentIndex = rates.indexOf(playbackRate);
                    const nextIndex = (currentIndex + 1) % rates.length;
                    handlePlaybackRateChange(rates[nextIndex]);
                  }}
                >
                  {playbackRate}x
                </Button>
              </Tooltip>
              
              <Tooltip title={t('videoTutorials.toggleChapters')}>
                <Button 
                  type="text" 
                  icon={<ExpandOutlined />} 
                  onClick={() => setShowChapters(!showChapters)}
                />
              </Tooltip>
              
              <Tooltip title={t('videoTutorials.fullscreen')}>
                <Button 
                  type="text" 
                  icon={<FullscreenOutlined />} 
                  onClick={toggleFullscreen}
                />
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
      
      <div className={`tutorial-info ${showChapters ? '' : 'collapsed'}`}>
        <div className="tutorial-header">
          <Title level={4}>{tutorial.title}</Title>
          <div className="tutorial-meta">
            <Tag color="blue">{t(`videoTutorials.categories.${tutorial.category}`)}</Tag>
            <Tag color={tutorial.difficulty === 'beginner' ? 'green' : (tutorial.difficulty === 'intermediate' ? 'orange' : 'red')}>
              {t(`videoTutorials.difficulty.${tutorial.difficulty}`)}
            </Tag>
            <Tag icon={<ClockCircleOutlined />}>{tutorial.duration} {t('videoTutorials.minutes')}</Tag>
            {VideoTutorialService.isTutorialWatched(tutorial.id) && (
              <Tag icon={<CheckCircleOutlined />} color="success">
                {t('videoTutorials.watched')}
              </Tag>
            )}
          </div>
        </div>
        
        <Divider style={{ margin: '12px 0' }} />
        
        <Paragraph>{tutorial.description}</Paragraph>
        
        {tutorial.chapters && tutorial.chapters.length > 0 && (
          <div className="chapters-list">
            <Title level={5}>{t('videoTutorials.chapters')}</Title>
            <List
              size="small"
              dataSource={tutorial.chapters}
              renderItem={chapter => (
                <List.Item
                  className={currentChapter?.id === chapter.id ? 'active-chapter' : ''}
                  onClick={() => handleChapterClick(chapter)}
                >
                  <div className="chapter-item">
                    <Text>{chapter.title}</Text>
                    <Text type="secondary">{formatTime(chapter.startTime)}</Text>
                  </div>
                </List.Item>
              )}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoTutorialPlayer;
