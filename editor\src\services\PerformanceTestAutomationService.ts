/**
 * 性能测试自动化服务
 * 用于自动化执行性能测试并生成报告
 */
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 性能指标类型
 */
export enum PerformanceMetricType {
  FPS = 'fps',
  RENDER_TIME = 'renderTime',
  PHYSICS_TIME = 'physicsTime',
  MEMORY_USAGE = 'memoryUsage',
  DRAW_CALLS = 'drawCalls',
  TRIANGLES = 'triangles',
  CUSTOM = 'custom'
}

/**
 * 性能指标
 */
export interface PerformanceMetric {
  /** 指标类型 */
  type: PerformanceMetricType;
  /** 指标名称 */
  name: string;
  /** 当前值 */
  value: number;
  /** 单位 */
  unit: string;
  /** 最小值 */
  min: number;
  /** 最大值 */
  max: number;
  /** 平均值 */
  average: number;
  /** 历史数据 */
  history: number[];
  /** 阈值 */
  threshold: number;
  /** 是否超出阈值 */
  isOverThreshold: boolean;
}

/**
 * 性能瓶颈
 */
export interface PerformanceBottleneck {
  /** 瓶颈类型 */
  type: PerformanceMetricType;
  /** 严重程度 (0-1) */
  severity: number;
  /** 描述 */
  description: string;
  /** 相关指标 */
  relatedMetrics: string[];
  /** 优化建议 */
  optimizationSuggestions: string[];
}

/**
 * 性能趋势
 */
export interface PerformanceTrend {
  /** 指标类型 */
  metricType: PerformanceMetricType;
  /** 趋势类型 */
  type: 'improving' | 'stable' | 'degrading';
  /** 变化率 */
  changeRate: number;
  /** 置信度 */
  confidence: number;
}

/**
 * 性能报告
 */
export interface PerformanceReport {
  /** 报告时间 */
  timestamp: number;
  /** 性能指标 */
  metrics: { [key: string]: PerformanceMetric };
  /** 性能瓶颈 */
  bottlenecks: PerformanceBottleneck[];
  /** 性能趋势 */
  trends: PerformanceTrend[];
  /** 总体性能评分 (0-100) */
  overallScore: number;
  /** 性能状态 */
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical' | 'unknown';
  /** 自定义数据 */
  customData?: { [key: string]: any };
}

/**
 * 简化的性能监控器
 */
class SimplePerformanceMonitor {
  private static instance: SimplePerformanceMonitor;
  private running: boolean = false;
  private metrics: { [key: string]: PerformanceMetric } = {};
  private startTime: number = 0;

  public static getInstance(): SimplePerformanceMonitor {
    if (!SimplePerformanceMonitor.instance) {
      SimplePerformanceMonitor.instance = new SimplePerformanceMonitor();
    }
    return SimplePerformanceMonitor.instance;
  }

  public configure(_config: any): void {
    // 简化的配置方法
    // 参数使用下划线前缀表示故意未使用
  }

  public start(): void {
    this.running = true;
    this.startTime = performance.now();
    this.initializeMetrics();
  }

  public stop(): void {
    this.running = false;
  }

  public clearAllHistory(): void {
    Object.values(this.metrics).forEach(metric => {
      metric.history = [];
    });
  }

  public getReport(): PerformanceReport {
    const now = performance.now();
    const duration = now - this.startTime;

    // 模拟FPS计算，只有在运行时才计算真实值
    const fps = this.running && duration > 0 ? Math.min(60, 1000 / (duration / 60)) : 60;

    return {
      timestamp: now,
      metrics: {
        fps: {
          type: PerformanceMetricType.FPS,
          name: 'FPS',
          value: fps,
          unit: 'fps',
          min: fps,
          max: fps,
          average: fps,
          history: [fps],
          threshold: 30,
          isOverThreshold: fps < 30
        },
        renderTime: {
          type: PerformanceMetricType.RENDER_TIME,
          name: '渲染时间',
          value: duration,
          unit: 'ms',
          min: duration,
          max: duration,
          average: duration,
          history: [duration],
          threshold: 16,
          isOverThreshold: duration > 16
        }
      },
      bottlenecks: [],
      trends: [],
      overallScore: this.running && fps >= 30 ? 80 : 60,
      status: this.running && fps >= 30 ? 'good' : 'fair',
      customData: {}
    };
  }

  private initializeMetrics(): void {
    this.metrics = {
      fps: {
        type: PerformanceMetricType.FPS,
        name: 'FPS',
        value: 0,
        unit: 'fps',
        min: 0,
        max: 0,
        average: 0,
        history: [],
        threshold: 30,
        isOverThreshold: false
      }
    };
  }
}

/**
 * 测试场景配置
 */
export interface TestSceneConfig {
  /** 场景ID */
  id: string;
  /** 场景名称 */
  name: string;
  /** 场景路径 */
  path: string;
  /** 测试持续时间（毫秒） */
  duration: number;
  /** 预热时间（毫秒） */
  warmupDuration: number;
  /** 相机位置 */
  cameraPosition?: { x: number; y: number; z: number };
  /** 相机目标 */
  cameraTarget?: { x: number; y: number; z: number };
  /** 测试动作 */
  actions?: TestAction[];
}

/**
 * 测试动作
 */
export interface TestAction {
  /** 动作类型 */
  type: 'wait' | 'move' | 'rotate' | 'scale' | 'setProperty' | 'executeFunction';
  /** 目标实体ID */
  targetId?: string;
  /** 延迟（毫秒） */
  delay?: number;
  /** 持续时间（毫秒） */
  duration?: number;
  /** 动作参数 */
  params?: any;
}

/**
 * 测试配置
 */
export interface TestConfig {
  /** 测试名称 */
  name: string;
  /** 测试描述 */
  description?: string;
  /** 测试场景 */
  scenes: TestSceneConfig[];
  /** 重复次数 */
  repetitions: number;
  /** 是否自动保存报告 */
  autoSaveReport: boolean;
  /** 报告保存路径 */
  reportPath?: string;
}

/**
 * 测试结果
 */
export interface TestResult {
  /** 测试配置 */
  config: TestConfig;
  /** 场景结果 */
  sceneResults: {
    /** 场景配置 */
    sceneConfig: TestSceneConfig;
    /** 性能报告 */
    reports: PerformanceReport[];
    /** 平均报告 */
    averageReport: PerformanceReport;
  }[];
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
  /** 总持续时间 */
  totalDuration: number;
}

/**
 * 性能测试自动化服务事件类型
 */
export enum PerformanceTestEventType {
  /** 测试开始 */
  TEST_STARTED = 'testStarted',
  /** 测试结束 */
  TEST_COMPLETED = 'testCompleted',
  /** 测试失败 */
  TEST_FAILED = 'testFailed',
  /** 场景测试开始 */
  SCENE_TEST_STARTED = 'sceneTestStarted',
  /** 场景测试结束 */
  SCENE_TEST_COMPLETED = 'sceneTestCompleted',
  /** 场景测试失败 */
  SCENE_TEST_FAILED = 'sceneTestFailed',
  /** 测试进度更新 */
  TEST_PROGRESS = 'testProgress'}

/**
 * 性能测试自动化服务
 */
export class PerformanceTestAutomationService {
  /** 单例实例 */
  private static instance: PerformanceTestAutomationService;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 是否正在测试 */
  private isTesting: boolean = false;

  /** 当前测试配置 */
  private currentTestConfig: TestConfig | null = null;

  /** 当前测试结果 */
  private currentTestResult: TestResult | null = null;

  /** 当前场景索引 */
  private currentSceneIndex: number = 0;

  /** 当前重复次数 */
  private currentRepetition: number = 0;

  /** 测试定时器ID */
  private testTimerId: number | null = null;

  /** 预定义测试配置 */
  private predefinedTests: Map<string, TestConfig> = new Map();

  /**
   * 私有构造函数
   */
  private constructor() {
    this.initializePredefinedTests();
  }

  /**
   * 获取单例实例
   * @returns 性能测试自动化服务实例
   */
  public static getInstance(): PerformanceTestAutomationService {
    if (!PerformanceTestAutomationService.instance) {
      PerformanceTestAutomationService.instance = new PerformanceTestAutomationService();
    }
    return PerformanceTestAutomationService.instance;
  }

  /**
   * 初始化预定义测试
   * @private
   */
  private initializePredefinedTests(): void {
    // 基本性能测试
    this.predefinedTests.set('basic', {
      name: '基本性能测试',
      description: '测试基本场景的性能',
      scenes: [
        {
          id: 'empty',
          name: '空场景',
          path: 'scenes/empty.json',
          duration: 5000,
          warmupDuration: 1000},
        {
          id: 'simple',
          name: '简单场景',
          path: 'scenes/simple.json',
          duration: 5000,
          warmupDuration: 1000},
      ],
      repetitions: 3,
      autoSaveReport: true});

    // 渲染性能测试
    this.predefinedTests.set('rendering', {
      name: '渲染性能测试',
      description: '测试不同复杂度场景的渲染性能',
      scenes: [
        {
          id: 'low',
          name: '低复杂度场景',
          path: 'scenes/rendering/low.json',
          duration: 10000,
          warmupDuration: 2000},
        {
          id: 'medium',
          name: '中等复杂度场景',
          path: 'scenes/rendering/medium.json',
          duration: 10000,
          warmupDuration: 2000},
        {
          id: 'high',
          name: '高复杂度场景',
          path: 'scenes/rendering/high.json',
          duration: 10000,
          warmupDuration: 2000},
      ],
      repetitions: 3,
      autoSaveReport: true});

    // 物理性能测试
    this.predefinedTests.set('physics', {
      name: '物理性能测试',
      description: '测试不同复杂度场景的物理性能',
      scenes: [
        {
          id: 'low',
          name: '低复杂度物理场景',
          path: 'scenes/physics/low.json',
          duration: 10000,
          warmupDuration: 2000},
        {
          id: 'medium',
          name: '中等复杂度物理场景',
          path: 'scenes/physics/medium.json',
          duration: 10000,
          warmupDuration: 2000},
        {
          id: 'high',
          name: '高复杂度物理场景',
          path: 'scenes/physics/high.json',
          duration: 10000,
          warmupDuration: 2000},
      ],
      repetitions: 3,
      autoSaveReport: true});
  }

  /**
   * 获取所有预定义测试
   * @returns 预定义测试配置
   */
  public getPredefinedTests(): Map<string, TestConfig> {
    return this.predefinedTests;
  }

  /**
   * 获取预定义测试
   * @param id 测试ID
   * @returns 测试配置
   */
  public getPredefinedTest(id: string): TestConfig | undefined {
    return this.predefinedTests.get(id);
  }

  /**
   * 添加预定义测试
   * @param id 测试ID
   * @param config 测试配置
   */
  public addPredefinedTest(id: string, config: TestConfig): void {
    this.predefinedTests.set(id, config);
  }

  /**
   * 删除预定义测试
   * @param id 测试ID
   * @returns 是否成功删除
   */
  public deletePredefinedTest(id: string): boolean {
    return this.predefinedTests.delete(id);
  }

  /**
   * 运行测试
   * @param config 测试配置
   * @returns 是否成功启动测试
   */
  public runTest(config: TestConfig): boolean {
    if (this.isTesting) {
      return false;
    }

    this.isTesting = true;
    this.currentTestConfig = config;
    this.currentSceneIndex = 0;
    this.currentRepetition = 0;

    this.currentTestResult = {
      config,
      sceneResults: config.scenes.map(sceneConfig => ({
        sceneConfig,
        reports: [],
        averageReport: this.createEmptyReport()})),
      startTime: Date.now(),
      endTime: 0,
      totalDuration: 0};

    this.eventEmitter.emit(PerformanceTestEventType.TEST_STARTED, {
      config,
      startTime: this.currentTestResult.startTime});

    // 开始测试第一个场景
    this.testNextScene();

    return true;
  }

  /**
   * 停止测试
   */
  public stopTest(): void {
    if (!this.isTesting) {
      return;
    }

    if (this.testTimerId !== null) {
      window.clearTimeout(this.testTimerId);
      this.testTimerId = null;
    }

    this.isTesting = false;
    this.currentTestConfig = null;
    this.currentTestResult = null;
    this.currentSceneIndex = 0;
    this.currentRepetition = 0;

    // 停止性能监控
    SimplePerformanceMonitor.getInstance().stop();

    this.eventEmitter.emit(PerformanceTestEventType.TEST_FAILED, {
      reason: '测试被手动停止'});
  }

  /**
   * 是否正在测试
   * @returns 是否正在测试
   */
  public isRunningTest(): boolean {
    return this.isTesting;
  }

  /**
   * 获取当前测试结果
   * @returns 当前测试结果
   */
  public getCurrentTestResult(): TestResult | null {
    return this.currentTestResult;
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public on(event: PerformanceTestEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public off(event: PerformanceTestEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 测试下一个场景
   * @private
   */
  private testNextScene(): void {
    if (!this.isTesting || !this.currentTestConfig || !this.currentTestResult) {
      return;
    }

    // 检查是否所有场景都已测试完成
    if (this.currentSceneIndex >= this.currentTestConfig.scenes.length) {
      // 检查是否需要重复测试
      if (this.currentRepetition < this.currentTestConfig.repetitions - 1) {
        this.currentRepetition++;
        this.currentSceneIndex = 0;
        this.testNextScene();
      } else {
        // 所有测试完成
        this.completeTest();
      }
      return;
    }

    const sceneConfig = this.currentTestConfig.scenes[this.currentSceneIndex];

    // 发出场景测试开始事件
    this.eventEmitter.emit(PerformanceTestEventType.SCENE_TEST_STARTED, {
      sceneConfig,
      repetition: this.currentRepetition + 1,
      totalRepetitions: this.currentTestConfig.repetitions});

    // 加载场景
    this.loadAndTestScene(sceneConfig)
      .then(report => {
        // 保存报告
        if (this.currentTestResult) {
          this.currentTestResult.sceneResults[this.currentSceneIndex].reports.push(report);
        }

        // 发出场景测试完成事件
        this.eventEmitter.emit(PerformanceTestEventType.SCENE_TEST_COMPLETED, {
          sceneConfig,
          report,
          repetition: this.currentRepetition + 1,
          totalRepetitions: this.currentTestConfig?.repetitions || 1});

        // 更新进度
        const progress = this.calculateProgress();
        this.eventEmitter.emit(PerformanceTestEventType.TEST_PROGRESS, {
          progress,
          currentScene: this.currentSceneIndex + 1,
          totalScenes: this.currentTestConfig?.scenes.length,
          currentRepetition: this.currentRepetition + 1,
          totalRepetitions: this.currentTestConfig?.repetitions});

        // 测试下一个场景
        this.currentSceneIndex++;
        this.testNextScene();
      })
      .catch(error => {
        console.error('场景测试失败:', error);

        // 发出场景测试失败事件
        this.eventEmitter.emit(PerformanceTestEventType.SCENE_TEST_FAILED, {
          sceneConfig,
          error,
          repetition: this.currentRepetition + 1,
          totalRepetitions: this.currentTestConfig?.repetitions});

        // 继续测试下一个场景
        this.currentSceneIndex++;
        this.testNextScene();
      });
  }

  /**
   * 加载并测试场景
   * @param sceneConfig 场景配置
   * @returns 性能报告Promise
   * @private
   */
  private async loadAndTestScene(sceneConfig: TestSceneConfig): Promise<PerformanceReport> {
    return new Promise<PerformanceReport>((resolve, reject) => {
      try {
        // 模拟场景加载
        const mockScene = {
          getActiveCamera: () => ({
            position: { set: () => {}, x: 0, y: 0, z: 0 },
            lookAt: () => {}
          }),
          getEntityById: () => ({
            getComponent: () => ({
              position: { set: () => {}, x: 0, y: 0, z: 0 },
              rotation: { set: () => {}, x: 0, y: 0, z: 0 },
              scale: { set: () => {}, x: 1, y: 1, z: 1 }
            })
          })
        };

        // 模拟场景加载
        Promise.resolve(mockScene)
          .then((scene: any) => {
            if (!scene) {
              throw new Error(`无法加载场景: ${sceneConfig.path}`);
            }

            // 设置相机位置
            if (sceneConfig.cameraPosition && sceneConfig.cameraTarget) {
              const camera = scene.getActiveCamera();
              if (camera) {
                camera.position.set(
                  sceneConfig.cameraPosition.x,
                  sceneConfig.cameraPosition.y,
                  sceneConfig.cameraPosition.z
                );
                camera.lookAt(
                  sceneConfig.cameraTarget.x,
                  sceneConfig.cameraTarget.y,
                  sceneConfig.cameraTarget.z
                );
              }
            }

            // 执行预热
            setTimeout(() => {
              // 配置性能监控器
              const performanceMonitor = SimplePerformanceMonitor.getInstance();
              performanceMonitor.configure({
                enabled: true,
                sampleInterval: 100,
                historyLimit: 100,
                autoSample: true,
                enableBottleneckDetection: true,
                enableTrendAnalysis: true,
                enablePerformanceScoring: true,
                enableOptimizationSuggestions: true});

              // 清除历史数据
              performanceMonitor.clearAllHistory();

              // 启动性能监控
              performanceMonitor.start();

              // 执行测试动作
              if (sceneConfig.actions) {
                this.executeTestActions(sceneConfig.actions, scene);
              }

              // 等待测试持续时间
              setTimeout(() => {
                // 停止性能监控
                performanceMonitor.stop();

                // 获取性能报告
                const report = performanceMonitor.getReport();

                // 返回报告
                resolve(report);
              }, sceneConfig.duration);
            }, sceneConfig.warmupDuration);
          })
          .catch((error: any) => {
            reject(error);
          });
      } catch (error: any) {
        reject(error);
      }
    });
  }

  /**
   * 执行测试动作
   * @param actions 测试动作
   * @param scene 场景
   * @private
   */
  private executeTestActions(actions: TestAction[], scene: any): void {
    actions.forEach(action => {
      setTimeout(() => {
        try {
          switch (action.type) {
            case 'wait':
              // 不执行任何操作，只是等待
              break;
            case 'move':
              if (action.targetId && action.params) {
                const entity = scene.getEntityById(action.targetId);
                if (entity) {
                  const transform = entity.getComponent('TransformComponent');
                  if (transform) {
                    transform.position.set(
                      action.params.x || transform.position.x,
                      action.params.y || transform.position.y,
                      action.params.z || transform.position.z
                    );
                  }
                }
              }
              break;
            case 'rotate':
              if (action.targetId && action.params) {
                const entity = scene.getEntityById(action.targetId);
                if (entity) {
                  const transform = entity.getComponent('TransformComponent');
                  if (transform) {
                    transform.rotation.set(
                      action.params.x || transform.rotation.x,
                      action.params.y || transform.rotation.y,
                      action.params.z || transform.rotation.z
                    );
                  }
                }
              }
              break;
            case 'scale':
              if (action.targetId && action.params) {
                const entity = scene.getEntityById(action.targetId);
                if (entity) {
                  const transform = entity.getComponent('TransformComponent');
                  if (transform) {
                    transform.scale.set(
                      action.params.x || transform.scale.x,
                      action.params.y || transform.scale.y,
                      action.params.z || transform.scale.z
                    );
                  }
                }
              }
              break;
            case 'setProperty':
              if (action.targetId && action.params) {
                const entity = scene.getEntityById(action.targetId);
                if (entity && action.params.component && action.params.property) {
                  const component = entity.getComponent(action.params.component);
                  if (component) {
                    component[action.params.property] = action.params.value;
                  }
                }
              }
              break;
            case 'executeFunction':
              if (action.targetId && action.params) {
                const entity = scene.getEntityById(action.targetId);
                if (entity && action.params.component && action.params.function) {
                  const component = entity.getComponent(action.params.component);
                  if (component && typeof component[action.params.function] === 'function') {
                    component[action.params.function](...(action.params.args || []));
                  }
                }
              }
              break;
          }
        } catch (error) {
          console.error('执行测试动作失败:', error);
        }
      }, action.delay || 0);
    });
  }

  /**
   * 完成测试
   * @private
   */
  private completeTest(): void {
    if (!this.isTesting || !this.currentTestResult) {
      return;
    }

    // 计算测试结束时间和总持续时间
    this.currentTestResult.endTime = Date.now();
    this.currentTestResult.totalDuration = this.currentTestResult.endTime - this.currentTestResult.startTime;

    // 计算每个场景的平均报告
    this.currentTestResult.sceneResults.forEach(sceneResult => {
      if (sceneResult.reports.length > 0) {
        sceneResult.averageReport = this.calculateAverageReport(sceneResult.reports);
      }
    });

    // 保存测试结果
    if (this.currentTestConfig?.autoSaveReport) {
      this.saveTestResult(this.currentTestResult);
    }

    // 发出测试完成事件
    this.eventEmitter.emit(PerformanceTestEventType.TEST_COMPLETED, this.currentTestResult);

    // 重置状态
    this.isTesting = false;
    this.currentTestConfig = null;
    this.currentSceneIndex = 0;
    this.currentRepetition = 0;
  }

  /**
   * 计算进度
   * @returns 进度（0-1）
   * @private
   */
  private calculateProgress(): number {
    if (!this.currentTestConfig) {
      return 0;
    }

    const totalScenes = this.currentTestConfig.scenes.length;
    const totalRepetitions = this.currentTestConfig.repetitions;
    const totalSteps = totalScenes * totalRepetitions;
    const completedSteps = this.currentRepetition * totalScenes + this.currentSceneIndex;

    return completedSteps / totalSteps;
  }

  /**
   * 计算平均报告
   * @param reports 报告数组
   * @returns 平均报告
   * @private
   */
  private calculateAverageReport(reports: PerformanceReport[]): PerformanceReport {
    if (reports.length === 0) {
      return this.createEmptyReport();
    }

    if (reports.length === 1) {
      return { ...reports[0] };
    }

    const averageReport: PerformanceReport = this.createEmptyReport();
    averageReport.timestamp = Date.now();

    // 计算指标平均值
    const metricTypes = Object.keys(reports[0].metrics);
    metricTypes.forEach(type => {
      const values = reports.map(report => report.metrics[type as any]?.value || 0);
      const sum = values.reduce((a, b) => a + b, 0);
      const average = sum / values.length;

      if (averageReport.metrics[type as any]) {
        averageReport.metrics[type as any].value = average;
        averageReport.metrics[type as any].average = average;
      }
    });

    // 合并瓶颈
    const bottleneckTypes = new Set<string>();
    reports.forEach(report => {
      report.bottlenecks.forEach(bottleneck => {
        bottleneckTypes.add(bottleneck.type);
      });
    });

    bottleneckTypes.forEach(type => {
      const bottlenecks = reports
        .map(report => report.bottlenecks.find(b => b.type === type))
        .filter(b => b !== undefined) as any[];

      if (bottlenecks.length > 0) {
        const severitySum = bottlenecks.reduce((sum, b) => sum + b.severity, 0);
        const averageSeverity = severitySum / bottlenecks.length;

        averageReport.bottlenecks.push({
          type: type as any,
          severity: averageSeverity,
          description: bottlenecks[0].description,
          relatedMetrics: bottlenecks[0].relatedMetrics,
          optimizationSuggestions: bottlenecks[0].optimizationSuggestions});
      }
    });

    // 计算平均评分
    const scores = reports.map(report => report.overallScore);
    const scoreSum = scores.reduce((a, b) => a + b, 0);
    averageReport.overallScore = scoreSum / scores.length;

    // 设置状态
    averageReport.status = reports[0].status;

    return averageReport;
  }

  /**
   * 创建空报告
   * @returns 空报告
   * @private
   */
  private createEmptyReport(): PerformanceReport {
    return {
      timestamp: Date.now(),
      metrics: {},
      bottlenecks: [],
      trends: [],
      overallScore: 0,
      status: 'unknown',
      customData: {}};
  }

  /**
   * 保存测试结果
   * @param result 测试结果
   * @private
   */
  private saveTestResult(result: TestResult): void {
    try {
      const resultJson = JSON.stringify(result, null, 2);
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const fileName = `performance-test-${timestamp}.json`;

      // 在浏览器环境中，创建下载链接
      const blob = new Blob([resultJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('保存测试结果失败:', error);
    }
  }
}
