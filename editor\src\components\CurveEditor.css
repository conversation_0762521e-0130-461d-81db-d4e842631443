/**
 * 曲线编辑器样式
 */

.curve-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.curve-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.curve-editor-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.curve-editor-toolbar {
  display: flex;
  gap: 8px;
  align-items: center;
}

.curve-editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.curve-editor-canvas-container {
  flex: 1;
  position: relative;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.curve-editor-canvas {
  width: 100%;
  height: 100%;
  cursor: crosshair;
}

.curve-editor-canvas:hover {
  cursor: pointer;
}

.curve-editor-canvas.dragging {
  cursor: grabbing;
}

.curve-editor-sidebar {
  width: 280px;
  border-left: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  flex-direction: column;
}

.curve-editor-curve-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.curve-editor-curve-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.curve-editor-curve-item:hover {
  border-color: #40a9ff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.curve-editor-curve-item.selected {
  border-color: #1890ff;
  background: #e6f7ff;
}

.curve-editor-curve-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  margin-right: 8px;
  border: 1px solid #d9d9d9;
}

.curve-editor-curve-name {
  flex: 1;
  font-size: 14px;
  color: #262626;
}

.curve-editor-curve-actions {
  display: flex;
  gap: 4px;
}

.curve-editor-properties {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: #fff;
}

.curve-editor-property-group {
  margin-bottom: 16px;
}

.curve-editor-property-label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.curve-editor-add-curve {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.curve-editor-add-curve-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.curve-editor-keypoint-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
}

.curve-editor-grid {
  stroke: #e8e8e8;
  stroke-width: 0.5;
}

.curve-editor-axis {
  stroke: #262626;
  stroke-width: 1;
}

.curve-editor-curve-line {
  fill: none;
  stroke-width: 2;
}

.curve-editor-keypoint {
  cursor: pointer;
  transition: r 0.2s;
}

.curve-editor-keypoint:hover {
  r: 6;
}

.curve-editor-handle {
  cursor: pointer;
  fill: #fff;
  stroke: #999;
  stroke-width: 1;
}

.curve-editor-handle-line {
  stroke: #999;
  stroke-width: 1;
  stroke-dasharray: 2, 2;
}

.curve-editor-selection-box {
  fill: rgba(24, 144, 255, 0.1);
  stroke: #1890ff;
  stroke-width: 1;
  stroke-dasharray: 4, 4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .curve-editor {
    flex-direction: column;
  }
  
  .curve-editor-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid #f0f0f0;
    max-height: 300px;
  }
  
  .curve-editor-toolbar {
    flex-wrap: wrap;
  }
}

/* 暗色主题 */
.curve-editor.dark {
  background: #141414;
  border-color: #434343;
  color: #fff;
}

.curve-editor.dark .curve-editor-header {
  background: #1f1f1f;
  border-color: #434343;
}

.curve-editor.dark .curve-editor-sidebar {
  background: #1f1f1f;
  border-color: #434343;
}

.curve-editor.dark .curve-editor-curve-item {
  background: #262626;
  border-color: #434343;
  color: #fff;
}

.curve-editor.dark .curve-editor-curve-item:hover {
  border-color: #40a9ff;
}

.curve-editor.dark .curve-editor-curve-item.selected {
  background: #111b26;
  border-color: #1890ff;
}

.curve-editor.dark .curve-editor-properties {
  background: #262626;
  border-color: #434343;
}

.curve-editor.dark .curve-editor-add-curve {
  border-color: #434343;
}
