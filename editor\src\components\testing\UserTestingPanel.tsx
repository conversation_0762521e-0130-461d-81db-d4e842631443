/**
 * 用户测试面板组件
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Switch,
  Space,
  Typography,
  Tabs,
  List,
  Tag,
  Progress,
  Empty,
  Badge,
  message
} from 'antd';
import {
  ExperimentOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  FileTextOutlined,
  BulbOutlined,
  BugOutlined,
  StarOutlined,
  VideoCameraOutlined,
  StopOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import {
  selectTestingEnabled,
  selectCurrentSession,
  selectTasks,
  selectCurrentTaskId,
  selectSessionsHistory,
  selectReports,
  setTestingEnabled
} from '../../store/testing/userTestingSlice';
import { userTestingService, TestTask } from '../../services/UserTestingService';
import FeedbackForm from './FeedbackForm';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Step } = Steps;

/**
 * 用户测试面板属性
 */
interface UserTestingPanelProps {
  onClose?: () => void;
}

/**
 * 用户测试面板组件
 */
const UserTestingPanel: React.FC<UserTestingPanelProps> = ({ onClose }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取状态
  const testingEnabled = useSelector(selectTestingEnabled);
  const currentSession = useSelector(selectCurrentSession);
  const tasks = useSelector(selectTasks);
  const currentTaskId = useSelector(selectCurrentTaskId);
  const sessionsHistory = useSelector(selectSessionsHistory);
  const reports = useSelector(selectReports);

  // 本地状态
  const [activeTab, setActiveTab] = useState('tasks');
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [recordingEnabled, setRecordingEnabled] = useState(false);
  const [selectedHistorySession, setSelectedHistorySession] = useState<string | null>(null);
  const [showReportModal, setShowReportModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState<string | null>(null);

  // 计算任务完成进度
  const completedTasks = tasks.filter(task => task.completed).length;
  const progress = tasks.length > 0 ? Math.round((completedTasks / tasks.length) * 100) : 0;

  // 处理测试开关
  const handleToggleTesting = (checked: boolean) => {
    if (checked && !currentSession) {
      // 启动测试会话
      const defaultTasks: Omit<TestTask, 'completed' | 'startTime' | 'endTime' | 'timeSpent'>[] = [
        {
          id: 'task1',
          title: '创建一个新场景',
          description: '使用编辑器创建一个新的空场景'
        },
        {
          id: 'task2',
          title: '添加一个3D对象',
          description: '向场景中添加一个立方体或球体'
        },
        {
          id: 'task3',
          title: '邀请协作者',
          description: '邀请另一个用户加入协作编辑'
        },
        {
          id: 'task4',
          title: '解决编辑冲突',
          description: '尝试同时编辑同一个对象，并解决产生的冲突'
        }
      ];

      try {
        userTestingService.startSession(defaultTasks, {
          projectId: 'test_project',
          sceneId: 'test_scene'
        });

        // 设置第一个任务为当前任务
        if (defaultTasks.length > 0) {
          userTestingService.setCurrentTask(defaultTasks[0].id);
        }
      } catch (error) {
        message.error('启动测试会话失败');
        return;
      }
    } else if (!checked && currentSession) {
      // 结束测试会话
      userTestingService.endSession();
    }

    // 更新状态
    userTestingService.setEnabled(checked);
    dispatch(setTestingEnabled(checked));
  };

  // 处理录制开关
  const handleToggleRecording = (checked: boolean) => {
    setRecordingEnabled(checked);
    userTestingService.setRecordingEnabled(checked);

    if (checked) {
      message.info('开始录制用户操作');
    } else {
      message.info('停止录制用户操作');
    }
  };

  // 处理任务点击
  const handleTaskClick = (taskId: string) => {
    if (!currentSession) return;

    try {
      userTestingService.setCurrentTask(taskId);
    } catch (error) {
      message.error('设置当前任务失败');
    }
  };

  // 处理任务完成
  const handleCompleteTask = (taskId: string) => {
    if (!currentSession) return;

    try {
      userTestingService.completeTask(taskId);
      message.success('任务已完成');

      // 如果所有任务都完成了，提示用户
      const allCompleted = tasks.every(task => task.id === taskId ? true : task.completed);
      if (allCompleted) {
        message.success('所有任务已完成，请提交您的反馈');
        setActiveTab('feedback');
      } else {
        // 自动选择下一个未完成的任务
        const nextTask = tasks.find(task => !task.completed && task.id !== taskId);
        if (nextTask) {
          userTestingService.setCurrentTask(nextTask.id);
        }
      }
    } catch (error) {
      message.error('完成任务失败');
    }
  };

  // 处理提交反馈
  const handleSubmitFeedback = () => {
    setShowFeedbackForm(true);
  };

  // 处理关闭反馈表单
  const handleCloseFeedbackForm = () => {
    setShowFeedbackForm(false);
  };

  // 渲染任务列表
  const renderTasks = () => {
    if (!tasks || tasks.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="没有测试任务"
        />
      );
    }

    return (
      <List
        itemLayout="horizontal"
        dataSource={tasks}
        renderItem={task => (
          <List.Item
            actions={[
              task.completed ? (
                <Tag color="success" icon={<CheckCircleOutlined />}>已完成</Tag>
              ) : task.id === currentTaskId ? (
                <Button
                  type="primary"
                  size="small"
                  icon={<CheckCircleOutlined />}
                  onClick={() => handleCompleteTask(task.id)}
                >
                  完成
                </Button>
              ) : (
                <Button
                  type="default"
                  size="small"
                  onClick={() => handleTaskClick(task.id)}
                >
                  开始
                </Button>
              )
            ]}
          >
            <List.Item.Meta
              avatar={
                <Badge
                  status={task.completed ? 'success' : task.id === currentTaskId ? 'processing' : 'default'}
                  style={{ marginTop: 8 }}
                />
              }
              title={
                <Space>
                  <Text strong>{task.title}</Text>
                  {task.id === currentTaskId && (
                    <Tag color="blue">当前</Tag>
                  )}
                </Space>
              }
              description={task.description}
            />
          </List.Item>
        )}
      />
    );
  };

  // 处理生成报告
  const handleGenerateReport = () => {
    if (!currentSession) return;

    try {
      const reportId = userTestingService.generateSessionReport(userTestingService.getCurrentSession()!);
      if (reportId) {
        setSelectedReport(reportId);
        setShowReportModal(true);
        message.success('测试报告已生成');
      }
    } catch (error) {
      message.error('生成测试报告失败');
    }
  };

  // 处理查看历史会话
  const handleViewHistorySession = (sessionId: string) => {
    setSelectedHistorySession(sessionId);
    // 这里可以添加加载历史会话的逻辑
  };

  // 处理截图
  const handleCaptureScreenshot = async () => {
    try {
      const screenshot = await userTestingService.captureScreenshot();
      if (screenshot) {
        message.success('截图已捕获');
        // 这里可以添加显示截图的逻辑
      }
    } catch (error) {
      message.error('捕获截图失败');
    }
  };

  // 渲染进度
  const renderProgress = () => {
    return (
      <Card size="small" title="测试进度" bordered={false}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Progress percent={progress} status="active" />
          <Text>
            已完成 {completedTasks}/{tasks.length} 个任务
          </Text>
          <Button
            type="primary"
            size="small"
            icon={<FileTextOutlined />}
            onClick={handleGenerateReport}
            disabled={!currentSession || tasks.length === 0}
          >
            生成报告
          </Button>
        </Space>
      </Card>
    );
  };

  // 渲染历史会话
  const renderHistory = () => {
    if (!sessionsHistory || sessionsHistory.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="没有历史测试会话"
        />
      );
    }

    return (
      <List
        itemLayout="horizontal"
        dataSource={sessionsHistory}
        renderItem={sessionId => (
          <List.Item
            actions={[
              <Button
                type="link"
                size="small"
                onClick={() => handleViewHistorySession(sessionId)}
              >
                查看
              </Button>
            ]}
          >
            <List.Item.Meta
              title={`会话 ${sessionId.split('_')[1]}`}
              description={`创建于 ${new Date(parseInt(sessionId.split('_')[1])).toLocaleString()}`}
            />
          </List.Item>
        )}
      />
    );
  };

  return (
    <Card
      title={
        <Space>
          <ExperimentOutlined />
          <span>用户测试</span>
        </Space>
      }
      extra={
        <Space>
          <Switch
            checked={testingEnabled}
            onChange={handleToggleTesting}
            checkedChildren="开启"
            unCheckedChildren="关闭"
          />
          {onClose && (
            <Button type="text" icon={<StopOutlined />} onClick={onClose} />
          )}
        </Space>
      }
      style={{ width: '100%', height: '100%' }}
    >
      {testingEnabled ? (
        <Space direction="vertical" style={{ width: '100%' }}>
          {renderProgress()}

          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane
              tab={
                <span>
                  <FileTextOutlined />
                  任务
                </span>
              }
              key="tasks"
            >
              {renderTasks()}
            </TabPane>

            <TabPane
              tab={
                <span>
                  <BulbOutlined />
                  反馈
                </span>
              }
              key="feedback"
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Paragraph>
                  请提供您对协作编辑功能的反馈，包括使用体验、发现的问题或改进建议。
                </Paragraph>

                <Space>
                  <Button
                    type="primary"
                    icon={<BulbOutlined />}
                    onClick={handleSubmitFeedback}
                  >
                    提交反馈
                  </Button>

                  <Button
                    icon={<VideoCameraOutlined />}
                    onClick={handleCaptureScreenshot}
                  >
                    捕获截图
                  </Button>
                </Space>
              </Space>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <VideoCameraOutlined />
                  录制
                </span>
              }
              key="recording"
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Paragraph>
                  启用操作录制功能，记录您的操作以帮助我们分析和改进用户体验。
                </Paragraph>

                <Space>
                  <Switch
                    checked={recordingEnabled}
                    onChange={handleToggleRecording}
                    checkedChildren={<VideoCameraOutlined />}
                    unCheckedChildren={<StopOutlined />}
                  />
                  <Text>{recordingEnabled ? '正在录制' : '未录制'}</Text>
                </Space>
              </Space>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <FileTextOutlined />
                  历史
                </span>
              }
              key="history"
            >
              {renderHistory()}
            </TabPane>
          </Tabs>
        </Space>
      ) : (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="启用用户测试以开始测试协作编辑功能"
        />
      )}

      {/* 反馈表单 */}
      <FeedbackForm
        visible={showFeedbackForm}
        onClose={handleCloseFeedbackForm}
      />

      {/* 报告模态框 */}
      {showReportModal && selectedReport && (
        <Modal
          title="测试报告"
          visible={showReportModal}
          width={800}
          footer={[
            <Button key="close" onClick={() => setShowReportModal(false)}>
              关闭
            </Button>,
            <Button
              key="download"
              type="primary"
              onClick={() => {
                // 下载报告逻辑
                const reportData = reports[selectedReport];
                if (reportData) {
                  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `测试报告_${selectedReport}.json`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                }
              }}
            >
              下载报告
            </Button>
          ]}
          onCancel={() => setShowReportModal(false)}
        >
          {reports[selectedReport] ? (
            <div style={{ maxHeight: '500px', overflow: 'auto' }}>
              <pre>{JSON.stringify(reports[selectedReport], null, 2)}</pre>
            </div>
          ) : (
            <Empty description="无法加载报告数据" />
          )}
        </Modal>
      )}
    </Card>
  );
};

export default UserTestingPanel;
