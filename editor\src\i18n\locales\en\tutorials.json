{"tutorials": {"title": "Tutorials", "start": "Start Tutorial", "next": "Next", "previous": "Previous", "skip": "<PERSON><PERSON>", "finish": "Finish", "restart": "<PERSON><PERSON>", "close": "Close", "progress": "Progress", "step": "Step", "of": "of", "completed": "Completed", "inProgress": "In Progress", "notStarted": "Not Started", "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "expert": "Expert"}, "categories": {"basics": "Basics", "modeling": "Modeling", "animation": "Animation", "materials": "Materials", "lighting": "Lighting", "physics": "Physics", "scripting": "Scripting", "ui": "User Interface", "optimization": "Optimization", "collaboration": "Collaboration"}, "basicTutorials": {"gettingStarted": {"title": "Getting Started", "description": "Learn the basic operations and interface of the editor", "steps": {"welcome": "Welcome to DL Engine Editor", "interface": "Understanding the Editor Interface", "navigation": "Learning Scene Navigation", "selection": "Selecting and Manipulating Objects", "properties": "Editing Object Properties"}}, "sceneCreation": {"title": "Creating Scenes", "description": "Learn how to create and manage scenes", "steps": {"newScene": "Creating a New Scene", "addObjects": "Adding Objects to Scene", "hierarchy": "Understanding Scene Hierarchy", "saveScene": "Saving the Scene"}}, "objectManipulation": {"title": "Object Manipulation", "description": "Learn how to manipulate 3D objects", "steps": {"transform": "Transforming Objects", "duplicate": "Duplicating Objects", "group": "Organizing Objects", "delete": "Deleting Objects"}}}, "animationTutorials": {"basicAnimation": {"title": "Basic Animation", "description": "Learn to create simple animations", "steps": {"timeline": "Understanding the Timeline", "keyframes": "Setting Keyframes", "interpolation": "Understanding Interpolation", "playback": "Playing Animations"}}, "characterAnimation": {"title": "Character Animation", "description": "Learn character animation creation", "steps": {"rigging": "Character Rigging", "skinning": "Skinning Setup", "posing": "Pose Adjustment", "walking": "Creating Walk Animation"}}}, "materialTutorials": {"basicMaterials": {"title": "Basic Materials", "description": "Learn the basic concepts of materials", "steps": {"create": "Creating Materials", "properties": "Setting Material Properties", "textures": "Applying Textures", "preview": "Previewing Material Effects"}}, "advancedMaterials": {"title": "Advanced Materials", "description": "Learn advanced material techniques", "steps": {"pbr": "PBR Materials", "nodes": "Node Editor", "shaders": "Custom Shaders", "optimization": "Material Optimization"}}}, "physicsTutorials": {"basicPhysics": {"title": "Basic Physics", "description": "Learn basic usage of the physics system", "steps": {"rigidbody": "Rigidbody Component", "colliders": "Colliders", "forces": "Applying Forces", "simulation": "Physics Simulation"}}, "characterController": {"title": "Character Controller", "description": "Learn character physics control", "steps": {"setup": "Setting up Character Controller", "movement": "Movement Control", "jumping": "Jumping Mechanism", "collision": "Collision Detection"}}}, "scriptingTutorials": {"introduction": {"title": "Scripting Introduction", "description": "Learn scripting programming basics", "steps": {"basics": "Scripting Basics", "components": "Component System", "events": "Event Handling", "debugging": "Debugging Techniques"}}, "gameLogic": {"title": "Game Logic", "description": "Implementing game logic", "steps": {"input": "Input Handling", "state": "State Management", "ai": "AI Behavior", "ui": "UI Interaction"}}}, "tips": {"title": "Tips", "hotkeys": "Hotkeys", "workflow": "Workflow", "bestPractices": "Best Practices", "troubleshooting": "Troubleshooting"}, "feedback": {"helpful": "Was this tutorial helpful?", "yes": "Yes", "no": "No", "improve": "How can we improve this tutorial?", "submit": "Submit <PERSON>", "thanks": "Thank you for your feedback!"}, "errors": {"loadFailed": "Failed to load tutorial", "stepFailed": "Failed to execute step", "validationFailed": "Validation failed", "timeout": "Operation timeout"}, "messages": {"tutorialStarted": "Tutorial started", "tutorialCompleted": "Tutorial completed", "stepCompleted": "Step completed", "allTutorialsCompleted": "All tutorials completed", "progressSaved": "Progress saved"}}}