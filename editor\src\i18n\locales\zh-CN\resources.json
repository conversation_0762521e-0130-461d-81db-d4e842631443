{"resources": {"hotUpdate": {"title": "资源热更新", "description": "管理资源热更新功能", "check": "检查", "checkForUpdates": "检查更新", "checking": "检查中", "checkAgain": "再次检查", "updates": "更新", "settings": "设置", "history": "历史", "updateAvailable": "有可用更新", "updateComplete": "更新完成", "updateFailed": "更新失败", "updateAppliedDesc": "更新已成功应用", "updateFailedDesc": "应用更新时发生错误", "viewDetails": "查看详情", "downloading": "下载中", "applying": "应用中", "applied": "已应用", "error": "错误", "upToDate": "已是最新", "readyToApply": "准备应用"}, "update": {"title": "资源更新", "checkUpdate": "检查更新", "checking": "检查中", "downloadUpdate": "下载更新", "cancelUpdate": "取消更新", "applyUpdate": "应用更新", "applying": "应用中", "retry": "重试", "details": "详情", "history": "历史记录", "rollback": "回滚", "version": "版本", "type": "类型", "size": "大小", "date": "日期", "description": "描述", "changelog": "更新日志", "noUpdateChecked": "尚未检查更新", "checkUpdateDesc": "点击\"检查更新\"按钮检查是否有可用更新", "upToDate": "已是最新版本", "upToDateDesc": "您的资源已是最新版本", "useUpdate": "使用更新", "statusChecking": "检查中", "statusAvailable": "可用", "statusDownloading": "下载中", "statusDownloaded": "已下载", "statusApplying": "应用中", "statusApplied": "已应用", "statusError": "错误", "statusCancelled": "已取消", "statusUpToDate": "已是最新", "statusRolledBack": "已回滚", "statusNone": "未知", "typeAssets": "资源", "typeScripts": "脚本", "typeShaders": "着色器", "typeConfigs": "配置", "typeSystem": "系统", "typeUnknown": "未知"}, "updateConfig": {"title": "更新配置", "configInfo": "更新配置", "configDescription": "配置资源热更新的行为和策略", "saveConfig": "保存配置", "resetConfig": "重置配置", "basicMode": "基本模式", "advancedMode": "高级模式", "autoCheckInterval": "自动检查间隔", "autoCheckIntervalTip": "自动检查更新的时间间隔（秒）。设置为0禁用自动检查。", "seconds": "秒", "milliseconds": "毫秒", "autoDownloadUpdates": "自动下载更新", "autoDownloadUpdatesTip": "当发现更新时自动下载", "autoApplyUpdates": "自动应用更新", "autoApplyUpdatesTip": "当下载完成后自动应用更新", "updateTypes": "更新类型", "updateTypesTip": "选择要更新的资源类型", "selectUpdateTypes": "选择更新类型", "typeAssets": "资源", "typeScripts": "脚本", "typeShaders": "着色器", "typeConfigs": "配置", "typeSystem": "系统", "updateChannel": "更新渠道", "updateChannelTip": "选择更新渠道", "channelStable": "稳定版", "channelBeta": "测试版", "channelDev": "开发版", "notifyOnUpdateAvailable": "有更新时通知", "notifyOnUpdateAvailableTip": "当有可用更新时显示通知", "advancedSettings": "高级设置", "downloadSettings": "下载设置", "maxConcurrentDownloads": "最大并发下载数", "maxConcurrentDownloadsTip": "同时下载的最大资源数", "retryCount": "重试次数", "retryCountTip": "下载失败时的重试次数", "retryDelay": "重试延迟", "retryDelayTip": "重试之间的延迟时间（毫秒）", "enableDeltaUpdates": "启用增量更新", "enableDeltaUpdatesTip": "只下载变更的部分，减少下载量", "compressionType": "压缩类型", "compressionTypeTip": "下载资源的压缩方式", "compressionGzip": "Gzip", "compressionBrotli": "<PERSON><PERSON><PERSON>", "compressionNone": "无压缩", "backupSettings": "备份设置", "backupBeforeUpdate": "更新前备份", "backupBeforeUpdateTip": "在应用更新前创建备份", "maxBackupCount": "最大备份数量", "maxBackupCountTip": "保留的最大备份数量", "cleanupAfterUpdate": "更新后清理", "cleanupAfterUpdateTip": "更新成功后清理临时文件", "serverSettings": "服务器设置", "updateServer": "更新服务器", "updateServerTip": "更新服务器的URL"}, "dependency": {"title": "资源依赖", "manager": "依赖管理器", "visualizer": "依赖可视化", "help": "帮助", "helpContent": "资源依赖管理器可以帮助您查看和管理资源之间的依赖关系。", "search": "搜索资源", "filter": "筛选", "showAll": "显示全部", "showDirect": "仅直接依赖", "showIndirect": "包含间接依赖", "showReverse": "显示反向依赖", "showCycles": "显示循环依赖", "noDependencies": "无依赖关系", "noResource": "未选择资源", "selectResource": "请选择一个资源", "resourceDetails": "资源详情", "dependencies": "依赖项", "dependents": "被依赖项", "cycles": "循环依赖", "warnings": "警告", "suggestions": "优化建议", "optimize": "优化", "optimizeAll": "优化全部", "optimizationSuccess": "优化成功", "optimizationFailed": "优化失败", "refreshGraph": "刷新图表", "exportGraph": "导出图表", "zoomIn": "放大", "zoomOut": "缩小", "fitView": "适应视图", "layoutHorizontal": "水平布局", "layoutVertical": "垂直布局", "layoutRadial": "辐射布局", "layoutForce": "力导向布局"}}}