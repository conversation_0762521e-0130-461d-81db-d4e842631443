/**
 * 曲线编辑器组件
 * 提供面部动画曲线编辑功能
 */
import React, { useState, useEffect, useRef } from 'react';
import { Select, Button, Space, Checkbox} from 'antd';
import { 
  ZoomInOutlined, 
  ZoomOutOutlined, 
  FullscreenOutlined, 
  LineChartOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Track, TrackType, Keyframe } from './TimelineEditor';
import './CurveEditor.less';

const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';

/**
 * 曲线编辑器属性
 */
interface CurveEditorProps {
  /** 实体ID */
  entityId?: string;
  /** 当前时间 */
  currentTime: number;
  /** 动画持续时间 */
  duration: number;
}

/**
 * 曲线类型
 */
enum CurveType {
  VALUE = 'value',
  WEIGHT = 'weight'
}

/**
 * 曲线编辑器组件
 */
const CurveEditor: React.FC<CurveEditorProps> = ({
  entityId,
  currentTime,
  duration
}) => {
  const { t } = useTranslation();
  
  // 引用
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 状态
  const [tracks, setTracks] = useState<Track[]>([]);
  const [selectedTrackIds, setSelectedTrackIds] = useState<string[]>([]);
  const [curveType, setCurveType] = useState<CurveType>(CurveType.VALUE);
  const [zoom, setZoom] = useState<{ x: number, y: number }>({ x: 1, y: 1 });
  const [pan, setPan] = useState<{ x: number, y: number }>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStart, setDragStart] = useState<{ x: number, y: number }>({ x: 0, y: 0 });
  const [selectedKeypoint, setSelectedKeypoint] = useState<{ trackId: string, keyframeId: string } | null>(null);
  const [showGrid, setShowGrid] = useState<boolean>(true);
  
  // 加载轨道数据
  useEffect(() => {
    if (entityId) {
      // 这里应该从引擎中加载实体的轨道数据
      // 示例数据，实际实现需要与引擎集成
      const mockTracks: Track[] = [
        {
          id: '1',
          name: t('editor.animation.expressionTrack'),
          type: TrackType.EXPRESSION,
          keyframes: [
            { id: '1-1', time: 0, value: 'neutral', type: 'expression', easing: 'linear' },
            { id: '1-2', time: 1, value: 'happy', type: 'expression', easing: 'easeInOutQuad' },
            { id: '1-3', time: 2, value: 'surprised', type: 'expression', easing: 'easeInOutQuad' },
            { id: '1-4', time: 3, value: 'happy', type: 'expression', easing: 'easeInOutQuad' },
            { id: '1-5', time: 4, value: 'neutral', type: 'expression', easing: 'easeOutQuad' }
          ],
          color: '#1890ff'
        },
        {
          id: '2',
          name: t('editor.animation.visemeTrack'),
          type: TrackType.VISEME,
          keyframes: [
            { id: '2-1', time: 0, value: 'silent', type: 'viseme', easing: 'linear' },
            { id: '2-2', time: 0.5, value: 'aa', type: 'viseme', easing: 'easeInQuad' },
            { id: '2-3', time: 1.5, value: 'oh', type: 'viseme', easing: 'easeInOutQuad' },
            { id: '2-4', time: 2.5, value: 'ee', type: 'viseme', easing: 'easeInOutQuad' },
            { id: '2-5', time: 3.5, value: 'silent', type: 'viseme', easing: 'easeOutQuad' }
          ],
          color: '#52c41a'
        }
      ];
      
      setTracks(mockTracks);
      
      if (mockTracks.length > 0) {
        setSelectedTrackIds([mockTracks[0].id]);
      }
    }
  }, [entityId, t]);
  
  // 绘制曲线
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 调整画布大小
    if (containerRef.current) {
      canvas.width = containerRef.current.clientWidth;
      canvas.height = containerRef.current.clientHeight;
    }
    
    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制网格
    if (showGrid) {
      drawGrid(ctx, canvas.width, canvas.height);
    }
    
    // 绘制时间指示器
    drawTimeIndicator(ctx, currentTime, canvas.width, canvas.height);
    
    // 绘制曲线
    selectedTrackIds.forEach(trackId => {
      const track = tracks.find(t => t.id === trackId);
      if (track) {
        drawCurve(ctx, track, canvas.width, canvas.height);
      }
    });
    
    // 绘制选中的关键点
    if (selectedKeypoint) {
      const track = tracks.find(t => t.id === selectedKeypoint.trackId);
      const keyframe = track?.keyframes.find(kf => kf.id === selectedKeypoint.keyframeId);
      
      if (track && keyframe) {
        drawSelectedKeypoint(ctx, track, keyframe, canvas.width, canvas.height);
      }
    }
  }, [tracks, selectedTrackIds, currentTime, curveType, zoom, pan, selectedKeypoint, showGrid]);
  
  // 绘制网格
  const drawGrid = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    ctx.save();
    ctx.strokeStyle = '#333333';
    ctx.lineWidth = 0.5;
    
    // 计算网格大小
    const gridSizeX = 50 * zoom.x;
    const gridSizeY = 50 * zoom.y;
    
    // 绘制垂直线
    for (let x = pan.x % gridSizeX; x < width; x += gridSizeX) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
    
    // 绘制水平线
    for (let y = pan.y % gridSizeY; y < height; y += gridSizeY) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
    
    // 绘制中心线
    ctx.strokeStyle = '#555555';
    ctx.lineWidth = 1;
    
    // 水平中心线
    const centerY = height / 2 + pan.y;
    ctx.beginPath();
    ctx.moveTo(0, centerY);
    ctx.lineTo(width, centerY);
    ctx.stroke();
    
    // 垂直中心线
    const centerX = width / 2 + pan.x;
    ctx.beginPath();
    ctx.moveTo(centerX, 0);
    ctx.lineTo(centerX, height);
    ctx.stroke();
    
    ctx.restore();
  };
  
  // 绘制时间指示器
  const drawTimeIndicator = (ctx: CanvasRenderingContext2D, time: number, width: number, height: number) => {
    ctx.save();
    ctx.strokeStyle = '#ff4d4f';
    ctx.lineWidth = 2;
    
    // 计算时间指示器位置
    const x = (time / duration) * width * zoom.x + pan.x;
    
    // 绘制时间指示器
    ctx.beginPath();
    ctx.moveTo(x, 0);
    ctx.lineTo(x, height);
    ctx.stroke();
    
    ctx.restore();
  };
  
  // 绘制曲线
  const drawCurve = (ctx: CanvasRenderingContext2D, track: Track, width: number, height: number) => {
    if (track.keyframes.length < 2) return;
    
    ctx.save();
    ctx.strokeStyle = track.color || '#1890ff';
    ctx.lineWidth = 2;
    
    // 排序关键帧
    const sortedKeyframes = [...track.keyframes].sort((a, b) => a.time - b.time);
    
    // 绘制曲线
    ctx.beginPath();
    
    // 遍历关键帧绘制曲线
    for (let i = 0; i < sortedKeyframes.length; i++) {
      const keyframe = sortedKeyframes[i];
      
      // 计算关键帧位置
      const x = (keyframe.time / duration) * width * zoom.x + pan.x;
      
      // 计算值
      let y;
      if (curveType === CurveType.VALUE) {
        // 根据不同类型的轨道计算值
        if (track.type === TrackType.EXPRESSION) {
          // 表情值映射到0-1
          const expressionValue = mapExpressionToValue(keyframe.value as string);
          y = height - (expressionValue * height * zoom.y) + pan.y;
        } else if (track.type === TrackType.VISEME) {
          // 口型值映射到0-1
          const visemeValue = mapVisemeToValue(keyframe.value as string);
          y = height - (visemeValue * height * zoom.y) + pan.y;
        } else {
          // 组合值，使用表情值
          const expressionValue = mapExpressionToValue((keyframe.value as any).expression);
          y = height - (expressionValue * height * zoom.y) + pan.y;
        }
      } else {
        // 权重值直接使用
        const weight = 1.0; // 这里应该从关键帧中获取权重
        y = height - (weight * height * zoom.y) + pan.y;
      }
      
      // 绘制点
      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        // 使用缓动函数计算曲线
        const prevKeyframe = sortedKeyframes[i - 1];
        const prevX = (prevKeyframe.time / duration) * width * zoom.x + pan.x;
        
        let prevY;
        if (curveType === CurveType.VALUE) {
          if (track.type === TrackType.EXPRESSION) {
            const expressionValue = mapExpressionToValue(prevKeyframe.value as string);
            prevY = height - (expressionValue * height * zoom.y) + pan.y;
          } else if (track.type === TrackType.VISEME) {
            const visemeValue = mapVisemeToValue(prevKeyframe.value as string);
            prevY = height - (visemeValue * height * zoom.y) + pan.y;
          } else {
            const expressionValue = mapExpressionToValue((prevKeyframe.value as any).expression);
            prevY = height - (expressionValue * height * zoom.y) + pan.y;
          }
        } else {
          const weight = 1.0;
          prevY = height - (weight * height * zoom.y) + pan.y;
        }
        
        // 根据缓动类型绘制曲线
        const easing = keyframe.easing || 'linear';
        if (easing === 'linear') {
          ctx.lineTo(x, y);
        } else {
          // 绘制贝塞尔曲线
          const cp1x = prevX + (x - prevX) / 3;
          const cp2x = prevX + (x - prevX) * 2 / 3;
          let cp1y, cp2y;
          
          // 根据缓动类型调整控制点
          if (easing.includes('easeIn')) {
            cp1y = prevY;
            cp2y = prevY + (y - prevY) / 2;
          } else if (easing.includes('easeOut')) {
            cp1y = prevY + (y - prevY) / 2;
            cp2y = y;
          } else {
            cp1y = prevY;
            cp2y = y;
          }
          
          ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y);
        }
      }
      
      // 绘制关键点
      ctx.save();
      ctx.fillStyle = track.color || '#1890ff';
      ctx.beginPath();
      ctx.arc(x, y, 5, 0, Math.PI * 2);
      ctx.fill();
      ctx.restore();
    }
    
    ctx.stroke();
    ctx.restore();
  };
  
  // 绘制选中的关键点
  const drawSelectedKeypoint = (
    ctx: CanvasRenderingContext2D, 
    track: Track, 
    keyframe: Keyframe, 
    width: number, 
    height: number
  ) => {
    ctx.save();
    
    // 计算关键点位置
    const x = (keyframe.time / duration) * width * zoom.x + pan.x;
    
    // 计算值
    let y;
    if (curveType === CurveType.VALUE) {
      if (track.type === TrackType.EXPRESSION) {
        const expressionValue = mapExpressionToValue(keyframe.value as string);
        y = height - (expressionValue * height * zoom.y) + pan.y;
      } else if (track.type === TrackType.VISEME) {
        const visemeValue = mapVisemeToValue(keyframe.value as string);
        y = height - (visemeValue * height * zoom.y) + pan.y;
      } else {
        const expressionValue = mapExpressionToValue((keyframe.value as any).expression);
        y = height - (expressionValue * height * zoom.y) + pan.y;
      }
    } else {
      const weight = 1.0;
      y = height - (weight * height * zoom.y) + pan.y;
    }
    
    // 绘制选中指示器
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(x, y, 8, 0, Math.PI * 2);
    ctx.stroke();
    
    // 绘制关键点
    ctx.fillStyle = track.color || '#1890ff';
    ctx.beginPath();
    ctx.arc(x, y, 5, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.restore();
  };
  
  // 映射表情到值
  const mapExpressionToValue = (expression: string): number => {
    // 简单映射，实际应用中可能需要更复杂的映射
    const expressionMap: { [key: string]: number } = {
      'neutral': 0.5,
      'happy': 0.8,
      'sad': 0.2,
      'angry': 0.7,
      'surprised': 0.9,
      'fear': 0.3,
      'disgust': 0.4,
      'contempt': 0.6
    };
    
    return expressionMap[expression] || 0.5;
  };
  
  // 映射口型到值
  const mapVisemeToValue = (viseme: string): number => {
    // 简单映射，实际应用中可能需要更复杂的映射
    const visemeMap: { [key: string]: number } = {
      'silent': 0.0,
      'aa': 0.9,
      'ee': 0.7,
      'ih': 0.5,
      'oh': 0.8,
      'ou': 0.6,
      'pp': 0.2,
      'ff': 0.3,
      'th': 0.4,
      'dd': 0.5,
      'kk': 0.6,
      'ch': 0.7,
      'ss': 0.4,
      'nn': 0.3,
      'rr': 0.5
    };
    
    return visemeMap[viseme] || 0.0;
  };
  
  // 处理鼠标按下
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!canvasRef.current) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // 检查是否点击了关键点
    let foundKeypoint = false;
    
    for (const trackId of selectedTrackIds) {
      const track = tracks.find(t => t.id === trackId);
      if (!track) continue;
      
      for (const keyframe of track.keyframes) {
        // 计算关键点位置
        const kx = (keyframe.time / duration) * canvasRef.current.width * zoom.x + pan.x;
        
        let ky;
        if (curveType === CurveType.VALUE) {
          if (track.type === TrackType.EXPRESSION) {
            const expressionValue = mapExpressionToValue(keyframe.value as string);
            ky = canvasRef.current.height - (expressionValue * canvasRef.current.height * zoom.y) + pan.y;
          } else if (track.type === TrackType.VISEME) {
            const visemeValue = mapVisemeToValue(keyframe.value as string);
            ky = canvasRef.current.height - (visemeValue * canvasRef.current.height * zoom.y) + pan.y;
          } else {
            const expressionValue = mapExpressionToValue((keyframe.value as any).expression);
            ky = canvasRef.current.height - (expressionValue * canvasRef.current.height * zoom.y) + pan.y;
          }
        } else {
          const weight = 1.0;
          ky = canvasRef.current.height - (weight * canvasRef.current.height * zoom.y) + pan.y;
        }
        
        // 检查点击是否在关键点范围内
        const distance = Math.sqrt(Math.pow(x - kx, 2) + Math.pow(y - ky, 2));
        if (distance <= 8) {
          setSelectedKeypoint({ trackId: track.id, keyframeId: keyframe.id });
          foundKeypoint = true;
          break;
        }
      }
      
      if (foundKeypoint) break;
    }
    
    if (!foundKeypoint) {
      // 开始拖动画布
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  };
  
  // 处理鼠标移动
  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      // 计算拖动距离
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;
      
      // 更新平移
      setPan({
        x: pan.x + deltaX,
        y: pan.y + deltaY
      });
      
      // 更新拖动起点
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  };
  
  // 处理鼠标释放
  const handleMouseUp = () => {
    setIsDragging(false);
  };
  
  // 处理缩放
  const handleZoom = (direction: 'in' | 'out') => {
    if (direction === 'in') {
      setZoom({
        x: Math.min(zoom.x * 1.2, 5),
        y: Math.min(zoom.y * 1.2, 5)
      });
    } else {
      setZoom({
        x: Math.max(zoom.x / 1.2, 0.2),
        y: Math.max(zoom.y / 1.2, 0.2)
      });
    }
  };
  
  // 重置视图
  const handleResetView = () => {
    setZoom({ x: 1, y: 1 });
    setPan({ x: 0, y: 0 });
  };
  
  // 切换轨道选择
  const handleTrackSelectionChange = (trackId: string) => {
    if (selectedTrackIds.includes(trackId)) {
      setSelectedTrackIds(selectedTrackIds.filter(id => id !== trackId));
    } else {
      setSelectedTrackIds([...selectedTrackIds, trackId]);
    }
  };
  
  return (
    <div className="curve-editor">
      <div className="curve-editor-toolbar">
        <Space>
          <Select
            value={curveType}
            onChange={setCurveType}
            style={{ width: 120 }}
          >
            <Option value={CurveType.VALUE}>{t('editor.animation.valueType')}</Option>
            <Option value={CurveType.WEIGHT}>{t('editor.animation.weightType')}</Option>
          </Select>
          
          <Button icon={<ZoomInOutlined />} onClick={() => handleZoom('in')} />
          <Button icon={<ZoomOutOutlined />} onClick={() => handleZoom('out')} />
          <Button icon={<FullscreenOutlined />} onClick={handleResetView} />
          <Checkbox checked={showGrid} onChange={(e) => setShowGrid(e.target.checked)}>
            {t('editor.animation.showGrid')}
          </Checkbox>
        </Space>
      </div>
      
      <div className="curve-editor-content">
        <div className="track-list">
          {tracks.map(track => (
            <div 
              key={track.id}
              className={`track-item ${selectedTrackIds.includes(track.id) ? 'selected' : ''}`}
              onClick={() => handleTrackSelectionChange(track.id)}
            >
              <div className="track-color" style={{ backgroundColor: track.color }} />
              <div className="track-name">{track.name}</div>
              <Button
                type="text"
                size="small"
                icon={selectedTrackIds.includes(track.id) ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              />
            </div>
          ))}
        </div>
        
        <div 
          className="curve-canvas-container" 
          ref={containerRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <canvas ref={canvasRef} />
        </div>
      </div>
    </div>
  );
};

export default CurveEditor;
