/**
 * 粒子系统状态切片
 */
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { particleService } from '../../services/particleService';

// 粒子系统接口
export interface ParticleSystem {
  id: string;
  name: string;
  maxParticles: number;
  emissionRate: number;
  lifetime: { min: number; max: number };
  startSize: { min: number; max: number };
  endSize: { min: number; max: number };
  startColor: string;
  endColor: string;
  startSpeed: { min: number; max: number };
  gravity: [number, number, number];
  emitterType: 'point' | 'circle' | 'box' | 'sphere';
  emitterSize: [number, number, number];
  emitterRadius: number;
  shape: 'circle' | 'square' | 'triangle' | 'custom';
  blendMode: 'normal' | 'add' | 'multiply' | 'screen';
  texture: string;
  loop: boolean;
  prewarm: boolean;
  simulationSpace: 'local' | 'world';
  renderMode: '3d' | 'billboard' | 'stretched';
  createdAt: string;
  updatedAt: string;
}

// 粒子系统状态
interface ParticlesState {
  particleSystems: ParticleSystem[];
  selectedParticleSystemId: string | null;
  loading: boolean;
  error: string | null;
}

// 初始状态
const initialState: ParticlesState = {
  particleSystems: [],
  selectedParticleSystemId: null,
  loading: false,
  error: null};

// 异步操作：获取所有粒子系统
export const fetchParticleSystems = createAsyncThunk(
  'particles/fetchParticleSystems',
  async (_, { rejectWithValue }) => {
    try {
      const response = await particleService.getParticleSystems();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：获取单个粒子系统
export const fetchParticleSystem = createAsyncThunk(
  'particles/fetchParticleSystem',
  async (particleSystemId: string, { rejectWithValue }) => {
    try {
      const response = await particleService.getParticleSystem(particleSystemId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：创建粒子系统
export const createParticleSystem = createAsyncThunk(
  'particles/createParticleSystem',
  async (particleSystemData: Omit<ParticleSystem, 'id' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const response = await particleService.createParticleSystem(particleSystemData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：更新粒子系统
export const updateParticleSystem = createAsyncThunk(
  'particles/updateParticleSystem',
  async (particleSystemData: Partial<ParticleSystem> & { id: string }, { rejectWithValue }) => {
    try {
      const response = await particleService.updateParticleSystem(particleSystemData.id, particleSystemData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：删除粒子系统
export const deleteParticleSystem = createAsyncThunk(
  'particles/deleteParticleSystem',
  async (particleSystemId: string, { rejectWithValue }) => {
    try {
      await particleService.deleteParticleSystem(particleSystemId);
      return particleSystemId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 创建粒子系统切片
const particlesSlice = createSlice({
  name: 'particles',
  initialState,
  reducers: {
    // 选择粒子系统
    selectParticleSystem: (state, action: PayloadAction<string | null>) => {
      state.selectedParticleSystemId = action.payload;
    }},
  extraReducers: (builder) => {
    builder
      // 获取所有粒子系统
      .addCase(fetchParticleSystems.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchParticleSystems.fulfilled, (state, action) => {
        state.loading = false;
        state.particleSystems = action.payload;
      })
      .addCase(fetchParticleSystems.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 获取单个粒子系统
      .addCase(fetchParticleSystem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchParticleSystem.fulfilled, (state, action) => {
        state.loading = false;
        const particleSystemIndex = state.particleSystems.findIndex(particleSystem => particleSystem.id === action.payload.id);
        
        if (particleSystemIndex !== -1) {
          state.particleSystems[particleSystemIndex] = action.payload;
        } else {
          state.particleSystems.push(action.payload);
        }
      })
      .addCase(fetchParticleSystem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 创建粒子系统
      .addCase(createParticleSystem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createParticleSystem.fulfilled, (state, action) => {
        state.loading = false;
        state.particleSystems.push(action.payload);
      })
      .addCase(createParticleSystem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 更新粒子系统
      .addCase(updateParticleSystem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateParticleSystem.fulfilled, (state, action) => {
        state.loading = false;
        const particleSystemIndex = state.particleSystems.findIndex(particleSystem => particleSystem.id === action.payload.id);
        
        if (particleSystemIndex !== -1) {
          state.particleSystems[particleSystemIndex] = action.payload;
        }
      })
      .addCase(updateParticleSystem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 删除粒子系统
      .addCase(deleteParticleSystem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteParticleSystem.fulfilled, (state, action) => {
        state.loading = false;
        state.particleSystems = state.particleSystems.filter(particleSystem => particleSystem.id !== action.payload);
        
        if (state.selectedParticleSystemId === action.payload) {
          state.selectedParticleSystemId = null;
        }
      })
      .addCase(deleteParticleSystem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }});

export const { selectParticleSystem } = particlesSlice.actions;

export default particlesSlice.reducer;
