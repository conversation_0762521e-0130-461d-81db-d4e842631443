/**
 * 资源更新进度监控组件
 * 用于显示资源更新的进度和状态
 */
import React, { useState, useEffect } from 'react';
import { Progress, Card, Button, Space, Typography, Tag, Alert, Divider, List } from 'antd';
import {
  CloudDownloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  RollbackOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { resourceHotUpdateService } from '../../services/ResourceHotUpdateService';
import { ResourceUpdateStatus, ResourceUpdateType } from '../../store/resources/resourceHotUpdateSlice';
import './ResourceUpdateProgressMonitor.less';

const { Text, Title } = Typography;

/**
 * 资源更新进度监控组件属性
 */
interface ResourceUpdateProgressMonitorProps {
  /** 是否显示详情 */
  showDetails?: boolean;
  /** 是否显示控制按钮 */
  showControls?: boolean;
  /** 是否显示历史记录 */
  showHistory?: boolean;
  /** 是否紧凑模式 */
  compact?: boolean;
  /** 关闭回调 */
  onClose?: () => void;
}

/**
 * 资源更新进度监控组件
 */
const ResourceUpdateProgressMonitor: React.FC<ResourceUpdateProgressMonitorProps> = ({
  showDetails = true,
  showControls = true,
  showHistory = false,
  compact = false,
  onClose
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 从Redux状态获取更新信息
  const { 
    status, 
    progress, 
    currentUpdate, 
    updateHistory,
    isRollingBack
  } = useSelector((state: RootState) => state.resourceHotUpdate);
  
  // 本地状态
  const [expandedDetails, setExpandedDetails] = useState<boolean>(false);
  const [selectedHistoryItem, setSelectedHistoryItem] = useState<string | null>(null);
  
  // 获取状态标签颜色
  const getStatusColor = (status: ResourceUpdateStatus): string => {
    switch (status) {
      case ResourceUpdateStatus.CHECKING:
        return 'processing';
      case ResourceUpdateStatus.AVAILABLE:
        return 'warning';
      case ResourceUpdateStatus.DOWNLOADING:
        return 'processing';
      case ResourceUpdateStatus.DOWNLOADED:
        return 'success';
      case ResourceUpdateStatus.APPLYING:
        return 'processing';
      case ResourceUpdateStatus.APPLIED:
        return 'success';
      case ResourceUpdateStatus.ERROR:
        return 'error';
      case ResourceUpdateStatus.CANCELLED:
        return 'default';
      case ResourceUpdateStatus.UP_TO_DATE:
        return 'success';
      case ResourceUpdateStatus.ROLLED_BACK:
        return 'warning';
      default:
        return 'default';
    }
  };
  
  // 获取状态图标
  const getStatusIcon = (status: ResourceUpdateStatus) => {
    switch (status) {
      case ResourceUpdateStatus.CHECKING:
        return <SyncOutlined spin />;
      case ResourceUpdateStatus.AVAILABLE:
        return <InfoCircleOutlined />;
      case ResourceUpdateStatus.DOWNLOADING:
        return <CloudDownloadOutlined />;
      case ResourceUpdateStatus.DOWNLOADED:
        return <CheckCircleOutlined />;
      case ResourceUpdateStatus.APPLYING:
        return <SyncOutlined spin />;
      case ResourceUpdateStatus.APPLIED:
        return <CheckCircleOutlined />;
      case ResourceUpdateStatus.ERROR:
        return <CloseCircleOutlined />;
      case ResourceUpdateStatus.CANCELLED:
        return <PauseCircleOutlined />;
      case ResourceUpdateStatus.UP_TO_DATE:
        return <CheckCircleOutlined />;
      case ResourceUpdateStatus.ROLLED_BACK:
        return <RollbackOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };
  
  // 获取状态文本
  const getStatusText = (status: ResourceUpdateStatus): string => {
    switch (status) {
      case ResourceUpdateStatus.CHECKING:
        return t('resources.update.statusChecking');
      case ResourceUpdateStatus.AVAILABLE:
        return t('resources.update.statusAvailable');
      case ResourceUpdateStatus.DOWNLOADING:
        return t('resources.update.statusDownloading');
      case ResourceUpdateStatus.DOWNLOADED:
        return t('resources.update.statusDownloaded');
      case ResourceUpdateStatus.APPLYING:
        return t('resources.update.statusApplying');
      case ResourceUpdateStatus.APPLIED:
        return t('resources.update.statusApplied');
      case ResourceUpdateStatus.ERROR:
        return t('resources.update.statusError');
      case ResourceUpdateStatus.CANCELLED:
        return t('resources.update.statusCancelled');
      case ResourceUpdateStatus.UP_TO_DATE:
        return t('resources.update.statusUpToDate');
      case ResourceUpdateStatus.ROLLED_BACK:
        return t('resources.update.statusRolledBack');
      default:
        return t('resources.update.statusNone');
    }
  };
  
  // 获取更新类型文本
  const getUpdateTypeText = (type: ResourceUpdateType): string => {
    switch (type) {
      case ResourceUpdateType.ASSETS:
        return t('resources.update.typeAssets');
      case ResourceUpdateType.SCRIPTS:
        return t('resources.update.typeScripts');
      case ResourceUpdateType.SHADERS:
        return t('resources.update.typeShaders');
      case ResourceUpdateType.CONFIGS:
        return t('resources.update.typeConfigs');
      case ResourceUpdateType.SYSTEM:
        return t('resources.update.typeSystem');
      default:
        return t('resources.update.typeUnknown');
    }
  };
  
  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  // 格式化日期
  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };
  
  // 检查更新
  const handleCheckUpdate = async () => {
    await resourceHotUpdateService.checkForUpdates();
  };
  
  // 下载更新
  const handleDownloadUpdate = async () => {
    if (currentUpdate) {
      await resourceHotUpdateService.downloadUpdate(currentUpdate.id);
    }
  };
  
  // 应用更新
  const handleApplyUpdate = async () => {
    if (currentUpdate) {
      await resourceHotUpdateService.applyUpdate(currentUpdate.id);
    }
  };
  
  // 取消更新
  const handleCancelUpdate = () => {
    resourceHotUpdateService.cancelUpdate();
  };
  
  // 回滚更新
  const handleRollbackUpdate = async (updateId: string) => {
    await resourceHotUpdateService.rollbackUpdate(updateId);
  };
  
  // 渲染控制按钮
  const renderControls = () => {
    if (!showControls) return null;
    
    return (
      <div className="update-controls">
        <Space>
          {status === ResourceUpdateStatus.NONE && (
            <Button 
              type="primary" 
              icon={<SyncOutlined />} 
              onClick={handleCheckUpdate}
            >
              {t('resources.update.checkUpdate')}
            </Button>
          )}
          
          {status === ResourceUpdateStatus.CHECKING && (
            <Button 
              type="primary" 
              icon={<SyncOutlined spin />} 
              disabled
            >
              {t('resources.update.checking')}
            </Button>
          )}
          
          {status === ResourceUpdateStatus.AVAILABLE && (
            <Button 
              type="primary" 
              icon={<CloudDownloadOutlined />} 
              onClick={handleDownloadUpdate}
            >
              {t('resources.update.downloadUpdate')}
            </Button>
          )}
          
          {status === ResourceUpdateStatus.DOWNLOADING && (
            <Button 
              danger
              icon={<CloseCircleOutlined />} 
              onClick={handleCancelUpdate}
            >
              {t('resources.update.cancelUpdate')}
            </Button>
          )}
          
          {status === ResourceUpdateStatus.DOWNLOADED && (
            <Button 
              type="primary" 
              icon={<CheckCircleOutlined />} 
              onClick={handleApplyUpdate}
            >
              {t('resources.update.applyUpdate')}
            </Button>
          )}
          
          {status === ResourceUpdateStatus.APPLYING && (
            <Button 
              type="primary" 
              icon={<SyncOutlined spin />} 
              disabled
            >
              {t('resources.update.applying')}
            </Button>
          )}
          
          {(status === ResourceUpdateStatus.APPLIED || status === ResourceUpdateStatus.UP_TO_DATE) && (
            <Button 
              type="primary" 
              icon={<SyncOutlined />} 
              onClick={handleCheckUpdate}
            >
              {t('resources.update.checkAgain')}
            </Button>
          )}
          
          {status === ResourceUpdateStatus.ERROR && (
            <Button 
              type="primary" 
              icon={<SyncOutlined />} 
              onClick={handleCheckUpdate}
            >
              {t('resources.update.retry')}
            </Button>
          )}
        </Space>
      </div>
    );
  };
  
  // 渲染更新详情
  const renderUpdateDetails = () => {
    if (!showDetails || !currentUpdate) return null;
    
    return (
      <div className="update-details">
        <Divider orientation="left">
          <Button 
            type="link" 
            onClick={() => setExpandedDetails(!expandedDetails)}
            icon={expandedDetails ? <InfoCircleOutlined /> : <InfoCircleOutlined />}
          >
            {t('resources.update.details')}
          </Button>
        </Divider>
        
        {expandedDetails && (
          <div className="details-content">
            <div className="detail-item">
              <Text strong>{t('resources.update.version')}:</Text>
              <Text>{currentUpdate.version}</Text>
            </div>
            
            <div className="detail-item">
              <Text strong>{t('resources.update.type')}:</Text>
              <Text>{getUpdateTypeText(currentUpdate.type)}</Text>
            </div>
            
            <div className="detail-item">
              <Text strong>{t('resources.update.size')}:</Text>
              <Text>{formatFileSize(currentUpdate.size)}</Text>
            </div>
            
            <div className="detail-item">
              <Text strong>{t('resources.update.date')}:</Text>
              <Text>{formatDate(currentUpdate.timestamp)}</Text>
            </div>
            
            <div className="detail-item">
              <Text strong>{t('resources.update.description')}:</Text>
              <Text>{currentUpdate.description}</Text>
            </div>
            
            {currentUpdate.changelogs && currentUpdate.changelogs.length > 0 && (
              <div className="detail-item changelog">
                <Text strong>{t('resources.update.changelog')}:</Text>
                <ul>
                  {currentUpdate.changelogs.map((log, index) => (
                    <li key={index}>{log}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };
  
  // 渲染更新历史
  const renderUpdateHistory = () => {
    if (!showHistory || updateHistory.length === 0) return null;
    
    return (
      <div className="update-history">
        <Divider orientation="left">{t('resources.update.history')}</Divider>
        
        <List
          dataSource={updateHistory}
          renderItem={item => (
            <List.Item
              key={item.id}
              actions={[
                item.status === ResourceUpdateStatus.APPLIED && (
                  <Button 
                    icon={<RollbackOutlined />} 
                    size="small"
                    onClick={() => handleRollbackUpdate(item.id)}
                    loading={isRollingBack && selectedHistoryItem === item.id}
                    disabled={isRollingBack}
                  >
                    {t('resources.update.rollback')}
                  </Button>
                )
              ]}
              onClick={() => setSelectedHistoryItem(item.id === selectedHistoryItem ? null : item.id)}
              className={item.id === selectedHistoryItem ? 'selected-history-item' : ''}
            >
              <List.Item.Meta
                title={
                  <Space>
                    <Text>{item.description}</Text>
                    <Tag color={getStatusColor(item.status)}>
                      {getStatusIcon(item.status)} {getStatusText(item.status)}
                    </Tag>
                  </Space>
                }
                description={
                  <Space>
                    <Text>{t('resources.update.version')}: {item.version}</Text>
                    <Text>{t('resources.update.date')}: {formatDate(item.timestamp)}</Text>
                    <Text>{t('resources.update.size')}: {formatFileSize(item.size)}</Text>
                  </Space>
                }
              />
              
              {item.id === selectedHistoryItem && item.changelogs && item.changelogs.length > 0 && (
                <div className="history-item-details">
                  <Text strong>{t('resources.update.changelog')}:</Text>
                  <ul>
                    {item.changelogs.map((log, index) => (
                      <li key={index}>{log}</li>
                    ))}
                  </ul>
                </div>
              )}
            </List.Item>
          )}
        />
      </div>
    );
  };
  
  // 渲染主要内容
  const renderContent = () => {
    // 如果没有更新状态，显示初始状态
    if (status === ResourceUpdateStatus.NONE) {
      return (
        <div className="update-initial">
          <Alert
            message={t('resources.update.noUpdateChecked')}
            description={t('resources.update.checkUpdateDesc')}
            type="info"
            showIcon
          />
          {renderControls()}
        </div>
      );
    }
    
    // 如果已是最新
    if (status === ResourceUpdateStatus.UP_TO_DATE) {
      return (
        <div className="update-up-to-date">
          <Alert
            message={t('resources.update.upToDate')}
            description={t('resources.update.upToDateDesc')}
            type="success"
            showIcon
          />
          {renderControls()}
        </div>
      );
    }
    
    // 如果有更新或正在更新
    return (
      <div className="update-progress">
        <div className="status-header">
          <Space>
            <Tag color={getStatusColor(status)}>
              {getStatusIcon(status)} {getStatusText(status)}
            </Tag>
            {currentUpdate && (
              <Text>{currentUpdate.description}</Text>
            )}
          </Space>
        </div>
        
        {(status === ResourceUpdateStatus.DOWNLOADING || status === ResourceUpdateStatus.APPLYING) && (
          <Progress 
            percent={progress} 
            status={status === ResourceUpdateStatus.ERROR ? 'exception' : 'active'} 
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068'}}
          />
        )}
        
        {renderControls()}
        {renderUpdateDetails()}
      </div>
    );
  };
  
  return (
    <div className={`resource-update-monitor ${compact ? 'compact' : ''}`}>
      <Card
        title={
          <div className="monitor-header">
            <Title level={4}>{t('resources.update.title')}</Title>
            {onClose && (
              <Button 
                type="text" 
                icon={<CloseCircleOutlined />} 
                onClick={onClose}
              />
            )}
          </div>
        }
        bordered={!compact}
      >
        {renderContent()}
        {renderUpdateHistory()}
      </Card>
    </div>
  );
};

export default ResourceUpdateProgressMonitor;
