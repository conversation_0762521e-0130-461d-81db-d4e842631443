/**
 * 水体物理状态切片
 * 用于管理水体物理相关的状态
 */
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';

/**
 * 水体物理状态接口
 */
export interface WaterBodyState {
  /** 实体ID */
  entityId: string;
  /** 是否启用 */
  enabled: boolean;
  /** 密度 */
  density: number;
  /** 粘度 */
  viscosity: number;
  /** 分辨率 */
  resolution: number;
  /** 大小 */
  size: {
    width: number;
    height: number;
    depth: number;
  };
  /** 是否启用浮力 */
  enableBuoyancy: boolean;
  /** 是否启用阻力 */
  enableDrag: boolean;
  /** 是否启用水流 */
  enableFlow: boolean;
  /** 是否启用波动 */
  enableWaves: boolean;
  /** 是否启用碰撞 */
  enableCollision: boolean;
  /** 是否启用空间分区 */
  enableSpatialPartitioning: boolean;
  /** 空间网格大小 */
  spatialGridSize: number;
  /** 是否启用自适应更新 */
  enableAdaptiveUpdate: boolean;
  /** 最小更新频率 */
  minUpdateFrequency: number;
  /** 最大更新频率 */
  maxUpdateFrequency: number;
  /** 是否启用多线程 */
  enableMultithreading: boolean;
  /** 是否启用水流冲击 */
  enableFlowImpact: boolean;
  /** 是否启用水体分裂 */
  enableWaterSplitting: boolean;
  /** 水流速度 */
  flowSpeed: number;
  /** 水流方向 */
  flowDirection: {
    x: number;
    y: number;
    z: number;
  };
  /** 波浪高度 */
  waveHeight: number;
  /** 波浪速度 */
  waveSpeed: number;
}

/**
 * 水体物理状态接口
 */
export interface WaterPhysicsState {
  /** 水体列表 */
  waterBodies: WaterBodyState[];
  /** 是否加载中 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
}

/**
 * 初始状态
 */
const initialState: WaterPhysicsState = {
  waterBodies: [],
  loading: false,
  error: null
};

/**
 * 水体物理状态切片
 */
const waterSlice = createSlice({
  name: 'waterPhysics',
  initialState,
  reducers: {
    /**
     * 设置加载状态
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    
    /**
     * 设置错误信息
     */
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    /**
     * 设置水体列表
     */
    setWaterBodies: (state, action: PayloadAction<WaterBodyState[]>) => {
      state.waterBodies = action.payload;
    },
    
    /**
     * 添加水体
     */
    addWaterBody: (state, action: PayloadAction<WaterBodyState>) => {
      state.waterBodies.push(action.payload);
    },
    
    /**
     * 更新水体
     */
    updateWaterBody: (state, action: PayloadAction<{ entityId: string; properties: Partial<WaterBodyState> }>) => {
      const { entityId, properties } = action.payload;
      const index = state.waterBodies.findIndex(wb => wb.entityId === entityId);
      
      if (index !== -1) {
        state.waterBodies[index] = {
          ...state.waterBodies[index],
          ...properties
        };
      }
    },
    
    /**
     * 删除水体
     */
    removeWaterBody: (state, action: PayloadAction<string>) => {
      state.waterBodies = state.waterBodies.filter(wb => wb.entityId !== action.payload);
    }
  },
  extraReducers: (builder) => {
    // 获取水体列表
    builder
      .addCase(fetchWaterBodies.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWaterBodies.fulfilled, (state, action) => {
        state.loading = false;
        state.waterBodies = action.payload;
      })
      .addCase(fetchWaterBodies.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 创建水体
    builder
      .addCase(createWaterBody.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createWaterBody.fulfilled, (state, action) => {
        state.loading = false;
        state.waterBodies.push(action.payload);
      })
      .addCase(createWaterBody.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 更新水体属性
    builder
      .addCase(updateWaterBodyProperties.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateWaterBodyProperties.fulfilled, (state, action) => {
        state.loading = false;
        const { entityId, properties } = action.payload;
        const index = state.waterBodies.findIndex(wb => wb.entityId === entityId);
        if (index !== -1) {
          state.waterBodies[index] = {
            ...state.waterBodies[index],
            ...properties
          };
        }
      })
      .addCase(updateWaterBodyProperties.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 删除水体
    builder
      .addCase(deleteWaterBody.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteWaterBody.fulfilled, (state, action) => {
        state.loading = false;
        state.waterBodies = state.waterBodies.filter(wb => wb.entityId !== action.payload);
      })
      .addCase(deleteWaterBody.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

// 导出动作创建器
export const {
  setLoading,
  setError,
  setWaterBodies,
  addWaterBody,
  updateWaterBody,
  removeWaterBody
} = waterSlice.actions;

/**
 * 获取水体列表
 */
export const fetchWaterBodies = createAsyncThunk(
  'waterPhysics/fetchWaterBodies',
  async (_, { rejectWithValue }) => {
    try {
      // 这里应该从引擎获取水体列表
      // const waterBodies = await api.getWaterBodies();
      // return waterBodies;

      // 临时返回空数组
      return [];
    } catch (error: any) {
      return rejectWithValue(error.message || '获取水体列表失败');
    }
  }
);

/**
 * 创建水体
 */
export const createWaterBody = createAsyncThunk(
  'waterPhysics/createWaterBody',
  async (waterBody: Omit<WaterBodyState, 'entityId'>, { rejectWithValue }) => {
    try {
      // 这里应该在引擎中创建水体
      // const createdWaterBody = await api.createWaterBody(waterBody);
      // return createdWaterBody;

      // 临时生成一个水体对象
      const createdWaterBody: WaterBodyState = {
        ...waterBody,
        entityId: `water_${Date.now()}`
      };
      return createdWaterBody;
    } catch (error: any) {
      return rejectWithValue(error.message || '创建水体失败');
    }
  }
);

/**
 * 更新水体属性
 */
export const updateWaterBodyProperties = createAsyncThunk(
  'waterPhysics/updateWaterBodyProperties',
  async ({ entityId, properties }: { entityId: string; properties: Partial<WaterBodyState> }, { rejectWithValue }) => {
    try {
      // 这里应该在引擎中更新水体属性
      // await api.updateWaterBody(entityId, properties);

      return { entityId, properties };
    } catch (error: any) {
      return rejectWithValue(error.message || '更新水体属性失败');
    }
  }
);

/**
 * 删除水体
 */
export const deleteWaterBody = createAsyncThunk(
  'waterPhysics/deleteWaterBody',
  async (entityId: string, { rejectWithValue }) => {
    try {
      // 这里应该在引擎中删除水体
      // await api.deleteWaterBody(entityId);

      return entityId;
    } catch (error: any) {
      return rejectWithValue(error.message || '删除水体失败');
    }
  }
);

export default waterSlice.reducer;
