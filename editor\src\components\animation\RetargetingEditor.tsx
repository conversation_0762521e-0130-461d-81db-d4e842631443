/**
 * 动画重定向编辑器组件
 * 用于编辑骨骼映射和重定向配置
 */
import React, { useState, useEffect, useRef } from 'react';
import { Table, Button, Select, Switch, Tabs, Space, message, Card, Row, Col } from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  SyncOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  SaveOutlined,
  ReloadOutlined,
  SettingOutlined,
  SearchOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import * as THREE from 'three';
// 移除引擎直接导入
import './AnimationEditor.less';

const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { TabPane } = Tabs;
const { Search } = Input;

/**
 * 重定向编辑器属性
 */
interface RetargetingEditorProps {
  /** 源骨骼 */
  sourceSkeleton?: THREE.Skeleton | THREE.Bone[];
  /** 目标骨骼 */
  targetSkeleton?: THREE.Skeleton | THREE.Bone[];
  /** 骨骼映射 */
  boneMapping: BoneMapping[];
  /** 重定向配置 */
  config: RetargetingConfig;
  /** 更新回调 */
  onUpdate: (boneMapping: BoneMapping[], config: RetargetingConfig) => void;
  /** 预览回调 */
  onPreview?: () => void;
  /** 是否显示预览 */
  showPreview?: boolean;
}

/**
 * 动画重定向编辑器组件
 */
const RetargetingEditor: React.FC<RetargetingEditorProps> = ({
  sourceSkeleton,
  targetSkeleton,
  boneMapping,
  config,
  onUpdate,
  onPreview,
  showPreview = true}) => {
  const { t } = useTranslation();
  const [localBoneMapping, setLocalBoneMapping] = useState<BoneMapping[]>([]);
  const [localConfig, setLocalConfig] = useState<RetargetingConfig>({
    boneMapping: [],
    preservePositionTracks: true,
    preserveScaleTracks: false,
    normalizeRotations: true,
    adjustRootHeight: true,
    adjustBoneLength: true});
  const [searchText, setSearchText] = useState('');
  const [selectedSourceBone, setSelectedSourceBone] = useState<string>('');
  const [selectedTargetBone, setSelectedTargetBone] = useState<string>('');
  const [sourceBones, setSourceBones] = useState<string[]>([]);
  const [targetBones, setTargetBones] = useState<string[]>([]);
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const [showBoneNames, setShowBoneNames] = useState(true);

  // 初始化
  useEffect(() => {
    setLocalBoneMapping([...boneMapping]);
    setLocalConfig({
      ...config,
      boneMapping: [...boneMapping]});

    // 提取骨骼名称
    if (sourceSkeleton) {
      const bones = extractBoneNames(sourceSkeleton);
      setSourceBones(bones);
    }

    if (targetSkeleton) {
      const bones = extractBoneNames(targetSkeleton);
      setTargetBones(bones);
    }
  }, [sourceSkeleton, targetSkeleton, boneMapping, config]);

  // 提取骨骼名称
  const extractBoneNames = (skeleton: THREE.Skeleton | THREE.Bone[]): string[] => {
    const bones = Array.isArray(skeleton) ? skeleton : skeleton.bones;
    return bones.map(bone => bone.name);
  };

  // 更新骨骼映射
  const updateBoneMapping = (newMapping: BoneMapping[]) => {
    setLocalBoneMapping(newMapping);
    onUpdate(newMapping, localConfig);
  };

  // 更新配置
  const updateConfig = (newConfig: Partial<RetargetingConfig>) => {
    const updatedConfig = { ...localConfig, ...newConfig };
    setLocalConfig(updatedConfig);
    onUpdate(localBoneMapping, updatedConfig);
  };

  // 添加骨骼映射
  const handleAddBoneMapping = () => {
    if (!selectedSourceBone || !selectedTargetBone) {
      message.warning(t('editor.animation.retargeting.selectBones'));
      return;
    }

    // 检查是否已存在
    const exists = localBoneMapping.some(
      mapping => mapping.source === selectedSourceBone
    );

    if (exists) {
      message.warning(t('editor.animation.retargeting.mappingExists'));
      return;
    }

    const newMapping = [...localBoneMapping, { source: selectedSourceBone, target: selectedTargetBone }];
    updateBoneMapping(newMapping);
    setSelectedSourceBone('');
    setSelectedTargetBone('');
  };

  // 删除骨骼映射
  const handleDeleteBoneMapping = (source: string) => {
    const newMapping = localBoneMapping.filter(mapping => mapping.source !== source);
    updateBoneMapping(newMapping);
  };

  // 更新骨骼映射目标
  const handleUpdateBoneMapping = (source: string, target: string) => {
    const newMapping = localBoneMapping.map(mapping => {
      if (mapping.source === source) {
        return { ...mapping, target };
      }
      return mapping;
    });
    updateBoneMapping(newMapping);
  };

  // 自动创建骨骼映射
  const handleAutoCreateMapping = () => {
    if (!sourceBones.length || !targetBones.length) {
      message.warning(t('editor.animation.retargeting.noSkeletons'));
      return;
    }

    // 尝试匹配相同名称的骨骼
    const newMapping: BoneMapping[] = [];
    
    for (const sourceBone of sourceBones) {
      // 如果已经有映射，则跳过
      const exists = localBoneMapping.some(mapping => mapping.source === sourceBone);
      if (exists) continue;

      // 查找相同名称的目标骨骼
      const targetBone = targetBones.find(bone => bone === sourceBone);
      if (targetBone) {
        newMapping.push({ source: sourceBone, target: targetBone });
      }
    }

    if (newMapping.length === 0) {
      message.info(t('editor.animation.retargeting.noAutoMapping'));
      return;
    }

    updateBoneMapping([...localBoneMapping, ...newMapping]);
    message.success(t('editor.animation.retargeting.autoMappingSuccess', { count: newMapping.length }));
  };

  // 清除所有映射
  const handleClearMapping = () => {
    updateBoneMapping([]);
  };

  // 过滤骨骼映射
  const filterBoneMapping = () => {
    if (!searchText) return localBoneMapping;
    
    return localBoneMapping.filter(
      mapping => 
        mapping.source.toLowerCase().includes(searchText.toLowerCase()) ||
        mapping.target.toLowerCase().includes(searchText.toLowerCase())
    );
  };

  // 表格列定义
  const columns = [
    {
      title: t('editor.animation.retargeting.sourceBone'),
      dataIndex: 'source',
      key: 'source',
      width: '40%'},
    {
      title: t('editor.animation.retargeting.targetBone'),
      dataIndex: 'target',
      key: 'target',
      width: '40%',
      render: (text: string, record: BoneMapping) => (
        <Select
          style={{ width: '100%' }}
          value={text}
          onChange={(value) => handleUpdateBoneMapping(record.source, value)}
          showSearch
          filterOption={(input, option) =>
            (option?.children as unknown as string).toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {targetBones.map(bone => (
            <Option key={bone} value={bone}>{bone}</Option>
          ))}
        </Select>
      )},
    {
      title: t('editor.animation.retargeting.actions'),
      key: 'actions',
      width: '20%',
      render: (_: any, record: BoneMapping) => (
        <Button
          icon={<DeleteOutlined />}
          danger
          size="small"
          onClick={() => handleDeleteBoneMapping(record.source)}
        />
      )},
  ];

  return (
    <div className="retargeting-editor">
      <Tabs defaultActiveKey="mapping">
        <TabPane tab={t('editor.animation.retargeting.boneMapping')} key="mapping">
          <div className="bone-mapping-tools">
            <Space style={{ marginBottom: '16px' }}>
              <Button
                icon={<SyncOutlined />}
                onClick={handleAutoCreateMapping}
                disabled={!sourceBones.length || !targetBones.length}
              >
                {t('editor.animation.retargeting.autoMap')}
              </Button>
              <Button
                icon={<DeleteOutlined />}
                danger
                onClick={handleClearMapping}
                disabled={!localBoneMapping.length}
              >
                {t('editor.animation.retargeting.clearAll')}
              </Button>
              <Search
                placeholder={t('editor.animation.retargeting.searchMapping')}
                onSearch={setSearchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 200 }}
              />
            </Space>

            <div className="add-mapping-form" style={{ marginBottom: '16px' }}>
              <Row gutter={8}>
                <Col span={10}>
                  <Select
                    style={{ width: '100%' }}
                    value={selectedSourceBone}
                    onChange={setSelectedSourceBone}
                    showSearch
                    placeholder={t('editor.animation.retargeting.selectSourceBone')}
                    filterOption={(input, option) =>
                      (option?.children as unknown as string).toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {sourceBones.map(bone => (
                      <Option key={bone} value={bone}>{bone}</Option>
                    ))}
                  </Select>
                </Col>
                <Col span={10}>
                  <Select
                    style={{ width: '100%' }}
                    value={selectedTargetBone}
                    onChange={setSelectedTargetBone}
                    showSearch
                    placeholder={t('editor.animation.retargeting.selectTargetBone')}
                    filterOption={(input, option) =>
                      (option?.children as unknown as string).toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {targetBones.map(bone => (
                      <Option key={bone} value={bone}>{bone}</Option>
                    ))}
                  </Select>
                </Col>
                <Col span={4}>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAddBoneMapping}
                    disabled={!selectedSourceBone || !selectedTargetBone}
                    style={{ width: '100%' }}
                  >
                    {t('editor.animation.retargeting.add')}
                  </Button>
                </Col>
              </Row>
            </div>

            <Table
              dataSource={filterBoneMapping()}
              columns={columns}
              rowKey="source"
              pagination={false}
              size="small"
              scroll={{ y: 300 }}
            />
          </div>
        </TabPane>

        <TabPane tab={t('editor.animation.retargeting.settings')} key="settings">
          <Card title={t('editor.animation.retargeting.retargetingOptions')} size="small">
            <div className="retargeting-options">
              <div className="option-item">
                <span>{t('editor.animation.retargeting.preservePositionTracks')}</span>
                <Switch
                  checked={localConfig.preservePositionTracks}
                  onChange={(checked) => updateConfig({ preservePositionTracks: checked })}
                />
              </div>
              <div className="option-item">
                <span>{t('editor.animation.retargeting.preserveScaleTracks')}</span>
                <Switch
                  checked={localConfig.preserveScaleTracks}
                  onChange={(checked) => updateConfig({ preserveScaleTracks: checked })}
                />
              </div>
              <div className="option-item">
                <span>{t('editor.animation.retargeting.normalizeRotations')}</span>
                <Switch
                  checked={localConfig.normalizeRotations}
                  onChange={(checked) => updateConfig({ normalizeRotations: checked })}
                />
              </div>
              <div className="option-item">
                <span>{t('editor.animation.retargeting.adjustRootHeight')}</span>
                <Switch
                  checked={localConfig.adjustRootHeight}
                  onChange={(checked) => updateConfig({ adjustRootHeight: checked })}
                />
              </div>
              <div className="option-item">
                <span>{t('editor.animation.retargeting.adjustBoneLength')}</span>
                <Switch
                  checked={localConfig.adjustBoneLength}
                  onChange={(checked) => updateConfig({ adjustBoneLength: checked })}
                />
              </div>
            </div>
          </Card>
        </TabPane>

        {showPreview && (
          <TabPane tab={t('editor.animation.retargeting.preview')} key="preview">
            <div className="preview-container">
              <canvas
                ref={previewCanvasRef}
                width={400}
                height={300}
                style={{ background: '#1e1e1e', borderRadius: '4px' }}
              />
              <div className="preview-controls">
                <Space>
                  <Button icon={<ReloadOutlined />} onClick={onPreview} size="small">
                    {t('editor.animation.retargeting.resetView')}
                  </Button>
                  <Switch
                    checkedChildren={<EyeOutlined />}
                    unCheckedChildren={<EyeInvisibleOutlined />}
                    checked={showBoneNames}
                    onChange={setShowBoneNames}
                  />
                </Space>
              </div>
            </div>
          </TabPane>
        )}
      </Tabs>
    </div>
  );
};

export default RetargetingEditor;
