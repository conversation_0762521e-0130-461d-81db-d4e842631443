/**
 * 工具栏组件
 */
import React from 'react';
import { Menu, Dropdown, But<PERSON>, Space, Divider, Tooltip } from 'antd';
import {
  FileOutlined,
  EditOutlined,
  EyeOutlined,
  ToolOutlined,
  QuestionOutlined,
  PlusOutlined,
  FolderOpenOutlined,
  SaveOutlined,
  ExportOutlined,
  ImportOutlined,
  SettingOutlined,
  UndoOutlined,
  RedoOutlined,
  CopyOutlined,
  ScissorOutlined,
  SnippetsOutlined,
  DeleteOutlinedOutlined,
  ArrowsAltOutlined,
  RotateRightOutlined,
  ColumnWidthOutlined,
  BorderOutlined,
  AppstoreOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setTransformMode,
  setTransformSpace,
  setSnapMode,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  TransformSpace,
  SnapMode,
  undo,
  redo} from '../../store/editor/editorSlice';
import { openDialog, DialogType } from '../../store/ui/uiSlice';

const Toolbar: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  
  const {
    transformMode,
    transformSpace,
    snapMode,
    showGrid,
    showAxes,
    isPlaying} = useAppSelector((state) => state.editor);
  
  // 文件菜单
  const fileMenu = (
    <Menu>
      <Menu.Item key="new" icon={<PlusOutlined />} onClick={() => dispatch(openDialog({ type: DialogType.NEW_PROJECT, title: t('editor.newProject') }))}>
        {t('editor.newProject')}
      </Menu.Item>
      <Menu.Item key="open" icon={<FolderOpenOutlined />} onClick={() => dispatch(openDialog({ type: DialogType.OPEN_PROJECT, title: t('editor.openProject') }))}>
        {t('editor.openProject')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="save" icon={<SaveOutlined />}>
        {t('editor.saveProject')}
      </Menu.Item>
      <Menu.Item key="saveAs" icon={<SaveOutlined />} onClick={() => dispatch(openDialog({ type: DialogType.SAVE_PROJECT_AS, title: t('editor.saveProjectAs') }))}>
        {t('editor.saveProjectAs')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="import" icon={<ImportOutlined />} onClick={() => dispatch(openDialog({ type: DialogType.IMPORT_ASSET, title: t('editor.importAsset') }))}>
        {t('editor.importAsset')}
      </Menu.Item>
      <Menu.Item key="export" icon={<ExportOutlined />} onClick={() => dispatch(openDialog({ type: DialogType.EXPORT_SCENE, title: t('editor.exportScene') }))}>
        {t('editor.exportScene')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="settings" icon={<SettingOutlined />} onClick={() => dispatch(openDialog({ type: DialogType.PROJECT_SETTINGS, title: t('editor.projectSettings') }))}>
        {t('editor.projectSettings')}
      </Menu.Item>
    </Menu>
  );
  
  // 编辑菜单
  const editMenu = (
    <Menu>
      <Menu.Item key="undo" icon={<UndoOutlined />} onClick={() => dispatch(undo())}>
        {t('editor.undo')}
      </Menu.Item>
      <Menu.Item key="redo" icon={<RedoOutlined />} onClick={() => dispatch(redo())}>
        {t('editor.redo')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="cut" icon={<ScissorOutlined />}>
        {t('editor.cut')}
      </Menu.Item>
      <Menu.Item key="copy" icon={<CopyOutlined />}>
        {t('editor.copy')}
      </Menu.Item>
      <Menu.Item key="paste" icon={<SnippetsOutlined />}>
        {t('editor.paste')}
      </Menu.Item>
      <Menu.Item key="delete" icon={<DeleteOutlined />}>
        {t('editor.delete')}
      </Menu.Item>
    </Menu>
  );
  
  // 视图菜单
  const viewMenu = (
    <Menu>
      <Menu.Item key="grid" icon={<BorderOutlined />} onClick={() => dispatch(setShowGrid(!showGrid))}>
        {showGrid ? t('editor.hideGrid') : t('editor.showGrid')}
      </Menu.Item>
      <Menu.Item key="axes" icon={<AppstoreOutlined />} onClick={() => dispatch(setShowAxes(!showAxes))}>
        {showAxes ? t('editor.hideAxes') : t('editor.showAxes')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="sceneView" icon={<EyeOutlined />}>
        {t('editor.sceneView')}
      </Menu.Item>
      <Menu.Item key="gameView" icon={<PlayCircleOutlined />}>
        {t('editor.gameView')}
      </Menu.Item>
    </Menu>
  );
  
  // 工具菜单
  const toolsMenu = (
    <Menu>
      <Menu.Item key="select" icon={<SelectOutlined />}>
        {t('editor.selectTool')}
      </Menu.Item>
      <Menu.Item
        key="translate"
        icon={<ArrowsAltOutlined />}
        onClick={() => dispatch(setTransformMode(TransformMode.TRANSLATE))}
      >
        {t('editor.translateTool')}
      </Menu.Item>
      <Menu.Item
        key="rotate"
        icon={<RotateRightOutlined />}
        onClick={() => dispatch(setTransformMode(TransformMode.ROTATE))}
      >
        {t('editor.rotateTool')}
      </Menu.Item>
      <Menu.Item
        key="scale"
        icon={<ColumnWidthOutlined />}
        onClick={() => dispatch(setTransformMode(TransformMode.SCALE))}
      >
        {t('editor.scaleTool')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.SubMenu key="transformSpace" title={t('editor.transformSpace')}>
        <Menu.Item
          key="local"
          onClick={() => dispatch(setTransformSpace(TransformSpace.LOCAL))}
        >
          {t('editor.localSpace')}
        </Menu.Item>
        <Menu.Item
          key="world"
          onClick={() => dispatch(setTransformSpace(TransformSpace.WORLD))}
        >
          {t('editor.worldSpace')}
        </Menu.Item>
      </Menu.SubMenu>
      <Menu.SubMenu key="snapMode" title={t('editor.snapMode')}>
        <Menu.Item
          key="disabled"
          onClick={() => dispatch(setSnapMode(SnapMode.DISABLED))}
        >
          {t('editor.snapDisabled')}
        </Menu.Item>
        <Menu.Item
          key="grid"
          onClick={() => dispatch(setSnapMode(SnapMode.GRID))}
        >
          {t('editor.snapToGrid')}
        </Menu.Item>
        <Menu.Item
          key="vertex"
          onClick={() => dispatch(setSnapMode(SnapMode.VERTEX))}
        >
          {t('editor.snapToVertex')}
        </Menu.Item>
      </Menu.SubMenu>
    </Menu>
  );
  
  // 帮助菜单
  const helpMenu = (
    <Menu>
      <Menu.Item key="documentation">
        {t('editor.documentation')}
      </Menu.Item>
      <Menu.Item key="tutorials">
        {t('editor.tutorials')}
      </Menu.Item>
      <Menu.Item key="about">
        {t('editor.about')}
      </Menu.Item>
    </Menu>
  );
  
  return (
    <div style={{ display: 'flex', justifyContent: 'space-between', padding: '0 16px' }}>
      <div>
        <Space size="small">
          <Dropdown overlay={fileMenu} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.file')}
            </Button>
          </Dropdown>
          <Dropdown overlay={editMenu} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.edit')}
            </Button>
          </Dropdown>
          <Dropdown overlay={viewMenu} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.view')}
            </Button>
          </Dropdown>
          <Dropdown overlay={toolsMenu} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.tools')}
            </Button>
          </Dropdown>
          <Dropdown overlay={helpMenu} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.help')}
            </Button>
          </Dropdown>
        </Space>
      </div>
      <div>
        <Space size="small">
          <Tooltip title={t('editor.undo')}>
            <Button type="text" icon={<UndoOutlined />} style={{ color: '#fff' }} onClick={() => dispatch(undo())} />
          </Tooltip>
          <Tooltip title={t('editor.redo')}>
            <Button type="text" icon={<RedoOutlined />} style={{ color: '#fff' }} onClick={() => dispatch(redo())} />
          </Tooltip>
          <Divider type="vertical" style={{ backgroundColor: '#444', height: 20, margin: '0 8px' }} />
          <Tooltip title={isPlaying ? t('editor.pause') : t('editor.play')}>
            <Button
              type="text"
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              style={{ color: '#fff' }}
              onClick={() => dispatch(setIsPlaying(!isPlaying))}
            />
          </Tooltip>
        </Space>
      </div>
    </div>
  );
};

export default Toolbar;
