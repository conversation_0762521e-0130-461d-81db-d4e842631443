/**
 * 资源版本状态管理Slice
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../index';

// 定义资源版本接口
export interface ResourceVersion {
  id: string;
  resourceId: string;
  version: string;
  timestamp: number;
  author: string;
  description?: string;
  size: number;
  checksum: string;
  metadata?: Record<string, any>;
}

// 定义版本比较结果接口
export interface VersionComparisonResult {
  version1: ResourceVersion;
  version2: ResourceVersion;
  differences: VersionDifference[];
  similarity: number;
}

// 定义版本差异接口
export interface VersionDifference {
  type: 'added' | 'removed' | 'modified';
  path: string;
  oldValue?: any;
  newValue?: any;
}

// 资源版本状态接口
export interface ResourceVersionState {
  versions: ResourceVersion[];
  currentVersionId: string | null;
  showVersionPanel: boolean;
  isRollingBack: boolean;
  comparisonResult: VersionComparisonResult | null;
  showComparePanel: boolean;
  currentResourceId: string | null;
}

// 初始状态
const initialState: ResourceVersionState = {
  versions: [],
  currentVersionId: null,
  showVersionPanel: false,
  isRollingBack: false,
  comparisonResult: null,
  showComparePanel: false,
  currentResourceId: null
};

// 创建Slice
export const resourceVersionSlice = createSlice({
  name: 'resourceVersion',
  initialState,
  reducers: {
    // 添加资源版本
    addResourceVersion: (state, action: PayloadAction<ResourceVersion>) => {
      // 检查是否已存在相同ID的版本
      const existingIndex = state.versions.findIndex((v: ResourceVersion) => v.id === action.payload.id);

      if (existingIndex >= 0) {
        // 更新现有版本
        state.versions[existingIndex] = action.payload;
      } else {
        // 添加新版本
        state.versions.push(action.payload);
      }
    },
    
    // 设置资源版本列表
    setResourceVersions: (state, action: PayloadAction<ResourceVersion[]>) => {
      state.versions = action.payload;
    },
    
    // 设置当前版本
    setCurrentResourceVersion: (state, action: PayloadAction<string | null>) => {
      state.currentVersionId = action.payload;
    },
    
    // 设置当前资源ID
    setCurrentResourceId: (state, action: PayloadAction<string | null>) => {
      state.currentResourceId = action.payload;
    },
    
    // 显示/隐藏版本面板
    setShowResourceVersionPanel: (state, action: PayloadAction<boolean>) => {
      state.showVersionPanel = action.payload;
    },
    
    // 设置是否正在回滚
    setIsRollingBack: (state, action: PayloadAction<boolean>) => {
      state.isRollingBack = action.payload;
    },
    
    // 设置比较结果
    setComparisonResult: (state, action: PayloadAction<VersionComparisonResult | null>) => {
      state.comparisonResult = action.payload;
      
      // 如果有比较结果，自动显示比较面板
      if (action.payload) {
        state.showComparePanel = true;
      }
    },
    
    // 显示/隐藏比较面板
    setShowComparePanel: (state, action: PayloadAction<boolean>) => {
      state.showComparePanel = action.payload;
      
      // 如果隐藏面板，清除比较结果
      if (!action.payload) {
        state.comparisonResult = null;
      }
    },
    
    // 清除所有版本
    clearResourceVersions: (state) => {
      state.versions = [];
      state.currentVersionId = null;
      state.comparisonResult = null;
    },
    
    // 删除版本
    deleteResourceVersion: (state, action: PayloadAction<string>) => {
      state.versions = state.versions.filter((v: ResourceVersion) => v.id !== action.payload);

      // 如果删除的是当前版本，清除当前版本ID
      if (state.currentVersionId === action.payload) {
        state.currentVersionId = null;
      }

      // 如果删除的是比较结果中的版本，清除比较结果
      if (state.comparisonResult &&
          (state.comparisonResult.version1.id === action.payload ||
           state.comparisonResult.version2.id === action.payload)) {
        state.comparisonResult = null;
        state.showComparePanel = false;
      }
    }
  }
});

// 导出Actions
export const {
  addResourceVersion,
  setResourceVersions,
  setCurrentResourceVersion,
  setCurrentResourceId,
  setShowResourceVersionPanel,
  setIsRollingBack,
  setComparisonResult,
  setShowComparePanel,
  clearResourceVersions,
  deleteResourceVersion
} = resourceVersionSlice.actions;

// 选择器
export const selectResourceVersions = (state: RootState) => state.resourceVersion.versions;
export const selectCurrentVersionId = (state: RootState) => state.resourceVersion.currentVersionId;
export const selectCurrentVersion = (state: RootState) => {
  const id = state.resourceVersion.currentVersionId;
  return id ? state.resourceVersion.versions.find((v: ResourceVersion) => v.id === id) || null : null;
};
export const selectShowVersionPanel = (state: RootState) => state.resourceVersion.showVersionPanel;
export const selectIsRollingBack = (state: RootState) => state.resourceVersion.isRollingBack;
export const selectComparisonResult = (state: RootState) => state.resourceVersion.comparisonResult;
export const selectShowComparePanel = (state: RootState) => state.resourceVersion.showComparePanel;
export const selectCurrentResourceId = (state: RootState) => state.resourceVersion.currentResourceId;

// 导出Reducer
export default resourceVersionSlice.reducer;
