/**
 * WebRTC数据通道
 * 用于WebRTC点对点数据传输
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';

export interface WebRTCDataChannelOptions {
  /** 通道标签 */
  label: string;
  /** 是否有序 */
  ordered?: boolean;
  /** 最大重传次数 */
  maxRetransmits?: number;
  /** 最大重传时间 */
  maxRetransmitTime?: number;
  /** 协议 */
  protocol?: string;
  /** 是否协商 */
  negotiated?: boolean;
  /** 通道ID */
  id?: number;
}

/**
 * WebRTC数据通道事件
 */
export interface WebRTCDataChannelEvents {
  /** 通道打开事件 */
  open: () => void;
  /** 通道关闭事件 */
  close: () => void;
  /** 通道错误事件 */
  error: (error: Event) => void;
  /** 消息接收事件 */
  message: (data: any) => void;
}

export class WebRTCDataChannel extends EventEmitter {
  private channel: RTCDataChannel;
  private _options: WebRTCDataChannelOptions;
  private isDestroyed: boolean = false;

  constructor(channel: RTCDataChannel, options: WebRTCDataChannelOptions) {
    super();
    this.channel = channel;
    this._options = options;
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.channel.onopen = () => {
      Debug.log('WebRTCDataChannel', `数据通道 ${this._options.label} 已打开`);
      this.emit('open');
    };

    this.channel.onclose = () => {
      Debug.log('WebRTCDataChannel', `数据通道 ${this._options.label} 已关闭`);
      this.emit('close');
    };

    this.channel.onerror = (error) => {
      Debug.error('WebRTCDataChannel', `数据通道 ${this._options.label} 错误:`, error);
      this.emit('error', error);
    };

    this.channel.onmessage = (event) => {
      this.handleMessage(event.data);
    };
  }

  private handleMessage(data: any): void {
    // 处理接收到的消息
    Debug.log('WebRTCDataChannel', `收到消息:`, data);
    this.emit('message', data);
  }

  public send(data: string | ArrayBuffer | Blob): void {
    if (this.isDestroyed) {
      Debug.warn('WebRTCDataChannel', '数据通道已销毁，无法发送消息');
      return;
    }

    if (this.channel.readyState === 'open') {
      try {
        // 根据数据类型进行适当的发送
        if (typeof data === 'string') {
          this.channel.send(data);
        } else if (data instanceof ArrayBuffer) {
          this.channel.send(data);
        } else if (data instanceof Blob) {
          this.channel.send(data);
        } else {
          Debug.warn('WebRTCDataChannel', '不支持的数据类型');
        }
      } catch (error) {
        Debug.error('WebRTCDataChannel', '发送消息失败:', error);
        this.emit('error', error as Event);
      }
    } else {
      Debug.warn('WebRTCDataChannel', `数据通道未打开，当前状态: ${this.channel.readyState}`);
    }
  }

  public close(): void {
    if (this.isDestroyed) {
      return;
    }

    try {
      this.channel.close();
      Debug.log('WebRTCDataChannel', `数据通道 ${this._options.label} 已关闭`);
    } catch (error) {
      Debug.error('WebRTCDataChannel', '关闭数据通道失败:', error);
    }
  }

  /**
   * 销毁数据通道
   */
  public destroy(): void {
    if (this.isDestroyed) {
      return;
    }

    this.isDestroyed = true;

    // 移除所有事件监听器
    this.removeAllListeners();

    // 关闭通道
    this.close();

    Debug.log('WebRTCDataChannel', `数据通道 ${this._options.label} 已销毁`);
  }

  /**
   * 检查通道是否已销毁
   */
  public get destroyed(): boolean {
    return this.isDestroyed;
  }

  /**
   * 获取通道状态
   */
  public get readyState(): RTCDataChannelState {
    return this.channel.readyState;
  }

  /**
   * 获取通道标签
   */
  public get label(): string {
    return this.channel.label;
  }

  /**
   * 获取通道ID
   */
  public get id(): number | null {
    return this.channel.id;
  }

  /**
   * 获取通道协议
   */
  public get protocol(): string {
    return this.channel.protocol;
  }

  /**
   * 获取通道配置选项
   */
  public get options(): WebRTCDataChannelOptions {
    return { ...this._options };
  }

  /**
   * 获取缓冲数据量
   */
  public get bufferedAmount(): number {
    return this.channel.bufferedAmount;
  }

  /**
   * 获取缓冲数据量低水位线
   */
  public get bufferedAmountLowThreshold(): number {
    return this.channel.bufferedAmountLowThreshold;
  }

  /**
   * 设置缓冲数据量低水位线
   */
  public set bufferedAmountLowThreshold(threshold: number) {
    this.channel.bufferedAmountLowThreshold = threshold;
  }

  /**
   * 检查通道是否已连接
   */
  public get isConnected(): boolean {
    return this.channel.readyState === 'open';
  }

  /**
   * 检查通道是否正在连接
   */
  public get isConnecting(): boolean {
    return this.channel.readyState === 'connecting';
  }

  /**
   * 检查通道是否已关闭
   */
  public get isClosed(): boolean {
    return this.channel.readyState === 'closed' || this.channel.readyState === 'closing';
  }
}
