/**
 * 分块场景加载面板组件
 * 用于配置和监控分块场景加载系统
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, InputNumber, Button, Tooltip, Space, Tag, Collapse, Progress, Statistic, Table} from 'antd';
import { useTranslation } from 'react-i18next';
import {
  ReloadOutlined,
  SettingOutlined,
  EyeOutlined,
  LineChartOutlined,
  ThunderboltOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  BarChartOutlined,
  PlusOutlined,
  DeleteOutlined,
  PartitionOutlined,
  CloudDownloadOutlined
} from '@ant-design/icons';
// 移除引擎直接导入
// 移除引擎直接导入
import { EngineService } from '../../services/EngineService';
import { SceneService } from '../../services/SceneService';
import './ChunkedSceneLoadingPanel.less';

const { Panel } = Collapse;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';

/**
 * 分块场景加载面板组件
 */
const ChunkedSceneLoadingPanel: React.FC = () => {
  const { t } = useTranslation();
  
  // 状态
  const [isEnabled, setIsEnabled] = useState(true);
  const [useOctree, setUseOctree] = useState(true);
  const [useLOD, setUseLOD] = useState(true);
  const [usePredictiveLoading, setUsePredictiveLoading] = useState(true);
  const [useProgressiveLoading, setUseProgressiveLoading] = useState(true);
  const [useMemoryManagement, setUseMemoryManagement] = useState(true);
  const [useDebugVisualization, setUseDebugVisualization] = useState(false);
  const [chunkSize, setChunkSize] = useState(100);
  const [maxChunkLevel, setMaxChunkLevel] = useState(4);
  const [loadDistance, setLoadDistance] = useState(200);
  const [unloadDistance, setUnloadDistance] = useState(300);
  const [preloadDistance, setPreloadDistance] = useState(250);
  const [maxMemoryUsage, setMaxMemoryUsage] = useState(1024);
  const [maxConcurrentLoads, setMaxConcurrentLoads] = useState(5);
  const [chunks, setChunks] = useState<SceneChunk[]>([]);
  const [loadedChunks, setLoadedChunks] = useState(0);
  const [totalChunks, setTotalChunks] = useState(0);
  const [memoryUsage, setMemoryUsage] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);
  const [loadingSystem, setLoadingSystem] = useState<ChunkedSceneLoadingSystem | null>(null);
  
  // 引用
  const timerRef = useRef<number | null>(null);
  
  // 初始化
  useEffect(() => {
    // 获取引擎
    const engine = EngineService.getInstance().getEngine();
    if (!engine) {
      return;
    }
    
    // 获取分块场景加载系统
    let system = engine.getSystem('ChunkedSceneLoadingSystem') as ChunkedSceneLoadingSystem;
    
    // 如果系统不存在，则创建
    if (!system) {
      system = new ChunkedSceneLoadingSystem({
        enabled: isEnabled,
        useOctree,
        useLOD,
        usePredictiveLoading,
        useProgressiveLoading,
        useMemoryManagement,
        useDebugVisualization,
        chunkSize,
        maxChunkLevel,
        loadDistance,
        unloadDistance,
        preloadDistance,
        maxMemoryUsage,
        maxConcurrentLoads
      });
      
      // 添加到引擎
      engine.addSystem(system);
    }
    
    // 设置系统
    setLoadingSystem(system);
    
    // 更新状态
    setIsEnabled(system.isEnabled());
    
    // 加载块信息
    loadChunks();
    
    // 添加事件监听器
    system.addEventListener(ChunkedSceneLoadingSystemEventType.CHUNK_LOAD_COMPLETE, handleChunkLoadComplete);
    system.addEventListener(ChunkedSceneLoadingSystemEventType.CHUNK_UNLOAD, handleChunkUnload);
    
    // 设置定时器定期更新块信息
    timerRef.current = window.setInterval(() => {
      loadChunks();
    }, 2000);
    
    setIsInitialized(true);
    
    // 清理函数
    return () => {
      // 移除事件监听器
      if (system) {
        system.removeEventListener(ChunkedSceneLoadingSystemEventType.CHUNK_LOAD_COMPLETE, handleChunkLoadComplete);
        system.removeEventListener(ChunkedSceneLoadingSystemEventType.CHUNK_UNLOAD, handleChunkUnload);
      }
      
      // 清除定时器
      if (timerRef.current !== null) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);
  
  // 加载块信息
  const loadChunks = () => {
    if (!loadingSystem) {
      return;
    }
    
    // 获取所有块
    const allChunks = loadingSystem.getAllChunks();
    setChunks(allChunks);
    
    // 计算已加载块数量
    const loaded = allChunks.filter(chunk => chunk.loaded).length;
    setLoadedChunks(loaded);
    setTotalChunks(allChunks.length);
    
    // 计算内存使用量
    // 这里应该从系统获取实际的内存使用量
    // 这只是一个示例实现
    const memory = (loadingSystem as any).currentMemoryUsage || 0;
    setMemoryUsage(memory);
  };
  
  // 处理块加载完成事件
  const handleChunkLoadComplete = (chunk: SceneChunk) => {
    loadChunks();
  };
  
  // 处理块卸载事件
  const handleChunkUnload = (chunk: SceneChunk) => {
    loadChunks();
  };
  
  // 启用/禁用系统
  const handleEnableChange = (checked: boolean) => {
    if (loadingSystem) {
      loadingSystem.setEnabled(checked);
      setIsEnabled(checked);
    }
  };
  
  // 启用/禁用八叉树
  const handleOctreeChange = (checked: boolean) => {
    setUseOctree(checked);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).useOctree = checked;
    }
  };
  
  // 启用/禁用LOD
  const handleLODChange = (checked: boolean) => {
    setUseLOD(checked);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).useLOD = checked;
    }
  };
  
  // 启用/禁用预测加载
  const handlePredictiveLoadingChange = (checked: boolean) => {
    setUsePredictiveLoading(checked);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).usePredictiveLoading = checked;
    }
  };
  
  // 启用/禁用渐进式加载
  const handleProgressiveLoadingChange = (checked: boolean) => {
    setUseProgressiveLoading(checked);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).useProgressiveLoading = checked;
    }
  };
  
  // 启用/禁用内存管理
  const handleMemoryManagementChange = (checked: boolean) => {
    setUseMemoryManagement(checked);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).useMemoryManagement = checked;
    }
  };
  
  // 启用/禁用调试可视化
  const handleDebugVisualizationChange = (checked: boolean) => {
    setUseDebugVisualization(checked);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).useDebugVisualization = checked;
    }
  };
  
  // 设置块大小
  const handleChunkSizeChange = (value: number) => {
    setChunkSize(value);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).chunkSize = value;
    }
  };
  
  // 设置最大块级别
  const handleMaxChunkLevelChange = (value: number) => {
    setMaxChunkLevel(value);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).maxChunkLevel = value;
    }
  };
  
  // 设置加载距离
  const handleLoadDistanceChange = (value: number) => {
    setLoadDistance(value);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).loadDistance = value;
    }
  };
  
  // 设置卸载距离
  const handleUnloadDistanceChange = (value: number) => {
    setUnloadDistance(value);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).unloadDistance = value;
    }
  };
  
  // 设置预加载距离
  const handlePreloadDistanceChange = (value: number) => {
    setPreloadDistance(value);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).preloadDistance = value;
    }
  };
  
  // 设置最大内存使用量
  const handleMaxMemoryUsageChange = (value: number) => {
    setMaxMemoryUsage(value);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).maxMemoryUsage = value;
    }
  };
  
  // 设置最大并发加载数
  const handleMaxConcurrentLoadsChange = (value: number) => {
    setMaxConcurrentLoads(value);
    if (loadingSystem) {
      // 假设有这个方法
      (loadingSystem as any).maxConcurrentLoads = value;
    }
  };
  
  // 加载块
  const handleLoadChunk = (chunkId: string) => {
    if (!loadingSystem) {
      return;
    }
    
    const chunk = loadingSystem.getChunk(chunkId);
    if (chunk) {
      // 假设有这个方法
      (loadingSystem as any).loadChunk(chunk);
    }
  };
  
  // 卸载块
  const handleUnloadChunk = (chunkId: string) => {
    if (!loadingSystem) {
      return;
    }
    
    const chunk = loadingSystem.getChunk(chunkId);
    if (chunk) {
      // 假设有这个方法
      (loadingSystem as any).unloadChunk(chunk);
    }
  };
  
  // 创建块
  const handleCreateChunk = () => {
    if (!loadingSystem) {
      return;
    }
    
    // 创建块
    loadingSystem.createChunk({
      name: `Chunk ${Date.now()}`,
      level: 0,
      priority: ResourcePriority.MEDIUM
    });
    
    // 更新块列表
    loadChunks();
  };
  
  // 表格列定义
  const columns = [
    {
      title: t('optimization.chunkedSceneLoading.name'),
      dataIndex: 'name',
      key: 'name'},
    {
      title: t('optimization.chunkedSceneLoading.level'),
      dataIndex: 'level',
      key: 'level',
      sorter: (a: SceneChunk, b: SceneChunk) => a.level - b.level},
    {
      title: t('optimization.chunkedSceneLoading.resources'),
      dataIndex: 'resources',
      key: 'resources',
      render: (resources: string[]) => resources.length,
      sorter: (a: SceneChunk, b: SceneChunk) => a.resources.length - b.resources.length},
    {
      title: t('optimization.chunkedSceneLoading.status'),
      key: 'status',
      render: (text: any, record: SceneChunk) => (
        <Space>
          {record.loaded && <Tag color="success">{t('optimization.chunkedSceneLoading.loaded')}</Tag>}
          {record.loading && <Tag color="processing">{t('optimization.chunkedSceneLoading.loading')}</Tag>}
          {!record.loaded && !record.loading && <Tag color="default">{t('optimization.chunkedSceneLoading.unloaded')}</Tag>}
        </Space>
      )},
    {
      title: t('optimization.chunkedSceneLoading.progress'),
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={Math.round(progress * 100)} size="small" />
      )},
    {
      title: t('common.actions'),
      key: 'actions',
      render: (text: any, record: SceneChunk) => (
        <Space>
          {!record.loaded && !record.loading && (
            <Button
              icon={<CloudDownloadOutlined />}
              size="small"
              onClick={() => handleLoadChunk(record.id)}
            />
          )}
          {record.loaded && (
            <Button
              icon={<DeleteOutlined />}
              size="small"
              danger
              onClick={() => handleUnloadChunk(record.id)}
            />
          )}
        </Space>
      )},
  ];
  
  // 如果未初始化，显示加载中
  if (!isInitialized) {
    return (
      <Card title={t('optimization.chunkedSceneLoading.title')} className="chunked-scene-loading-panel">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">{t('common.loading')}</div>
        </div>
      </Card>
    );
  }
  
  return (
    <Card 
      title={
        <Space>
          <span>{t('optimization.chunkedSceneLoading.title')}</span>
          <Switch 
            checked={isEnabled} 
            onChange={handleEnableChange} 
            size="small"
          />
        </Space>
      } 
      className="chunked-scene-loading-panel"
      extra={
        <Space>
          <Tooltip title={t('optimization.chunkedSceneLoading.refresh')}>
            <Button 
              icon={<ReloadOutlined />} 
              size="small"
              onClick={loadChunks}
            />
          </Tooltip>
          <Tooltip title={t('optimization.chunkedSceneLoading.createChunk')}>
            <Button 
              icon={<PlusOutlined />} 
              size="small"
              onClick={handleCreateChunk}
              disabled={!isEnabled}
            />
          </Tooltip>
        </Space>
      }
    >
      <Collapse defaultActiveKey={['1', '2']}>
        <Panel header={t('optimization.chunkedSceneLoading.statistics')} key="1">
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Statistic 
                title={t('optimization.chunkedSceneLoading.loadedChunks')} 
                value={loadedChunks} 
                suffix={`/ ${totalChunks}`}
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title={t('optimization.chunkedSceneLoading.loadPercentage')} 
                value={totalChunks > 0 ? Math.round((loadedChunks / totalChunks) * 100) : 0} 
                suffix="%" 
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title={t('optimization.chunkedSceneLoading.memoryUsage')} 
                value={(memoryUsage / 1024).toFixed(2)} 
                suffix="MB"
              />
            </Col>
            <Col span={24}>
              <Progress 
                percent={totalChunks > 0 ? Math.round((loadedChunks / totalChunks) * 100) : 0} 
                status="active" 
              />
            </Col>
          </Row>
        </Panel>
        
        <Panel header={t('optimization.chunkedSceneLoading.chunks')} key="2">
          <Table 
            dataSource={chunks} 
            columns={columns} 
            rowKey="id"
            size="small"
            pagination={{ pageSize: 5 }}
          />
        </Panel>
        
        <Panel header={t('optimization.chunkedSceneLoading.settings')} key="3">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.useOctree')}
                  <Tooltip title={t('optimization.chunkedSceneLoading.useOctreeTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useOctree} 
                    onChange={handleOctreeChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.useLOD')}
                  <Tooltip title={t('optimization.chunkedSceneLoading.useLODTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useLOD} 
                    onChange={handleLODChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.usePredictiveLoading')}
                  <Tooltip title={t('optimization.chunkedSceneLoading.usePredictiveLoadingTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={usePredictiveLoading} 
                    onChange={handlePredictiveLoadingChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.useProgressiveLoading')}
                  <Tooltip title={t('optimization.chunkedSceneLoading.useProgressiveLoadingTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useProgressiveLoading} 
                    onChange={handleProgressiveLoadingChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.useMemoryManagement')}
                  <Tooltip title={t('optimization.chunkedSceneLoading.useMemoryManagementTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useMemoryManagement} 
                    onChange={handleMemoryManagementChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.useDebugVisualization')}
                  <Tooltip title={t('optimization.chunkedSceneLoading.useDebugVisualizationTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useDebugVisualization} 
                    onChange={handleDebugVisualizationChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.chunkSize')}
                </div>
                <div className="setting-control">
                  <InputNumber
                    min={10}
                    max={1000}
                    step={10}
                    value={chunkSize}
                    onChange={handleChunkSizeChange}
                    disabled={!isEnabled}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.maxChunkLevel')}
                </div>
                <div className="setting-control">
                  <InputNumber
                    min={1}
                    max={10}
                    step={1}
                    value={maxChunkLevel}
                    onChange={handleMaxChunkLevelChange}
                    disabled={!isEnabled}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.loadDistance')}
                </div>
                <div className="setting-control">
                  <InputNumber
                    min={10}
                    max={1000}
                    step={10}
                    value={loadDistance}
                    onChange={handleLoadDistanceChange}
                    disabled={!isEnabled}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.unloadDistance')}
                </div>
                <div className="setting-control">
                  <InputNumber
                    min={10}
                    max={2000}
                    step={10}
                    value={unloadDistance}
                    onChange={handleUnloadDistanceChange}
                    disabled={!isEnabled}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.preloadDistance')}
                </div>
                <div className="setting-control">
                  <InputNumber
                    min={10}
                    max={1500}
                    step={10}
                    value={preloadDistance}
                    onChange={handlePreloadDistanceChange}
                    disabled={!isEnabled}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.maxMemoryUsage')}
                </div>
                <div className="setting-control">
                  <InputNumber
                    min={64}
                    max={4096}
                    step={64}
                    value={maxMemoryUsage}
                    onChange={handleMaxMemoryUsageChange}
                    disabled={!isEnabled}
                    style={{ width: '100%' }}
                    addonAfter="MB"
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.chunkedSceneLoading.maxConcurrentLoads')}
                </div>
                <div className="setting-control">
                  <InputNumber
                    min={1}
                    max={20}
                    step={1}
                    value={maxConcurrentLoads}
                    onChange={handleMaxConcurrentLoadsChange}
                    disabled={!isEnabled}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </Panel>
      </Collapse>
    </Card>
  );
};

export default ChunkedSceneLoadingPanel;
