/**
 * 交互编辑器
 */
import React from 'react';
import GenericEditor from '../common/GenericEditor';

/**
 * 交互编辑器属性
 */
interface InteractionEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * 交互编辑器组件
 */
const InteractionEditor: React.FC<InteractionEditorProps> = ({ data, onChange }) => {
  return <GenericEditor title="交互" data={data} onChange={onChange} />;
};

export default InteractionEditor;
