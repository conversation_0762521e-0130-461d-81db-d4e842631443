#!/usr/bin/env node

/**
 * 修复jest语法错误
 */

import fs from 'fs';
import path from 'path';

function fixJestSyntax(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    // 修复jest.fn<any, any>()语法错误
    content = content.replace(/jest\.fn<any, any>\(\)/g, 'jest.fn()');
    
    // 修复其他常见的导入错误
    const fixes = [
      // 修复antd导入错误
      { from: /SwitchNumber/g, to: 'InputNumber' },
      { from: /FormNumber/g, to: 'InputNumber' },
      
      // 修复缺失的导入
      { from: /const { Option } = Select;/, to: 'const { Option } = Select;\nimport { Select, Switch, InputNumber, Form } from \'antd\';' },
      
      // 修复i18n导入错误
      { from: /import { i18n } from/g, to: 'import i18n from' },
    ];
    
    fixes.forEach(({ from, to }) => {
      if (from.test && from.test(content)) {
        content = content.replace(from, to);
        changed = true;
      } else if (typeof from === 'string' && content.includes(from)) {
        content = content.replace(new RegExp(from, 'g'), to);
        changed = true;
      }
    });
    
    if (changed) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`已修复: ${filePath}`);
    }
  } catch (error) {
    console.error(`修复失败 ${filePath}:`, error.message);
  }
}

function findTsFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          traverse(fullPath);
        } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`无法读取目录 ${currentDir}:`, error.message);
    }
  }
  
  traverse(dir);
  return files;
}

// 查找所有TypeScript文件
const tsFiles = findTsFiles('./src');

console.log(`找到 ${tsFiles.length} 个TypeScript文件`);

// 修复每个文件
tsFiles.forEach(fixJestSyntax);

console.log('修复完成!');
