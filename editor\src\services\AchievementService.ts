/**
 * 成就服务
 * 负责管理编辑器的成就和奖励系统
 */
import { EventEmitter } from '../utils/EventEmitter';
import { message } from 'antd';
import i18n from '../i18n';
import { store } from '../store';
import { 
  addAchievement, 
  unlockAchievement, 
  updateProgress 
} from '../store/achievements/achievementsSlice';

/**
 * 成就难度枚举
 */
export enum AchievementDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
  EXPERT = 'expert'
}

/**
 * 成就类型枚举
 */
export enum AchievementType {
  TUTORIAL = 'tutorial',
  EDITOR = 'editor',
  CREATION = 'creation',
  COLLABORATION = 'collaboration',
  SPECIAL = 'special'
}

/**
 * 成就接口
 */
export interface Achievement {
  id: string;
  title: string;
  description: string;
  type: AchievementType;
  difficulty: AchievementDifficulty;
  icon: string;
  unlocked: boolean;
  unlockedAt?: number;
  progress?: number;
  maxProgress?: number;
  hidden?: boolean;
  reward?: {
    type: string;
    value: any;
  };
  prerequisites?: string[];
}

/**
 * 成就服务类
 */
export class AchievementService {
  private static instance: AchievementService;
  private achievements: Map<string, Achievement> = new Map();
  private events = new EventEmitter();
  private enabled: boolean = true;

  /**
   * 私有构造函数
   */
  private constructor() {
    this.loadAchievements();
    this.loadUserProgress();
  }

  /**
   * 获取成就服务实例
   */
  public static getInstance(): AchievementService {
    if (!AchievementService.instance) {
      AchievementService.instance = new AchievementService();
    }
    return AchievementService.instance;
  }

  /**
   * 加载成就数据
   */
  private loadAchievements(): void {
    // 教程相关成就
    this.registerAchievement({
      id: 'tutorial_basics_complete',
      title: i18n.t('achievements.tutorialBasicsComplete.title'),
      description: i18n.t('achievements.tutorialBasicsComplete.description'),
      type: AchievementType.TUTORIAL,
      difficulty: AchievementDifficulty.EASY,
      icon: 'trophy',
      unlocked: false,
      reward: {
        type: 'badge',
        value: 'beginner'
      }
    });

    this.registerAchievement({
      id: 'tutorial_animation_complete',
      title: i18n.t('achievements.tutorialAnimationComplete.title'),
      description: i18n.t('achievements.tutorialAnimationComplete.description'),
      type: AchievementType.TUTORIAL,
      difficulty: AchievementDifficulty.MEDIUM,
      icon: 'animation',
      unlocked: false,
      prerequisites: ['tutorial_basics_complete'],
      reward: {
        type: 'badge',
        value: 'animator'
      }
    });

    this.registerAchievement({
      id: 'tutorial_all_complete',
      title: i18n.t('achievements.tutorialAllComplete.title'),
      description: i18n.t('achievements.tutorialAllComplete.description'),
      type: AchievementType.TUTORIAL,
      difficulty: AchievementDifficulty.HARD,
      icon: 'star',
      unlocked: false,
      progress: 0,
      maxProgress: 8, // 总教程数量
      reward: {
        type: 'badge',
        value: 'master'
      }
    });

    // 编辑器使用相关成就
    this.registerAchievement({
      id: 'editor_first_scene',
      title: i18n.t('achievements.editorFirstScene.title'),
      description: i18n.t('achievements.editorFirstScene.description'),
      type: AchievementType.EDITOR,
      difficulty: AchievementDifficulty.EASY,
      icon: 'scene',
      unlocked: false
    });

    // 创作相关成就
    this.registerAchievement({
      id: 'creation_first_character',
      title: i18n.t('achievements.creationFirstCharacter.title'),
      description: i18n.t('achievements.creationFirstCharacter.description'),
      type: AchievementType.CREATION,
      difficulty: AchievementDifficulty.MEDIUM,
      icon: 'user',
      unlocked: false
    });

    // 协作相关成就
    this.registerAchievement({
      id: 'collaboration_first_session',
      title: i18n.t('achievements.collaborationFirstSession.title'),
      description: i18n.t('achievements.collaborationFirstSession.description'),
      type: AchievementType.COLLABORATION,
      difficulty: AchievementDifficulty.MEDIUM,
      icon: 'team',
      unlocked: false
    });
  }

  /**
   * 注册成就
   */
  private registerAchievement(achievement: Achievement): void {
    this.achievements.set(achievement.id, achievement);
    store.dispatch(addAchievement(achievement));
  }

  /**
   * 加载用户进度
   */
  private loadUserProgress(): void {
    try {
      // 从本地存储加载已解锁的成就
      const unlockedAchievements = localStorage.getItem('unlockedAchievements');
      if (unlockedAchievements) {
        const unlockedIds = JSON.parse(unlockedAchievements) as string[];
        unlockedIds.forEach(id => {
          const achievement = this.achievements.get(id);
          if (achievement) {
            achievement.unlocked = true;
            achievement.unlockedAt = Date.now();
            store.dispatch(unlockAchievement(id));
          }
        });
      }

      // 从本地存储加载成就进度
      const achievementProgress = localStorage.getItem('achievementProgress');
      if (achievementProgress) {
        const progress = JSON.parse(achievementProgress) as Record<string, number>;
        Object.entries(progress).forEach(([id, value]) => {
          const achievement = this.achievements.get(id);
          if (achievement && achievement.maxProgress) {
            achievement.progress = value;
            store.dispatch(updateProgress({ id, progress: value }));
          }
        });
      }
    } catch (error) {
      console.error('Failed to load achievement progress:', error);
    }
  }

  /**
   * 保存用户进度
   */
  private saveUserProgress(): void {
    try {
      // 保存已解锁的成就到本地存储
      const unlockedIds = Array.from(this.achievements.values())
        .filter(achievement => achievement.unlocked)
        .map(achievement => achievement.id);
      localStorage.setItem('unlockedAchievements', JSON.stringify(unlockedIds));

      // 保存成就进度到本地存储
      const progress: Record<string, number> = {};
      this.achievements.forEach((achievement, id) => {
        if (achievement.progress !== undefined && achievement.maxProgress !== undefined) {
          progress[id] = achievement.progress;
        }
      });
      localStorage.setItem('achievementProgress', JSON.stringify(progress));
    } catch (error) {
      console.error('Failed to save achievement progress:', error);
    }
  }

  /**
   * 获取所有成就
   */
  public getAchievements(): Achievement[] {
    return Array.from(this.achievements.values());
  }

  /**
   * 获取已解锁的成就
   */
  public getUnlockedAchievements(): Achievement[] {
    return Array.from(this.achievements.values()).filter(achievement => achievement.unlocked);
  }

  /**
   * 获取特定类型的成就
   */
  public getAchievementsByType(type: AchievementType): Achievement[] {
    return Array.from(this.achievements.values()).filter(achievement => achievement.type === type);
  }

  /**
   * 获取特定成就
   */
  public getAchievement(id: string): Achievement | undefined {
    return this.achievements.get(id);
  }

  /**
   * 解锁成就
   */
  public unlockAchievement(id: string): boolean {
    if (!this.enabled) return false;

    const achievement = this.achievements.get(id);
    if (!achievement || achievement.unlocked) {
      return false;
    }

    // 检查前置条件
    if (achievement.prerequisites && achievement.prerequisites.length > 0) {
      const allPrerequisitesMet = achievement.prerequisites.every(prereqId => {
        const prereq = this.achievements.get(prereqId);
        return prereq && prereq.unlocked;
      });

      if (!allPrerequisitesMet) {
        return false;
      }
    }

    // 解锁成就
    achievement.unlocked = true;
    achievement.unlockedAt = Date.now();
    
    // 更新Redux状态
    store.dispatch(unlockAchievement(id));
    
    // 保存进度
    this.saveUserProgress();
    
    // 发出事件
    this.events.emit('achievementUnlocked', achievement);
    
    // 显示通知
    message.success({
      content: i18n.t('achievements.unlocked', { title: achievement.title }),
      duration: 5
    });
    
    return true;
  }

  /**
   * 更新成就进度
   */
  public updateAchievementProgress(id: string, progress: number): boolean {
    if (!this.enabled) return false;

    const achievement = this.achievements.get(id);
    if (!achievement || achievement.unlocked || achievement.maxProgress === undefined) {
      return false;
    }

    // 限制进度范围
    progress = Math.max(0, Math.min(achievement.maxProgress, progress));

    // 更新进度
    achievement.progress = progress;
    
    // 更新Redux状态
    store.dispatch(updateProgress({ id, progress }));
    
    // 如果达到最大进度，解锁成就
    if (progress >= achievement.maxProgress) {
      this.unlockAchievement(id);
    } else {
      // 保存进度
      this.saveUserProgress();
      
      // 发出事件
      this.events.emit('progressUpdated', { achievement, progress });
    }
    
    return true;
  }

  /**
   * 处理教程完成
   */
  public handleTutorialCompleted(tutorialId: string): void {
    // 解锁对应的教程成就
    switch (tutorialId) {
      case 'editor-basics':
        this.unlockAchievement('tutorial_basics_complete');
        break;
      case 'animation-system':
        this.unlockAchievement('tutorial_animation_complete');
        break;
      // 其他教程...
    }

    // 更新"完成所有教程"成就的进度
    const allTutorialsAchievement = this.getAchievement('tutorial_all_complete');
    if (allTutorialsAchievement && allTutorialsAchievement.progress !== undefined) {
      this.updateAchievementProgress('tutorial_all_complete', allTutorialsAchievement.progress + 1);
    }
  }

  /**
   * 添加事件监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.events.on(event, listener);
  }

  /**
   * 移除事件监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.events.off(event, listener);
  }

  /**
   * 启用/禁用成就系统
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
}

// 导出单例实例
export const achievementService = AchievementService.getInstance();
