/**
 * 地形编辑器组件
 * 用于编辑地形
 */
import React, { useState, useEffect, useRef } from 'react';
import { Tabs, Card, Button, Space, Typography, message } from 'antd';
import {
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import TerrainSculptingTool from './TerrainSculptingTool';
import TerrainTextureTool from './TerrainTextureTool';
import TerrainGenerationTool from './TerrainGenerationTool';
import TerrainLODTool from './TerrainLODTool';
import TerrainPhysicsSettingsTool from './TerrainPhysicsSettingsTool';
import TerrainPerformancePanel from './TerrainPerformancePanel';
import TerrainImportExportPanel from './TerrainImportExportPanel';
import './TerrainEditor.less';

const { TabPane } = Tabs;
const { Title, Text } = Typography;

/**
 * 地形编辑器属性
 */
interface TerrainEditorProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
}

/**
 * 地形编辑器组件
 */
const TerrainEditor: React.FC<TerrainEditorProps> = ({
  entityId,
  editable = true
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 状态
  const [activeTab, setActiveTab] = useState('sculpting');
  const [undoStack, setUndoStack] = useState<any[]>([]);
  const [redoStack, setRedoStack] = useState<any[]>([]);
  const [isModified, setIsModified] = useState(false);

  // 从Redux获取地形数据
  const terrainData = useSelector((state: RootState) =>
    entityId ? state.entities.byId[entityId]?.components?.TerrainComponent : null
  );

  // 引用
  const terrainRef = useRef<any>(null);

  // 处理标签页变更
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  // 处理保存
  const handleSave = () => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return;
    }

    // 保存地形数据
    // 这里需要实现保存逻辑

    message.success(t('terrain.messages.saveSuccess'));
    setIsModified(false);
  };

  // 处理撤销
  const handleUndo = () => {
    if (undoStack.length === 0) {
      return;
    }

    // 从撤销栈中弹出最后一个操作
    const lastOperation = undoStack[undoStack.length - 1];
    const newUndoStack = undoStack.slice(0, undoStack.length - 1);

    // 将当前状态推入重做栈
    // 这里需要实现获取当前状态的逻辑
    const currentState = {}; // 临时占位
    const newRedoStack = [...redoStack, currentState];

    // 应用撤销操作
    // 这里需要实现应用操作的逻辑

    // 更新状态
    setUndoStack(newUndoStack);
    setRedoStack(newRedoStack);
  };

  // 处理重做
  const handleRedo = () => {
    if (redoStack.length === 0) {
      return;
    }

    // 从重做栈中弹出最后一个操作
    const lastOperation = redoStack[redoStack.length - 1];
    const newRedoStack = redoStack.slice(0, redoStack.length - 1);

    // 将当前状态推入撤销栈
    // 这里需要实现获取当前状态的逻辑
    const currentState = {}; // 临时占位
    const newUndoStack = [...undoStack, currentState];

    // 应用重做操作
    // 这里需要实现应用操作的逻辑

    // 更新状态
    setUndoStack(newUndoStack);
    setRedoStack(newRedoStack);
  };

  // 处理地形修改
  const handleTerrainModified = () => {
    setIsModified(true);
  };

  // 处理地形操作
  const handleTerrainOperation = (operation: any) => {
    // 将操作推入撤销栈
    setUndoStack([...undoStack, operation]);

    // 清空重做栈
    setRedoStack([]);

    // 标记为已修改
    setIsModified(true);
  };

  // 检查是否有地形数据
  const hasTerrainData = !!terrainData;

  return (
    <div className="terrain-editor">
      <Card
        title={
          <div className="terrain-editor-header">
            <Title level={4}>{t('terrain.editor.title')}</Title>
            <Space>
              <Button
                icon={<SaveOutlined />}
                type="primary"
                onClick={handleSave}
                disabled={!hasTerrainData || !isModified || !editable}
              >
                {t('common.save')}
              </Button>
              <Button
                icon={<UndoOutlined />}
                onClick={handleUndo}
                disabled={undoStack.length === 0 || !editable}
              >
                {t('common.undo')}
              </Button>
              <Button
                icon={<RedoOutlined />}
                onClick={handleRedo}
                disabled={redoStack.length === 0 || !editable}
              >
                {t('common.redo')}
              </Button>
            </Space>
          </div>
        }
      >
        {!hasTerrainData ? (
          <div className="terrain-editor-empty">
            <InfoCircleOutlined className="terrain-editor-empty-icon" />
            <Text>{t('terrain.editor.noTerrainSelected')}</Text>
            <Button type="primary" disabled={!editable}>
              {t('terrain.editor.createTerrain')}
            </Button>
          </div>
        ) : (
          <Tabs activeKey={activeTab} onChange={handleTabChange}>
            <TabPane tab={t('terrain.tabs.sculpting')} key="sculpting">
              <TerrainSculptingTool
                entityId={entityId}
                editable={editable}
                onTerrainModified={handleTerrainModified}
                onOperation={handleTerrainOperation}
              />
            </TabPane>
            <TabPane tab={t('terrain.tabs.texture')} key="texture">
              <TerrainTextureTool
                entityId={entityId}
                editable={editable}
                onTerrainModified={handleTerrainModified}
                onOperation={handleTerrainOperation}
              />
            </TabPane>
            <TabPane tab={t('terrain.tabs.generation')} key="generation">
              <TerrainGenerationTool
                entityId={entityId}
                editable={editable}
                onTerrainModified={handleTerrainModified}
                onOperation={handleTerrainOperation}
              />
            </TabPane>
            <TabPane tab={t('terrain.tabs.lod')} key="lod">
              <TerrainLODTool
                entityId={entityId}
                editable={editable}
                onTerrainModified={handleTerrainModified}
                onOperation={handleTerrainOperation}
              />
            </TabPane>
            <TabPane tab={t('terrain.tabs.physics')} key="physics">
              <TerrainPhysicsSettingsTool
                entityId={entityId}
                editable={editable}
                onTerrainModified={handleTerrainModified}
                onOperation={handleTerrainOperation}
              />
            </TabPane>
            <TabPane tab={t('terrain.tabs.performance')} key="performance">
              <TerrainPerformancePanel
                entityId={entityId}
                editable={editable}
              />
            </TabPane>
            <TabPane tab={t('terrain.tabs.importExport')} key="importExport">
              <TerrainImportExportPanel
                entityId={entityId}
                editable={editable}
                onTerrainModified={handleTerrainModified}
              />
            </TabPane>
          </Tabs>
        )}
      </Card>
    </div>
  );
};

export default TerrainEditor;
