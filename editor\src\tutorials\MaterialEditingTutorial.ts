/**
 * 材质编辑教程
 * 引导用户学习如何创建和编辑材质
 */
import { Tutorial, TutorialStep } from '../services/TutorialService';
import i18n from '../i18n';

export const MaterialEditingTutorial: Tutorial = {
  id: 'material-editing',
  title: i18n.t('tutorials.materialEditing.title'),
  description: i18n.t('tutorials.materialEditing.description'),
  category: 'materials',
  difficulty: 'intermediate',
  duration: 15,
  prerequisites: ['editor-basics'],
  tags: ['材质', '着色器', 'PBR', '纹理'],
  steps: [
    // 步骤1：介绍
    {
      id: 'introduction',
      title: i18n.t('tutorials.materialEditing.steps.introduction.title'),
      description: i18n.t('tutorials.materialEditing.steps.introduction.description'),
      nextButtonText: i18n.t('tutorials.next'),
      skipButtonText: i18n.t('tutorials.skip')
    },
    
    // 步骤2：创建新材质
    {
      id: 'create-material',
      title: i18n.t('tutorials.materialEditing.steps.createMaterial.title'),
      description: i18n.t('tutorials.materialEditing.steps.createMaterial.description'),
      targetElement: 'project-panel',
      highlightElement: true,
      action: 'right-click',
      completionCriteria: 'material-created',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤3：选择材质类型
    {
      id: 'select-material-type',
      title: i18n.t('tutorials.materialEditing.steps.selectMaterialType.title'),
      description: i18n.t('tutorials.materialEditing.steps.selectMaterialType.description'),
      targetElement: 'material-type-dropdown',
      highlightElement: true,
      action: 'select',
      completionCriteria: 'material-type-selected',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤4：设置基本属性
    {
      id: 'set-basic-properties',
      title: i18n.t('tutorials.materialEditing.steps.setBasicProperties.title'),
      description: i18n.t('tutorials.materialEditing.steps.setBasicProperties.description'),
      targetElement: 'material-basic-properties',
      highlightElement: true,
      action: 'edit',
      completionCriteria: 'basic-properties-set',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤5：添加纹理
    {
      id: 'add-textures',
      title: i18n.t('tutorials.materialEditing.steps.addTextures.title'),
      description: i18n.t('tutorials.materialEditing.steps.addTextures.description'),
      targetElement: 'material-textures',
      highlightElement: true,
      action: 'drag-drop',
      completionCriteria: 'textures-added',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤6：调整纹理设置
    {
      id: 'adjust-texture-settings',
      title: i18n.t('tutorials.materialEditing.steps.adjustTextureSettings.title'),
      description: i18n.t('tutorials.materialEditing.steps.adjustTextureSettings.description'),
      targetElement: 'texture-settings',
      highlightElement: true,
      action: 'edit',
      completionCriteria: 'texture-settings-adjusted',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤7：设置着色器变体
    {
      id: 'set-shader-variants',
      title: i18n.t('tutorials.materialEditing.steps.setShaderVariants.title'),
      description: i18n.t('tutorials.materialEditing.steps.setShaderVariants.description'),
      targetElement: 'shader-variants',
      highlightElement: true,
      action: 'toggle',
      completionCriteria: 'shader-variants-set',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤8：预览材质
    {
      id: 'preview-material',
      title: i18n.t('tutorials.materialEditing.steps.previewMaterial.title'),
      description: i18n.t('tutorials.materialEditing.steps.previewMaterial.description'),
      targetElement: 'material-preview',
      highlightElement: true,
      action: 'interact',
      completionCriteria: 'material-previewed',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤9：应用到对象
    {
      id: 'apply-to-object',
      title: i18n.t('tutorials.materialEditing.steps.applyToObject.title'),
      description: i18n.t('tutorials.materialEditing.steps.applyToObject.description'),
      targetElement: 'scene-hierarchy',
      highlightElement: true,
      action: 'drag-drop',
      completionCriteria: 'material-applied',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤10：保存材质
    {
      id: 'save-material',
      title: i18n.t('tutorials.materialEditing.steps.saveMaterial.title'),
      description: i18n.t('tutorials.materialEditing.steps.saveMaterial.description'),
      targetElement: 'save-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'material-saved',
      nextButtonText: i18n.t('tutorials.finish'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤11：完成
    {
      id: 'completion',
      title: i18n.t('tutorials.materialEditing.steps.completion.title'),
      description: i18n.t('tutorials.materialEditing.steps.completion.description'),
      nextButtonText: i18n.t('tutorials.close'),
      previousButtonText: i18n.t('tutorials.previous')
    }
  ]
};

export default MaterialEditingTutorial;
