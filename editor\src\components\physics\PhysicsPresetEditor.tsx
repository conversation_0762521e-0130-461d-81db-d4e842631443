/**
 * 物理预设编辑器组件
 * 用于管理物理预设
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message 
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined, 
  EditOutlined, 
  CopyOutlined, 
  ImportOutlined, 
  ExportOutlined 
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';

const { TabPane } = Tabs;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';

/**
 * 物理预设编辑器组件
 */
const PhysicsPresetEditor: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 预设列表
  const [presets, setPresets] = useState<any[]>([]);
  
  // 预设类别
  const [categories, setCategories] = useState<string[]>([]);
  
  // 当前选中的预设
  const [selectedPreset, setSelectedPreset] = useState<any>(null);
  
  // 是否显示预设编辑对话框
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  
  // 是否显示导入对话框
  const [showImportModal, setShowImportModal] = useState<boolean>(false);
  
  // 导入JSON
  const [importJson, setImportJson] = useState<string>('');
  
  // 表单实例
  const [form] = Form.useForm();
  
  // 加载预设数据
  useEffect(() => {
    // 模拟从预设管理器加载数据
    const mockPresets = [
      {
        key: '1',
        name: 'dynamic_box',
        description: '动态盒子预设',
        category: '物理体',
        type: '物理体'
      },
      {
        key: '2',
        name: 'static_ground',
        description: '静态地面预设',
        category: '物理体',
        type: '物理体'
      },
      {
        key: '3',
        name: 'bouncy_ball',
        description: '弹力球预设',
        category: '物理体',
        type: '物理体'
      },
      {
        key: '4',
        name: 'vehicle',
        description: '简单车辆预设',
        category: '车辆',
        type: '物理体'
      },
      {
        key: '5',
        name: 'earth_gravity',
        description: '地球重力预设',
        category: '世界',
        type: '世界'
      }
    ];
    
    setPresets(mockPresets);
    
    // 提取类别
    const uniqueCategories = Array.from(new Set(mockPresets.map(preset => preset.category)));
    setCategories(uniqueCategories);
  }, []);
  
  // 表格列定义
  const columns = [
    {
      title: t('editor.physics.presetName'),
      dataIndex: 'name',
      key: 'name',
      sorter: (a: any, b: any) => a.name.localeCompare(b.name)
    },
    {
      title: t('editor.physics.presetDescription'),
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: t('editor.physics.presetCategory'),
      dataIndex: 'category',
      key: 'category',
      filters: categories.map(category => ({ text: category, value: category })),
      onFilter: (value: string, record: any) => record.category === value
    },
    {
      title: t('editor.physics.presetType'),
      dataIndex: 'type',
      key: 'type',
      filters: [
        { text: t('editor.physics.presetTypeBody'), value: '物理体' },
        { text: t('editor.physics.presetTypeWorld'), value: '世界' },
        { text: t('editor.physics.presetTypeMaterial'), value: '材质' }
      ],
      onFilter: (value: string, record: any) => record.type === value
    },
    {
      title: t('editor.common.actions'),
      key: 'actions',
      render: (text: string, record: any) => (
        <Space size="small">
          <Button 
            icon={<EditOutlined />} 
            size="small" 
            onClick={() => handleEditPreset(record)}
            title={t('editor.physics.editPreset')}
          />
          <Button 
            icon={<CopyOutlined />} 
            size="small" 
            onClick={() => handleDuplicatePreset(record)}
            title={t('editor.physics.duplicatePreset')}
          />
          <Button 
            icon={<DeleteOutlined />} 
            size="small" 
            danger
            onClick={() => handleDeletePreset(record)}
            title={t('editor.physics.deletePreset')}
            disabled={record.name === 'default_world'}
          />
        </Space>
      )
    }
  ];
  
  // 编辑预设
  const handleEditPreset = (preset: any) => {
    setSelectedPreset(preset);
    form.setFieldsValue({
      name: preset.name,
      description: preset.description,
      category: preset.category
    });
    setShowEditModal(true);
  };
  
  // 复制预设
  const handleDuplicatePreset = (preset: any) => {
    const newPreset = {
      ...preset,
      key: Date.now().toString(),
      name: `${preset.name}_copy`,
      description: `${preset.description} (复制)`
    };
    
    setPresets([...presets, newPreset]);
    message.success(t('editor.physics.presetDuplicated', { name: preset.name }));
  };
  
  // 删除预设
  const handleDeletePreset = (preset: any) => {
    Modal.confirm({
      title: t('editor.physics.confirmDeletePreset'),
      content: t('editor.physics.confirmDeletePresetContent', { name: preset.name }),
      onOk: () => {
        setPresets(presets.filter(p => p.key !== preset.key));
        message.success(t('editor.physics.presetDeleted', { name: preset.name }));
      }
    });
  };
  
  // 保存预设
  const handleSavePreset = () => {
    form.validateFields().then(values => {
      if (selectedPreset) {
        // 更新现有预设
        const updatedPresets = presets.map(p => 
          p.key === selectedPreset.key ? { ...p, ...values } : p
        );
        setPresets(updatedPresets);
        message.success(t('editor.physics.presetUpdated', { name: values.name }));
      } else {
        // 创建新预设
        const newPreset = {
          key: Date.now().toString(),
          ...values,
          type: values.category === '世界' ? '世界' : '物理体'
        };
        setPresets([...presets, newPreset]);
        message.success(t('editor.physics.presetCreated', { name: values.name }));
      }
      
      setShowEditModal(false);
      setSelectedPreset(null);
      form.resetFields();
    });
  };
  
  // 创建新预设
  const handleCreatePreset = () => {
    setSelectedPreset(null);
    form.resetFields();
    form.setFieldsValue({
      category: '物理体'
    });
    setShowEditModal(true);
  };
  
  // 导出所有预设
  const handleExportAllPresets = () => {
    try {
      // 创建JSON
      const json = JSON.stringify(presets, null, 2);
      
      // 创建下载链接
      const blob = new Blob([json], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'physics_presets.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      message.success(t('editor.physics.presetsExported'));
    } catch (error) {
      message.error(t('editor.physics.exportError'));
      console.error('导出预设错误:', error);
    }
  };
  
  // 导入预设
  const handleImportPresets = () => {
    try {
      // 解析JSON
      const importedPresets = JSON.parse(importJson);
      
      if (!Array.isArray(importedPresets)) {
        message.error(t('editor.physics.invalidPresetData'));
        return;
      }
      
      // 添加唯一键
      const presetsWithKeys = importedPresets.map((preset: any) => ({
        ...preset,
        key: preset.key || Date.now().toString() + Math.random().toString(36).substr(2, 5)
      }));
      
      // 合并预设
      const mergedPresets = [...presets];
      
      for (const importedPreset of presetsWithKeys) {
        const existingIndex = mergedPresets.findIndex(p => p.name === importedPreset.name);
        if (existingIndex >= 0) {
          // 更新现有预设
          mergedPresets[existingIndex] = importedPreset;
        } else {
          // 添加新预设
          mergedPresets.push(importedPreset);
        }
      }
      
      setPresets(mergedPresets);
      
      // 更新类别
      const uniqueCategories = Array.from(new Set(mergedPresets.map(preset => preset.category)));
      setCategories(uniqueCategories);
      
      // 关闭对话框
      setShowImportModal(false);
      setImportJson('');
      
      message.success(t('editor.physics.presetsImported', { count: presetsWithKeys.length }));
    } catch (error) {
      message.error(t('editor.physics.importError'));
      console.error('导入预设错误:', error);
    }
  };
  
  return (
    <div className="physics-preset-editor">
      <Card 
        title={t('editor.physics.presets')}
        extra={
          <Space>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleCreatePreset}
            >
              {t('editor.physics.newPreset')}
            </Button>
            <Button 
              icon={<ImportOutlined />} 
              onClick={() => setShowImportModal(true)}
            >
              {t('editor.physics.importPresets')}
            </Button>
            <Button 
              icon={<ExportOutlined />} 
              onClick={handleExportAllPresets}
            >
              {t('editor.physics.exportPresets')}
            </Button>
          </Space>
        }
      >
        <Table 
          dataSource={presets} 
          columns={columns} 
          rowKey="key"
          pagination={{ pageSize: 10 }}
        />
      </Card>
      
      {/* 预设编辑对话框 */}
      <Modal
        title={selectedPreset ? t('editor.physics.editPreset') : t('editor.physics.newPreset')}
        open={showEditModal}
        onOk={handleSavePreset}
        onCancel={() => setShowEditModal(false)}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={t('editor.physics.presetName')}
            rules={[{ required: true, message: t('editor.physics.presetNameRequired') }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="description"
            label={t('editor.physics.presetDescription')}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          
          <Form.Item
            name="category"
            label={t('editor.physics.presetCategory')}
            rules={[{ required: true, message: t('editor.physics.presetCategoryRequired') }]}
          >
            <Select>
              {categories.map(category => (
                <Option key={category} value={category}>{category}</Option>
              ))}
              <Option value="自定义">{t('editor.physics.customCategory')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 导入预设对话框 */}
      <Modal
        title={t('editor.physics.importPresets')}
        open={showImportModal}
        onOk={handleImportPresets}
        onCancel={() => setShowImportModal(false)}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('editor.physics.presetsJson')}
            required
          >
            <Input.TextArea 
              value={importJson} 
              onChange={e => setImportJson(e.target.value)} 
              placeholder={t('editor.physics.enterPresetsJson')}
              rows={10}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PhysicsPresetEditor;
