/**
 * 测试报告查看器组件
 */
import React, { useState } from 'react';
import {
  Card,
  Tabs,
  Typography,
  Space,
  Descriptions,
  Table,
  Progress,
  Tag,
  Button,
  Divider,
  Statistic,
  Row,
  Col,
  Empty,
  Tooltip
} from 'antd';
import {
  FileTextOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  Bar<PERSON>hartOutlined,
  BulbOutlined,
  UserOutlined,
  DownloadOutlined,
  PrinterOutlined,
  ShareAltOutlined,
  ExportOutlined
} from '@ant-design/icons';
import { TestReport } from './TestReportGenerator';
import { UserAction, UserActionType } from './UserBehaviorAnalyzer';
import UserActionPlayer from './UserActionPlayer';

const { Text, Title, Paragraph } = Typography;
const { TabPane } = Tabs;

/**
 * 测试报告查看器组件属性
 */
interface TestReportViewerProps {
  /** 测试报告 */
  report: TestReport;
  /** 用户操作记录 */
  actions?: UserAction[];
  /** 是否允许导出 */
  allowExport?: boolean;
  /** 是否允许打印 */
  allowPrint?: boolean;
  /** 是否允许分享 */
  allowShare?: boolean;
  /** 导出回调 */
  onExport?: (format: 'json' | 'html' | 'pdf' | 'csv') => void;
}

/**
 * 测试报告查看器组件
 */
const TestReportViewer: React.FC<TestReportViewerProps> = ({
  report,
  actions = [],
  allowExport = true,
  allowPrint = true,
  allowShare = false,
  onExport
}) => {
  // 本地状态
  const [activeTab, setActiveTab] = useState<string>('summary');
  
  // 格式化时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };
  
  // 格式化时长
  const formatDuration = (duration: number) => {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    const remainingMinutes = minutes % 60;
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}小时 ${remainingMinutes}分钟 ${remainingSeconds}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟 ${remainingSeconds}秒`;
    } else {
      return `${seconds}秒`;
    }
  };
  
  // 获取任务状态文本
  const getTaskStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'in_progress':
        return '进行中';
      case 'not_started':
        return '未开始';
      default:
        return status;
    }
  };
  
  // 获取任务状态颜色
  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in_progress':
        return 'processing';
      case 'not_started':
        return 'default';
      default:
        return 'default';
    }
  };
  
  // 获取反馈类型文本
  const getFeedbackTypeText = (type: string) => {
    switch (type) {
      case 'bug':
        return '问题报告';
      case 'suggestion':
        return '改进建议';
      case 'rating':
        return '功能评分';
      case 'comment':
        return '一般评论';
      default:
        return type;
    }
  };
  
  // 获取反馈类型颜色
  const getFeedbackTypeColor = (type: string) => {
    switch (type) {
      case 'bug':
        return 'error';
      case 'suggestion':
        return 'warning';
      case 'rating':
        return 'processing';
      case 'comment':
        return 'default';
      default:
        return 'default';
    }
  };
  
  // 处理导出
  const handleExport = (format: 'json' | 'html' | 'pdf' | 'csv') => {
    if (onExport) {
      onExport(format);
    }
  };
  
  // 处理打印
  const handlePrint = () => {
    window.print();
  };
  
  // 渲染摘要
  const renderSummary = () => {
    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        <Card>
          <Descriptions title="测试会话信息" bordered>
            <Descriptions.Item label="会话ID" span={3}>{report.sessionId}</Descriptions.Item>
            <Descriptions.Item label="用户">{report.userName} ({report.userId})</Descriptions.Item>
            <Descriptions.Item label="开始时间">{formatTime(report.sessionStartTime)}</Descriptions.Item>
            <Descriptions.Item label="结束时间">{formatTime(report.sessionEndTime)}</Descriptions.Item>
            <Descriptions.Item label="总时长" span={3}>{formatDuration(report.sessionDuration)}</Descriptions.Item>
            {report.metadata && (
              <>
                <Descriptions.Item label="项目ID">{report.metadata.projectId || '-'}</Descriptions.Item>
                <Descriptions.Item label="场景ID">{report.metadata.sceneId || '-'}</Descriptions.Item>
                <Descriptions.Item label="浏览器">{report.metadata.browser || '-'}</Descriptions.Item>
                <Descriptions.Item label="操作系统">{report.metadata.os || '-'}</Descriptions.Item>
                <Descriptions.Item label="屏幕尺寸" span={2}>{report.metadata.screenSize || '-'}</Descriptions.Item>
              </>
            )}
          </Descriptions>
        </Card>
        
        <Card title="测试结果概览">
          <Row gutter={16}>
            <Col span={8}>
              <Statistic
                title="任务完成率"
                value={report.taskStats.completionRate * 100}
                precision={2}
                suffix="%"
                valueStyle={{ color: report.taskStats.completionRate >= 0.8 ? '#3f8600' : '#cf1322' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="平均任务完成时间"
                value={formatDuration(report.taskStats.averageCompletionTime)}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="反馈数量"
                value={report.feedbackStats.total}
              />
            </Col>
          </Row>
          
          <Divider />
          
          <Progress
            percent={report.taskStats.completionRate * 100}
            status={report.taskStats.completionRate >= 1 ? 'success' : 'active'}
            format={percent => `${report.taskStats.completed}/${report.taskStats.total} 任务完成 (${percent?.toFixed(2)}%)`}
          />
        </Card>
      </Space>
    );
  };
  
  // 渲染任务详情
  const renderTasks = () => {
    const columns = [
      {
        title: '任务',
        dataIndex: 'title',
        key: 'title',
        render: (text: string, record: any) => (
          <Space direction="vertical">
            <Text strong>{text}</Text>
            <Text type="secondary">{record.description}</Text>
          </Space>
        )},
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => (
          <Tag color={getTaskStatusColor(status)}>
            {getTaskStatusText(status)}
          </Tag>
        )},
      {
        title: '开始时间',
        dataIndex: 'startTime',
        key: 'startTime',
        render: (time: number) => time ? formatTime(time) : '-'},
      {
        title: '结束时间',
        dataIndex: 'endTime',
        key: 'endTime',
        render: (time: number) => time ? formatTime(time) : '-'},
      {
        title: '耗时',
        dataIndex: 'timeSpent',
        key: 'timeSpent',
        render: (time: number) => time ? formatDuration(time) : '-'},
      {
        title: '完成度',
        dataIndex: 'completionPercentage',
        key: 'completionPercentage',
        render: (percent: number) => (
          <Progress percent={percent} size="small" />
        )},
    ];
    
    return (
      <Table
        columns={columns}
        dataSource={report.taskStats.tasks}
        rowKey="id"
        pagination={false}
      />
    );
  };
  
  // 渲染反馈详情
  const renderFeedback = () => {
    const columns = [
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        render: (type: string) => (
          <Tag color={getFeedbackTypeColor(type)}>
            {getFeedbackTypeText(type)}
          </Tag>
        )},
      {
        title: '内容',
        dataIndex: 'content',
        key: 'content'},
      {
        title: '评分',
        dataIndex: 'rating',
        key: 'rating',
        render: (rating: number) => rating ? `${rating}/5` : '-'},
      {
        title: '时间',
        dataIndex: 'timestamp',
        key: 'timestamp',
        render: (time: number) => formatTime(time)},
    ];
    
    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        <Card title="反馈统计">
          <Row gutter={16}>
            {Object.entries(report.feedbackStats.byType).map(([type, count]) => (
              <Col span={6} key={type}>
                <Statistic
                  title={getFeedbackTypeText(type)}
                  value={count}
                  valueStyle={{ color: getFeedbackTypeColor(type) === 'error' ? '#cf1322' : undefined }}
                />
              </Col>
            ))}
            <Col span={6}>
              <Statistic
                title="平均评分"
                value={report.feedbackStats.averageRating}
                precision={1}
                suffix="/5"
                valueStyle={{ color: report.feedbackStats.averageRating >= 4 ? '#3f8600' : '#cf1322' }}
              />
            </Col>
          </Row>
        </Card>
        
        <Table
          columns={columns}
          dataSource={report.feedbackStats.feedback}
          rowKey="id"
          expandable={{
            expandedRowRender: record => (
              <Space direction="vertical">
                {record.screenshots && record.screenshots.length > 0 && (
                  <div>
                    <Text strong>截图:</Text>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px', marginTop: '10px' }}>
                      {record.screenshots.map((screenshot, index) => (
                        <img
                          key={index}
                          src={screenshot}
                          alt={`Screenshot ${index + 1}`}
                          style={{ maxWidth: '200px', maxHeight: '150px', border: '1px solid #d9d9d9' }}
                        />
                      ))}
                    </div>
                  </div>
                )}
                {record.metadata && (
                  <Descriptions title="元数据" size="small" bordered>
                    {Object.entries(record.metadata).map(([key, value]) => (
                      <Descriptions.Item key={key} label={key}>{String(value)}</Descriptions.Item>
                    ))}
                  </Descriptions>
                )}
              </Space>
            )}}
        />
      </Space>
    );
  };
  
  // 渲染行为分析
  const renderBehaviorAnalysis = () => {
    if (!report.behaviorStats) {
      return (
        <Empty description="没有行为分析数据" />
      );
    }
    
    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        <Card title="操作统计">
          <Row gutter={16}>
            <Col span={8}>
              <Statistic
                title="总操作数"
                value={report.behaviorStats.totalActions}
              />
            </Col>
            {report.behaviorStats.hotspots && (
              <Col span={8}>
                <Statistic
                  title="热点区域数"
                  value={report.behaviorStats.hotspots.length}
                />
              </Col>
            )}
            {report.behaviorStats.navigationPaths && (
              <Col span={8}>
                <Statistic
                  title="导航路径数"
                  value={report.behaviorStats.navigationPaths.length}
                />
              </Col>
            )}
          </Row>
        </Card>
        
        {actions.length > 0 && (
          <Card title="操作回放">
            <UserActionPlayer
              actions={actions}
              showDetails={true}
              showStats={true}
            />
          </Card>
        )}
      </Space>
    );
  };
  
  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card
        title={
          <Space>
            <FileTextOutlined />
            <span>测试报告</span>
          </Space>
        }
        extra={
          <Space>
            {allowExport && (
              <Tooltip title="导出报告">
                <Button
                  icon={<ExportOutlined />}
                  onClick={() => handleExport('json')}
                >
                  导出
                </Button>
              </Tooltip>
            )}
            {allowPrint && (
              <Tooltip title="打印报告">
                <Button
                  icon={<PrinterOutlined />}
                  onClick={handlePrint}
                >
                  打印
                </Button>
              </Tooltip>
            )}
            {allowShare && (
              <Tooltip title="分享报告">
                <Button
                  icon={<ShareAltOutlined />}
                >
                  分享
                </Button>
              </Tooltip>
            )}
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <BarChartOutlined />
                摘要
              </span>
            }
            key="summary"
          >
            {renderSummary()}
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <CheckCircleOutlined />
                任务
              </span>
            }
            key="tasks"
          >
            {renderTasks()}
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <BulbOutlined />
                反馈
              </span>
            }
            key="feedback"
          >
            {renderFeedback()}
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <UserOutlined />
                行为分析
              </span>
            }
            key="behavior"
          >
            {renderBehaviorAnalysis()}
          </TabPane>
        </Tabs>
      </Card>
    </Space>
  );
};

export default TestReportViewer;
