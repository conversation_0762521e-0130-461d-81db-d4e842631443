/**
 * 冲突差异查看器组件
 * 用于可视化显示冲突的差异并提供合并建议
 */
import React, { useState, useEffect, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Tabs,
  Button,
  Space,
  Divider,
  Typography,
  Tag,
  Alert,
  Radio,
  Collapse,
  Empty,
  message
} from 'antd';
import {
  DiffOutlined,
  MergeCellsOutlined,
  CheckOutlined,
  CloseOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  UserOutlined,
  HistoryOutlined,
  SyncOutlined
} from '@ant-design/icons';
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';
import { RootState } from '../../store';
import { MergeConflict } from '../../services/RecursiveMergeService';
import { advancedRecursiveMergeService, AdvancedMergeStrategy } from '../../services/AdvancedRecursiveMergeService';
import { conflictResolutionService } from '../../services/ConflictResolutionService';
import { resolveConflict, setSelectedConflictId } from '../../store/collaboration/conflictSlice';
import { formatDateTime } from '../../utils/formatters';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;

// 差异查看器主题
const diffViewerTheme = {
  variables: {
    light: {
      diffViewerBackground: '#f7f7f7',
      diffViewerColor: '#212121',
      addedBackground: '#e6ffed',
      addedColor: '#24292e',
      removedBackground: '#ffebe9',
      removedColor: '#24292e',
      wordAddedBackground: '#acf2bd',
      wordRemovedBackground: '#fdb8c0',
      addedGutterBackground: '#cdffd8',
      removedGutterBackground: '#ffdce0',
      gutterBackground: '#f7f7f7',
      gutterBackgroundDark: '#f3f1f1',
      highlightBackground: '#fffbdd',
      highlightGutterBackground: '#fff5b1',
      codeFoldGutterBackground: '#dbedff',
      codeFoldBackground: '#f1f8ff',
      emptyLineBackground: '#fafbfc',
      gutterColor: '#212121',
      addedGutterColor: '#212121',
      removedGutterColor: '#212121',
      codeFoldContentColor: '#212121',
      diffViewerTitleBackground: '#fafbfc',
      diffViewerTitleColor: '#212121',
      diffViewerTitleBorderColor: '#eee'}
  }
};

// 合并策略选项
const mergeStrategyOptions = [
  { label: '优先本地', value: 'prefer_local' },
  { label: '优先远程', value: 'prefer_remote' },
  { label: '智能合并', value: 'smart_merge' },
  { label: '手动选择', value: 'manual' }
];

// 冲突差异查看器属性
interface ConflictDiffViewerProps {
  conflictId: string;
  onResolve?: () => void;
}

/**
 * 冲突差异查看器组件
 */
const ConflictDiffViewer: React.FC<ConflictDiffViewerProps> = ({ conflictId, onResolve }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取冲突信息
  const conflict = useSelector((state: RootState) =>
    state.conflict.conflicts.find(c => c.id === conflictId)
  );

  // 本地状态
  const [mergeStrategy, setMergeStrategy] = useState<string>('smart_merge');
  const [manualSelections, setManualSelections] = useState<Record<string, 'local' | 'remote'>>({});
  const [mergePreview, setMergePreview] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<string>('diff');
  const [diffMethod, setDiffMethod] = useState<DiffMethod>(DiffMethod.WORDS);

  // 如果冲突不存在，显示空状态
  if (!conflict) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description={t('collaboration.conflict.notFound')}
      />
    );
  }

  // 获取冲突的本地和远程数据
  const { localData, remoteData, mergeConflicts, timestamp, userId, userName } = conflict;

  // 格式化数据为字符串，用于差异查看器
  const formatDataForDiff = (data: any): string => {
    if (data === null || data === undefined) {
      return '';
    }

    try {
      return JSON.stringify(data, null, 2);
    } catch (error) {
      console.error('格式化数据时出错:', error);
      return String(data);
    }
  };

  // 本地和远程数据的格式化字符串
  const localDataStr = useMemo(() => formatDataForDiff(localData), [localData]);
  const remoteDataStr = useMemo(() => formatDataForDiff(remoteData), [remoteData]);

  // 生成合并预览
  useEffect(() => {
    if (mergeStrategy === 'manual') {
      // 手动合并预览
      generateManualMergePreview();
    } else {
      // 自动合并预览
      generateAutoMergePreview();
    }
  }, [mergeStrategy, manualSelections, localData, remoteData]);

  // 生成自动合并预览
  const generateAutoMergePreview = () => {
    try {
      let strategy;
      let advancedStrategy;

      switch (mergeStrategy) {
        case 'prefer_local':
          strategy = 'prefer_local';
          break;

        case 'prefer_remote':
          strategy = 'prefer_remote';
          break;

        case 'smart_merge':
        default:
          strategy = 'deep_merge';
          advancedStrategy = AdvancedMergeStrategy.SMART_ARRAY_MERGE;
          break;
      }

      const result = advancedRecursiveMergeService.mergeObjects(localData, remoteData, {
        strategy: strategy as any,
        advancedStrategy: advancedStrategy as any
      });

      setMergePreview(result.merged);
    } catch (error) {
      console.error('生成合并预览时出错:', error);
      message.error('生成合并预览时出错');
    }
  };

  // 生成手动合并预览
  const generateManualMergePreview = () => {
    try {
      // 创建合并结果的深拷贝
      const result = JSON.parse(JSON.stringify(localData));

      // 应用手动选择
      Object.entries(manualSelections).forEach(([path, selection]) => {
        const pathParts = path.split('.');
        let current = selection === 'local' ? localData : remoteData;

        // 遍历路径获取值
        for (const part of pathParts) {
          if (current === undefined || current === null) {
            break;
          }
          current = current[part];
        }

        // 设置值到结果
        let target = result;
        for (let i = 0; i < pathParts.length - 1; i++) {
          const part = pathParts[i];
          if (!(part in target)) {
            target[part] = {};
          }
          target = target[part];
        }

        target[pathParts[pathParts.length - 1]] = current;
      });

      setMergePreview(result);
    } catch (error) {
      console.error('生成手动合并预览时出错:', error);
      message.error('生成手动合并预览时出错');
    }
  };

  // 处理合并策略变更
  const handleMergeStrategyChange = (e: any) => {
    setMergeStrategy(e.target.value);
  };

  // 处理手动选择变更
  const handleManualSelectionChange = (path: string, value: 'local' | 'remote') => {
    setManualSelections(prev => ({
      ...prev,
      [path]: value
    }));
  };

  // 处理解决冲突
  const handleResolveConflict = () => {
    if (!mergePreview) {
      message.error('无法解决冲突：合并预览为空');
      return;
    }

    // 调用冲突解决服务
    conflictResolutionService.resolveConflict(conflictId, mergePreview)
      .then(() => {
        // 更新Redux状态
        dispatch(resolveConflict({ id: conflictId, resolvedData: mergePreview }));
        message.success('冲突已成功解决');

        // 清除选中的冲突
        dispatch(setSelectedConflictId(null));

        // 调用回调
        if (onResolve) {
          onResolve();
        }
      })
      .catch(error => {
        console.error('解决冲突时出错:', error);
        message.error('解决冲突时出错');
      });
  };

  // 渲染冲突项
  const renderConflictItem = (conflict: MergeConflict, index: number) => {
    const { path, localValue, remoteValue } = conflict;
    const pathStr = path.join('.');
    const selection = manualSelections[pathStr] || (mergeStrategy === 'prefer_local' ? 'local' : 'remote');

    return (
      <Panel
        key={index}
        header={
          <Space>
            <Text strong>{pathStr || '根对象'}</Text>
            <Tag color={selection === 'local' ? 'blue' : 'green'}>
              {selection === 'local' ? '本地值' : '远程值'}
            </Tag>
          </Space>
        }
      >
        <div className="conflict-item">
          <div className="conflict-values">
            <div className="local-value">
              <Title level={5}>本地值</Title>
              <pre>{formatDataForDiff(localValue)}</pre>
              {mergeStrategy === 'manual' && (
                <Button
                  type={selection === 'local' ? 'primary' : 'default'}
                  onClick={() => handleManualSelectionChange(pathStr, 'local')}
                >
                  选择本地值
                </Button>
              )}
            </div>

            <div className="remote-value">
              <Title level={5}>远程值</Title>
              <pre>{formatDataForDiff(remoteValue)}</pre>
              {mergeStrategy === 'manual' && (
                <Button
                  type={selection === 'remote' ? 'primary' : 'default'}
                  onClick={() => handleManualSelectionChange(pathStr, 'remote')}
                >
                  选择远程值
                </Button>
              )}
            </div>
          </div>
        </div>
      </Panel>
    );
  };

  // 渲染合并预览
  const renderMergePreview = () => {
    return (
      <div className="merge-preview">
        <Alert
          type="info"
          message={t('collaboration.conflict.mergePreviewInfo')}
          description={t('collaboration.conflict.mergePreviewDescription')}
          showIcon
        />
        <Divider />
        <pre className="merge-preview-content">
          {formatDataForDiff(mergePreview)}
        </pre>
      </div>
    );
  };

  return (
    <Card
      title={
        <Space>
          <DiffOutlined />
          <span>{t('collaboration.conflict.diffViewer')}</span>
        </Space>
      }
      extra={
        <Space>
          <Button
            type="primary"
            icon={<CheckOutlined />}
            onClick={handleResolveConflict}
            disabled={!mergePreview}
          >
            {t('collaboration.conflict.resolve')}
          </Button>
        </Space>
      }
      className="conflict-diff-viewer"
    >
      <div className="conflict-info">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            type="warning"
            message={t('collaboration.conflict.detected')}
            description={
              <Space direction="vertical">
                <Text>
                  <UserOutlined /> {t('collaboration.conflict.user')}: {userName}
                </Text>
                <Text>
                  <HistoryOutlined /> {t('collaboration.conflict.time')}: {formatDateTime(timestamp)}
                </Text>
              </Space>
            }
            showIcon
          />

          <div className="merge-strategy-selector">
            <Title level={5}>{t('collaboration.conflict.mergeStrategy')}</Title>
            <Radio.Group
              options={mergeStrategyOptions}
              onChange={handleMergeStrategyChange}
              value={mergeStrategy}
              optionType="button"
              buttonStyle="solid"
            />
          </div>
        </Space>
      </div>

      <Divider />

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <DiffOutlined />
              {t('collaboration.conflict.diff')}
            </span>
          }
          key="diff"
        >
          <div className="diff-viewer-container">
            <div className="diff-viewer-toolbar">
              <Radio.Group
                value={diffMethod}
                onChange={(e) => setDiffMethod(e.target.value)}
                size="small"
              >
                <Radio.Button value={DiffMethod.CHARS}>字符</Radio.Button>
                <Radio.Button value={DiffMethod.WORDS}>单词</Radio.Button>
                <Radio.Button value={DiffMethod.LINES}>行</Radio.Button>
              </Radio.Group>
            </div>

            <ReactDiffViewer
              oldValue={localDataStr}
              newValue={remoteDataStr}
              splitView={true}
              compareMethod={diffMethod}
              leftTitle={t('collaboration.conflict.localVersion')}
              rightTitle={t('collaboration.conflict.remoteVersion')}
              styles={diffViewerTheme}
            />
          </div>
        </TabPane>

        <TabPane
          tab={
            <span>
              <MergeCellsOutlined />
              {t('collaboration.conflict.conflicts')}
            </span>
          }
          key="conflicts"
        >
          {mergeConflicts.length > 0 ? (
            <Collapse>
              {mergeConflicts.map((conflict, index) => renderConflictItem(conflict, index))}
            </Collapse>
          ) : (
            <Empty description={t('collaboration.conflict.noConflicts')} />
          )}
        </TabPane>

        <TabPane
          tab={
            <span>
              <SyncOutlined />
              {t('collaboration.conflict.mergePreview')}
            </span>
          }
          key="preview"
        >
          {renderMergePreview()}
        </TabPane>
      </Tabs>

      <style jsx>{`
        .conflict-diff-viewer {
          margin-bottom: 16px;
        }

        .conflict-info {
          margin-bottom: 16px;
        }

        .merge-strategy-selector {
          margin-top: 16px;
        }

        .diff-viewer-container {
          border: 1px solid #eee;
          border-radius: 4px;
          overflow: hidden;
        }

        .diff-viewer-toolbar {
          padding: 8px;
          background-color: #f7f7f7;
          border-bottom: 1px solid #eee;
        }

        .conflict-item {
          padding: 8px;
        }

        .conflict-values {
          display: flex;
          flex-direction: row;
          gap: 16px;
        }

        .local-value, .remote-value {
          flex: 1;
          padding: 8px;
          border: 1px solid #eee;
          border-radius: 4px;
        }

        .local-value {
          background-color: #e6f7ff;
        }

        .remote-value {
          background-color: #f6ffed;
        }

        .merge-preview {
          padding: 8px;
        }

        .merge-preview-content {
          padding: 16px;
          background-color: #f7f7f7;
          border: 1px solid #eee;
          border-radius: 4px;
          overflow: auto;
          max-height: 500px;
        }
      `}</style>
    </Card>
  );
}

export default ConflictDiffViewer;