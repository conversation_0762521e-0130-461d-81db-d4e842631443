/**
 * 增强版面部肌肉模拟系统
 * 支持更多的物理驱动功能
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import type { World } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { CannonPhysicsEngine, PhysicsObjectType, PhysicsConstraintType } from './CannonPhysicsEngine';
import { FacialMuscleSimulationComponent, FacialMuscleSimulationConfig, MuscleConfig, MuscleType } from './FacialMuscleSimulation';
import { FacialExpressionType } from '../FacialAnimation';
import { FacialAnimationComponent } from '../../avatar/components/FacialAnimationComponent';

/**
 * 软体类型
 */
export enum SoftBodyType {
  /** 布料 */
  CLOTH = 'cloth',
  /** 绳索 */
  ROPE = 'rope',
  /** 可变形物体 */
  DEFORMABLE = 'deformable',
  /** 气球 */
  BALLOON = 'balloon',
  /** 果冻 */
  JELLY = 'jelly'
}

/**
 * 软体配置
 */
export interface SoftBodyConfig {
  /** 软体类型 */
  type: SoftBodyType;
  /** 软体名称 */
  name: string;
  /** 位置 */
  position: THREE.Vector3;
  /** 尺寸 */
  size: THREE.Vector3;
  /** 质量 */
  mass?: number;
  /** 弹性 */
  stiffness?: number;
  /** 阻尼 */
  damping?: number;
  /** 压力（仅适用于气球类型） */
  pressure?: number;
  /** 分辨率 */
  resolution?: THREE.Vector2;
  /** 是否固定边缘 */
  fixedEdges?: boolean;
  /** 是否使用重力 */
  useGravity?: boolean;
  /** 是否可撕裂（仅适用于布料和可变形物体） */
  tearable?: boolean;
  /** 撕裂阈值 */
  tearThreshold?: number;
}

/**
 * 增强版面部肌肉模拟系统配置
 */
export interface EnhancedFacialMuscleSimulationSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 重力 */
  gravity?: THREE.Vector3;
  /** 迭代次数 */
  iterations?: number;
  /** 是否使用软体 */
  useSoftBodies?: boolean;
  /** 是否使用GPU加速 */
  useGPU?: boolean;
  /** 是否使用自适应时间步长 */
  useAdaptiveTimeStep?: boolean;
  /** 最小时间步长 */
  minTimeStep?: number;
  /** 最大时间步长 */
  maxTimeStep?: number;
  /** 是否使用碰撞检测 */
  useCollisionDetection?: boolean;
  /** 是否使用连续碰撞检测 */
  useContinuousCollisionDetection?: boolean;
  /** 是否使用睡眠状态 */
  useSleepState?: boolean;
  /** 是否使用约束求解器 */
  useConstraintSolver?: boolean;
  /** 是否使用并行计算 */
  useParallelComputing?: boolean;
  /** 是否使用物理材质 */
  usePhysicsMaterials?: boolean;
  /** 是否使用物理调试渲染 */
  usePhysicsDebugRenderer?: boolean;
}

/**
 * 增强版面部肌肉模拟系统
 */
export class EnhancedFacialMuscleSimulationSystem extends System {
  /** 系统类型 */
  static readonly type = 'EnhancedFacialMuscleSimulation';

  /** 面部肌肉模拟组件 */
  private components: Map<Entity, FacialMuscleSimulationComponent> = new Map();

  /** 配置 */
  private config: EnhancedFacialMuscleSimulationSystemConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: EnhancedFacialMuscleSimulationSystemConfig = {
    debug: false,
    gravity: new THREE.Vector3(0, -9.81, 0),
    iterations: 10,
    useSoftBodies: true,
    useGPU: false,
    useAdaptiveTimeStep: true,
    minTimeStep: 1/240,
    maxTimeStep: 1/60,
    useCollisionDetection: true,
    useContinuousCollisionDetection: false,
    useSleepState: true,
    useConstraintSolver: true,
    useParallelComputing: false,
    usePhysicsMaterials: true,
    usePhysicsDebugRenderer: false
  };

  /** 物理引擎 */
  private physicsEngine: CannonPhysicsEngine;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 软体对象 */
  private softBodies: Map<string, any> = new Map();

  /** 物理调试渲染器 */
  private physicsDebugRenderer: any = null;

  /** 累积时间 */
  private accumulatedTime: number = 0;

  /** 固定时间步长 */
  private fixedTimeStep: number = 1/60;

  /** 是否使用GPU加速 */
  private useGPU: boolean = false;

  /** 是否使用自适应时间步长 */
  private useAdaptiveTimeStep: boolean = true;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config?: Partial<EnhancedFacialMuscleSimulationSystemConfig>) {
    super(100); // 设置优先级
    this.config = { ...EnhancedFacialMuscleSimulationSystem.DEFAULT_CONFIG, ...config };

    // 设置属性
    this.useGPU = this.config.useGPU || false;
    this.useAdaptiveTimeStep = this.config.useAdaptiveTimeStep || true;
    this.fixedTimeStep = this.config.minTimeStep || 1/60;

    // 创建物理引擎
    this.physicsEngine = new CannonPhysicsEngine({
      gravity: new CANNON.Vec3(
        this.config.gravity?.x || 0,
        this.config.gravity?.y || -9.81,
        this.config.gravity?.z || 0
      ),
      iterations: this.config.iterations,
      debug: this.config.debug
      // useSleepState: this.config.useSleepState, // 暂时注释掉，因为PhysicsEngineConfig中没有这个属性
      // useConstraintSolver: this.config.useConstraintSolver // 暂时注释掉，因为PhysicsEngineConfig中没有这个属性
    });

    // 创建物理调试渲染器
    if (this.config.usePhysicsDebugRenderer) {
      this.createPhysicsDebugRenderer();
    }
  }

  /**
   * 创建物理调试渲染器
   */
  private createPhysicsDebugRenderer(): void {
    // TODO: 实现物理调试渲染器
    if (this.config.debug) {
      console.log('创建物理调试渲染器');
    }
  }

  /**
   * 创建面部肌肉模拟组件
   * @param entity 实体
   * @param config 配置
   * @returns 面部肌肉模拟组件
   */
  public createFacialMuscleSimulation(entity: Entity, config?: Partial<FacialMuscleSimulationConfig>): FacialMuscleSimulationComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 合并配置
    const mergedConfig: FacialMuscleSimulationConfig = {
      debug: config?.debug !== undefined ? config.debug : this.config.debug,
      physicsEngine: this.physicsEngine,
      gravity: config?.gravity || this.config.gravity,
      iterations: config?.iterations || this.config.iterations,
      useSoftBodies: config?.useSoftBodies !== undefined ? config.useSoftBodies : this.config.useSoftBodies
    };

    // 创建新组件
    const component = new FacialMuscleSimulationComponent(this.physicsEngine, mergedConfig);
    entity.addComponent(component);
    this.components.set(entity, component);

    if (this.config.debug) {
      console.log(`创建面部肌肉模拟组件: ${entity.id}`);
    }

    return component;
  }

  /**
   * 移除面部肌肉模拟组件
   * @param entity 实体
   */
  public removeFacialMuscleSimulation(entity: Entity): void {
    if (this.components.has(entity)) {
      this.components.delete(entity);

      if (this.config.debug) {
        console.log(`移除面部肌肉模拟组件: ${entity.id}`);
      }
    }
  }

  /**
   * 获取面部肌肉模拟组件
   * @param entity 实体
   * @returns 面部肌肉模拟组件，如果不存在则返回null
   */
  public getFacialMuscleSimulation(entity: Entity): FacialMuscleSimulationComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 创建软体
   * @param entity 实体
   * @param config 软体配置
   * @returns 软体ID
   */
  public createSoftBody(entity: Entity, config: SoftBodyConfig): string {
    // 检查是否启用软体
    if (!this.config.useSoftBodies) {
      console.warn('软体功能未启用');
      return '';
    }

    // 创建软体ID
    const softBodyId = `${entity.id}_softbody_${config.name}`;

    // 根据软体类型创建不同的软体
    switch (config.type) {
      case SoftBodyType.CLOTH:
        this.createClothSoftBody(entity, config, softBodyId);
        break;

      case SoftBodyType.ROPE:
        this.createRopeSoftBody(entity, config, softBodyId);
        break;

      case SoftBodyType.DEFORMABLE:
        this.createDeformableSoftBody(entity, config, softBodyId);
        break;

      case SoftBodyType.BALLOON:
        this.createBalloonSoftBody(entity, config, softBodyId);
        break;

      case SoftBodyType.JELLY:
        this.createJellySoftBody(entity, config, softBodyId);
        break;

      default:
        console.warn(`不支持的软体类型: ${config.type}`);
        return '';
    }

    return softBodyId;
  }

  /**
   * 创建布料软体
   * @param entity 实体
   * @param config 软体配置
   * @param softBodyId 软体ID
   */
  private createClothSoftBody(entity: Entity, config: SoftBodyConfig, softBodyId: string): void {
    // 获取分辨率
    const resolution = config.resolution || new THREE.Vector2(10, 10);

    // 创建粒子
    const particles: CANNON.Body[] = [];
    const particleDistance = new THREE.Vector3(
      config.size.x / resolution.x,
      0,
      config.size.y / resolution.y
    );

    // 创建粒子网格
    for (let i = 0; i <= resolution.x; i++) {
      for (let j = 0; j <= resolution.y; j++) {
        const x = config.position.x - config.size.x / 2 + i * particleDistance.x;
        const y = config.position.y;
        const z = config.position.z - config.size.y / 2 + j * particleDistance.z;

        const particleId = `${softBodyId}_particle_${i}_${j}`;
        const isEdge = i === 0 || i === resolution.x || j === 0 || j === resolution.y;

        // 创建粒子
        const particle = this.physicsEngine.createBody(particleId, {
          type: PhysicsObjectType.RIGID_BODY,
          mass: isEdge && config.fixedEdges ? 0 : (config.mass || 0.1) / (resolution.x * resolution.y),
          position: new THREE.Vector3(x, y, z),
          shape: new CANNON.Sphere(particleDistance.x * 0.5),
          linearDamping: config.damping || 0.5,
          angularDamping: config.damping || 0.5,
          userData: { softBodyId, isEdge }
        });

        particles.push(particle);
      }
    }

    // 创建约束
    const constraints: CANNON.Constraint[] = [];
    const stiffness = config.stiffness || 100;

    // 创建水平和垂直约束
    for (let i = 0; i <= resolution.x; i++) {
      for (let j = 0; j <= resolution.y; j++) {
        const index = i * (resolution.y + 1) + j;

        // 水平约束
        if (i < resolution.x) {
          const rightIndex = (i + 1) * (resolution.y + 1) + j;
          const constraintId = `${softBodyId}_constraint_h_${i}_${j}`;

          const constraint = this.physicsEngine.createConstraint(constraintId, {
            type: PhysicsConstraintType.SPRING,
            bodyA: particles[index],
            bodyB: particles[rightIndex],
            userData: { softBodyId, stiffness, damping: config.damping || 0.5, restLength: particleDistance.x }
          });

          constraints.push(constraint);
        }

        // 垂直约束
        if (j < resolution.y) {
          const bottomIndex = i * (resolution.y + 1) + (j + 1);
          const constraintId = `${softBodyId}_constraint_v_${i}_${j}`;

          const constraint = this.physicsEngine.createConstraint(constraintId, {
            type: PhysicsConstraintType.SPRING,
            bodyA: particles[index],
            bodyB: particles[bottomIndex],
            userData: { softBodyId, stiffness, damping: config.damping || 0.5, restLength: particleDistance.z }
          });

          constraints.push(constraint);
        }

        // 对角线约束（增加稳定性）
        if (i < resolution.x && j < resolution.y) {
          const diagonalIndex = (i + 1) * (resolution.y + 1) + (j + 1);
          const constraintId = `${softBodyId}_constraint_d1_${i}_${j}`;

          const diagonalDistance = Math.sqrt(particleDistance.x * particleDistance.x + particleDistance.z * particleDistance.z);

          const constraint = this.physicsEngine.createConstraint(constraintId, {
            type: PhysicsConstraintType.SPRING,
            bodyA: particles[index],
            bodyB: particles[diagonalIndex],
            userData: { softBodyId, stiffness: stiffness * 0.5, damping: config.damping || 0.5, restLength: diagonalDistance }
          });

          constraints.push(constraint);
        }
      }
    }

    // 存储软体信息
    this.softBodies.set(softBodyId, {
      type: SoftBodyType.CLOTH,
      config,
      particles,
      constraints,
      resolution,
      entity
    });

    if (this.config.debug) {
      console.log(`创建布料软体: ${softBodyId}`);
    }
  }

  /**
   * 创建绳索软体
   * @param entity 实体
   * @param config 软体配置
   * @param softBodyId 软体ID
   */
  private createRopeSoftBody(entity: Entity, config: SoftBodyConfig, softBodyId: string): void {
    // 获取分辨率（绳索只需要一维分辨率）
    const segments = config.resolution ? config.resolution.x : 10;

    // 创建粒子
    const particles: CANNON.Body[] = [];
    const particleDistance = config.size.x / segments;

    // 创建粒子链
    for (let i = 0; i <= segments; i++) {
      const x = config.position.x - config.size.x / 2 + i * particleDistance;
      const y = config.position.y;
      const z = config.position.z;

      const particleId = `${softBodyId}_particle_${i}`;
      const isEnd = i === 0 || i === segments;

      // 创建粒子
      const particle = this.physicsEngine.createBody(particleId, {
        type: PhysicsObjectType.RIGID_BODY,
        mass: isEnd && config.fixedEdges ? 0 : (config.mass || 0.1) / segments,
        position: new THREE.Vector3(x, y, z),
        shape: new CANNON.Sphere(particleDistance * 0.5),
        linearDamping: config.damping || 0.5,
        angularDamping: config.damping || 0.5,
        userData: { softBodyId, isEnd }
      });

      particles.push(particle);
    }

    // 创建约束
    const constraints: CANNON.Constraint[] = [];
    const stiffness = config.stiffness || 100;

    // 创建相邻粒子之间的约束
    for (let i = 0; i < segments; i++) {
      const constraintId = `${softBodyId}_constraint_${i}`;

      const constraint = this.physicsEngine.createConstraint(constraintId, {
        type: PhysicsConstraintType.SPRING,
        bodyA: particles[i],
        bodyB: particles[i + 1],
        userData: { softBodyId, stiffness, damping: config.damping || 0.5, restLength: particleDistance }
      });

      constraints.push(constraint);
    }

    // 存储软体信息
    this.softBodies.set(softBodyId, {
      type: SoftBodyType.ROPE,
      config,
      particles,
      constraints,
      segments,
      entity
    });

    if (this.config.debug) {
      console.log(`创建绳索软体: ${softBodyId}`);
    }
  }

  /**
   * 创建可变形软体
   * @param entity 实体
   * @param config 软体配置
   * @param softBodyId 软体ID
   */
  private createDeformableSoftBody(entity: Entity, config: SoftBodyConfig, softBodyId: string): void {
    // 获取分辨率
    const resolution = config.resolution || new THREE.Vector2(5, 5);

    // 创建3D网格的粒子
    const particles: CANNON.Body[] = [];
    const particleDistance = new THREE.Vector3(
      config.size.x / resolution.x,
      config.size.y / resolution.y,
      config.size.z / (resolution.y || resolution.x)
    );

    // 创建粒子网格
    for (let i = 0; i <= resolution.x; i++) {
      for (let j = 0; j <= resolution.y; j++) {
        for (let k = 0; k <= (resolution.y || resolution.x); k++) {
          const x = config.position.x - config.size.x / 2 + i * particleDistance.x;
          const y = config.position.y - config.size.y / 2 + j * particleDistance.y;
          const z = config.position.z - config.size.z / 2 + k * particleDistance.z;

          const particleId = `${softBodyId}_particle_${i}_${j}_${k}`;
          const isEdge = i === 0 || i === resolution.x ||
                         j === 0 || j === resolution.y ||
                         k === 0 || k === (resolution.y || resolution.x);

          // 创建粒子
          const particle = this.physicsEngine.createBody(particleId, {
            type: PhysicsObjectType.RIGID_BODY,
            mass: isEdge && config.fixedEdges ? 0 : (config.mass || 1) / (resolution.x * resolution.y * (resolution.y || resolution.x)),
            position: new THREE.Vector3(x, y, z),
            shape: new CANNON.Sphere(Math.min(particleDistance.x, particleDistance.y, particleDistance.z) * 0.5),
            linearDamping: config.damping || 0.5,
            angularDamping: config.damping || 0.5,
            userData: { softBodyId, isEdge }
          });

          particles.push(particle);
        }
      }
    }

    // 创建约束
    const constraints: CANNON.Constraint[] = [];
    const stiffness = config.stiffness || 100;

    // TODO: 创建3D网格的约束

    // 存储软体信息
    this.softBodies.set(softBodyId, {
      type: SoftBodyType.DEFORMABLE,
      config,
      particles,
      constraints,
      resolution,
      entity
    });

    if (this.config.debug) {
      console.log(`创建可变形软体: ${softBodyId}`);
    }
  }

  /**
   * 创建气球软体
   * @param entity 实体
   * @param config 软体配置
   * @param softBodyId 软体ID
   */
  private createBalloonSoftBody(entity: Entity, config: SoftBodyConfig, softBodyId: string): void {
    // TODO: 实现气球软体
    if (this.config.debug) {
      console.log(`创建气球软体: ${softBodyId}`);
    }
  }

  /**
   * 创建果冻软体
   * @param entity 实体
   * @param config 软体配置
   * @param softBodyId 软体ID
   */
  private createJellySoftBody(entity: Entity, config: SoftBodyConfig, softBodyId: string): void {
    // TODO: 实现果冻软体
    if (this.config.debug) {
      console.log(`创建果冻软体: ${softBodyId}`);
    }
  }

  /**
   * 移除软体
   * @param softBodyId 软体ID
   * @returns 是否成功移除
   */
  public removeSoftBody(softBodyId: string): boolean {
    const softBody = this.softBodies.get(softBodyId);
    if (!softBody) return false;

    // 移除约束
    if (softBody.constraints) {
      for (const constraint of softBody.constraints) {
        this.physicsEngine.removeConstraint(constraint.id);
      }
    }

    // 移除粒子
    if (softBody.particles) {
      for (const particle of softBody.particles) {
        this.physicsEngine.removeBody(particle.id);
      }
    }

    // 移除软体
    this.softBodies.delete(softBodyId);

    if (this.config.debug) {
      console.log(`移除软体: ${softBodyId}`);
    }

    return true;
  }

  /**
   * 获取软体
   * @param softBodyId 软体ID
   * @returns 软体对象
   */
  public getSoftBody(softBodyId: string): any {
    return this.softBodies.get(softBodyId) || null;
  }

  /**
   * 应用力到软体
   * @param softBodyId 软体ID
   * @param force 力
   * @param position 位置（可选，默认为软体中心）
   * @param radius 影响半径（可选，默认为整个软体）
   * @returns 是否成功应用
   */
  public applySoftBodyForce(softBodyId: string, force: THREE.Vector3, position?: THREE.Vector3, radius?: number): boolean {
    const softBody = this.softBodies.get(softBodyId);
    if (!softBody || !softBody.particles) return false;

    const cannonForce = new CANNON.Vec3(force.x, force.y, force.z);

    // 如果没有指定位置，应用到所有粒子
    if (!position) {
      for (const particle of softBody.particles) {
        particle.applyForce(cannonForce, particle.position);
      }
      return true;
    }

    // 如果指定了位置，只应用到范围内的粒子
    const cannonPosition = new CANNON.Vec3(position.x, position.y, position.z);

    for (const particle of softBody.particles) {
      const distance = particle.position.distanceTo(cannonPosition);

      // 如果没有指定半径或在半径内
      if (!radius || distance <= radius) {
        // 力随距离衰减
        const factor = radius ? 1 - (distance / radius) : 1;
        const scaledForce = cannonForce.scale(factor);

        particle.applyForce(scaledForce, particle.position);
      }
    }

    return true;
  }

  /**
   * 应用表情到软体
   * @param entity 实体
   * @param expression 表情类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public applySoftBodyExpression(entity: Entity, expression: FacialExpressionType, weight: number): boolean {
    // 获取实体的所有软体
    const entitySoftBodies = Array.from(this.softBodies.values())
      .filter(softBody => softBody.entity === entity);

    if (entitySoftBodies.length === 0) return false;

    // 根据表情类型应用不同的力
    switch (expression) {
      case FacialExpressionType.HAPPY:
        this.applySoftBodyHappyExpression(entitySoftBodies, weight);
        break;

      case FacialExpressionType.SAD:
        this.applySoftBodySadExpression(entitySoftBodies, weight);
        break;

      case FacialExpressionType.ANGRY:
        this.applySoftBodyAngryExpression(entitySoftBodies, weight);
        break;

      case FacialExpressionType.SURPRISED:
        this.applySoftBodySurprisedExpression(entitySoftBodies, weight);
        break;

      case FacialExpressionType.FEAR:
        this.applySoftBodyFearfulExpression(entitySoftBodies, weight);
        break;

      case FacialExpressionType.DISGUSTED:
        this.applySoftBodyDisgustedExpression(entitySoftBodies, weight);
        break;

      // case FacialExpressionType.CONTEMPT:
      //   this.applySoftBodyContemptExpression(entitySoftBodies, weight);
      //   break;

      case FacialExpressionType.NEUTRAL:
      default:
        this.resetSoftBodies(entitySoftBodies);
        break;
    }

    return true;
  }

  /**
   * 应用开心表情到软体
   * @param softBodies 软体数组
   * @param weight 权重
   */
  private applySoftBodyHappyExpression(softBodies: any[], weight: number): void {
    for (const softBody of softBodies) {
      if (softBody.type === SoftBodyType.CLOTH || softBody.type === SoftBodyType.DEFORMABLE) {
        // 嘴角上扬
        const force = new THREE.Vector3(0, weight * 5, 0);
        const position = new THREE.Vector3(
          (softBody.config as any).getPosition().x,
          (softBody.config as any).getPosition().y - softBody.config.size.y * 0.3,
          (softBody.config as any).getPosition().z
        );
        const radius = softBody.config.size.x * 0.3;

        this.applySoftBodyForce(softBody.id, force, position, radius);
      }
    }
  }

  /**
   * 应用悲伤表情到软体
   * @param softBodies 软体数组
   * @param weight 权重
   */
  private applySoftBodySadExpression(softBodies: any[], weight: number): void {
    for (const softBody of softBodies) {
      if (softBody.type === SoftBodyType.CLOTH || softBody.type === SoftBodyType.DEFORMABLE) {
        // 嘴角下垂
        const force = new THREE.Vector3(0, -weight * 5, 0);
        const position = new THREE.Vector3(
          (softBody.config as any).getPosition().x,
          (softBody.config as any).getPosition().y - softBody.config.size.y * 0.3,
          (softBody.config as any).getPosition().z
        );
        const radius = softBody.config.size.x * 0.3;

        this.applySoftBodyForce(softBody.id, force, position, radius);

        // 眉毛内侧上扬
        const eyebrowForce = new THREE.Vector3(0, weight * 3, 0);
        const eyebrowPosition = new THREE.Vector3(
          (softBody.config as any).getPosition().x,
          (softBody.config as any).getPosition().y + softBody.config.size.y * 0.3,
          (softBody.config as any).getPosition().z
        );
        const eyebrowRadius = softBody.config.size.x * 0.2;

        this.applySoftBodyForce(softBody.id, eyebrowForce, eyebrowPosition, eyebrowRadius);
      }
    }
  }

  /**
   * 应用愤怒表情到软体
   * @param softBodies 软体数组
   * @param weight 权重
   */
  private applySoftBodyAngryExpression(softBodies: any[], weight: number): void {
    // TODO: 实现愤怒表情
  }

  /**
   * 应用惊讶表情到软体
   * @param softBodies 软体数组
   * @param weight 权重
   */
  private applySoftBodySurprisedExpression(softBodies: any[], weight: number): void {
    // TODO: 实现惊讶表情
  }

  /**
   * 应用恐惧表情到软体
   * @param softBodies 软体数组
   * @param weight 权重
   */
  private applySoftBodyFearfulExpression(softBodies: any[], weight: number): void {
    // TODO: 实现恐惧表情
  }

  /**
   * 应用厌恶表情到软体
   * @param softBodies 软体数组
   * @param weight 权重
   */
  private applySoftBodyDisgustedExpression(softBodies: any[], weight: number): void {
    // TODO: 实现厌恶表情
  }

  /**
   * 应用蔑视表情到软体
   * @param softBodies 软体数组
   * @param weight 权重
   */
  private applySoftBodyContemptExpression(softBodies: any[], weight: number): void {
    // TODO: 实现蔑视表情
  }

  /**
   * 重置软体
   * @param softBodies 软体数组
   */
  private resetSoftBodies(softBodies: any[]): void {
    for (const softBody of softBodies) {
      if (softBody.particles) {
        for (const particle of softBody.particles) {
          particle.velocity.set(0, 0, 0);
          particle.angularVelocity.set(0, 0, 0);
        }
      }
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 使用自适应时间步长
    if (this.useAdaptiveTimeStep) {
      // 限制时间步长
      const clampedDeltaTime = Math.min(
        Math.max(deltaTime, this.config.minTimeStep || 1/240),
        this.config.maxTimeStep || 1/60
      );

      // 累积时间
      this.accumulatedTime += clampedDeltaTime;

      // 固定时间步长更新
      while (this.accumulatedTime >= this.fixedTimeStep) {
        // 更新物理引擎
        this.physicsEngine.update(this.fixedTimeStep);

        // 减少累积时间
        this.accumulatedTime -= this.fixedTimeStep;
      }
    } else {
      // 直接更新物理引擎
      this.physicsEngine.update(deltaTime);
    }

    // 更新所有面部肌肉模拟组件
    for (const component of this.components.values()) {
      component.update(deltaTime);
    }

    // 更新物理调试渲染器
    if (this.physicsDebugRenderer) {
      this.physicsDebugRenderer.update();
    }

    // 发送更新事件
    this.eventEmitter.emit('update', { deltaTime });
  }
}
