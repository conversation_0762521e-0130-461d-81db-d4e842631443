/**
 * 编辑器集成测试
 */
import { render, screen, waitFor } from '../../__tests__/utils/test-utils';
import { EditorPage } from '../../pages/EditorPage';
import { jest } from '@jest/globals';
import { MemoryRouter, Route, Routes } from 'react-router-dom';

// 模拟服务
jest.mock('../../services/EngineService', () => ({
  __esModule: true,
  default: {
    initialize: jest.fn().mockResolvedValue(undefined),
    on: jest.fn(),
    off: jest.fn(),
    getActiveScene: jest.fn(),
    getActiveCamera: jest.fn(),
    EngineEventType: {
      OBJECT_SELECTED: 'object-selected',
      OBJECT_DESELECTED: 'object-deselected'}},
  EngineEventType: {
    OBJECT_SELECTED: 'object-selected',
    OBJECT_DESELECTED: 'object-deselected'},
  TransformMode: {
    TRANSLATE: 'translate',
    ROTATE: 'rotate',
    SCALE: 'scale'},
  TransformSpace: {
    LOCAL: 'local',
    WORLD: 'world'}}));

jest.mock('../../services/SceneService', () => ({
  __esModule: true,
  default: {
    loadScene: jest.fn().mockResolvedValue(undefined),
    on: jest.fn(),
    off: jest.fn(),
    SceneEventType: {
      SCENE_LOADED: 'scene-loaded',
      SCENE_UNLOADED: 'scene-unloaded'}},
  SceneEventType: {
    SCENE_LOADED: 'scene-loaded',
    SCENE_UNLOADED: 'scene-unloaded'}}));

jest.mock('../../services/ProjectService', () => ({
  __esModule: true,
  default: {
    getProject: jest.fn().mockResolvedValue({
      id: 'test-project',
      name: '测试项目',
      description: '测试项目描述',
      scenes: [
        {
          id: 'test-scene',
          name: '测试场景'},
      ]}),
    getScenes: jest.fn().mockResolvedValue([
      {
        id: 'test-scene',
        name: '测试场景'},
    ])}}));

// 模拟rc-dock
jest.mock('rc-dock', () => ({
  DockLayout: ({ defaultLayout, onLayoutChange, style }: any) => (
    <div data-testid="mock-dock-layout" style={style}>
      <div data-testid="mock-dock-layout-content">
        {JSON.stringify(defaultLayout)}
      </div>
      <button 
        data-testid="mock-layout-change-button"
        onClick={() => onLayoutChange && onLayoutChange({ test: 'new-layout' })}
      >
        Change Layout
      </button>
    </div>
  )}));

// 模拟组件
jest.mock('../../components/layout/EditorLayout', () => ({
  __esModule: true,
  EditorLayout: ({ projectId, sceneId }: any) => (
    <div data-testid="mock-editor-layout">
      <div>Project ID: {projectId}</div>
      <div>Scene ID: {sceneId}</div>
    </div>
  )}));

describe('编辑器集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染编辑器页面', async () => {
    render(
      <MemoryRouter initialEntries={['/editor/test-project/test-scene']}>
        <Routes>
          <Route path="/editor/:projectId/:sceneId" element={<EditorPage />} />
        </Routes>
      </MemoryRouter>
    );

    // 等待编辑器布局加载
    await waitFor(() => {
      expect(screen.getByTestId('mock-editor-layout')).toBeInTheDocument();
    });

    // 验证项目ID和场景ID已正确传递
    expect(screen.getByText('Project ID: test-project')).toBeInTheDocument();
    expect(screen.getByText('Scene ID: test-scene')).toBeInTheDocument();
  });

  it('应该在加载编辑器时显示加载状态', async () => {
    // 模拟ProjectService.getProject返回一个延迟的Promise
    const mockGetProject = jest.fn().mockImplementation(() => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            id: 'test-project',
            name: '测试项目',
            description: '测试项目描述',
            scenes: [
              {
                id: 'test-scene',
                name: '测试场景'},
            ]});
        }, 100);
      });
    });

    // 替换模拟实现
    jest.mock('../../services/ProjectService', () => ({
      __esModule: true,
      default: {
        getProject: mockGetProject,
        getScenes: jest.fn().mockResolvedValue([
          {
            id: 'test-scene',
            name: '测试场景'},
        ])}}), { virtual: true });

    render(
      <MemoryRouter initialEntries={['/editor/test-project/test-scene']}>
        <Routes>
          <Route path="/editor/:projectId/:sceneId" element={<EditorPage />} />
        </Routes>
      </MemoryRouter>
    );

    // 验证加载状态
    expect(screen.getByText('editor.loading')).toBeInTheDocument();

    // 等待加载完成
    await waitFor(() => {
      expect(screen.getByTestId('mock-editor-layout')).toBeInTheDocument();
    });
  });

  it('应该处理无效的项目ID或场景ID', async () => {
    // 模拟ProjectService.getProject返回错误
    const mockGetProject = jest.fn().mockRejectedValue(new Error('项目不存在'));

    // 替换模拟实现
    jest.mock('../../services/ProjectService', () => ({
      __esModule: true,
      default: {
        getProject: mockGetProject,
        getScenes: jest.fn().mockResolvedValue([])}}), { virtual: true });

    render(
      <MemoryRouter initialEntries={['/editor/invalid-project/invalid-scene']}>
        <Routes>
          <Route path="/editor/:projectId/:sceneId" element={<EditorPage />} />
        </Routes>
      </MemoryRouter>
    );

    // 等待错误消息显示
    await waitFor(() => {
      expect(screen.getByText('editor.projectNotFound')).toBeInTheDocument();
    });
  });
});
