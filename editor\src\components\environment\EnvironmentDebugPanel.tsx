/**
 * 环境调试面板
 * 
 * 该面板提供环境感知和响应系统的调试功能，包括日志、状态检查等。
 */
import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Tabs, 
  Button, 
  Space, 
  List, 
  Tag, 
  Tooltip, 
  Switch, 
  Select,
  Typography,
  Badge,
  Collapse,
  message
} from 'antd';
import { 
  BugOutlined, 
  ClearOutlined, 
  DownloadOutlined, 
  ReloadOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SettingOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
// 移除引擎直接导入
// 移除引擎直接导入

const { TabPane } = Tabs;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { Text } = Typography;
const { Panel } = Collapse;
const { Search } = Input;

/**
 * 日志级别枚举
 */
enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error'
}

/**
 * 日志项接口
 */
interface LogItem {
  id: string;
  timestamp: number;
  level: LogLevel;
  message: string;
  details?: any;
}

/**
 * 环境调试面板属性接口
 */
interface EnvironmentDebugPanelProps {
  entityId?: string;
  environmentData?: EnvironmentAwarenessData;
  activeResponses?: Map<string, any[]>;
  logs?: LogItem[];
  onClearLogs?: () => void;
  onRefresh?: () => void;
  onToggleDebug?: (enabled: boolean) => void;
  debugEnabled?: boolean;
}

/**
 * 环境调试面板组件
 */
const EnvironmentDebugPanel: React.FC<EnvironmentDebugPanelProps> = ({
  entityId,
  environmentData,
  activeResponses = new Map(),
  logs = [],
  onClearLogs,
  onRefresh,
  onToggleDebug,
  debugEnabled = false
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('logs');
  const [filteredLogs, setFilteredLogs] = useState<LogItem[]>(logs);
  const [logLevelFilter, setLogLevelFilter] = useState<string[]>(['debug', 'info', 'warning', 'error']);
  const [searchValue, setSearchValue] = useState<string>('');
  const [autoScroll, setAutoScroll] = useState<boolean>(true);
  const logsEndRef = useRef<HTMLDivElement>(null);

  // 过滤日志
  useEffect(() => {
    filterLogs(logs, logLevelFilter, searchValue);
  }, [logs, logLevelFilter, searchValue]);

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [filteredLogs, autoScroll]);

  /**
   * 过滤日志
   * @param logs 日志
   * @param levels 级别
   * @param search 搜索
   */
  const filterLogs = (logs: LogItem[], levels: string[], search: string) => {
    let filtered = logs.filter(log => levels.includes(log.level));
    
    if (search) {
      const lowerSearch = search.toLowerCase();
      filtered = filtered.filter(log => 
        log.message.toLowerCase().includes(lowerSearch) || 
        (log.details && JSON.stringify(log.details).toLowerCase().includes(lowerSearch))
      );
    }
    
    setFilteredLogs(filtered);
  };

  /**
   * 处理日志级别过滤变更
   * @param levels 级别
   */
  const handleLogLevelFilterChange = (levels: string[]) => {
    setLogLevelFilter(levels);
  };

  /**
   * 处理搜索
   * @param value 搜索值
   */
  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  /**
   * 处理清除日志
   */
  const handleClearLogs = () => {
    if (onClearLogs) {
      onClearLogs();
      message.success(t('environment.logsCleared'));
    }
  };

  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
      message.success(t('environment.dataRefreshed'));
    }
  };

  /**
   * 处理切换调试
   */
  const handleToggleDebug = () => {
    if (onToggleDebug) {
      onToggleDebug(!debugEnabled);
    }
  };

  /**
   * 处理下载日志
   */
  const handleDownloadLogs = () => {
    try {
      // 创建要下载的数据
      const dataToDownload = {
        logs: logs,
        timestamp: new Date().toISOString(),
        entityId
      };

      // 转换为JSON字符串
      const jsonString = JSON.stringify(dataToDownload, null, 2);
      
      // 创建Blob对象
      const blob = new Blob([jsonString], { type: 'application/json' });
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `environment_logs_${new Date().toISOString()}.json`;
      
      // 触发下载
      document.body.appendChild(a);
      a.click();
      
      // 清理
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      message.success(t('environment.logsDownloaded'));
    } catch (error) {
      console.error('下载日志失败:', error);
      message.error(t('environment.downloadFailed'));
    }
  };

  /**
   * 获取日志级别标签
   * @param level 级别
   * @returns 标签
   */
  const getLogLevelTag = (level: LogLevel) => {
    switch (level) {
      case LogLevel.DEBUG:
        return <Tag color="blue">{t('environment.debug')}</Tag>;
      case LogLevel.INFO:
        return <Tag color="green">{t('environment.info')}</Tag>;
      case LogLevel.WARNING:
        return <Tag color="orange">{t('environment.warning')}</Tag>;
      case LogLevel.ERROR:
        return <Tag color="red">{t('environment.error')}</Tag>;
      default:
        return <Tag>{level}</Tag>;
    }
  };

  /**
   * 格式化时间戳
   * @param timestamp 时间戳
   * @returns 格式化的时间
   */
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString() + '.' + date.getMilliseconds().toString().padStart(3, '0');
  };

  /**
   * 渲染日志标签页
   */
  const renderLogsTab = () => {
    return (
      <div className="logs-tab">
        <div className="logs-controls">
          <Space style={{ marginBottom: 16 }}>
            <Select
              mode="multiple"
              placeholder={t('environment.selectLogLevels')}
              value={logLevelFilter}
              onChange={handleLogLevelFilterChange}
              style={{ minWidth: 200 }}
            >
              <Option value="debug">{t('environment.debug')}</Option>
              <Option value="info">{t('environment.info')}</Option>
              <Option value="warning">{t('environment.warning')}</Option>
              <Option value="error">{t('environment.error')}</Option>
            </Select>
            
            <Search
              placeholder={t('environment.searchLogs')}
              allowClear
              onSearch={handleSearch}
              style={{ width: 200 }}
            />
            
            <Switch
              checkedChildren={t('environment.autoScroll')}
              unCheckedChildren={t('environment.manualScroll')}
              checked={autoScroll}
              onChange={setAutoScroll}
            />
            
            <Button
              icon={<ClearOutlined />}
              onClick={handleClearLogs}
            >
              {t('environment.clearLogs')}
            </Button>
            
            <Button
              icon={<DownloadOutlined />}
              onClick={handleDownloadLogs}
            >
              {t('environment.downloadLogs')}
            </Button>
          </Space>
        </div>
        
        <div className="logs-container" style={{ maxHeight: 400, overflow: 'auto' }}>
          {filteredLogs.length > 0 ? (
            <List
              itemLayout="horizontal"
              dataSource={filteredLogs}
              renderItem={log => (
                <List.Item key={log.id}>
                  <Space>
                    <Text type="secondary">[{formatTimestamp(log.timestamp)}]</Text>
                    {getLogLevelTag(log.level)}
                    <Text>{log.message}</Text>
                    {log.details && (
                      <Tooltip title={JSON.stringify(log.details, null, 2)}>
                        <InfoCircleOutlined />
                      </Tooltip>
                    )}
                  </Space>
                </List.Item>
              )}
            />
          ) : (
            <div className="empty-logs">
              <p>{t('environment.noLogs')}</p>
            </div>
          )}
          <div ref={logsEndRef} />
        </div>
      </div>
    );
  };

  /**
   * 渲染活动响应标签页
   */
  const renderActiveResponsesTab = () => {
    const responseEntries = Array.from(activeResponses.entries());
    
    return (
      <div className="active-responses-tab">
        <div className="responses-header">
          <Space style={{ marginBottom: 16 }}>
            <Badge count={responseEntries.length} overflowCount={99} style={{ backgroundColor: '#52c41a' }}>
              <Tag>{t('environment.activeResponses')}</Tag>
            </Badge>
            
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              {t('environment.refresh')}
            </Button>
          </Space>
        </div>
        
        <div className="responses-container">
          {responseEntries.length > 0 ? (
            <Collapse defaultActiveKey={responseEntries.map(([id]) => id)}>
              {responseEntries.map(([ruleId, actions]) => (
                <Panel 
                  key={ruleId} 
                  header={
                    <Space>
                      <Text strong>{ruleId}</Text>
                      <Badge count={actions.length} overflowCount={99} style={{ backgroundColor: '#108ee9' }}>
                        <Tag>{t('environment.actions')}</Tag>
                      </Badge>
                    </Space>
                  }
                >
                  <List
                    itemLayout="horizontal"
                    dataSource={actions}
                    renderItem={(action, index) => (
                      <List.Item key={index}>
                        <Space>
                          <Tag color="blue">{action.type}</Tag>
                          <Text>{JSON.stringify(action.params)}</Text>
                        </Space>
                      </List.Item>
                    )}
                  />
                </Panel>
              ))}
            </Collapse>
          ) : (
            <div className="empty-responses">
              <p>{t('environment.noActiveResponses')}</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  /**
   * 渲染环境状态标签页
   */
  const renderEnvironmentStateTab = () => {
    if (!environmentData) {
      return (
        <div className="no-data">
          <p>{t('environment.noData')}</p>
          <Button type="primary" onClick={handleRefresh}>
            {t('environment.refresh')}
          </Button>
        </div>
      );
    }

    return (
      <div className="environment-state-tab">
        <div className="state-header">
          <Space style={{ marginBottom: 16 }}>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              {t('environment.refresh')}
            </Button>
          </Space>
        </div>
        
        <Collapse defaultActiveKey={['basic', 'conditions', 'other']}>
          <Panel header={t('environment.basicInfo')} key="basic">
            <List>
              <List.Item>
                <Text strong>{t('environment.environmentType')}:</Text>
                <Text>{environmentData.environmentType}</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.weatherType')}:</Text>
                <Text>{environmentData.weatherType}</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.terrainType')}:</Text>
                <Text>{environmentData.terrainType}</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.timeOfDay')}:</Text>
                <Text>{Math.floor(environmentData.timeOfDay)}:{(environmentData.timeOfDay % 1 * 60).toFixed(0).padStart(2, '0')}</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.lastChangeTime')}:</Text>
                <Text>{new Date(environmentData.lastEnvironmentChangeTime).toLocaleString()}</Text>
              </List.Item>
            </List>
          </Panel>
          
          <Panel header={t('environment.conditions')} key="conditions">
            <List>
              <List.Item>
                <Text strong>{t('environment.temperature')}:</Text>
                <Text>{environmentData.temperature.toFixed(1)}°C</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.humidity')}:</Text>
                <Text>{(environmentData.humidity * 100).toFixed(0)}%</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.windSpeed')}:</Text>
                <Text>{environmentData.windSpeed.toFixed(1)} m/s</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.windDirection')}:</Text>
                <Text>X: {environmentData.windDirection.x.toFixed(2)}, Y: {environmentData.windDirection.y.toFixed(2)}, Z: {environmentData.windDirection.z.toFixed(2)}</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.visibility')}:</Text>
                <Text>{environmentData.visibility.toFixed(0)} m</Text>
              </List.Item>
            </List>
          </Panel>
          
          <Panel header={t('environment.other')} key="other">
            <List>
              <List.Item>
                <Text strong>{t('environment.lightIntensity')}:</Text>
                <Text>{(environmentData.lightIntensity * 100).toFixed(0)}%</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.noiseLevel')}:</Text>
                <Text>{(environmentData.noiseLevel * 100).toFixed(0)}%</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.airQuality')}:</Text>
                <Text>{(environmentData.airQuality * 100).toFixed(0)}%</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.waterLevel')}:</Text>
                <Text>{environmentData.waterLevel.toFixed(1)} m</Text>
              </List.Item>
              <List.Item>
                <Text strong>{t('environment.awarenessRange')}:</Text>
                <Text>{environmentData.awarenessRange.toFixed(0)} m</Text>
              </List.Item>
            </List>
          </Panel>
          
          <Panel header={t('environment.customParameters')} key="custom">
            {environmentData.customParameters.size > 0 ? (
              <List>
                {Array.from(environmentData.customParameters.entries()).map(([key, value]) => (
                  <List.Item key={key}>
                    <Text strong>{key}:</Text>
                    <Text>{JSON.stringify(value)}</Text>
                  </List.Item>
                ))}
              </List>
            ) : (
              <p>{t('environment.noCustomParameters')}</p>
            )}
          </Panel>
        </Collapse>
      </div>
    );
  };

  return (
    <div className="environment-debug-panel">
      <Card
        title={
          <Space>
            <BugOutlined />
            <span>{t('environment.debugPanel')}</span>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title={debugEnabled ? t('environment.disableDebug') : t('environment.enableDebug')}>
              <Switch
                checkedChildren={<BugOutlined />}
                unCheckedChildren={<BugOutlined />}
                checked={debugEnabled}
                onChange={handleToggleDebug}
              />
            </Tooltip>
            <Tooltip title={t('environment.refresh')}>
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              />
            </Tooltip>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <InfoCircleOutlined />
                {t('environment.logs')}
              </span>
            }
            key="logs"
          >
            {renderLogsTab()}
          </TabPane>
          <TabPane
            tab={
              <span>
                <CheckCircleOutlined />
                {t('environment.activeResponses')}
              </span>
            }
            key="activeResponses"
          >
            {renderActiveResponsesTab()}
          </TabPane>
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                {t('environment.environmentState')}
              </span>
            }
            key="environmentState"
          >
            {renderEnvironmentStateTab()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default EnvironmentDebugPanel;
