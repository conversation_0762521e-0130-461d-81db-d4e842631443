/**
 * 高级递归合并服务
 * 提供增强的递归合并算法，支持复杂对象的智能合并
 */
import { EventEmitter } from '../utils/EventEmitter';
import { cloneDeep, isArray, isObject, isEqual } from 'lodash';
import { Operation } from './CollaborationService';
import { MergeStrategy, MergeConflict, CustomMergeFn } from './RecursiveMergeService';

/**
 * 高级合并策略枚举
 */
export enum AdvancedMergeStrategy {
  SMART_ARRAY_MERGE = 'smart_array_merge',   // 智能数组合并
  SEMANTIC_MERGE = 'semantic_merge',         // 语义合并
  STRUCTURAL_MERGE = 'structural_merge',     // 结构合并
  PROPERTY_BASED_MERGE = 'property_based_merge', // 基于属性的合并
  INTENT_BASED_MERGE = 'intent_based_merge'  // 基于意图的合并
}

/**
 * 数组项标识符配置
 */
export interface ArrayItemIdentifierConfig {
  idField: string;                // 标识字段名称
  fallbackFields?: string[];      // 备用标识字段
  generateId?: boolean;           // 是否为新项生成ID
}

/**
 * 高级合并选项
 */
export interface AdvancedMergeOptions {
  strategy?: MergeStrategy;                  // 基本合并策略
  advancedStrategy?: AdvancedMergeStrategy;  // 高级合并策略
  customMergeFn?: CustomMergeFn;             // 自定义合并函数
  maxDepth?: number;                         // 最大递归深度
  arrayItemIdentifiers?: Record<string, ArrayItemIdentifierConfig>; // 数组项标识符配置
  semanticRules?: Record<string, any>;       // 语义合并规则
  structuralRules?: Record<string, any>;     // 结构合并规则
  propertyRules?: Record<string, any>;       // 属性合并规则
  intentRules?: Record<string, any>;         // 意图合并规则
  preserveOrder?: boolean;                   // 是否保留数组顺序
  detectMoves?: boolean;                     // 是否检测移动
  detectRenames?: boolean;                   // 是否检测重命名
  ignoreFields?: string[];                   // 忽略的字段
  priorityFields?: string[];                 // 优先处理的字段
  conflictResolver?: (conflict: MergeConflict) => any; // 冲突解析器
}

/**
 * 高级合并结果
 */
export interface AdvancedMergeResult {
  merged: any;                     // 合并后的数据
  conflicts: MergeConflict[];      // 合并过程中的冲突
  success: boolean;                // 合并是否成功
  stats: {                         // 合并统计
    totalNodes: number;            // 总节点数
    conflictNodes: number;         // 冲突节点数
    autoResolvedConflicts: number; // 自动解决的冲突数
    mergeTime: number;             // 合并耗时(ms)
  };
}

/**
 * 高级递归合并服务类
 */
class AdvancedRecursiveMergeService extends EventEmitter {
  // 单例实例
  private static instance: AdvancedRecursiveMergeService;

  // 默认配置
  private defaultOptions: AdvancedMergeOptions = {
    strategy: MergeStrategy.DEEP_MERGE,
    advancedStrategy: AdvancedMergeStrategy.SMART_ARRAY_MERGE,
    maxDepth: 20,
    arrayItemIdentifiers: {},
    preserveOrder: true,
    detectMoves: true,
    detectRenames: false,
    ignoreFields: [],
    priorityFields: []
  };

  /**
   * 获取单例实例
   */
  public static getInstance(): AdvancedRecursiveMergeService {
    if (!AdvancedRecursiveMergeService.instance) {
      AdvancedRecursiveMergeService.instance = new AdvancedRecursiveMergeService();
    }
    return AdvancedRecursiveMergeService.instance;
  }

  /**
   * 构造函数
   */
  constructor() {
    super();
  }

  /**
   * 设置默认选项
   * @param options 默认选项
   */
  public setDefaultOptions(options: Partial<AdvancedMergeOptions>): void {
    this.defaultOptions = {
      ...this.defaultOptions,
      ...options
    };
  }

  /**
   * 获取默认选项
   * @returns 默认选项
   */
  public getDefaultOptions(): AdvancedMergeOptions {
    return { ...this.defaultOptions };
  }
  /**
   * 高级递归合并两个对象
   * @param local 本地对象
   * @param remote 远程对象
   * @param options 合并选项
   * @returns 合并结果
   */
  public mergeObjects(
    local: any,
    remote: any,
    options: AdvancedMergeOptions = {}
  ): AdvancedMergeResult {
    // 合并用户选项与默认选项
    const mergedOptions = {
      ...this.defaultOptions,
      ...options
    };

    const {
      strategy,
      advancedStrategy,
      customMergeFn,
      maxDepth,
      arrayItemIdentifiers,
      preserveOrder,
      detectMoves,
      detectRenames,
      ignoreFields,
      priorityFields,
      conflictResolver
    } = mergedOptions;

    const conflicts: MergeConflict[] = [];
    const startTime = Date.now();

    // 创建统计对象，使用对象引用来确保统计数据能正确更新
    const stats = {
      totalNodes: 0,
      conflictNodes: 0,
      autoResolvedConflicts: 0
    };

    // 执行递归合并
    const merged = this._recursiveMerge(
      local,
      remote,
      [],
      conflicts,
      {
        strategy,
        advancedStrategy,
        customMergeFn,
        maxDepth,
        arrayItemIdentifiers,
        preserveOrder,
        detectMoves,
        detectRenames,
        ignoreFields,
        priorityFields,
        conflictResolver,
        stats
      },
      0
    );

    const endTime = Date.now();

    return {
      merged,
      conflicts,
      success: true,
      stats: {
        totalNodes: stats.totalNodes,
        conflictNodes: stats.conflictNodes,
        autoResolvedConflicts: stats.autoResolvedConflicts,
        mergeTime: endTime - startTime
      }
    };
  }

  /**
   * 合并操作数据
   * @param localOperation 本地操作
   * @param remoteOperation 远程操作
   * @param options 合并选项
   * @returns 合并后的操作数据
   */
  public mergeOperationData(
    localOperation: Operation,
    remoteOperation: Operation,
    options: AdvancedMergeOptions = {}
  ): { data: any; conflicts: MergeConflict[]; stats: any } {
    const localData = localOperation.data || {};
    const remoteData = remoteOperation.data || {};

    const result = this.mergeObjects(localData, remoteData, options);

    return {
      data: result.merged,
      conflicts: result.conflicts,
      stats: result.stats
    };
  }

  /**
   * 递归合并实现
   * @private
   */
  private _recursiveMerge(
    local: any,
    remote: any,
    path: string[],
    conflicts: MergeConflict[],
    options: any,
    depth: number = 0
  ): any {
    // 更新统计信息
    options.stats.totalNodes++;

    // 防止无限递归
    if (depth > options.maxDepth) {
      const conflict = this._createConflict(local, remote, path, options.strategy);
      conflicts.push(conflict);
      options.stats.conflictNodes++;
      return options.strategy === MergeStrategy.PREFER_REMOTE ? remote : local;
    }

    // 如果值相等，直接返回
    if (isEqual(local, remote)) {
      return cloneDeep(local);
    }

    // 如果一方为null或undefined
    if (local === null || local === undefined) {
      return cloneDeep(remote);
    }
    if (remote === null || remote === undefined) {
      return cloneDeep(local);
    }

    // 如果类型不同，记录冲突并根据策略返回
    if (typeof local !== typeof remote || Array.isArray(local) !== Array.isArray(remote)) {
      const conflict = this._createConflict(local, remote, path, options.strategy);
      conflicts.push(conflict);
      options.stats.conflictNodes++;

      // 使用冲突解析器
      if (options.conflictResolver) {
        const resolved = options.conflictResolver(conflict);
        if (resolved !== undefined) {
          options.stats.autoResolvedConflicts++;
          return resolved;
        }
      }

      return options.strategy === MergeStrategy.PREFER_REMOTE ? cloneDeep(remote) : cloneDeep(local);
    }

    // 处理数组
    if (isArray(local)) {
      return this._mergeArrays(local, remote, path, conflicts, options, depth);
    }

    // 处理对象
    if (isObject(local)) {
      return this._mergeObjects(local, remote, path, conflicts, options, depth);
    }

    // 处理基本类型
    const conflict = this._createConflict(local, remote, path, options.strategy);
    conflicts.push(conflict);
    options.stats.conflictNodes++;

    // 使用冲突解析器
    if (options.conflictResolver) {
      const resolved = options.conflictResolver(conflict);
      if (resolved !== undefined) {
        options.stats.autoResolvedConflicts++;
        return resolved;
      }
    }

    // 使用自定义合并函数
    if (options.strategy === MergeStrategy.CUSTOM && options.customMergeFn) {
      const result = options.customMergeFn(local, remote, path);
      conflict.resolvedValue = result;
      options.stats.autoResolvedConflicts++;
      return result;
    }

    return options.strategy === MergeStrategy.PREFER_REMOTE ? cloneDeep(remote) : cloneDeep(local);
  }

  /**
   * 合并数组
   * @private
   */
  private _mergeArrays(
    local: any[],
    remote: any[],
    path: string[],
    conflicts: MergeConflict[],
    options: any,
    depth: number
  ): any[] {
    const pathStr = path.join('.');
    const arrayIdentifier = options.arrayItemIdentifiers[pathStr];

    // 如果有数组项标识符配置，使用智能数组合并
    if (arrayIdentifier || options.advancedStrategy === AdvancedMergeStrategy.SMART_ARRAY_MERGE) {
      return this._smartMergeArrays(local, remote, path, conflicts, options, depth, arrayIdentifier);
    }

    // 如果数组长度相同，尝试逐项合并
    if (local.length === remote.length) {
      return local.map((item, index) => {
        const itemPath = [...path, index.toString()];
        return this._recursiveMerge(
          item,
          remote[index],
          itemPath,
          conflicts,
          options,
          depth + 1
        );
      });
    }

    // 如果数组长度不同，记录冲突并根据策略返回
    const conflict = this._createConflict(local, remote, path, options.strategy);
    conflicts.push(conflict);
    options.stats.conflictNodes++;

    // 使用冲突解析器
    if (options.conflictResolver) {
      const resolved = options.conflictResolver(conflict);
      if (resolved !== undefined) {
        options.stats.autoResolvedConflicts++;
        return resolved;
      }
    }

    // 使用自定义合并函数
    if (options.strategy === MergeStrategy.CUSTOM && options.customMergeFn) {
      const result = options.customMergeFn(local, remote, path);
      conflict.resolvedValue = result;
      options.stats.autoResolvedConflicts++;
      return result;
    }

    return options.strategy === MergeStrategy.PREFER_REMOTE ? cloneDeep(remote) : cloneDeep(local);
  }

  /**
   * 智能合并数组
   * @private
   */
  private _smartMergeArrays(
    local: any[],
    remote: any[],
    path: string[],
    conflicts: MergeConflict[],
    options: any,
    depth: number,
    arrayIdentifier?: ArrayItemIdentifierConfig
  ): any[] {
    // 如果没有标识符配置，使用默认配置
    const idField = arrayIdentifier?.idField || 'id';
    const fallbackFields = arrayIdentifier?.fallbackFields || ['key', 'name', 'uuid'];

    // 创建本地和远程项的映射
    const localMap = new Map();
    const remoteMap = new Map();

    // 填充本地映射
    local.forEach(item => {
      const id = this._getItemId(item, idField, fallbackFields);
      if (id !== undefined) {
        localMap.set(id, item);
      }
    });

    // 填充远程映射
    remote.forEach(item => {
      const id = this._getItemId(item, idField, fallbackFields);
      if (id !== undefined) {
        remoteMap.set(id, item);
      }
    });

    // 合并结果
    const result: any[] = [];
    const processedIds = new Set();

    // 处理本地数组中的项
    local.forEach(localItem => {
      const id = this._getItemId(localItem, idField, fallbackFields);

      // 如果项没有ID，直接添加到结果
      if (id === undefined) {
        result.push(cloneDeep(localItem));
        return;
      }

      processedIds.add(id);

      // 如果远程也有该项，合并它们
      if (remoteMap.has(id)) {
        const remoteItem = remoteMap.get(id);
        const itemPath = [...path, id.toString()];

        const mergedItem = this._recursiveMerge(
          localItem,
          remoteItem,
          itemPath,
          conflicts,
          options,
          depth + 1
        );

        result.push(mergedItem);
      } else {
        // 远程没有该项，保留本地项
        result.push(cloneDeep(localItem));
      }
    });

    // 处理远程数组中的新项
    remote.forEach(remoteItem => {
      const id = this._getItemId(remoteItem, idField, fallbackFields);

      // 如果项没有ID或已处理，跳过
      if (id === undefined || processedIds.has(id)) {
        return;
      }

      // 远程有新项，添加到结果
      result.push(cloneDeep(remoteItem));
    });

    // 如果需要保持顺序，尝试恢复原始顺序
    if (options.preserveOrder) {
      this._preserveArrayOrder(result, local, remote, idField, fallbackFields);
    }

    return result;
  }

  /**
   * 合并对象
   * @private
   */
  private _mergeObjects(
    local: Record<string, any>,
    remote: Record<string, any>,
    path: string[],
    conflicts: MergeConflict[],
    options: any,
    depth: number
  ): Record<string, any> {
    const result: Record<string, any> = {};
    const allKeys = new Set([...Object.keys(local), ...Object.keys(remote)]);

    // 优先处理优先字段
    const priorityKeys = options.priorityFields.filter((field: string) => allKeys.has(field));
    const normalKeys = Array.from(allKeys).filter(key => !priorityKeys.includes(key));
    const sortedKeys = [...priorityKeys, ...normalKeys];

    // 处理所有键
    for (const key of sortedKeys) {
      // 如果是忽略字段，跳过
      if (options.ignoreFields.includes(key)) {
        // 保留本地值
        if (key in local) {
          result[key] = cloneDeep(local[key]);
        } else if (key in remote) {
          result[key] = cloneDeep(remote[key]);
        }
        continue;
      }

      const keyPath = [...path, key];

      // 键只存在于本地
      if (!(key in remote)) {
        result[key] = cloneDeep(local[key]);
        continue;
      }

      // 键只存在于远程
      if (!(key in local)) {
        result[key] = cloneDeep(remote[key]);
        continue;
      }

      // 键在两边都存在，递归合并
      result[key] = this._recursiveMerge(
        local[key],
        remote[key],
        keyPath,
        conflicts,
        options,
        depth + 1
      );
    }

    return result;
  }

  /**
   * 获取数组项的ID
   * @private
   */
  private _getItemId(
    item: any,
    idField: string,
    fallbackFields: string[]
  ): any {
    // 如果项不是对象，返回undefined
    if (!isObject(item)) {
      return undefined;
    }

    // 将item转换为Record类型以避免类型错误
    const itemRecord = item as Record<string, any>;

    // 尝试使用主ID字段
    if (idField in itemRecord && itemRecord[idField] !== undefined && itemRecord[idField] !== null) {
      return itemRecord[idField];
    }

    // 尝试使用备用ID字段
    for (const field of fallbackFields) {
      if (field in itemRecord && itemRecord[field] !== undefined && itemRecord[field] !== null) {
        return itemRecord[field];
      }
    }

    return undefined;
  }

  /**
   * 保持数组顺序
   * @private
   */
  private _preserveArrayOrder(
    result: any[],
    local: any[],
    remote: any[],
    idField: string,
    fallbackFields: string[]
  ): void {
    // 创建ID到项的映射
    const resultMap = new Map();

    result.forEach((item) => {
      const id = this._getItemId(item, idField, fallbackFields);
      if (id !== undefined) {
        resultMap.set(id, { item });
      }
    });

    // 创建排序后的结果
    const sortedResult: any[] = new Array(result.length);
    let nextIndex = 0;

    // 首先尝试保持本地顺序
    local.forEach(localItem => {
      const id = this._getItemId(localItem, idField, fallbackFields);
      if (id !== undefined && resultMap.has(id)) {
        const { item } = resultMap.get(id);
        sortedResult[nextIndex] = item;
        resultMap.delete(id);
        nextIndex++;
      }
    });

    // 然后处理远程中的新项
    remote.forEach(remoteItem => {
      const id = this._getItemId(remoteItem, idField, fallbackFields);
      if (id !== undefined && resultMap.has(id)) {
        const { item } = resultMap.get(id);
        sortedResult[nextIndex] = item;
        resultMap.delete(id);
        nextIndex++;
      }
    });

    // 最后处理剩余项
    resultMap.forEach(({ item }) => {
      sortedResult[nextIndex] = item;
      nextIndex++;
    });

    // 更新结果数组
    for (let i = 0; i < result.length; i++) {
      result[i] = sortedResult[i];
    }
  }

  /**
   * 创建合并冲突
   * @private
   */
  private _createConflict(
    localValue: any,
    remoteValue: any,
    path: string[],
    strategy: MergeStrategy
  ): MergeConflict {
    const resolvedValue = strategy === MergeStrategy.PREFER_REMOTE ? remoteValue : localValue;

    return {
      path,
      localValue,
      remoteValue,
      resolvedValue,
      strategy
    };
  }
}

// 创建单例实例
export const advancedRecursiveMergeService = AdvancedRecursiveMergeService.getInstance();
