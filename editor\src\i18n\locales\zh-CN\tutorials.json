{"tutorials": {"title": "教程", "start": "开始教程", "next": "下一步", "previous": "上一步", "skip": "跳过", "finish": "完成", "restart": "重新开始", "close": "关闭", "progress": "进度", "step": "步骤", "of": "共", "completed": "已完成", "inProgress": "进行中", "notStarted": "未开始", "difficulty": {"beginner": "初级", "intermediate": "中级", "advanced": "高级", "expert": "专家"}, "categories": {"basics": "基础操作", "modeling": "建模", "animation": "动画", "materials": "材质", "lighting": "光照", "physics": "物理", "scripting": "脚本", "ui": "用户界面", "optimization": "优化", "collaboration": "协作"}, "basicTutorials": {"gettingStarted": {"title": "入门指南", "description": "学习编辑器的基本操作和界面", "steps": {"welcome": "欢迎使用DL引擎编辑器", "interface": "了解编辑器界面", "navigation": "学习场景导航", "selection": "选择和操作对象", "properties": "编辑对象属性"}}, "sceneCreation": {"title": "创建场景", "description": "学习如何创建和管理场景", "steps": {"newScene": "创建新场景", "addObjects": "添加对象到场景", "hierarchy": "理解场景层次结构", "saveScene": "保存场景"}}, "objectManipulation": {"title": "对象操作", "description": "学习如何操作3D对象", "steps": {"transform": "变换对象", "duplicate": "复制对象", "group": "组织对象", "delete": "删除对象"}}}, "animationTutorials": {"basicAnimation": {"title": "基础动画", "description": "学习创建简单的动画", "steps": {"timeline": "了解时间轴", "keyframes": "设置关键帧", "interpolation": "理解插值", "playback": "播放动画"}}, "characterAnimation": {"title": "角色动画", "description": "学习角色动画制作", "steps": {"rigging": "角色绑定", "skinning": "蒙皮设置", "posing": "姿态调整", "walking": "制作行走动画"}}}, "materialTutorials": {"basicMaterials": {"title": "基础材质", "description": "学习材质的基本概念", "steps": {"create": "创建材质", "properties": "设置材质属性", "textures": "应用纹理", "preview": "预览材质效果"}}, "advancedMaterials": {"title": "高级材质", "description": "学习高级材质技术", "steps": {"pbr": "PBR材质", "nodes": "节点编辑器", "shaders": "自定义着色器", "optimization": "材质优化"}}}, "physicsTutorials": {"basicPhysics": {"title": "基础物理", "description": "学习物理系统的基本使用", "steps": {"rigidbody": "刚体组件", "colliders": "碰撞器", "forces": "施加力", "simulation": "物理模拟"}}, "characterController": {"title": "角色控制器", "description": "学习角色物理控制", "steps": {"setup": "设置角色控制器", "movement": "移动控制", "jumping": "跳跃机制", "collision": "碰撞检测"}}}, "scriptingTutorials": {"introduction": {"title": "脚本入门", "description": "学习脚本编程基础", "steps": {"basics": "脚本基础", "components": "组件系统", "events": "事件处理", "debugging": "调试技巧"}}, "gameLogic": {"title": "游戏逻辑", "description": "实现游戏逻辑", "steps": {"input": "输入处理", "state": "状态管理", "ai": "AI行为", "ui": "UI交互"}}}, "tips": {"title": "提示", "hotkeys": "快捷键", "workflow": "工作流程", "bestPractices": "最佳实践", "troubleshooting": "故障排除"}, "feedback": {"helpful": "这个教程有帮助吗？", "yes": "是的", "no": "不是", "improve": "如何改进这个教程？", "submit": "提交反馈", "thanks": "感谢您的反馈！"}, "errors": {"loadFailed": "加载教程失败", "stepFailed": "执行步骤失败", "validationFailed": "验证失败", "timeout": "操作超时"}, "messages": {"tutorialStarted": "教程已开始", "tutorialCompleted": "教程已完成", "stepCompleted": "步骤已完成", "allTutorialsCompleted": "所有教程已完成", "progressSaved": "进度已保存"}}}