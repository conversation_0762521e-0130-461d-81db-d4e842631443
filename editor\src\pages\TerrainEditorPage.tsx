/**
 * 地形编辑器页面
 * 用于展示和编辑地形
 */
import React, { useState, useEffect } from 'react';
import { Layout, Card, Row, Col, Button, Typography, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import TerrainEditor from '../components/terrain/TerrainEditor';
import { EntityList } from '../components/entity/EntityList';
import { EntityDetails } from '../components/entity/EntityDetails';
import './TerrainEditorPage.less';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

/**
 * 地形编辑器页面
 */
const TerrainEditorPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 获取选中的实体
  const selectedEntityId = useSelector((state: RootState) => state.editor.selectedEntityId);
  
  // 检查选中的实体是否有地形组件
  const hasTerrainComponent = useSelector((state: RootState) => {
    if (!selectedEntityId) return false;
    const entity = state.entities.byId[selectedEntityId];
    return entity && entity.components && entity.components.TerrainComponent;
  });

  return (
    <Layout className="terrain-editor-page">
      <Header className="page-header">
        <Title level={3}>{t('editor.terrain.terrainEditor')}</Title>
      </Header>

      <Layout>
        <Sider width={300} className="editor-sider">
          <Card title={t('editor.common.entities')} className="entity-list-card">
            <EntityList />
          </Card>
        </Sider>

        <Content className="editor-content">
          {selectedEntityId ? (
            hasTerrainComponent ? (
              <TerrainEditor entityId={selectedEntityId} />
            ) : (
              <div className="no-terrain-component">
                <Text>{t('editor.terrain.noTerrainComponent')}</Text>
                <Button 
                  type="primary"
                  onClick={() => {
                    // 这里需要实现添加地形组件的逻辑
                    message.success(t('editor.terrain.terrainComponentAdded'));
                  }}
                >
                  {t('editor.terrain.addTerrainComponent')}
                </Button>
              </div>
            )
          ) : (
            <div className="no-entity-selected">
              <Text>{t('editor.common.noEntitySelected')}</Text>
            </div>
          )}
        </Content>

        <Sider width={300} className="properties-sider">
          <Card title={t('editor.common.properties')} className="entity-properties-card">
            {selectedEntityId ? (
              <EntityDetails entityId={selectedEntityId} />
            ) : (
              <div className="no-entity-selected">
                <Text>{t('editor.common.noEntitySelected')}</Text>
              </div>
            )}
          </Card>
        </Sider>
      </Layout>
    </Layout>
  );
};

export default TerrainEditorPage;
