# 命令行工具参考文档

本文档提供了DL（Digital Learning）引擎编辑器命令行工具的详细参考信息，包括命令行工具的安装、配置、命令和使用示例。

## 目录

- [概述](#概述)
- [安装](#安装)
- [配置](#配置)
- [基本命令](#基本命令)
- [项目管理](#项目管理)
- [资产管理](#资产管理)
- [构建和发布](#构建和发布)
- [批处理操作](#批处理操作)
- [自动化脚本](#自动化脚本)
- [高级用法](#高级用法)
- [故障排除](#故障排除)

## 概述

DL（Digital Learning）引擎命令行工具（IR-CLI）是一个功能强大的命令行界面，用于自动化DL（Digital Learning）引擎项目的各种任务，包括项目创建、资产管理、构建和发布等。它特别适合持续集成/持续部署（CI/CD）流程和批处理操作。

## 安装

### 全局安装

```bash
npm install -g dl-engine-cli
```

### 项目内安装

```bash
npm install --save-dev dl-engine-cli
```

### 验证安装

```bash
ir-cli --version
```

## 配置

IR-CLI可以通过配置文件或命令行参数进行配置。

### 配置文件

在项目根目录创建`ir-cli.config.js`文件：

```javascript
module.exports = {
  // 项目设置
  project: {
    name: 'MyProject',
    version: '1.0.0',
    outputDir: './dist'
  },

  // 构建设置
  build: {
    platform: 'web',
    optimization: 'size',
    sourceMaps: true
  },

  // 资产设置
  assets: {
    inputDir: './assets',
    outputDir: './dist/assets',
    compression: 'medium'
  },

  // 服务器设置
  server: {
    port: 8080,
    host: 'localhost'
  }
};
```

### 环境变量

也可以通过环境变量配置IR-CLI：

```bash
# 设置构建平台
export IR_CLI_PLATFORM=web

# 设置输出目录
export IR_CLI_OUTPUT_DIR=./dist

# 设置优化级别
export IR_CLI_OPTIMIZATION=size
```

## 基本命令

### 帮助

显示帮助信息：

```bash
ir-cli --help
```

显示特定命令的帮助信息：

```bash
ir-cli build --help
```

### 版本

显示IR-CLI和DL（Digital Learning）引擎的版本信息：

```bash
ir-cli --version
```

### 初始化

初始化新项目：

```bash
ir-cli init [项目名称]
```

选项：

| 选项 | 描述 |
|------|------|
| --template <模板名称> | 指定项目模板 |
| --directory <目录> | 指定项目目录 |
| --package-manager <npm\|yarn\|pnpm> | 指定包管理器 |

示例：

```bash
# 使用基本模板创建新项目
ir-cli init my-ir-project --template basic

# 使用游戏模板创建新项目并指定目录
ir-cli init my-game --template game --directory ./projects
```

## 项目管理

### 创建项目

创建新项目：

```bash
ir-cli create [项目名称]
```

选项：

| 选项 | 描述 |
|------|------|
| --template <模板名称> | 指定项目模板 |
| --directory <目录> | 指定项目目录 |
| --description <描述> | 项目描述 |
| --author <作者> | 项目作者 |

### 打开项目

打开现有项目：

```bash
ir-cli open [项目路径]
```

### 项目信息

显示项目信息：

```bash
ir-cli info [项目路径]
```

## 资产管理

### 导入资产

导入资产到项目：

```bash
ir-cli asset import [资产路径] [目标路径]
```

选项：

| 选项 | 描述 |
|------|------|
| --type <资产类型> | 指定资产类型 |
| --compress | 压缩资产 |
| --no-convert | 不转换资产格式 |

示例：

```bash
# 导入模型
ir-cli asset import ./models/character.fbx ./assets/models

# 导入并压缩纹理
ir-cli asset import ./textures/*.png ./assets/textures --compress
```

### 导出资产

从项目导出资产：

```bash
ir-cli asset export [资产路径] [目标路径]
```

选项：

| 选项 | 描述 |
|------|------|
| --format <导出格式> | 指定导出格式 |
| --quality <质量> | 指定导出质量 |

### 资产列表

列出项目资产：

```bash
ir-cli asset list [目录路径]
```

选项：

| 选项 | 描述 |
|------|------|
| --type <资产类型> | 按类型筛选 |
| --format <输出格式> | 指定输出格式（json、table） |

## 构建和发布

### 构建项目

构建项目：

```bash
ir-cli build [项目路径]
```

选项：

| 选项 | 描述 |
|------|------|
| --platform <平台> | 构建平台（web、windows、mac、android、ios） |
| --mode <模式> | 构建模式（development、production） |
| --output <输出目录> | 指定输出目录 |
| --sourcemap | 生成源映射 |
| --minify | 最小化输出 |
| --verbose | 显示详细日志 |

示例：

```bash
# 构建Web平台生产版本
ir-cli build --platform web --mode production

# 构建Windows平台开发版本并生成源映射
ir-cli build --platform windows --mode development --sourcemap
```

### 发布项目

发布项目：

```bash
ir-cli publish [项目路径]
```

选项：

| 选项 | 描述 |
|------|------|
| --platform <平台> | 发布平台 |
| --target <目标> | 发布目标（如服务器URL） |
| --version <版本> | 指定版本号 |
| --notes <发布说明> | 发布说明 |

示例：

```bash
# 发布到Web服务器
ir-cli publish --platform web --target https://example.com/deploy --version 1.0.0

# 发布到应用商店
ir-cli publish --platform android --target playstore --version 1.0.0 --notes "初始版本"
```

## 批处理操作

IR-CLI提供了强大的批处理功能，可以自动化执行重复性任务，提高工作效率。

### 批量资产操作

```bash
# 批量导入资产
ir-cli asset import-batch ./assets/batch-import.json

# 批量导出资产
ir-cli asset export-batch ./assets/batch-export.json

# 批量转换资产
ir-cli asset convert-batch ./assets/batch-convert.json
```

批处理配置文件使用JSON格式，例如：

```json
// batch-import.json
{
  "targetDirectory": "./assets/models",
  "overwrite": true,
  "compress": true,
  "items": [
    {
      "source": "./raw-assets/models/character.fbx",
      "targetName": "player-character",
      "options": {
        "scale": 0.01,
        "generateLODs": true,
        "maxLODLevel": 3
      }
    },
    {
      "source": "./raw-assets/models/environment/*.fbx",
      "options": {
        "combineMeshes": true,
        "optimizeForStatic": true
      }
    }
  ]
}
```

### 批量构建操作

```bash
# 批量构建多个平台
ir-cli build-batch ./builds/batch-build.json

# 批量发布多个平台
ir-cli publish-batch ./builds/batch-publish.json
```

批量构建配置示例：

```json
// batch-build.json
{
  "projectPath": "./",
  "outputBaseDir": "./builds",
  "commonOptions": {
    "bundleIdentifier": "com.example.irgame",
    "version": "1.0.0",
    "minify": true,
    "sourcemap": false
  },
  "builds": [
    {
      "platform": "web",
      "mode": "production",
      "outputDir": "web",
      "options": {
        "webgl2": true,
        "compression": "brotli"
      }
    },
    {
      "platform": "windows",
      "mode": "production",
      "outputDir": "windows",
      "options": {
        "architecture": "x64",
        "directx12": true
      }
    },
    {
      "platform": "android",
      "mode": "production",
      "outputDir": "android",
      "options": {
        "minSdkVersion": 24,
        "targetSdkVersion": 33,
        "buildAAB": true
      }
    }
  ]
}
```

### 批量测试操作

```bash
# 运行所有测试
ir-cli test-all

# 批量运行特定测试
ir-cli test-batch ./tests/batch-test.json

# 生成测试报告
ir-cli test-report --format html --output ./reports/test-report.html
```

批量测试配置示例：

```json
// batch-test.json
{
  "testGroups": [
    {
      "name": "Core Tests",
      "tests": ["./tests/core/**/*.test.ts"],
      "timeout": 5000
    },
    {
      "name": "Physics Tests",
      "tests": ["./tests/physics/**/*.test.ts"],
      "timeout": 10000,
      "environment": {
        "PHYSICS_ENGINE": "advanced"
      }
    }
  ],
  "reporters": ["default", "junit"],
  "junitReportPath": "./reports/junit-report.xml"
}
```

### 批量场景操作

```bash
# 批量优化场景
ir-cli scene optimize-batch ./scenes/batch-optimize.json

# 批量合并场景
ir-cli scene merge-batch ./scenes/batch-merge.json

# 批量导出场景
ir-cli scene export-batch ./scenes/batch-export.json --format gltf
```

批量场景优化配置示例：

```json
// batch-optimize.json
{
  "scenes": [
    "./assets/scenes/level1.scene",
    "./assets/scenes/level2.scene",
    "./assets/scenes/level3.scene"
  ],
  "optimizations": {
    "combineMeshes": true,
    "removeEmptyNodes": true,
    "optimizeHierarchy": true,
    "generateLODs": true,
    "compressTextures": true,
    "lightmapResolution": 1024,
    "occlusionCulling": true
  },
  "backup": true,
  "backupDir": "./backups/scenes"
}
```

## 自动化脚本

IR-CLI支持使用JavaScript或TypeScript编写自定义自动化脚本，以扩展其功能。

### 创建自动化脚本

创建一个名为`ir-cli-scripts`的目录，并在其中创建脚本文件：

```bash
mkdir ir-cli-scripts
touch ir-cli-scripts/optimize-project.js
```

脚本示例：

```javascript
// ir-cli-scripts/optimize-project.js
module.exports = async (cli, args) => {
  // 显示开始消息
  cli.log.info('开始项目优化...');

  // 解析参数
  const targetDir = args.targetDir || './assets';
  const backupDir = args.backupDir || './backups';

  // 创建备份
  cli.log.info(`创建备份到 ${backupDir}...`);
  await cli.exec('backup', {
    source: targetDir,
    destination: backupDir,
    timestamp: true
  });

  // 优化纹理
  cli.log.info('优化纹理...');
  await cli.exec('asset', 'optimize', {
    type: 'texture',
    directory: `${targetDir}/textures`,
    compression: 'high'
  });

  // 优化模型
  cli.log.info('优化模型...');
  await cli.exec('asset', 'optimize', {
    type: 'model',
    directory: `${targetDir}/models`,
    combineMeshes: true,
    generateLODs: true
  });

  // 优化场景
  cli.log.info('优化场景...');
  await cli.exec('scene', 'optimize', {
    directory: `${targetDir}/scenes`,
    occlusionCulling: true,
    lightmapResolution: 1024
  });

  // 显示完成消息
  cli.log.success('项目优化完成！');

  // 返回结果
  return {
    success: true,
    optimizedFiles: 150, // 示例数据
    savedSpace: '1.2GB'  // 示例数据
  };
};
```

### 运行自动化脚本

```bash
# 运行脚本
ir-cli run optimize-project

# 带参数运行脚本
ir-cli run optimize-project --targetDir=./custom-assets --backupDir=./custom-backups
```

### 脚本API

自动化脚本可以访问以下API：

| API | 描述 |
|-----|------|
| cli.exec(command, args) | 执行CLI命令 |
| cli.log.info(message) | 输出信息日志 |
| cli.log.success(message) | 输出成功日志 |
| cli.log.warning(message) | 输出警告日志 |
| cli.log.error(message) | 输出错误日志 |
| cli.fs | 文件系统操作 |
| cli.path | 路径操作 |
| cli.prompt | 用户交互提示 |
| cli.config | 配置访问 |
| cli.project | 项目信息 |

### 脚本钩子

IR-CLI支持在特定事件发生时自动运行脚本：

```javascript
// ir-cli.config.js
module.exports = {
  // ... 其他配置 ...

  hooks: {
    // 构建前钩子
    preBuild: './ir-cli-scripts/pre-build.js',

    // 构建后钩子
    postBuild: './ir-cli-scripts/post-build.js',

    // 发布前钩子
    prePublish: './ir-cli-scripts/pre-publish.js',

    // 发布后钩子
    postPublish: './ir-cli-scripts/post-publish.js',

    // 导入资产前钩子
    preAssetImport: './ir-cli-scripts/pre-asset-import.js',

    // 导入资产后钩子
    postAssetImport: './ir-cli-scripts/post-asset-import.js'
  }
};
```

## 高级用法

### 持续集成/持续部署

IR-CLI可以轻松集成到CI/CD流程中：

```yaml
# .github/workflows/build-and-deploy.yml
name: Build and Deploy

on:
  push:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'

    - name: Install dependencies
      run: npm ci

    - name: Install IR-CLI
      run: npm install -g dl-engine-cli

    - name: Build project
      run: ir-cli build --platform web --mode production

    - name: Run tests
      run: ir-cli test-all

    - name: Deploy to staging
      if: success()
      run: ir-cli publish --platform web --target ${{ secrets.STAGING_URL }} --version ${{ github.sha }}
```

### 插件系统

IR-CLI支持插件系统，可以扩展其功能：

```bash
# 安装插件
ir-cli plugin install ir-cli-plugin-aws

# 列出已安装的插件
ir-cli plugin list

# 使用插件命令
ir-cli aws deploy-to-s3 --bucket my-game-bucket
```

### 远程操作

IR-CLI支持远程操作，可以在远程服务器上执行命令：

```bash
# 配置远程服务器
ir-cli remote add production ssh://<EMAIL>

# 在远程服务器上执行命令
ir-cli remote exec production build --platform web

# 将构建结果从远程服务器下载到本地
ir-cli remote download production ./dist ./local-dist
```

## 故障排除

### 常见问题

#### 问题：安装失败

```
npm ERR! code EACCES
npm ERR! syscall access
npm ERR! path /usr/local/lib/node_modules
```

**解决方案**：使用sudo安装或修复npm权限：

```bash
sudo npm install -g dl-engine-cli
# 或
npm install -g dl-engine-cli --unsafe-perm
```

#### 问题：命令未找到

```
command not found: ir-cli
```

**解决方案**：确保全局npm bin目录在PATH中：

```bash
export PATH="$PATH:$(npm config get prefix)/bin"
```

#### 问题：构建失败

```
Error: Cannot find module '@dl-engine/core'
```

**解决方案**：检查依赖安装：

```bash
npm install
# 或
ir-cli doctor
```

### 诊断工具

IR-CLI提供了诊断工具来帮助解决问题：

```bash
# 运行诊断
ir-cli doctor

# 检查特定组件
ir-cli doctor --component build

# 生成诊断报告
ir-cli doctor --report ./ir-cli-diagnosis.log
```

### 日志和调试

```bash
# 启用详细日志
ir-cli build --verbose

# 启用调试模式
ir-cli build --debug

# 将日志保存到文件
ir-cli build --log-file ./build.log
```

### 获取支持

如果您遇到无法解决的问题，可以通过以下方式获取支持：

- 官方文档：https://dl-engine.example.com/docs/cli
- GitHub问题：https://github.com/dl-engine/ir-cli/issues
- 社区论坛：https://forum.dl-engine.example.com
- 电子邮件支持：<EMAIL>

![命令行工具](../../assets/images/cli-tool.png)
