/**
 * 场景状态切片测试
 */
import sceneReducer, {
  selectEntity,
  addEntity,
  removeEntity,
  updateEntity,
  undo,
  redo,
  clearScene,
  Entity} from '../scene/sceneSlice';

// 模拟实体数据
const mockEntity1: Entity = {
  id: 'entity1',
  name: '实体1',
  type: 'mesh',
  parentId: null,
  visible: true,
  locked: false,
  transform: {
    position: [0, 0, 0],
    rotation: [0, 0, 0],
    scale: [1, 1, 1]},
  components: {}};

const mockEntity2: Entity = {
  id: 'entity2',
  name: '实体2',
  type: 'light',
  parentId: null,
  visible: true,
  locked: false,
  transform: {
    position: [1, 1, 1],
    rotation: [0, 0, 0],
    scale: [1, 1, 1]},
  components: {}};

const mockEntity3: Entity = {
  id: 'entity3',
  name: '实体3',
  type: 'mesh',
  parentId: 'entity1',
  visible: true,
  locked: false,
  transform: {
    position: [0, 0, 0],
    rotation: [0, 0, 0],
    scale: [1, 1, 1]},
  components: {}};

describe('场景状态切片', () => {
  // 初始状态测试
  it('应该返回初始状态', () => {
    const initialState = sceneReducer(undefined, { type: 'unknown' });
    expect(initialState).toEqual({
      entities: [],
      selectedEntityId: null,
      activeSceneId: null,
      loading: false,
      error: null,
      undoStack: [],
      redoStack: []});
  });

  // 选择实体测试
  it('应该处理selectEntity', () => {
    const previousState = {
      entities: [mockEntity1, mockEntity2],
      selectedEntityId: null,
      activeSceneId: null,
      loading: false,
      error: null,
      undoStack: [],
      redoStack: []};

    const nextState = sceneReducer(previousState, selectEntity('entity2'));

    expect(nextState.selectedEntityId).toEqual('entity2');
    expect(nextState.entities).toEqual([mockEntity1, mockEntity2]);
  });

  // 添加实体测试
  it('应该处理addEntity', () => {
    const previousState = {
      entities: [mockEntity1, mockEntity2],
      selectedEntityId: null,
      activeSceneId: null,
      loading: false,
      error: null,
      undoStack: [],
      redoStack: []};

    const newEntity: Entity = {
      id: 'entity4',
      name: '新实体',
      type: 'camera',
      parentId: null,
      visible: true,
      locked: false,
      transform: {
        position: [0, 0, 0],
        rotation: [0, 0, 0],
        scale: [1, 1, 1]},
      components: {}};

    const nextState = sceneReducer(previousState, addEntity(newEntity));

    expect(nextState.entities.length).toBe(3);
    expect(nextState.entities[2]).toEqual(newEntity);
    expect(nextState.undoStack.length).toBe(1);
    expect(nextState.redoStack.length).toBe(0);
  });

  // 移除实体测试
  it('应该处理removeEntity', () => {
    const previousState = {
      entities: [mockEntity1, mockEntity2, mockEntity3],
      selectedEntityId: 'entity2',
      activeSceneId: null,
      loading: false,
      error: null,
      undoStack: [],
      redoStack: []};

    // 移除顶级实体
    let nextState = sceneReducer(previousState, removeEntity('entity2'));
    expect(nextState.entities.length).toBe(2);
    expect(nextState.entities.find(e => e.id === 'entity2')).toBeUndefined();
    expect(nextState.selectedEntityId).toBeNull(); // 选中的实体被删除后应该清空选择
    expect(nextState.undoStack.length).toBe(1);

    // 移除子实体
    const stateWithChild = {
      entities: [mockEntity1, mockEntity3],
      selectedEntityId: null,
      activeSceneId: null,
      loading: false,
      error: null,
      undoStack: [],
      redoStack: []};
    nextState = sceneReducer(stateWithChild, removeEntity('entity3'));
    expect(nextState.entities.find(e => e.id === 'entity3')).toBeUndefined();
  });

  // 更新实体测试
  it('应该处理updateEntity', () => {
    const previousState = {
      entities: [mockEntity1, mockEntity2],
      selectedEntityId: null,
      activeSceneId: null,
      loading: false,
      error: null,
      undoStack: [],
      redoStack: []};

    const changes = {
      name: '更新后的实体',
      transform: {
        position: [1, 2, 3] as [number, number, number],
        rotation: [0, 0, 0] as [number, number, number],
        scale: [1, 1, 1] as [number, number, number]}};

    const nextState = sceneReducer(previousState, updateEntity({ id: 'entity1', changes }));
    const updatedEntity = nextState.entities.find(e => e.id === 'entity1');

    expect(updatedEntity?.name).toBe('更新后的实体');
    expect(updatedEntity?.transform.position).toEqual([1, 2, 3]);
    expect(nextState.undoStack.length).toBe(1);
    expect(nextState.redoStack.length).toBe(0);
  });

  // 撤销操作测试
  it('应该处理undo', () => {
    const previousState = {
      entities: [mockEntity1],
      selectedEntityId: null,
      activeSceneId: null,
      loading: false,
      error: null,
      undoStack: [[mockEntity1, mockEntity2]], // 有一个撤销状态
      redoStack: []};

    const nextState = sceneReducer(previousState, undo());

    expect(nextState.entities).toEqual([mockEntity1, mockEntity2]);
    expect(nextState.undoStack.length).toBe(0);
    expect(nextState.redoStack.length).toBe(1);
  });

  // 重做操作测试
  it('应该处理redo', () => {
    const previousState = {
      entities: [mockEntity1, mockEntity2],
      selectedEntityId: null,
      activeSceneId: null,
      loading: false,
      error: null,
      undoStack: [],
      redoStack: [[mockEntity1]], // 有一个重做状态
    };

    const nextState = sceneReducer(previousState, redo());

    expect(nextState.entities).toEqual([mockEntity1]);
    expect(nextState.undoStack.length).toBe(1);
    expect(nextState.redoStack.length).toBe(0);
  });

  // 清空场景测试
  it('应该处理clearScene', () => {
    const previousState = {
      entities: [mockEntity1, mockEntity2],
      selectedEntityId: 'entity1',
      activeSceneId: null,
      loading: false,
      error: null,
      undoStack: [],
      redoStack: []};

    const nextState = sceneReducer(previousState, clearScene());

    expect(nextState.entities).toEqual([]);
    expect(nextState.selectedEntityId).toBeNull();
    expect(nextState.undoStack.length).toBe(1);
    expect(nextState.redoStack.length).toBe(0);
  });
});
