/**
 * 教程面板组件
 * 显示交互式教程、视频教程和项目案例教程
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Progress, Typography, Tag, Space, Modal, Tooltip, Tabs } from 'antd';
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  StarOutlined,
  LeftOutlined,
  RightOutlined,
  CloseOutlined,
  TrophyOutlined,
  BookOutlined,
  InfoCircleOutlined,
  AppstoreOutlined,
  CodeOutlined,
  ReadOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import TutorialService, { Tutorial, TutorialStep } from '../../services/TutorialService';
import { tutorialValidationService } from '../../services/TutorialValidationService';
import { achievementService } from '../../services/AchievementService';
import {
  tutorialRecommendationService,
  TutorialItem,
  TutorialSeries,
  TutorialType,
  TutorialDifficulty
} from '../../services/TutorialRecommendationService';
import TutorialStepGuide from './TutorialStepGuide';
import TutorialHighlightContainer from './TutorialHighlight';
import { TutorialRecommendations } from './TutorialRecommendations';
import { TutorialSeriesPanel } from './TutorialSeriesPanel';
import { TutorialDifficultyPanel } from './TutorialDifficultyPanel';
import { ProjectTutorialPanel } from './ProjectTutorialPanel';
import './TutorialPanel.less';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface TutorialPanelProps {
  onClose?: () => void;
}

const TutorialPanel: React.FC<TutorialPanelProps> = ({ onClose }) => {
  const { t } = useTranslation();
  const [tutorials, setTutorials] = useState<Tutorial[]>([]);
  const [recommendedTutorials, setRecommendedTutorials] = useState<Tutorial[]>([]);
  const [activeTutorial, setActiveTutorial] = useState<Tutorial | null>(null);
  const [currentStep, setCurrentStep] = useState<TutorialStep | null>(null);
  const [showTutorialModal, setShowTutorialModal] = useState(false);
  const [activeTab, setActiveTab] = useState('recommendations');
  const [selectedSeries, setSelectedSeries] = useState<TutorialSeries | null>(null);

  // 初始化
  useEffect(() => {
    // 获取所有教程
    const allTutorials = TutorialService.getTutorials();
    setTutorials(allTutorials);

    // 获取推荐教程
    const recommended = TutorialService.getRecommendedTutorials();
    setRecommendedTutorials(recommended);

    // 获取当前教程和步骤
    const currentTutorial = TutorialService.getCurrentTutorial();
    if (currentTutorial) {
      setActiveTutorial(currentTutorial);
      setCurrentStep(TutorialService.getCurrentStep());
      setShowTutorialModal(true);
    }

    // 监听教程事件
    TutorialService.on('tutorialStarted', handleTutorialStarted);
    TutorialService.on('tutorialCompleted', handleTutorialCompleted);
    TutorialService.on('tutorialExited', handleTutorialExited);
    TutorialService.on('stepChanged', handleStepChanged);

    // 监听验证服务事件
    tutorialValidationService.on('tutorialStarted', handleTutorialStarted);
    tutorialValidationService.on('tutorialCompleted', handleTutorialCompleted);
    tutorialValidationService.on('tutorialCancelled', handleTutorialExited);
    tutorialValidationService.on('stepChanged', handleStepChanged);

    return () => {
      // 清理事件监听
      TutorialService.off('tutorialStarted', handleTutorialStarted);
      TutorialService.off('tutorialCompleted', handleTutorialCompleted);
      TutorialService.off('tutorialExited', handleTutorialExited);
      TutorialService.off('stepChanged', handleStepChanged);

      tutorialValidationService.off('tutorialStarted', handleTutorialStarted);
      tutorialValidationService.off('tutorialCompleted', handleTutorialCompleted);
      tutorialValidationService.off('tutorialCancelled', handleTutorialExited);
      tutorialValidationService.off('stepChanged', handleStepChanged);
    };
  }, []);

  // 处理教程开始
  const handleTutorialStarted = (tutorial: Tutorial) => {
    setActiveTutorial(tutorial);
    setCurrentStep(TutorialService.getCurrentStep());
    setShowTutorialModal(true);
  };

  // 处理教程完成
  const handleTutorialCompleted = (tutorial: Tutorial) => {
    setActiveTutorial(null);
    setCurrentStep(null);
    setShowTutorialModal(false);

    // 更新推荐教程
    const recommended = TutorialService.getRecommendedTutorials();
    setRecommendedTutorials(recommended);

    // 解锁成就
    achievementService.handleTutorialCompleted(tutorial.id);

    // 显示完成提示
    Modal.success({
      title: t('tutorials.completedTitle'),
      content: (
        <div>
          <p>{t('tutorials.completedMessage', { title: tutorial.title })}</p>
          <p><TrophyOutlined style={{ color: 'gold' }} /> {t('tutorials.achievementUnlocked')}</p>
        </div>
      )});
  };

  // 处理教程退出
  const handleTutorialExited = () => {
    setActiveTutorial(null);
    setCurrentStep(null);
    setShowTutorialModal(false);
  };

  // 处理步骤变化
  const handleStepChanged = (step: TutorialStep) => {
    setCurrentStep(step);
  };

  // 开始教程
  const startTutorial = (tutorialId: string) => {
    const tutorial = tutorials.find(t => t.id === tutorialId);
    if (tutorial) {
      // 使用新的验证服务启动教程
      tutorialValidationService.setActiveTutorial({
        ...tutorial,
        currentStepIndex: 0,
        completed: false,
        category: tutorial.category || 'general',
        difficulty: tutorial.difficulty,
        estimatedTime: tutorial.duration + ' ' + t('tutorials.minutes'),
        steps: tutorial.steps.map(step => ({
          ...step,
          completed: false,
          tasks: step.tasks || [],
          nextButtonEnabled: false
        }))
      });
    } else {
      // 兼容旧版本
      TutorialService.startTutorial(tutorialId);
    }
  };

  // 处理教程项选择
  const handleTutorialItemSelect = (item: TutorialItem) => {
    // 根据教程类型打开相应的教程
    switch (item.type) {
      case TutorialType.INTERACTIVE:
        const tutorialId = item.id.split(':')[1];
        startTutorial(tutorialId);
        break;
      default:
        // 其他类型的教程由各自的组件处理
        break;
    }
  };

  // 处理教程系列选择
  const handleSeriesSelect = (series: TutorialSeries) => {
    setSelectedSeries(series);
    setActiveTab('series');
  };

  // 下一步
  const nextStep = () => {
    // 尝试使用新的验证服务
    if (tutorialValidationService.getActiveTutorial()) {
      tutorialValidationService.nextStep();
    } else {
      // 兼容旧版本
      TutorialService.nextStep();
    }
  };

  // 上一步
  const previousStep = () => {
    // 尝试使用新的验证服务
    if (tutorialValidationService.getActiveTutorial()) {
      tutorialValidationService.previousStep();
    } else {
      // 兼容旧版本
      TutorialService.previousStep();
    }
  };

  // 退出教程
  const exitTutorial = () => {
    // 尝试使用新的验证服务
    if (tutorialValidationService.getActiveTutorial()) {
      tutorialValidationService.clearActiveTutorial();
    } else {
      // 兼容旧版本
      TutorialService.endTutorial(false);
    }
  };

  // 渲染难度标签
  const renderDifficultyTag = (difficulty: string) => {
    let color = '';
    switch (difficulty) {
      case 'beginner':
        color = 'green';
        break;
      case 'intermediate':
        color = 'blue';
        break;
      case 'advanced':
        color = 'orange';
        break;
      case 'expert':
        color = 'red';
        break;
      default:
        color = 'default';
    }

    return (
      <Tag color={color}>
        {t(`tutorials.difficulty.${difficulty}`)}
      </Tag>
    );
  };

  // 渲染教程卡片
  const renderTutorialCard = (tutorial: Tutorial) => {
    const isCompleted = TutorialService.isTutorialCompleted(tutorial.id);
    const progress = TutorialService.getTutorialProgress(tutorial.id);
    const totalSteps = tutorial.steps.length;
    const progressPercent = totalSteps > 0 ? Math.round((progress / totalSteps) * 100) : 0;

    // 检查是否有相关成就
    const hasAchievement = achievementService.getAchievements().some(
      a => a.unlocked && a.id.includes(tutorial.id)
    );

    return (
      <Card
        className={`tutorial-card ${isCompleted ? 'completed' : ''}`}
        title={
          <div className="tutorial-card-title">
            <span>{tutorial.title}</span>
            {hasAchievement && (
              <Tooltip title={t('tutorials.achievementUnlocked')}>
                <TrophyOutlined className="achievement-icon" />
              </Tooltip>
            )}
          </div>
        }
        extra={
          <Space>
            {isCompleted && <CheckCircleOutlined className="completed-icon" />}
            {renderDifficultyTag(tutorial.difficulty)}
          </Space>
        }
        actions={[
          <Tooltip title={t('tutorials.duration')}>
            <Space>
              <ClockCircleOutlined />
              <span>{tutorial.duration} {t('tutorials.minutes')}</span>
            </Space>
          </Tooltip>,
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={() => startTutorial(tutorial.id)}
          >
            {isCompleted ? t('tutorials.restart') : (progress > 0 ? t('tutorials.continue') : t('tutorials.start'))}
          </Button>
        ]}
      >
        <Paragraph>{tutorial.description}</Paragraph>

        {progress > 0 && !isCompleted && (
          <div className="tutorial-progress">
            <Progress percent={progressPercent} size="small" />
            <Text type="secondary">{t('tutorials.progressStatus', { current: progress, total: totalSteps })}</Text>
          </div>
        )}

        <div className="tutorial-tags">
          {tutorial.tags?.map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
          {tutorial.category && (
            <Tag color="blue" icon={<BookOutlined />}>
              {tutorial.category}
            </Tag>
          )}
          {tutorial.prerequisites?.length > 0 && (
            <Tooltip title={t('tutorials.prerequisites')}>
              <Tag color="orange" icon={<InfoCircleOutlined />}>
                {t('tutorials.hasPrerequisites')}
              </Tag>
            </Tooltip>
          )}
        </div>
      </Card>
    );
  };

  // 渲染教程步骤模态框
  const renderTutorialModal = () => {
    if (!activeTutorial || !currentStep) {
      return null;
    }

    // 检查是否使用新的验证服务
    const validationTutorial = tutorialValidationService.getActiveTutorial();
    if (validationTutorial) {
      return (
        <Modal
          open={showTutorialModal}
          footer={null}
          closable={false}
          maskClosable={false}
          className="tutorial-modal"
          width={450}
        >
          <TutorialStepGuide
            tutorial={validationTutorial}
            onClose={() => {
              setShowTutorialModal(false);
              tutorialValidationService.clearActiveTutorial();
            }}
          />
        </Modal>
      );
    }

    // 兼容旧版本
    const currentIndex = activeTutorial.steps.findIndex(step => step.id === currentStep.id);
    const totalSteps = activeTutorial.steps.length;
    const progressPercent = totalSteps > 0 ? Math.round(((currentIndex + 1) / totalSteps) * 100) : 0;

    return (
      <Modal
        title={
          <div className="tutorial-modal-title">
            <div>{activeTutorial.title}</div>
            <div className="tutorial-modal-progress">
              <Text type="secondary">{t('tutorials.step')} {currentIndex + 1}/{totalSteps}</Text>
              <Progress percent={progressPercent} size="small" showInfo={false} />
            </div>
          </div>
        }
        open={showTutorialModal}
        footer={null}
        closable={false}
        maskClosable={false}
        className="tutorial-modal"
      >
        <div className="tutorial-step-content">
          <Title level={4}>{currentStep.title}</Title>
          <Paragraph>{currentStep.description}</Paragraph>
        </div>

        <div className="tutorial-modal-footer">
          <Button
            onClick={exitTutorial}
            icon={<CloseOutlined />}
          >
            {currentStep.skipButtonText || t('tutorials.exit')}
          </Button>

          <Space>
            {currentIndex > 0 && (
              <Button
                onClick={previousStep}
                icon={<LeftOutlined />}
              >
                {currentStep.previousButtonText || t('tutorials.previous')}
              </Button>
            )}

            <Button
              type="primary"
              onClick={nextStep}
              icon={<RightOutlined />}
            >
              {currentStep.nextButtonText || t('tutorials.next')}
            </Button>
          </Space>
        </div>
      </Modal>
    );
  };

  // 渲染教程面板内容
  const renderPanelContent = () => {
    if (activeTab === 'series' && selectedSeries) {
      return (
        <TutorialSeriesPanel
          series={selectedSeries}
          onBack={() => setSelectedSeries(null)}
          onTutorialSelect={handleTutorialItemSelect}
        />
      );
    }

    switch (activeTab) {
      case 'recommendations':
        return (
          <TutorialRecommendations
            onTutorialSelect={handleTutorialItemSelect}
            onSeriesSelect={handleSeriesSelect}
          />
        );
      case 'difficulty':
        return (
          <TutorialDifficultyPanel
            onTutorialSelect={handleTutorialItemSelect}
          />
        );
      case 'projects':
        return (
          <ProjectTutorialPanel />
        );
      case 'interactive':
        return (
          <div className="tutorial-section">
            <Title level={4}>{t('tutorials.interactiveTutorials')}</Title>
            <Paragraph>{t('tutorials.interactiveDescription')}</Paragraph>

            <div className="tutorial-grid">
              {tutorials
                .filter(tutorial => tutorial.interactive)
                .map(tutorial => (
                  <div key={tutorial.id} className="tutorial-grid-item">
                    {renderTutorialCard(tutorial)}
                  </div>
                ))
              }

              {tutorials.filter(tutorial => tutorial.interactive).length === 0 && (
                <div className="tutorial-empty">
                  <Text type="secondary">{t('tutorials.noInteractiveTutorials')}</Text>
                </div>
              )}
            </div>
          </div>
        );
      case 'all':
      default:
        return (
          <div className="tutorial-section">
            <Title level={4}>{t('tutorials.allTutorials')}</Title>
            <div className="tutorial-grid">
              {tutorials.map(tutorial => (
                <div key={tutorial.id} className="tutorial-grid-item">
                  {renderTutorialCard(tutorial)}
                </div>
              ))}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="tutorial-panel">
      <div className="tutorial-panel-header">
        <Title level={3}>{t('tutorials.title')}</Title>
        <Paragraph>{t('tutorials.description')}</Paragraph>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={(key) => {
          setActiveTab(key);
          if (key !== 'series') {
            setSelectedSeries(null);
          }
        }}
        className="tutorial-panel-tabs"
      >
        <TabPane
          tab={
            <span>
              <StarOutlined /> {t('tutorials.recommended')}
            </span>
          }
          key="recommendations"
        />
        <TabPane
          tab={
            <span>
              <AppstoreOutlined /> {t('tutorials.byDifficulty')}
            </span>
          }
          key="difficulty"
        />
        <TabPane
          tab={
            <span>
              <CodeOutlined /> {t('tutorials.projectTutorials')}
            </span>
          }
          key="projects"
        />
        <TabPane
          tab={
            <span>
              <ReadOutlined /> {t('tutorials.interactive')}
            </span>
          }
          key="interactive"
        />
        <TabPane
          tab={
            <span>
              <BookOutlined /> {t('tutorials.allTutorials')}
            </span>
          }
          key="all"
        />
        {selectedSeries && (
          <TabPane
            tab={
              <span>
                <TeamOutlined /> {selectedSeries.title}
              </span>
            }
            key="series"
          />
        )}
      </Tabs>

      <div className="tutorial-panel-content">
        {renderPanelContent()}
      </div>

      {renderTutorialModal()}
      <TutorialHighlightContainer />
    </div>
  );
};

export default TutorialPanel;
