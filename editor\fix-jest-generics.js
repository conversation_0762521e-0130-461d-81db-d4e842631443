#!/usr/bin/env node

/**
 * 修复jest泛型语法错误
 */

import fs from 'fs';
import path from 'path';

function fixJestGenerics(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    // 修复jest.fn<any, any>()语法错误
    const originalContent = content;
    content = content.replace(/jest\.fn<any,\s*any>\(\)/g, 'jest.fn()');
    content = content.replace(/jest\.fn<any, any>\(\)/g, 'jest.fn()');
    
    if (content !== originalContent) {
      changed = true;
    }
    
    if (changed) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`已修复: ${filePath}`);
    }
  } catch (error) {
    console.error(`修复失败 ${filePath}:`, error.message);
  }
}

function findTestFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          traverse(fullPath);
        } else if (stat.isFile() && (item.endsWith('.test.ts') || item.endsWith('.test.tsx'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`无法读取目录 ${currentDir}:`, error.message);
    }
  }
  
  traverse(dir);
  return files;
}

// 查找所有测试文件
const testFiles = findTestFiles('./src');

console.log(`找到 ${testFiles.length} 个测试文件`);

// 修复每个文件
testFiles.forEach(fixJestGenerics);

console.log('修复完成!');
