/**
 * 国际化配置
 */
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入语言文件
import zhCN from './locales/zh-CN.json';
import enUS from './locales/en-US.json';

// 导入调试相关语言文件
import zhCNDebug from './locales/zh-CN/debug.json';
import enUSDebug from './locales/en-US/debug.json';

// 导入网络相关语言文件
import zhCNNetwork from './locales/zh-CN/network.json';

// 导入教程相关语言文件
import zhCNTutorials from './locales/zh-CN/tutorials.json';
import enUSTutorials from './locales/en/tutorials.json';

// 导入成就相关语言文件
import zhCNAchievements from './locales/zh-CN/achievements.json';
import enUSAchievements from './locales/en/achievements.json';

// 导入资源热更新相关语言文件
import zhCNResources from './locales/zh-CN/resources.json';

// 导入地形编辑器相关语言文件
import zhCNTerrain from './locales/zh-CN/terrain.json';

// 支持的语言列表
export const supportedLanguages = [
  { code: 'zh-CN', name: '简体中文' },
  { code: 'en-US', name: 'English' },
];

// 初始化i18next
i18n
  // 使用语言检测器
  .use(LanguageDetector)
  // 将i18n实例传递给react-i18next
  .use(initReactI18next)
  // 初始化i18next
  .init({
    // 资源
    resources: {
      'zh-CN': {
        ...zhCN,
        debug: zhCNDebug.debug,
        network: zhCNNetwork.network,
        tutorials: zhCNTutorials.tutorials,
        achievements: zhCNAchievements.achievements,
        resources: zhCNResources.resources,
        terrain: zhCNTerrain.terrain
      },
      'en-US': {
        ...enUS,
        debug: enUSDebug.debug,
        tutorials: enUSTutorials.tutorials,
        achievements: enUSAchievements.achievements
      }},
    // 默认语言
    fallbackLng: 'zh-CN',
    // 调试
    debug: process.env.NODE_ENV === 'development',
    // 命名空间
    ns: ['common', 'editor', 'auth', 'collaboration', 'debug', 'network', 'tutorials', 'achievements', 'resources', 'terrain'],
    defaultNS: 'common',
    // 缓存
    cache: {
      enabled: true},
    // 插值
    interpolation: {
      escapeValue: false, // 不转义HTML
    },
    // 检测选项
    detection: {
      order: ['localStorage', 'navigator'],
      lookupLocalStorage: 'i18nextLng',
      caches: ['localStorage']}});

/**
 * 切换语言
 * @param langCode 语言代码
 */
export const changeLanguage = (langCode: string) => {
  i18n.changeLanguage(langCode);
  // 保存到本地存储
  localStorage.setItem('i18nextLng', langCode);
};

/**
 * 获取当前语言
 * @returns 当前语言代码
 */
export const getCurrentLanguage = () => {
  return i18n.language || 'zh-CN';
};

/**
 * 获取当前语言名称
 * @returns 当前语言名称
 */
export const getCurrentLanguageName = () => {
  const lang = supportedLanguages.find(lang => lang.code === getCurrentLanguage());
  return lang ? lang.name : '简体中文';
};

export default i18n;
