/**
 * 状态机服务
 * 用于与引擎交互，加载和保存状态机数据
 */
import { AnimationStateMachineData } from '../libs/dl-engine';
import engineService from './EngineService';

/**
 * 状态机服务
 */
class StateMachineService {
  /**
   * 加载状态机
   * @param entityId 实体ID
   * @returns 状态机数据
   */
  public async loadStateMachine(entityId: string): Promise<AnimationStateMachineData> {
    try {
      // 调用引擎API获取状态机数据
      const data = await engineService.callEngineMethod('loadStateMachine', entityId);
      return data;
    } catch (error) {
      console.error('加载状态机失败:', error);
      throw new Error('加载状态机失败');
    }
  }

  /**
   * 保存状态机
   * @param entityId 实体ID
   * @param data 状态机数据
   */
  public async saveStateMachine(entityId: string, data: AnimationStateMachineData): Promise<void> {
    try {
      // 调用引擎API保存状态机数据
      await engineService.callEngineMethod('saveStateMachine', entityId, data);
    } catch (error) {
      console.error('保存状态机失败:', error);
      throw new Error('保存状态机失败');
    }
  }

  /**
   * 创建新状态机
   * @param entityId 实体ID
   * @returns 状态机数据
   */
  public async createStateMachine(entityId: string): Promise<AnimationStateMachineData> {
    try {
      // 调用引擎API创建新状态机
      const data = await engineService.callEngineMethod('createStateMachine', entityId);
      return data;
    } catch (error) {
      console.error('创建状态机失败:', error);
      throw new Error('创建状态机失败');
    }
  }

  /**
   * 获取可用的动画片段
   * @param entityId 实体ID
   * @returns 动画片段名称列表
   */
  public async getAvailableAnimationClips(entityId: string): Promise<string[]> {
    try {
      // 调用引擎API获取可用的动画片段
      const clips = await engineService.callEngineMethod('getAvailableAnimationClips', entityId);
      return clips;
    } catch (error) {
      console.error('获取动画片段失败:', error);
      throw new Error('获取动画片段失败');
    }
  }

  /**
   * 播放状态机
   * @param entityId 实体ID
   */
  public async playStateMachine(entityId: string): Promise<void> {
    try {
      // 调用引擎API播放状态机
      await engineService.callEngineMethod('playStateMachine', entityId);
    } catch (error) {
      console.error('播放状态机失败:', error);
      throw new Error('播放状态机失败');
    }
  }

  /**
   * 暂停状态机
   * @param entityId 实体ID
   */
  public async pauseStateMachine(entityId: string): Promise<void> {
    try {
      // 调用引擎API暂停状态机
      await engineService.callEngineMethod('pauseStateMachine', entityId);
    } catch (error) {
      console.error('暂停状态机失败:', error);
      throw new Error('暂停状态机失败');
    }
  }

  /**
   * 停止状态机
   * @param entityId 实体ID
   */
  public async stopStateMachine(entityId: string): Promise<void> {
    try {
      // 调用引擎API停止状态机
      await engineService.callEngineMethod('stopStateMachine', entityId);
    } catch (error) {
      console.error('停止状态机失败:', error);
      throw new Error('停止状态机失败');
    }
  }

  /**
   * 设置状态机参数
   * @param entityId 实体ID
   * @param name 参数名称
   * @param value 参数值
   */
  public async setStateMachineParameter(entityId: string, name: string, value: any): Promise<void> {
    try {
      // 调用引擎API设置状态机参数
      await engineService.callEngineMethod('setStateMachineParameter', entityId, name, value);
    } catch (error) {
      console.error('设置状态机参数失败:', error);
      throw new Error('设置状态机参数失败');
    }
  }

  /**
   * 获取状态机参数
   * @param entityId 实体ID
   * @param name 参数名称
   * @returns 参数值
   */
  public async getStateMachineParameter(entityId: string, name: string): Promise<any> {
    try {
      // 调用引擎API获取状态机参数
      const value = await engineService.callEngineMethod('getStateMachineParameter', entityId, name);
      return value;
    } catch (error) {
      console.error('获取状态机参数失败:', error);
      throw new Error('获取状态机参数失败');
    }
  }

  /**
   * 获取状态机调试信息
   * @param entityId 实体ID
   * @returns 调试信息
   */
  public async getStateMachineDebugInfo(entityId: string): Promise<any> {
    try {
      // 调用引擎API获取状态机调试信息
      const info = await engineService.callEngineMethod('getStateMachineDebugInfo', entityId);
      return info;
    } catch (error) {
      console.error('获取状态机调试信息失败:', error);
      throw new Error('获取状态机调试信息失败');
    }
  }

  /**
   * 启用状态机调试
   * @param entityId 实体ID
   * @param enabled 是否启用
   */
  public async enableStateMachineDebug(entityId: string, enabled: boolean): Promise<void> {
    try {
      // 调用引擎API启用状态机调试
      await engineService.callEngineMethod('enableStateMachineDebug', entityId, enabled);
    } catch (error) {
      console.error('启用状态机调试失败:', error);
      throw new Error('启用状态机调试失败');
    }
  }

  /**
   * 设置状态机当前状态
   * @param entityId 实体ID
   * @param stateName 状态名称
   */
  public async setStateMachineCurrentState(entityId: string, stateName: string): Promise<void> {
    try {
      // 调用引擎API设置状态机当前状态
      await engineService.callEngineMethod('setStateMachineCurrentState', entityId, stateName);
    } catch (error) {
      console.error('设置状态机当前状态失败:', error);
      throw new Error('设置状态机当前状态失败');
    }
  }
}

export const stateMachineService = new StateMachineService();
