#!/usr/bin/env node

/**
 * 批量替换测试文件中的vitest引用为jest
 */

import fs from 'fs';
import path from 'path';

function replaceViToJest(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 替换导入语句
    content = content.replace(/from 'vitest'/g, "from '@jest/globals'");
    content = content.replace(/import { vi }/g, "import { jest }");
    content = content.replace(/import { describe, it, expect, vi,/g, "import { describe, it, expect, jest,");
    
    // 替换vi.为jest.
    content = content.replace(/vi\./g, 'jest.');
    
    // 替换vi.mock为jest.mock
    content = content.replace(/jest\.mock/g, 'jest.mock');
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`已修复: ${filePath}`);
  } catch (error) {
    console.error(`修复失败 ${filePath}:`, error.message);
  }
}

function findTestFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && (item.endsWith('.test.ts') || item.endsWith('.test.tsx'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// 查找所有测试文件
const testFiles = findTestFiles('./src');

console.log(`找到 ${testFiles.length} 个测试文件`);

// 修复每个文件
testFiles.forEach(replaceViToJest);

console.log('修复完成!');
