/**
 * 水体预设系统测试
 */

// 模拟 THREE.js 对象
const THREE = {
  Vector3: class {
    constructor(x = 0, y = 0, z = 0) {
      this.x = x;
      this.y = y;
      this.z = z;
    }
    clone() {
      return new THREE.Vector3(this.x, this.y, this.z);
    }
  },
  Vector2: class {
    constructor(x = 0, y = 0) {
      this.x = x;
      this.y = y;
    }
    clone() {
      return new THREE.Vector2(this.x, this.y);
    }
  },
  Color: class {
    constructor(color = 0xffffff) {
      this.color = color;
    }
  },
  Euler: class {
    constructor(x = 0, y = 0, z = 0) {
      this.x = x;
      this.y = y;
      this.z = z;
    }
  }
};

// 导入编译后的模块
const { WaterPresets, WaterPresetType } = require('./dist/physics/water/WaterPresets.js');

// 模拟实体
class MockEntity {
  constructor(id, name) {
    this.id = id;
    this.name = name;
    this.components = new Map();
  }

  getComponent(name) {
    return this.components.get(name) || null;
  }

  addComponent(component) {
    this.components.set(component.getType(), component);
  }

  removeComponent(name) {
    this.components.delete(name);
  }

  hasComponent(name) {
    return this.components.has(name);
  }
}

// 运行测试
function runTests() {
  console.log('🧪 开始运行水体预设系统测试...\n');

  try {
    // 测试 1: 获取所有预设类型
    console.log('📋 测试 1: 获取所有预设类型');
    const allTypes = WaterPresets.getAllPresetTypes();
    console.log(`✅ 获取到 ${allTypes.length} 个预设类型:`, allTypes);
    console.log('');

    // 测试 2: 获取预设显示名称
    console.log('🏷️  测试 2: 获取预设显示名称');
    allTypes.forEach(type => {
      const displayName = WaterPresets.getPresetDisplayName(type);
      console.log(`   ${type} -> ${displayName}`);
    });
    console.log('✅ 所有预设显示名称获取成功\n');

    // 测试 3: 获取预设配置
    console.log('⚙️  测试 3: 获取预设配置');
    const testTypes = [
      WaterPresetType.LAKE,
      WaterPresetType.RIVER,
      WaterPresetType.OCEAN,
      WaterPresetType.POOL
    ];

    testTypes.forEach(type => {
      const config = WaterPresets.getPresetConfig(type);
      console.log(`   ${type}:`);
      console.log(`     尺寸: ${config.size.width}x${config.size.height}x${config.size.depth}`);
      console.log(`     密度: ${config.physicsParams.density}`);
      console.log(`     粘度: ${config.physicsParams.viscosity}`);
      console.log(`     流速: ${config.flowParams.speed}`);
      console.log(`     波动幅度: ${config.waveParams.amplitude}`);
    });
    console.log('✅ 所有预设配置获取成功\n');

    // 测试 4: 创建水体预设
    console.log('🏗️  测试 4: 创建水体预设');
    const mockEntity = new MockEntity('test-water', 'TestWater');

    testTypes.forEach(type => {
      try {
        const config = { type };
        const waterBody = WaterPresets.createPreset(mockEntity, config);
        console.log(`   ✅ ${type} 水体创建成功`);
        console.log(`     水体类型: ${waterBody.getWaterType()}`);
        console.log(`     尺寸: ${JSON.stringify(waterBody.getSize())}`);
        console.log(`     密度: ${waterBody.getDensity()}`);
        console.log(`     粘度: ${waterBody.getViscosity()}`);
      } catch (error) {
        console.log(`   ❌ ${type} 水体创建失败:`, error.message);
      }
    });
    console.log('');

    // 测试 5: 自定义配置
    console.log('🎛️  测试 5: 自定义配置');
    const customConfig = {
      type: WaterPresetType.LAKE,
      size: { width: 50, height: 3, depth: 50 },
      physicsParams: { density: 1200, viscosity: 1.5 }
    };

    const customWaterBody = WaterPresets.createPreset(mockEntity, customConfig);
    console.log('   ✅ 自定义湖泊水体创建成功');
    console.log(`     自定义尺寸: ${JSON.stringify(customWaterBody.getSize())}`);
    console.log(`     自定义密度: ${customWaterBody.getDensity()}`);
    console.log(`     自定义粘度: ${customWaterBody.getViscosity()}`);
    console.log('');

    console.log('🎉 所有测试通过！水体预设系统工作正常！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error(error.stack);
  }
}

// 运行测试
runTests();
