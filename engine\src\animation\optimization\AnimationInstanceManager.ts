/**
 * 动画实例管理器
 * 用于管理和优化大量相同动画的实例
 */
import * as THREE from 'three';
import type { AnimationClip } from '../AnimationClip';
import { Animator } from '../Animator';
import type { Entity } from '../../core/Entity';

/**
 * 动画实例数据
 */
interface AnimationInstanceData {
  /** 实体 */
  entity: Entity;
  /** 动画控制器 */
  animator: Animator;
  /** 当前时间 */
  time: number;
  /** 时间缩放 */
  timeScale: number;
  /** 权重 */
  weight: number;
  /** 是否循环 */
  loop: boolean;
  /** 当前动画名称 */
  currentClipName: string;
  /** 下一个动画名称 */
  nextClipName: string | null;
  /** 混合因子 */
  blendFactor: number;
  /** 混合持续时间 */
  blendDuration: number;
  /** 混合开始时间 */
  blendStartTime: number;
  /** 是否启用 */
  enabled: boolean;
  /** 上次更新时间 */
  lastUpdateTime: number;
  /** 更新频率 */
  updateFrequency: number;
  /** 是否需要更新 */
  needsUpdate: boolean;
}

/**
 * 动画实例组
 */
interface AnimationInstanceGroup {
  /** 原型动画控制器 */
  prototypeAnimator: Animator;
  /** 原型动画片段 */
  prototypeClips: Map<string, AnimationClip>;
  /** 实例数据 */
  instances: Map<Entity, AnimationInstanceData>;
  /** 共享动画混合器 */
  sharedMixer: THREE.AnimationMixer | null;
  /** 共享动作 */
  sharedActions: Map<string, THREE.AnimationAction>;
  /** 是否使用GPU蒙皮 */
  useGPUSkinning: boolean;
}

/**
 * 动画实例管理器
 */
export class AnimationInstanceManager {
  /** 实例组 */
  private instanceGroups: Map<string, AnimationInstanceGroup> = new Map();
  /** 实体到组的映射 */
  private entityToGroup: Map<Entity, string> = new Map();
  /** 是否启用GPU蒙皮 */
  private enableGPUSkinning: boolean;
  /** 是否启用实例化 */
  private enableInstancing: boolean;
  /** 最大实例数量 */
  private maxInstancesPerGroup: number;
  /** 上次更新时间 */
  private lastUpdateTime: number = 0;

  /**
   * 构造函数
   * @param options 选项
   */
  constructor(options: {
    enableGPUSkinning?: boolean;
    enableInstancing?: boolean;
    maxInstancesPerGroup?: number;
  } = {}) {
    this.enableGPUSkinning = options.enableGPUSkinning !== undefined ? options.enableGPUSkinning : true;
    this.enableInstancing = options.enableInstancing !== undefined ? options.enableInstancing : true;
    this.maxInstancesPerGroup = options.maxInstancesPerGroup || 100;
  }

  /**
   * 注册动画实例
   * @param groupId 组ID
   * @param entity 实体
   * @param animator 动画控制器
   * @returns 是否成功注册
   */
  public registerInstance(groupId: string, entity: Entity, animator: Animator): boolean {
    // 如果实体已经注册，则先注销
    if (this.entityToGroup.has(entity)) {
      this.unregisterInstance(entity);
    }

    // 获取或创建实例组
    let group = this.instanceGroups.get(groupId);
    if (!group) {
      group = {
        prototypeAnimator: animator,
        prototypeClips: new Map(),
        instances: new Map(),
        sharedMixer: null,
        sharedActions: new Map(),
        useGPUSkinning: this.enableGPUSkinning
      };

      // 复制动画片段
      const clips = animator.getClips();
      for (const clip of clips) {
        group.prototypeClips.set(clip.name, clip);
      }

      // 创建共享混合器
      if (this.enableInstancing) {
        const root = new THREE.Object3D();
        group.sharedMixer = new THREE.AnimationMixer(root);

        // 创建共享动作
        for (const [name, clip] of group.prototypeClips.entries()) {
          const threeClip = clip.toThreeAnimationClip();
          const action = group.sharedMixer.clipAction(threeClip);
          group.sharedActions.set(name, action);
        }
      }

      this.instanceGroups.set(groupId, group);
    }

    // 检查实例数量是否超过限制
    if (group.instances.size >= this.maxInstancesPerGroup) {
      console.warn(`动画实例组 "${groupId}" 已达到最大实例数量 ${this.maxInstancesPerGroup}`);
      return false;
    }

    // 创建实例数据
    const instanceData: AnimationInstanceData = {
      entity,
      animator,
      time: 0,
      timeScale: animator.getTimeScale(),
      weight: 1,
      loop: animator.getLoop(),
      currentClipName: '',
      nextClipName: null,
      blendFactor: 0,
      blendDuration: 0,
      blendStartTime: 0,
      enabled: true,
      lastUpdateTime: 0,
      updateFrequency: 1/60, // 默认每帧更新
      needsUpdate: true
    };

    // 获取当前动画
    const currentClip = animator.getCurrentClip();
    if (currentClip) {
      instanceData.currentClipName = currentClip.name;
    }

    // 添加实例
    group.instances.set(entity, instanceData);
    this.entityToGroup.set(entity, groupId);

    return true;
  }

  /**
   * 注销动画实例
   * @param entity 实体
   * @returns 是否成功注销
   */
  public unregisterInstance(entity: Entity): boolean {
    // 获取实体所属的组
    const groupId = this.entityToGroup.get(entity);
    if (!groupId) {
      return false;
    }

    // 获取实例组
    const group = this.instanceGroups.get(groupId);
    if (!group) {
      this.entityToGroup.delete(entity);
      return false;
    }

    // 移除实例
    group.instances.delete(entity);
    this.entityToGroup.delete(entity);

    // 如果组为空，则移除组
    if (group.instances.size === 0) {
      // 清理共享资源
      if (group.sharedMixer) {
        group.sharedMixer.stopAllAction();
        group.sharedMixer.uncacheRoot(group.sharedMixer.getRoot());
      }
      group.sharedActions.clear();
      this.instanceGroups.delete(groupId);
    }

    return true;
  }

  /**
   * 播放动画
   * @param entity 实体
   * @param clipName 动画片段名称
   * @param blendTime 混合时间
   * @returns 是否成功播放
   */
  public play(entity: Entity, clipName: string, blendTime: number = 0.3): boolean {
    // 获取实体所属的组
    const groupId = this.entityToGroup.get(entity);
    if (!groupId) {
      return false;
    }

    // 获取实例组
    const group = this.instanceGroups.get(groupId);
    if (!group) {
      return false;
    }

    // 获取实例数据
    const instanceData = group.instances.get(entity);
    if (!instanceData) {
      return false;
    }

    // 检查动画片段是否存在
    if (!group.prototypeClips.has(clipName)) {
      console.warn(`动画片段 "${clipName}" 不存在`);
      return false;
    }

    // 如果已经在播放该片段，则不做任何操作
    if (instanceData.currentClipName === clipName && !instanceData.nextClipName) {
      return true;
    }

    // 如果混合时间为0，则直接切换
    if (blendTime <= 0) {
      instanceData.currentClipName = clipName;
      instanceData.nextClipName = null;
      instanceData.blendFactor = 0;
      instanceData.time = 0;
      instanceData.needsUpdate = true;
      return true;
    }

    // 否则，设置为混合状态
    instanceData.nextClipName = clipName;
    instanceData.blendStartTime = instanceData.time;
    instanceData.blendDuration = blendTime;
    instanceData.blendFactor = 0;
    instanceData.needsUpdate = true;

    return true;
  }

  /**
   * 设置更新频率
   * @param entity 实体
   * @param frequency 更新频率（秒）
   */
  public setUpdateFrequency(entity: Entity, frequency: number): void {
    // 获取实体所属的组
    const groupId = this.entityToGroup.get(entity);
    if (!groupId) {
      return;
    }

    // 获取实例组
    const group = this.instanceGroups.get(groupId);
    if (!group) {
      return;
    }

    // 获取实例数据
    const instanceData = group.instances.get(entity);
    if (!instanceData) {
      return;
    }

    // 设置更新频率
    instanceData.updateFrequency = Math.max(0, frequency);
  }

  /**
   * 标记需要更新
   * @param entity 实体
   */
  public markNeedsUpdate(entity: Entity): void {
    // 获取实体所属的组
    const groupId = this.entityToGroup.get(entity);
    if (!groupId) {
      return;
    }

    // 获取实例组
    const group = this.instanceGroups.get(groupId);
    if (!group) {
      return;
    }

    // 获取实例数据
    const instanceData = group.instances.get(entity);
    if (!instanceData) {
      return;
    }

    // 标记需要更新
    instanceData.needsUpdate = true;
  }

  /**
   * 更新动画实例
   * @param deltaTime 时间增量（秒）
   */
  public update(deltaTime: number): void {
    const currentTime = performance.now() / 1000;
    const systemDeltaTime = currentTime - this.lastUpdateTime;
    this.lastUpdateTime = currentTime;

    // 如果deltaTime为0，则使用系统时间差
    if (deltaTime <= 0) {
      deltaTime = systemDeltaTime;
    }

    // 更新每个实例组
    for (const [groupId, group] of this.instanceGroups) {
      // 如果使用实例化，则更新共享混合器
      if (this.enableInstancing && group.sharedMixer) {
        group.sharedMixer.update(deltaTime);
      }

      // 创建需要更新的实例列表
      const instancesToUpdate: [Entity, AnimationInstanceData][] = [];

      // 检查哪些实例需要更新
      for (const [entity, instanceData] of group.instances) {
        // 如果未启用，则跳过
        if (!instanceData.enabled) {
          continue;
        }

        // 计算距离上次更新的时间
        const timeSinceLastUpdate = currentTime - instanceData.lastUpdateTime;

        // 如果时间超过更新频率或者标记为需要更新，则添加到更新列表
        if (timeSinceLastUpdate >= instanceData.updateFrequency || instanceData.needsUpdate) {
          instancesToUpdate.push([entity, instanceData]);
        }
      }

      // 如果没有需要更新的实例，则跳过
      if (instancesToUpdate.length === 0) {
        continue;
      }

      // 按照优先级排序（距离上次更新时间越长，优先级越高）
      instancesToUpdate.sort((a, b) => {
        const timeA = currentTime - a[1].lastUpdateTime;
        const timeB = currentTime - b[1].lastUpdateTime;
        return timeB - timeA;
      });

      // 更新实例
      for (const [entity, instanceData] of instancesToUpdate) {
        // 更新时间
        const scaledDeltaTime = deltaTime * instanceData.timeScale;
        instanceData.time += scaledDeltaTime;

        // 处理混合状态
        if (instanceData.nextClipName) {
          // 计算混合因子
          const blendProgress = (instanceData.time - instanceData.blendStartTime) / instanceData.blendDuration;
          instanceData.blendFactor = Math.min(blendProgress, 1.0);

          // 如果混合完成，则切换到下一个片段
          if (instanceData.blendFactor >= 1.0) {
            instanceData.currentClipName = instanceData.nextClipName;
            instanceData.nextClipName = null;
            instanceData.blendFactor = 0;
          }
        }

        // 更新最后更新时间和状态
        instanceData.lastUpdateTime = currentTime;
        instanceData.needsUpdate = false;

        // 如果不使用实例化，则更新原始动画控制器
        if (!this.enableInstancing) {
          instanceData.animator.update(deltaTime);
        } else {
          // 否则，应用共享动画状态到实体
          this.applySharedAnimationState(group, entity, instanceData);
        }
      }
    }
  }

  /**
   * 应用共享动画状态到实体
   * @param group 实例组
   * @param entity 实体
   * @param instanceData 实例数据
   */
  private applySharedAnimationState(
    group: AnimationInstanceGroup,
    entity: Entity,
    instanceData: AnimationInstanceData
  ): void {
    // 获取当前动画片段
    const currentClip = group.prototypeClips.get(instanceData.currentClipName);
    if (!currentClip) {
      return;
    }

    // 计算当前时间
    let currentTime = instanceData.time;
    const currentDuration = currentClip.duration;

    // 处理循环
    if (currentDuration > 0 && instanceData.loop) {
      currentTime = currentTime % currentDuration;
    }

    // 获取动画状态
    const animationState = new Map<string, any>();
    currentClip.getStateAtTime(currentTime, animationState);

    // 如果在混合状态，则混合下一个片段的状态
    if (instanceData.nextClipName && instanceData.blendFactor > 0) {
      const nextClip = group.prototypeClips.get(instanceData.nextClipName);
      if (nextClip) {
        // 计算下一个片段的时间
        let nextTime = instanceData.time - instanceData.blendStartTime;
        const nextDuration = nextClip.duration;

        // 处理循环
        if (nextDuration > 0 && instanceData.loop) {
          nextTime = nextTime % nextDuration;
        }

        // 获取下一个片段的状态
        const nextState = new Map<string, any>();
        nextClip.getStateAtTime(nextTime, nextState);

        // 混合两个状态
        for (const [path, nextValue] of nextState.entries()) {
          const currentValue = animationState.get(path);

          if (currentValue !== undefined) {
            // 如果当前状态中有该路径，则混合值
            const blendedValue = this.blendValues(currentValue, nextValue, instanceData.blendFactor);
            animationState.set(path, blendedValue);
          } else {
            // 如果当前状态中没有该路径，则直接使用下一个值
            animationState.set(path, nextValue);
          }
        }
      }
    }

    // 应用动画状态到实体
    this.applyAnimationState(entity, animationState);
  }

  /**
   * 混合两个值
   * @param a 值A
   * @param b 值B
   * @param t 混合因子（0-1）
   * @returns 混合结果
   */
  private blendValues(a: any, b: any, t: number): any {
    // 根据值的类型选择不同的混合方法
    if (a instanceof THREE.Vector3 && b instanceof THREE.Vector3) {
      return new THREE.Vector3().copy(a).lerp(b, t);
    } else if (a instanceof THREE.Quaternion && b instanceof THREE.Quaternion) {
      return new THREE.Quaternion().copy(a).slerp(b, t);
    } else if (a instanceof THREE.Color && b instanceof THREE.Color) {
      return new THREE.Color().copy(a).lerp(b, t);
    } else if (typeof a === 'number' && typeof b === 'number') {
      return a + (b - a) * t;
    } else if (typeof a === 'boolean' && typeof b === 'boolean') {
      return t < 0.5 ? a : b;
    } else if (a && b && typeof a.lerp === 'function') {
      return a.clone().lerp(b, t);
    } else {
      return t < 0.5 ? a : b;
    }
  }

  /**
   * 应用动画状态到实体
   * @param entity 实体
   * @param animationState 动画状态
   */
  private applyAnimationState(entity: Entity, animationState: Map<string, any>): void {
    // 获取实体的对象
    const transform = entity.getComponent('Transform') as any as any as any;
    if (!transform) {
      return;
    }

    // 使用类型断言访问getObject3D方法
    const transformWithObject3D = transform as any;
    if (typeof transformWithObject3D.getObject3D !== 'function') {
      return;
    }

    const object3D = transformWithObject3D.getObject3D();
    if (!object3D) {
      return;
    }

    // 应用动画状态到对象
    for (const [path, value] of animationState.entries()) {
      // 解析路径
      const parts = path.split('.');
      let target: any = object3D;

      // 遍历路径，找到目标对象
      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];

        // 如果是骨骼名称，则在骨骼列表中查找
        if (target.type === 'SkinnedMesh' && target.skeleton && i === 0) {
          const bone = target.skeleton.bones.find((b: THREE.Bone) => b.name === part);
          if (bone) {
            target = bone;
            continue;
          }
        }

        // 否则，在子对象中查找
        if (target.children) {
          const child = target.children.find((c: THREE.Object3D) => c.name === part);
          if (child) {
            target = child;
            continue;
          }
        }

        // 如果是属性，则获取属性
        if (target[part] !== undefined) {
          target = target[part];
        } else {
          // 如果找不到目标，则跳过
          target = null;
          break;
        }
      }

      // 如果找到目标，则设置属性
      if (target) {
        const property = parts[parts.length - 1];
        target[property] = value;
      }
    }
  }
}
