/**
 * 血管系统 - 模拟血管网络和连接
 */
import * as THREE from 'three';
import { Component } from '../../../core/Component';
import { VesselConnectionType } from './OrganSoftBody';

/**
 * 血管类型枚举
 */
export enum VesselType {
  /** 动脉 */
  ARTERY = 'artery',
  /** 静脉 */
  VEIN = 'vein',
  /** 毛细血管 */
  CAPILLARY = 'capillary',
  /** 主动脉 */
  AORTA = 'aorta',
  /** 肺动脉 */
  PULMONARY_ARTERY = 'pulmonary_artery',
  /** 冠状动脉 */
  CORONARY_ARTERY = 'coronary_artery'
}

/**
 * 血管组件
 */
export class VesselComponent extends Component {
  /** 血管类型 */
  public vesselType: VesselType;
  /** 血管直径 (mm) */
  public diameter: number;
  /** 血管长度 (mm) */
  public length: number;
  /** 血管壁厚度 (mm) */
  public wallThickness: number;
  /** 血管弹性 */
  public elasticity: number;
  /** 血管阻力 */
  public resistance: number;
  /** 当前流量 (ml/min) */
  public flowRate: number;
  /** 当前压力 (mmHg) */
  public pressure: number;
  /** 血管路径点 */
  public pathPoints: THREE.Vector3[];
  /** 连接的血管 */
  public connectedVessels: string[];

  constructor(vesselType: VesselType, diameter: number, length: number) {
    super('VesselComponent');
    this.vesselType = vesselType;
    this.diameter = diameter;
    this.length = length;
    this.wallThickness = diameter * 0.1; // 默认壁厚为直径的10%
    this.elasticity = 1.0;
    this.resistance = 1.0;
    this.flowRate = 0;
    this.pressure = 0;
    this.pathPoints = [];
    this.connectedVessels = [];
  }
}

/**
 * 流体系统接口
 */
export interface FluidSystem {
  /** 计算流体流动 */
  calculateFlow(vessel: VesselComponent, deltaTime: number): number;
  /** 更新压力 */
  updatePressure(vessel: VesselComponent, pressure: number): void;
  /** 获取粘度 */
  getViscosity(): number;
  /** 获取密度 */
  getDensity(): number;
}

/**
 * 简单流体系统实现
 */
export class SimpleFluidSystem implements FluidSystem {
  /** 血液粘度 (Pa·s) */
  private viscosity: number = 0.004;
  /** 血液密度 (kg/m³) */
  private density: number = 1060;

  public calculateFlow(vessel: VesselComponent, deltaTime: number): number {
    // 使用泊肃叶定律计算瞬时流量: Q = (π * r^4 * ΔP) / (8 * η * L)
    const radius = vessel.diameter / 2000; // 转换为米
    let length = vessel.length / 1000; // 转换为米

    // 确保长度不为零，避免除零错误
    length = Math.max(length, 0.001); // 最小1mm

    // 将压力从 mmHg 转换为 Pa (1 mmHg = 133.322 Pa)
    const pressureDiffPa = vessel.pressure * 133.322; // 压力差 (Pa)

    // 计算瞬时流量 (m³/s)
    const instantaneousFlow = (Math.PI * Math.pow(radius, 4) * pressureDiffPa) /
                              (8 * this.viscosity * length);

    // 确保时间步长不为零，避免除零错误
    const safeDeltaTime = Math.max(deltaTime, 0.001); // 最小1ms

    // 考虑时间步长，计算在deltaTime时间内的流量
    const flowVolume = instantaneousFlow * safeDeltaTime; // m³

    // 转换为 ml/min
    const flowRatePerMinute = (flowVolume * 1000000) / (safeDeltaTime / 60);

    return Math.max(0, flowRatePerMinute); // 确保流量不为负
  }

  public updatePressure(vessel: VesselComponent, pressure: number): void {
    vessel.pressure = pressure;
  }

  public getViscosity(): number {
    return this.viscosity;
  }

  public getDensity(): number {
    return this.density;
  }
}

/**
 * 血管连接类
 */
export class VesselConnection {
  /** 连接ID */
  public id: string;
  /** 源血管 */
  public sourceVessel: VesselComponent;
  /** 目标血管 */
  public targetVessel: VesselComponent;
  /** 连接类型 */
  public type: VesselConnectionType;
  /** 连接位置 */
  public position: THREE.Vector3;
  /** 连接方向 */
  public direction: THREE.Vector3;
  /** 血管直径 */
  public diameter: number;
  /** 流量 */
  public flowRate: number;
  /** 压力 */
  public pressure: number;
  /** 阻力 */
  public resistance: number;
  /** 最大流量 */
  public maxFlowRate: number;

  constructor(sourceVessel: VesselComponent, targetVessel: VesselComponent, type: VesselConnectionType) {
    this.id = `vessel_connection_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    this.sourceVessel = sourceVessel;
    this.targetVessel = targetVessel;
    this.type = type;
    this.position = new THREE.Vector3();
    this.direction = new THREE.Vector3(0, 1, 0);
    this.diameter = Math.min(sourceVessel.diameter, targetVessel.diameter);
    this.flowRate = 0;
    this.pressure = 0;
    this.resistance = 1.0;
    this.maxFlowRate = 100.0;
  }

  /**
   * 设置流体流动参数
   * @param params 流动参数
   */
  public setFlowParameters(params: {
    resistance?: number;
    flowRate?: number;
    maxFlowRate?: number;
  }): void {
    if (params.resistance !== undefined) {
      this.resistance = params.resistance;
    }
    if (params.flowRate !== undefined) {
      this.flowRate = params.flowRate;
    }
    if (params.maxFlowRate !== undefined) {
      this.maxFlowRate = params.maxFlowRate;
    }
  }

  /**
   * 计算连接阻力
   * @returns 连接阻力
   */
  public calculateResistance(): number {
    // 基于血管直径和长度计算阻力
    const radius = this.diameter / 2000; // 转换为米

    // 计算连接长度，如果目标血管没有路径点，使用默认长度
    let length: number;
    if (this.targetVessel.pathPoints.length > 0) {
      length = this.position.distanceTo(this.targetVessel.pathPoints[0]) / 1000; // 转换为米
    } else {
      // 使用血管直径作为默认连接长度
      length = this.diameter / 1000; // 转换为米
    }

    // 确保长度不为零，避免除零错误
    length = Math.max(length, 0.001); // 最小1mm

    // 使用泊肃叶定律计算阻力: R = (8 * η * L) / (π * r^4)
    const viscosity = 0.004; // 血液粘度 (Pa·s)
    const resistance = (8 * viscosity * length) / (Math.PI * Math.pow(radius, 4));

    return Math.max(resistance, 0.1); // 确保阻力有最小值
  }
}

/**
 * 血管系统配置
 */
export interface VascularSystemConfig {
  /** 是否启用血液流动模拟 */
  enableFlowSimulation?: boolean;
  /** 是否启用压力计算 */
  enablePressureCalculation?: boolean;
  /** 是否启用血管弹性 */
  enableVesselElasticity?: boolean;
  /** 流体系统 */
  fluidSystem?: FluidSystem;
  /** 心率 (次/分钟) */
  heartRate?: number;
  /** 收缩压 (mmHg) */
  systolicPressure?: number;
  /** 舒张压 (mmHg) */
  diastolicPressure?: number;
}

export class VascularSystem {
  /** 血管网络 */
  private vessels: Map<string, VesselComponent> = new Map();
  /** 血管连接 */
  private connections: Map<string, VesselConnection> = new Map();
  /** 流体系统引用 */
  private fluidSystem: FluidSystem;
  /** 系统配置 */
  private config: VascularSystemConfig;
  /** 当前心率 */
  private heartRate: number;
  /** 收缩压 */
  private systolicPressure: number;
  /** 舒张压 */
  private diastolicPressure: number;

  /**
   * 构造函数
   * @param config 血管系统配置
   */
  constructor(config: VascularSystemConfig = {}) {
    this.config = {
      enableFlowSimulation: true,
      enablePressureCalculation: true,
      enableVesselElasticity: true,
      heartRate: 72,
      systolicPressure: 120,
      diastolicPressure: 80,
      ...config
    };

    this.fluidSystem = config.fluidSystem || new SimpleFluidSystem();
    this.heartRate = this.config.heartRate!;
    this.systolicPressure = this.config.systolicPressure!;
    this.diastolicPressure = this.config.diastolicPressure!;
  }
  
  /**
   * 添加血管
   * @param id 血管ID
   * @param vessel 血管组件
   */
  public addVessel(id: string, vessel: VesselComponent): void {
    this.vessels.set(id, vessel);
    console.log(`添加血管: ${id}, 类型: ${vessel.vesselType}`);
  }

  /**
   * 获取血管
   * @param id 血管ID
   * @returns 血管组件
   */
  public getVessel(id: string): VesselComponent | undefined {
    return this.vessels.get(id);
  }

  /**
   * 获取所有血管
   * @returns 血管映射
   */
  public getAllVessels(): Map<string, VesselComponent> {
    return this.vessels;
  }

  /**
   * 创建血管连接
   * @param sourceVessel 源血管
   * @param targetVessel 目标血管
   * @param type 连接类型 (吻合/缝合)
   */
  public createVesselConnection(
    sourceVessel: VesselComponent,
    targetVessel: VesselComponent,
    type: VesselConnectionType
  ): VesselConnection {
    // 创建血管连接
    const connection = new VesselConnection(sourceVessel, targetVessel, type);

    // 设置流体流动参数
    connection.setFlowParameters({
      resistance: 1.0,
      flowRate: 0.0,
      maxFlowRate: 100.0
    });

    // 添加到连接映射
    this.connections.set(connection.id, connection);

    // 更新血管的连接信息
    sourceVessel.connectedVessels.push(connection.id);
    targetVessel.connectedVessels.push(connection.id);

    console.log(`创建血管连接: ${connection.id}`);
    return connection;
  }

  /**
   * 获取血管连接
   * @param id 连接ID
   * @returns 血管连接
   */
  public getConnection(id: string): VesselConnection | undefined {
    return this.connections.get(id);
  }

  /**
   * 获取所有连接
   * @returns 连接映射
   */
  public getAllConnections(): Map<string, VesselConnection> {
    return this.connections;
  }

  /**
   * 设置心率
   * @param rate 心率 (次/分钟)
   */
  public setHeartRate(rate: number): void {
    this.heartRate = rate;
    console.log(`设置心率: ${rate} bpm`);
  }

  /**
   * 获取心率
   * @returns 心率
   */
  public getHeartRate(): number {
    return this.heartRate;
  }

  /**
   * 设置血压
   * @param systolic 收缩压 (mmHg)
   * @param diastolic 舒张压 (mmHg)
   */
  public setBloodPressure(systolic: number, diastolic: number): void {
    this.systolicPressure = systolic;
    this.diastolicPressure = diastolic;
    console.log(`设置血压: ${systolic}/${diastolic} mmHg`);
  }

  /**
   * 获取血压
   * @returns 血压 {收缩压, 舒张压}
   */
  public getBloodPressure(): { systolic: number; diastolic: number } {
    return {
      systolic: this.systolicPressure,
      diastolic: this.diastolicPressure
    };
  }

  /**
   * 模拟血液流动
   * @param deltaTime 时间步长
   */
  public simulateBloodFlow(deltaTime: number): void {
    if (!this.config.enableFlowSimulation) {
      return;
    }

    // 更新所有血管中的血液流动
    for (const vessel of this.vessels.values()) {
      // 计算血管中的流量
      const flow = this.fluidSystem.calculateFlow(vessel, deltaTime);
      vessel.flowRate = flow;

      // 更新压力
      if (this.config.enablePressureCalculation) {
        const currentTime = Date.now() / 1000;
        const heartCycle = 60 / this.heartRate; // 心跳周期
        const cyclePosition = (currentTime % heartCycle) / heartCycle;

        // 模拟心跳周期中的压力变化
        let pressure: number;
        if (cyclePosition < 0.35) {
          // 收缩期
          pressure = this.systolicPressure;
        } else {
          // 舒张期
          pressure = this.diastolicPressure;
        }

        // 根据血管类型调整压力
        switch (vessel.vesselType) {
          case VesselType.AORTA:
            vessel.pressure = pressure;
            break;
          case VesselType.ARTERY:
            vessel.pressure = pressure * 0.8;
            break;
          case VesselType.VEIN:
            vessel.pressure = pressure * 0.1;
            break;
          case VesselType.CAPILLARY:
            vessel.pressure = pressure * 0.3;
            break;
          default:
            vessel.pressure = pressure * 0.5;
        }

        this.fluidSystem.updatePressure(vessel, vessel.pressure);
      }
    }

    // 更新连接中的流动
    for (const connection of this.connections.values()) {
      // 计算连接阻力
      connection.resistance = connection.calculateResistance();

      // 计算流量 - 将压力从 mmHg 转换为 Pa
      const pressureDiffMmHg = connection.sourceVessel.pressure - connection.targetVessel.pressure;
      const pressureDiffPa = pressureDiffMmHg * 133.322; // 转换为 Pa

      // 使用欧姆定律类比计算流量: Q = ΔP / R
      const calculatedFlow = pressureDiffPa / connection.resistance;

      connection.flowRate = Math.min(
        Math.max(0, calculatedFlow), // 确保流量不为负
        connection.maxFlowRate
      );
    }

    console.log(`血液流动模拟更新完成，处理了 ${this.vessels.size} 个血管和 ${this.connections.size} 个连接`);
  }

  /**
   * 获取系统统计信息
   * @returns 统计信息
   */
  public getSystemStats(): {
    vesselCount: number;
    connectionCount: number;
    totalFlow: number;
    averagePressure: number;
  } {
    let totalFlow = 0;
    let totalPressure = 0;
    let vesselCount = 0;

    for (const vessel of this.vessels.values()) {
      totalFlow += vessel.flowRate;
      totalPressure += vessel.pressure;
      vesselCount++;
    }

    return {
      vesselCount: this.vessels.size,
      connectionCount: this.connections.size,
      totalFlow,
      averagePressure: vesselCount > 0 ? totalPressure / vesselCount : 0
    };
  }
}